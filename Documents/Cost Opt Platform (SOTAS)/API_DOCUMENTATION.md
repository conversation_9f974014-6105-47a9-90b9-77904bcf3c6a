# Claude Optimizer Platform - API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
Include your API key in the Authorization header:
```
Authorization: Bearer your_api_key_here
```

## Core Optimization Endpoints

### 1. Single Optimization
**POST** `/api/v1/optimize/`

Optimize a single prompt for cost reduction while maintaining quality.

**Request Body:**
```json
{
  "prompt": "Explain quantum computing in simple terms",
  "quality_threshold": 0.85,
  "optimization_level": 3,
  "model_preference": "claude-3-sonnet",
  "max_tokens": 1000,
  "temperature": 0.7
}
```

**Response:**
```json
{
  "id": "opt_12345",
  "original_prompt": "Explain quantum computing...",
  "optimized_response": "Quantum computing is...",
  "optimization_stats": {
    "original_cost": 0.045,
    "optimized_cost": 0.008,
    "savings_percentage": 82.2,
    "quality_score": 0.89,
    "processing_time_ms": 1250
  },
  "model_used": "claude-3-sonnet",
  "cache_hit": false,
  "compression_ratio": 0.75
}
```

### 2. Batch Optimization
**POST** `/api/v1/optimize/batch`

Optimize multiple prompts in parallel or sequential mode.

**Request Body:**
```json
{
  "requests": [
    {
      "prompt": "First prompt here",
      "quality_threshold": 0.8
    },
    {
      "prompt": "Second prompt here", 
      "quality_threshold": 0.9
    }
  ],
  "parallel": true,
  "max_concurrent": 5
}
```

**Response:**
```json
{
  "batch_id": "batch_67890",
  "results": [
    {
      "id": "opt_12346",
      "status": "success",
      "optimization_stats": {...}
    },
    {
      "id": "opt_12347", 
      "status": "success",
      "optimization_stats": {...}
    }
  ],
  "batch_stats": {
    "total_requests": 2,
    "successful": 2,
    "failed": 0,
    "total_savings": 156.7,
    "average_quality": 0.87
  }
}
```

## Monitoring & Metrics Endpoints

### 3. Dashboard Metrics
**GET** `/api/v1/metrics/dashboard`

Get key metrics for the dashboard display.

**Response:**
```json
{
  "total_savings": 1250.75,
  "savings_rate": 85.2,
  "avg_latency": 45,
  "cache_hit_rate": 92.5,
  "requests_today": 156,
  "quality_score_avg": 0.87,
  "recent_optimizations": [
    {
      "id": "opt_001",
      "timestamp": "2025-06-27T00:00:00Z",
      "original_cost": 10.50,
      "optimized_cost": 1.25,
      "savings": 88.1,
      "model": "claude-3-sonnet"
    }
  ]
}
```

### 4. System Metrics
**GET** `/api/v1/metrics/system`

Get detailed system performance metrics.

**Query Parameters:**
- `timeframe`: hour|day|week|month (default: day)
- `include_cache`: boolean (default: true)

**Response:**
```json
{
  "timeframe": "day",
  "metrics": {
    "requests": {
      "total": 1250,
      "successful": 1198,
      "failed": 52,
      "success_rate": 95.8
    },
    "performance": {
      "avg_latency_ms": 1250,
      "p95_latency_ms": 2100,
      "p99_latency_ms": 3500
    },
    "costs": {
      "total_original": 450.75,
      "total_optimized": 67.25,
      "total_savings": 383.50,
      "savings_rate": 85.1
    },
    "cache": {
      "hit_rate": 92.5,
      "total_hits": 1156,
      "total_misses": 94
    }
  }
}
```

### 5. Cache Statistics
**GET** `/api/v1/metrics/cache`

Get detailed cache performance across all layers.

**Response:**
```json
{
  "cache_layers": {
    "memory": {
      "hit_rate": 45.2,
      "size": "256MB",
      "entries": 15420
    },
    "redis": {
      "hit_rate": 32.1,
      "size": "1.2GB", 
      "entries": 45680
    },
    "semantic": {
      "hit_rate": 15.3,
      "size": "2.1GB",
      "entries": 12450
    }
  },
  "overall_hit_rate": 92.6,
  "cache_efficiency": 0.89
}
```

## Health & Status Endpoints

### 6. Health Check
**GET** `/health`

Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-27T00:00:00Z",
  "version": "1.0.0",
  "uptime_seconds": 86400
}
```

### 7. Detailed Health
**GET** `/api/v1/health/detailed`

Comprehensive health check including dependencies.

**Response:**
```json
{
  "status": "healthy",
  "services": {
    "database": {
      "status": "healthy",
      "response_time_ms": 12
    },
    "redis": {
      "status": "healthy", 
      "response_time_ms": 3
    },
    "openrouter": {
      "status": "healthy",
      "response_time_ms": 245
    }
  },
  "system": {
    "cpu_usage": 15.2,
    "memory_usage": 68.5,
    "disk_usage": 45.1
  }
}
```

## Configuration Endpoints

### 8. Get Settings
**GET** `/api/v1/settings`

Get current optimization settings.

**Response:**
```json
{
  "default_quality_threshold": 0.85,
  "default_optimization_level": 3,
  "compression_enabled": true,
  "cache_enabled": true,
  "max_tokens_default": 1000,
  "supported_models": [
    "claude-3-sonnet",
    "claude-3-opus", 
    "claude-3-haiku"
  ]
}
```

### 9. Update Settings
**PUT** `/api/v1/settings`

Update optimization settings.

**Request Body:**
```json
{
  "default_quality_threshold": 0.9,
  "default_optimization_level": 4,
  "compression_enabled": true
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Quality threshold must be between 0.6 and 1.0",
    "details": {
      "field": "quality_threshold",
      "value": 1.5
    }
  },
  "request_id": "req_12345"
}
```

### Common Error Codes
- `INVALID_REQUEST`: Malformed request or invalid parameters
- `UNAUTHORIZED`: Missing or invalid API key
- `RATE_LIMITED`: Too many requests
- `SERVICE_UNAVAILABLE`: External service (OpenRouter) unavailable
- `QUALITY_THRESHOLD_NOT_MET`: Response quality below threshold
- `INTERNAL_ERROR`: Unexpected server error

## Rate Limits

- **Free Tier**: 100 requests/hour
- **Paid Tier**: 1000 requests/hour
- **Batch Requests**: Count as individual requests

Rate limit headers included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## SDKs and Examples

### Python SDK
```python
from claude_optimizer import CostOptimizer

client = CostOptimizer(api_key="your_key")

result = client.optimize(
    prompt="Explain machine learning",
    quality_threshold=0.85
)

print(f"Saved ${result.savings:.2f}")
```

### cURL Examples
```bash
# Single optimization
curl -X POST "http://localhost:8000/api/v1/optimize/" \
  -H "Authorization: Bearer your_key" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Test prompt", "quality_threshold": 0.85}'

# Get metrics
curl "http://localhost:8000/api/v1/metrics/dashboard" \
  -H "Authorization: Bearer your_key"
```

## WebSocket Support

Real-time optimization updates available via WebSocket:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/optimization');
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  console.log('Optimization update:', update);
};
```

## Webhooks

Configure webhooks for optimization completion:

**POST** `/api/v1/webhooks`
```json
{
  "url": "https://your-app.com/webhook",
  "events": ["optimization.completed", "batch.completed"]
}
```
