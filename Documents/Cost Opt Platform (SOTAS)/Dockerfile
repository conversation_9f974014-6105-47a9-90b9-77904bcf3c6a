# Production-Grade Multi-Stage Dockerfile for Claude Cost Optimizer
# Optimized for OrbStack deployment with security hardening

# Build stage - compile dependencies and prepare application
FROM python:3.11-slim AS builder

# Set build environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install poetry for dependency management
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy dependency files
COPY requirements.txt requirements-prod.txt ./

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies with security updates
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements-prod.txt && \
    pip install --no-cache-dir gunicorn[gevent] && \
    pip check

# Production stage - minimal runtime image
FROM python:3.11-slim AS production

# Security labels for container scanning
LABEL maintainer="Cost Optimization Platform" \
      version="1.0.0" \
      description="Production Claude Cost Optimizer" \
      security.scan="enabled"

# Set production environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    DEBUG=false \
    WORKERS=4 \
    MAX_WORKERS=8 \
    WORKER_CLASS=gevent \
    WORKER_CONNECTIONS=1000 \
    TIMEOUT=30 \
    KEEPALIVE=2 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100

# Install only runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    libpq5 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && rm -rf /tmp/* /var/tmp/*

# Create non-root user with specific UID/GID for security
RUN groupadd -r -g 1001 appuser && \
    useradd -r -u 1001 -g appuser -d /app -s /bin/bash appuser

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Set work directory
WORKDIR /app

# Copy application code with proper ownership
COPY --chown=appuser:appuser src/ ./src/
COPY --chown=appuser:appuser alembic/ ./alembic/
COPY --chown=appuser:appuser alembic.ini ./
COPY --chown=appuser:appuser scripts/ ./scripts/
COPY --chown=appuser:appuser static/ ./static/

# Copy configuration files
COPY --chown=appuser:appuser simple-entrypoint.sh ./
COPY --chown=appuser:appuser gunicorn.conf.py ./
COPY --chown=appuser:appuser healthcheck.py ./

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data /app/tmp /app/cache \
    && chown -R appuser:appuser /app \
    && chmod 755 /app \
    && chmod 750 /app/logs /app/data /app/tmp /app/cache \
    && chmod +x simple-entrypoint.sh \
    && chmod +x healthcheck.py \
    && chmod +x scripts/*.py 2>/dev/null || true

# Security hardening - remove unnecessary packages and files
RUN find /app -type f -name "*.pyc" -delete \
    && find /app -type d -name "__pycache__" -delete \
    && rm -rf /root/.cache /tmp/* /var/tmp/*

# Switch to non-root user
USER appuser

# Expose port (non-privileged)
EXPOSE 8000

# Add security headers and resource limits
ENV SECURE_HEADERS=true \
    RATE_LIMIT_ENABLED=true \
    CORS_ENABLED=true \
    REQUEST_TIMEOUT=30 \
    MAX_REQUEST_SIZE=10485760

# Health check with improved reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python healthcheck.py || exit 1

# Production-optimized startup command
CMD ["./simple-entrypoint.sh"]
