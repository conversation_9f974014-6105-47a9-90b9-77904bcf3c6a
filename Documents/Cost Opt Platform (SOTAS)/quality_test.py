#!/usr/bin/env python3
"""
Quality Test for Cost Optimization Platform
Test the improved quality with Claude 4 Sonnet
"""

import asyncio
import time
from cost_optimizer import CostOptimizer, OptimizationRequest, TaskComplexity

async def test_quality_improvements():
    """Test quality improvements with different complexity levels"""
    print("🧪 Testing Quality Improvements")
    print("=" * 50)
    
    # Load environment variables manually
    import os
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except FileNotFoundError:
        print("⚠️  .env file not found")
    
    optimizer = CostOptimizer()
    
    test_cases = [
        {
            "name": "Simple Task (High Quality)",
            "request": OptimizationRequest(
                prompt="What is the capital of France?",
                quality_threshold=0.9,
                prefer_cost=False
            )
        },
        {
            "name": "Simple Task (Cost Optimized)",
            "request": OptimizationRequest(
                prompt="What is 2+2?",
                quality_threshold=0.6,
                prefer_cost=True
            )
        },
        {
            "name": "Complex Task (High Quality)",
            "request": OptimizationRequest(
                prompt="Explain quantum computing and its applications in cryptography",
                quality_threshold=0.9,
                prefer_cost=False,
                task_complexity=TaskComplexity.COMPLEX
            )
        },
        {
            "name": "Expert Task (Premium Required)",
            "request": OptimizationRequest(
                prompt="Design a distributed system architecture for handling 1 million concurrent users with fault tolerance",
                quality_threshold=0.95,
                prefer_cost=False,
                task_complexity=TaskComplexity.EXPERT
            )
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Test model selection
            selected_model = optimizer._select_optimal_model(test_case['request'])
            print(f"Selected Model: {selected_model}")
            
            # Test optimization (if API keys available)
            if os.getenv("OPENROUTER_API_KEY") and not os.getenv("OPENROUTER_API_KEY").startswith("your-"):
                start_time = time.time()
                result = await optimizer.optimize(test_case['request'])
                processing_time = (time.time() - start_time) * 1000
                
                print(f"✅ Optimization completed:")
                print(f"   Model Used: {result.model_used}")
                print(f"   Cost: ${result.cost_usd:.6f}")
                print(f"   Quality Score: {result.quality_score:.2f}")
                print(f"   Processing Time: {processing_time:.1f}ms")
                print(f"   Savings: ${result.optimization_savings:.6f}")
                print(f"   Response Preview: {result.content[:100]}...")
                
                results.append({
                    "test_name": test_case['name'],
                    "model_used": result.model_used,
                    "cost_usd": float(result.cost_usd),
                    "quality_score": result.quality_score,
                    "processing_time_ms": processing_time,
                    "savings_usd": float(result.optimization_savings)
                })
            else:
                print("⚠️  API key not configured, testing model selection only")
                
                # Estimate quality and cost
                estimated_quality = optimizer._estimate_quality_score(selected_model, test_case['request'].task_complexity or TaskComplexity.MEDIUM)
                estimated_cost = optimizer._calculate_cost(selected_model, 100, 50)  # Rough estimate
                
                print(f"   Estimated Quality: {estimated_quality:.2f}")
                print(f"   Estimated Cost: ${estimated_cost:.6f}")
                
                results.append({
                    "test_name": test_case['name'],
                    "model_used": selected_model,
                    "estimated_quality": estimated_quality,
                    "estimated_cost": float(estimated_cost)
                })
        
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append({
                "test_name": test_case['name'],
                "error": str(e)
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 QUALITY TEST SUMMARY")
    print("=" * 50)
    
    claude_usage = sum(1 for r in results if r.get('model_used') == 'claude-4-sonnet')
    llama_usage = sum(1 for r in results if r.get('model_used') == 'llama-3-8b')
    
    print(f"Claude 4 Sonnet Usage: {claude_usage}/{len(results)} tests")
    print(f"Llama 3 8B Usage: {llama_usage}/{len(results)} tests")
    
    if claude_usage > 0:
        print("✅ Quality-first approach working - using premium model for complex tasks")
    else:
        print("⚠️  All requests using budget model - check quality thresholds")
    
    # Calculate average quality and cost
    quality_scores = [r.get('quality_score', r.get('estimated_quality', 0)) for r in results if 'error' not in r]
    costs = [r.get('cost_usd', r.get('estimated_cost', 0)) for r in results if 'error' not in r]
    
    if quality_scores:
        avg_quality = sum(quality_scores) / len(quality_scores)
        total_cost = sum(costs)
        print(f"Average Quality Score: {avg_quality:.2f}")
        print(f"Total Cost: ${total_cost:.6f}")
        
        if avg_quality >= 0.8:
            print("✅ Quality target met (≥0.8)")
        else:
            print("❌ Quality target not met (<0.8)")
    
    return results

if __name__ == "__main__":
    results = asyncio.run(test_quality_improvements())
    
    # Save results
    import json
    with open("quality_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Results saved to: quality_test_results.json")
