#!/usr/bin/env python3
"""
Port Manager & Service Configuration
Scan and manage ports for all services to prevent conflicts
"""

import socket
import json
import subprocess
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class PortManager:
    """Manage ports and service configurations"""
    
    def __init__(self):
        self.service_ports = {
            "fastapi": {"preferred": 8000, "range": (8000, 8010), "status": "unknown"},
            "redis": {"preferred": 6379, "range": (6379, 6389), "status": "unknown"},
            "prometheus": {"preferred": 8001, "range": (8001, 8005), "status": "unknown"},
            "grafana": {"preferred": 3000, "range": (3000, 3010), "status": "unknown"},
            "jaeger": {"preferred": 16686, "range": (16686, 16690), "status": "unknown"},
            "chromadb": {"preferred": 8002, "range": (8002, 8006), "status": "unknown"},
            "celery_flower": {"preferred": 5555, "range": (5555, 5560), "status": "unknown"},
            "nginx": {"preferred": 80, "range": (8080, 8090), "status": "unknown"},
            "frontend": {"preferred": 3001, "range": (3001, 3010), "status": "unknown"},
            "k3s_api": {"preferred": 6443, "range": (6443, 6450), "status": "unknown"}
        }
        
        self.port_assignments = {}
        self.scan_results = {}
    
    def scan_port_ranges(self) -> Dict[str, List[int]]:
        """Scan specified port ranges for availability"""
        print("🔍 Scanning port ranges for availability...")
        
        ranges_to_scan = [
            (3000, 4000),  # Frontend/Web services
            (6000, 7000),  # Database/Cache services  
            (8000, 9000),  # API/Backend services
            (5000, 6000),  # Monitoring/Admin services
        ]
        
        available_ports = {"available": [], "occupied": []}
        
        for start, end in ranges_to_scan:
            print(f"  Scanning ports {start}-{end}...")
            
            for port in range(start, end + 1):
                if self._is_port_available(port):
                    available_ports["available"].append(port)
                else:
                    occupied_ports_info = self._get_port_info(port)
                    available_ports["occupied"].append({
                        "port": port,
                        "info": occupied_ports_info
                    })
        
        self.scan_results = available_ports
        print(f"  ✅ Found {len(available_ports['available'])} available ports")
        print(f"  ⚠️ Found {len(available_ports['occupied'])} occupied ports")
        
        return available_ports
    
    def _is_port_available(self, port: int) -> bool:
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0
        except:
            return True
    
    def _get_port_info(self, port: int) -> str:
        """Get information about what's using a port"""
        try:
            # Try to get process info using netstat
            result = subprocess.run(
                ['netstat', '-tulpn'], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            
            for line in result.stdout.split('\n'):
                if f':{port} ' in line:
                    return line.strip()
            
            return "Unknown process"
            
        except:
            return "Could not determine"
    
    def assign_optimal_ports(self) -> Dict[str, int]:
        """Assign optimal ports to all services"""
        print("🎯 Assigning optimal ports to services...")
        
        if not self.scan_results:
            self.scan_port_ranges()
        
        available_ports = set(self.scan_results["available"])
        
        for service, config in self.service_ports.items():
            preferred_port = config["preferred"]
            port_range = config["range"]
            
            # Try preferred port first
            if preferred_port in available_ports:
                self.port_assignments[service] = preferred_port
                available_ports.remove(preferred_port)
                config["status"] = "assigned_preferred"
                print(f"  ✅ {service}: {preferred_port} (preferred)")
            else:
                # Find alternative in range
                alternative = None
                for port in range(port_range[0], port_range[1] + 1):
                    if port in available_ports:
                        alternative = port
                        break
                
                if alternative:
                    self.port_assignments[service] = alternative
                    available_ports.remove(alternative)
                    config["status"] = "assigned_alternative"
                    print(f"  ✅ {service}: {alternative} (alternative)")
                else:
                    config["status"] = "no_port_available"
                    print(f"  ❌ {service}: No available port in range {port_range}")
        
        return self.port_assignments
    
    def generate_service_configs(self) -> Dict[str, Dict[str, any]]:
        """Generate configuration files for all services"""
        print("📝 Generating service configurations...")
        
        configs = {}
        
        # FastAPI configuration
        if "fastapi" in self.port_assignments:
            configs["fastapi"] = {
                "host": "0.0.0.0",
                "port": self.port_assignments["fastapi"],
                "reload": True,
                "workers": 4,
                "log_level": "info"
            }
        
        # Redis configuration
        if "redis" in self.port_assignments:
            configs["redis"] = {
                "port": self.port_assignments["redis"],
                "bind": "127.0.0.1",
                "maxmemory": "256mb",
                "maxmemory-policy": "allkeys-lru",
                "save": "900 1 300 10 60 10000"
            }
        
        # Prometheus configuration
        if "prometheus" in self.port_assignments:
            configs["prometheus"] = {
                "global": {
                    "scrape_interval": "15s",
                    "evaluation_interval": "15s"
                },
                "scrape_configs": [
                    {
                        "job_name": "cost-optimizer",
                        "static_configs": [
                            {
                                "targets": [f"localhost:{self.port_assignments['prometheus']}"]
                            }
                        ]
                    }
                ]
            }
        
        # Grafana configuration
        if "grafana" in self.port_assignments:
            configs["grafana"] = {
                "server": {
                    "http_port": self.port_assignments["grafana"],
                    "domain": "localhost"
                },
                "database": {
                    "type": "sqlite3",
                    "path": "grafana.db"
                }
            }
        
        # Frontend configuration
        if "frontend" in self.port_assignments:
            configs["frontend"] = {
                "port": self.port_assignments["frontend"],
                "proxy": {
                    "/api": f"http://localhost:{self.port_assignments.get('fastapi', 8000)}"
                }
            }
        
        return configs
    
    def generate_docker_compose(self) -> str:
        """Generate docker-compose.yml with assigned ports"""
        compose_template = f"""version: '3.8'

services:
  fastapi:
    build: .
    ports:
      - "{self.port_assignments.get('fastapi', 8000)}:8000"
    environment:
      - DATABASE_URL=sqlite+aiosqlite:///./data/cost_optimizer.db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - chromadb
    volumes:
      - ./data:/app/data
    networks:
      - cost-optimizer-network

  redis:
    image: redis:7-alpine
    ports:
      - "{self.port_assignments.get('redis', 6379)}:6379"
    volumes:
      - redis_data:/data
    networks:
      - cost-optimizer-network

  chromadb:
    image: chromadb/chroma:latest
    ports:
      - "{self.port_assignments.get('chromadb', 8002)}:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    networks:
      - cost-optimizer-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "{self.port_assignments.get('prometheus', 8001)}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - cost-optimizer-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "{self.port_assignments.get('grafana', 3000)}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - cost-optimizer-network

  frontend:
    build: ./frontend
    ports:
      - "{self.port_assignments.get('frontend', 3001)}:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:{self.port_assignments.get('fastapi', 8000)}
    networks:
      - cost-optimizer-network

volumes:
  redis_data:
  chromadb_data:
  prometheus_data:
  grafana_data:

networks:
  cost-optimizer-network:
    driver: bridge
"""
        return compose_template
    
    def generate_kubernetes_manifests(self) -> Dict[str, str]:
        """Generate Kubernetes manifests with assigned ports"""
        manifests = {}
        
        # FastAPI deployment
        manifests["fastapi-deployment.yaml"] = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: fastapi-deployment
  labels:
    app: fastapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fastapi
  template:
    metadata:
      labels:
        app: fastapi
    spec:
      containers:
      - name: fastapi
        image: cost-optimizer:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "sqlite+aiosqlite:///./data/cost_optimizer.db"
        - name: REDIS_URL
          value: "redis://redis-service:6379/0"
---
apiVersion: v1
kind: Service
metadata:
  name: fastapi-service
spec:
  selector:
    app: fastapi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
      nodePort: {self.port_assignments.get('fastapi', 8000)}
  type: NodePort
"""
        
        # Redis deployment
        manifests["redis-deployment.yaml"] = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-deployment
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
      nodePort: {self.port_assignments.get('redis', 6379)}
  type: NodePort
"""
        
        return manifests
    
    def save_port_documentation(self, filename: str = "port_assignments.json"):
        """Save port assignments and documentation"""
        documentation = {
            "timestamp": datetime.utcnow().isoformat(),
            "port_assignments": self.port_assignments,
            "service_configurations": self.service_ports,
            "scan_results": {
                "available_ports_count": len(self.scan_results.get("available", [])),
                "occupied_ports_count": len(self.scan_results.get("occupied", [])),
                "total_scanned": len(self.scan_results.get("available", [])) + len(self.scan_results.get("occupied", []))
            },
            "port_ranges_scanned": [
                "3000-4000 (Frontend/Web services)",
                "6000-7000 (Database/Cache services)",
                "8000-9000 (API/Backend services)",
                "5000-6000 (Monitoring/Admin services)"
            ],
            "service_descriptions": {
                "fastapi": "Main API server for cost optimization platform",
                "redis": "Cache and session storage",
                "prometheus": "Metrics collection and monitoring",
                "grafana": "Metrics visualization dashboard",
                "jaeger": "Distributed tracing",
                "chromadb": "Vector database for embeddings",
                "celery_flower": "Celery task monitoring",
                "nginx": "Reverse proxy and load balancer",
                "frontend": "React.js frontend application",
                "k3s_api": "Kubernetes API server"
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(documentation, f, indent=2)
        
        print(f"📄 Port documentation saved to {filename}")
    
    def print_port_summary(self):
        """Print a summary of port assignments"""
        print("\n" + "=" * 60)
        print("📊 PORT ASSIGNMENT SUMMARY")
        print("=" * 60)
        
        for service, port in self.port_assignments.items():
            status = self.service_ports[service]["status"]
            preferred = self.service_ports[service]["preferred"]
            
            status_icon = "✅" if status.startswith("assigned") else "❌"
            preferred_text = " (preferred)" if port == preferred else " (alternative)"
            
            print(f"{status_icon} {service:15} : {port:5}{preferred_text}")
        
        print(f"\nTotal services configured: {len(self.port_assignments)}")
        print(f"Services using preferred ports: {sum(1 for s in self.service_ports.values() if s['status'] == 'assigned_preferred')}")

def main():
    """Run port management and configuration"""
    print("🚀 PORT MANAGER & SERVICE CONFIGURATION")
    print("=" * 50)
    
    manager = PortManager()
    
    # Scan ports
    manager.scan_port_ranges()
    
    # Assign ports
    manager.assign_optimal_ports()
    
    # Generate configurations
    configs = manager.generate_service_configs()
    
    # Generate Docker Compose
    docker_compose = manager.generate_docker_compose()
    with open("docker-compose.yml", "w") as f:
        f.write(docker_compose)
    print("📄 Generated docker-compose.yml")
    
    # Generate Kubernetes manifests
    k8s_manifests = manager.generate_kubernetes_manifests()
    for filename, content in k8s_manifests.items():
        with open(f"k8s/{filename}", "w") as f:
            f.write(content)
    print(f"📄 Generated {len(k8s_manifests)} Kubernetes manifests")
    
    # Save documentation
    manager.save_port_documentation()
    
    # Print summary
    manager.print_port_summary()
    
    print("\n🎉 Port management and service configuration complete!")

if __name__ == "__main__":
    main()
