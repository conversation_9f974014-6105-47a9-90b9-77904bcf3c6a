# Production Dependencies for Cost Optimization Platform
# FAANG+ production standards with security and performance focus

# Production WSGI Server
gunicorn>=21.2.0
uvicorn[standard]>=0.23.0

# Process Management
dumb-init>=1.2.5
setproctitle>=1.3.2

# Production Monitoring
prometheus-client>=0.17.0
opentelemetry-api>=1.18.0
opentelemetry-sdk>=1.18.0
opentelemetry-exporter-jaeger>=1.18.0
opentelemetry-instrumentation-fastapi>=0.39b0
opentelemetry-instrumentation-sqlalchemy>=0.39b0
opentelemetry-instrumentation-redis>=0.39b0

# Production Logging
structlog>=23.1.0
python-json-logger>=2.0.7

# Security
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# Performance Optimization
orjson>=3.9.0  # Faster JSON serialization
ujson>=5.8.0   # Alternative fast JSON
msgpack>=1.0.5  # Binary serialization

# Memory Optimization
pympler>=0.9    # Memory profiling
psutil>=5.9.0   # System monitoring

# Production Database
asyncpg>=0.28.0  # PostgreSQL async driver
psycopg2-binary>=2.9.7  # PostgreSQL sync driver (backup)

# Production Cache
redis[hiredis]>=4.6.0  # Redis with C parser
hiredis>=2.2.3

# HTTP Client Optimization
httpx[http2]>=0.24.0
aiofiles>=23.1.0

# Compression
lz4>=4.3.2
zstandard>=0.21.0

# Production Configuration
python-dotenv>=1.0.0
pydantic-settings>=2.0.0

# Health Checks
aiohttp>=3.8.0  # For health check client

# Graceful Shutdown
signal-handler>=1.0.0

# Production Utilities
click>=8.1.0  # CLI tools
rich>=13.4.0  # Rich console output
typer>=0.9.0  # CLI framework
