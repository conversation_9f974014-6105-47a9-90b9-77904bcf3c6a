# Development Environment Configuration
# Claude Cost Optimization Platform

# Core Settings
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Database
DATABASE_URL=postgresql://costopt:costopt123@localhost:5432/costopt_dev

# Redis
REDIS_URL=redis://localhost:6379/0

# OpenRouter (Required - Get from https://openrouter.ai/keys)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Security (Development Only)
SECRET_KEY=development-secret-key-32-characters-long

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:3000

# Vector Databases
CHROMADB_HOST=localhost
CHROMADB_PORT=8001
QDRANT_HOST=localhost
QDRANT_PORT=6333
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
MILVUS_HOST=localhost
MILVUS_PORT=19530
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# Monitoring
JAEGER_HOST=localhost
JAEGER_PORT=14268
JAEGER_ENABLED=true
METRICS_ENABLED=true
LOG_LEVEL=DEBUG
LOG_FORMAT=text

# Performance (Development)
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT_SECONDS=60
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

# Features
COMPRESSION_ENABLED=true
ADAPTIVE_LEARNING_ENABLED=true
VECTOR_CACHING_ENABLED=true
QUALITY_ASSESSMENT_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true
