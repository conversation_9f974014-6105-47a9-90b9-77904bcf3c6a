#!/usr/bin/env python3
"""
Production Deployment Validation for Cost Optimization Platform
Comprehensive end-to-end testing of the LiteLLM-based platform
"""

import asyncio
import json
import time
import requests
import concurrent.futures
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ValidationResult:
    test_name: str
    status: str  # PASS, FAIL, WARNING
    details: str
    latency_ms: Optional[float] = None
    error: Optional[str] = None

class ProductionValidator:
    """Comprehensive production validation suite"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.results = []
        
    def log_result(self, result: ValidationResult):
        """Log validation result"""
        self.results.append(result)
        status_emoji = "✅" if result.status == "PASS" else "⚠️" if result.status == "WARNING" else "❌"
        print(f"{status_emoji} {result.test_name}: {result.details}")
        if result.latency_ms:
            print(f"   Latency: {result.latency_ms:.1f}ms")
        if result.error:
            print(f"   Error: {result.error}")
    
    def test_health_endpoint(self) -> ValidationResult:
        """Test health check endpoint"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            latency_ms = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    return ValidationResult(
                        "Health Check",
                        "PASS",
                        f"Service healthy (HTTP {response.status_code})",
                        latency_ms
                    )
                else:
                    return ValidationResult(
                        "Health Check",
                        "FAIL",
                        f"Unhealthy status: {data.get('status')}",
                        latency_ms
                    )
            else:
                return ValidationResult(
                    "Health Check",
                    "FAIL",
                    f"HTTP {response.status_code}",
                    latency_ms
                )
        except Exception as e:
            return ValidationResult(
                "Health Check",
                "FAIL",
                "Connection failed",
                error=str(e)
            )
    
    def test_optimization_endpoint(self) -> ValidationResult:
        """Test optimization endpoint with real request"""
        try:
            start_time = time.time()
            payload = {
                "prompt": "What is machine learning?",
                "max_tokens": 100,
                "temperature": 0.7,
                "prefer_cost": True
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/optimize",
                json=payload,
                timeout=30
            )
            latency_ms = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ["content", "model_used", "cost_usd", "tokens_used"]
                
                if all(field in data for field in required_fields):
                    cost_usd = data.get("cost_usd", 0)
                    savings = data.get("optimization_savings", 0)
                    
                    return ValidationResult(
                        "Optimization API",
                        "PASS",
                        f"Model: {data['model_used']}, Cost: ${cost_usd:.6f}, Savings: ${savings:.6f}",
                        latency_ms
                    )
                else:
                    missing = [f for f in required_fields if f not in data]
                    return ValidationResult(
                        "Optimization API",
                        "FAIL",
                        f"Missing fields: {missing}",
                        latency_ms
                    )
            else:
                return ValidationResult(
                    "Optimization API",
                    "FAIL",
                    f"HTTP {response.status_code}: {response.text[:100]}",
                    latency_ms
                )
        except Exception as e:
            return ValidationResult(
                "Optimization API",
                "FAIL",
                "Request failed",
                error=str(e)
            )
    
    def test_stats_endpoint(self) -> ValidationResult:
        """Test statistics endpoint"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/v1/stats", timeout=10)
            latency_ms = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ["total_requests", "total_cost_usd", "total_savings_usd"]
                
                if all(field in data for field in required_fields):
                    return ValidationResult(
                        "Statistics API",
                        "PASS",
                        f"Requests: {data['total_requests']}, Cost: ${data['total_cost_usd']:.4f}",
                        latency_ms
                    )
                else:
                    return ValidationResult(
                        "Statistics API",
                        "FAIL",
                        "Missing required fields",
                        latency_ms
                    )
            else:
                return ValidationResult(
                    "Statistics API",
                    "FAIL",
                    f"HTTP {response.status_code}",
                    latency_ms
                )
        except Exception as e:
            return ValidationResult(
                "Statistics API",
                "FAIL",
                "Request failed",
                error=str(e)
            )
    
    def test_dashboard_access(self) -> ValidationResult:
        """Test web dashboard accessibility"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/", timeout=10)
            latency_ms = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                content = response.text
                if "Cost Optimization Platform" in content and "optimize()" in content:
                    return ValidationResult(
                        "Web Dashboard",
                        "PASS",
                        "Dashboard accessible with expected content",
                        latency_ms
                    )
                else:
                    return ValidationResult(
                        "Web Dashboard",
                        "WARNING",
                        "Dashboard accessible but content may be incomplete",
                        latency_ms
                    )
            else:
                return ValidationResult(
                    "Web Dashboard",
                    "FAIL",
                    f"HTTP {response.status_code}",
                    latency_ms
                )
        except Exception as e:
            return ValidationResult(
                "Web Dashboard",
                "FAIL",
                "Request failed",
                error=str(e)
            )
    
    def test_concurrent_requests(self, num_requests: int = 5) -> ValidationResult:
        """Test concurrent request handling"""
        try:
            def make_request():
                start_time = time.time()
                payload = {"prompt": f"Test request {time.time()}", "max_tokens": 50}
                response = requests.post(
                    f"{self.base_url}/api/v1/optimize",
                    json=payload,
                    timeout=30
                )
                latency = (time.time() - start_time) * 1000
                return response.status_code == 200, latency
            
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:
                futures = [executor.submit(make_request) for _ in range(num_requests)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            total_time = (time.time() - start_time) * 1000
            successful = sum(1 for success, _ in results if success)
            avg_latency = sum(latency for _, latency in results) / len(results)
            
            if successful == num_requests:
                return ValidationResult(
                    "Concurrent Requests",
                    "PASS",
                    f"{successful}/{num_requests} successful, avg latency: {avg_latency:.1f}ms",
                    total_time
                )
            elif successful > 0:
                return ValidationResult(
                    "Concurrent Requests",
                    "WARNING",
                    f"{successful}/{num_requests} successful",
                    total_time
                )
            else:
                return ValidationResult(
                    "Concurrent Requests",
                    "FAIL",
                    "All requests failed",
                    total_time
                )
        except Exception as e:
            return ValidationResult(
                "Concurrent Requests",
                "FAIL",
                "Test failed",
                error=str(e)
            )
    
    def test_cost_optimization_validation(self) -> ValidationResult:
        """Validate cost optimization is working"""
        try:
            # Test with different complexity prompts
            test_cases = [
                {"prompt": "What is 2+2?", "expected_model": "llama-3-8b"},
                {"prompt": "Explain quantum computing in detail", "expected_model": "claude-4-sonnet"}
            ]
            
            results = []
            for case in test_cases:
                response = requests.post(
                    f"{self.base_url}/api/v1/optimize",
                    json=case,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    results.append({
                        "model": data.get("model_used"),
                        "cost": data.get("cost_usd", 0),
                        "savings": data.get("optimization_savings", 0)
                    })
            
            if len(results) == 2:
                total_savings = sum(r["savings"] for r in results)
                if total_savings > 0:
                    return ValidationResult(
                        "Cost Optimization",
                        "PASS",
                        f"Optimization working, total savings: ${total_savings:.6f}"
                    )
                else:
                    return ValidationResult(
                        "Cost Optimization",
                        "WARNING",
                        "No cost savings detected"
                    )
            else:
                return ValidationResult(
                    "Cost Optimization",
                    "FAIL",
                    "Could not complete optimization tests"
                )
        except Exception as e:
            return ValidationResult(
                "Cost Optimization",
                "FAIL",
                "Test failed",
                error=str(e)
            )
    
    def run_full_validation(self) -> Dict:
        """Run complete validation suite"""
        print("🚅 Production Deployment Validation")
        print("=" * 50)
        
        # Run all tests
        tests = [
            self.test_health_endpoint,
            self.test_optimization_endpoint,
            self.test_stats_endpoint,
            self.test_dashboard_access,
            self.test_concurrent_requests,
            self.test_cost_optimization_validation
        ]
        
        for test in tests:
            result = test()
            self.log_result(result)
            time.sleep(1)  # Brief pause between tests
        
        # Calculate summary
        passed = sum(1 for r in self.results if r.status == "PASS")
        warnings = sum(1 for r in self.results if r.status == "WARNING")
        failed = sum(1 for r in self.results if r.status == "FAIL")
        
        # Performance analysis
        latencies = [r.latency_ms for r in self.results if r.latency_ms is not None]
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        max_latency = max(latencies) if latencies else 0
        
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_tests": len(self.results),
            "passed": passed,
            "warnings": warnings,
            "failed": failed,
            "success_rate": (passed / len(self.results)) * 100,
            "avg_latency_ms": round(avg_latency, 1),
            "max_latency_ms": round(max_latency, 1),
            "performance_targets": {
                "latency_under_100ms": avg_latency < 100,
                "all_tests_pass": failed == 0,
                "production_ready": failed == 0 and warnings <= 1
            },
            "detailed_results": [
                {
                    "test": r.test_name,
                    "status": r.status,
                    "details": r.details,
                    "latency_ms": r.latency_ms,
                    "error": r.error
                }
                for r in self.results
            ]
        }
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {passed} ✅")
        print(f"Warnings: {warnings} ⚠️")
        print(f"Failed: {failed} ❌")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Average Latency: {summary['avg_latency_ms']}ms")
        print(f"Max Latency: {summary['max_latency_ms']}ms")
        
        print("\n🎯 PRODUCTION READINESS:")
        targets = summary['performance_targets']
        print(f"• All Tests Pass: {'✅' if targets['all_tests_pass'] else '❌'}")
        print(f"• Latency < 100ms: {'✅' if targets['latency_under_100ms'] else '❌'}")
        print(f"• Production Ready: {'✅' if targets['production_ready'] else '❌'}")
        
        if targets['production_ready']:
            print("\n🚀 DEPLOYMENT STATUS: READY FOR PRODUCTION")
        else:
            print("\n⚠️  DEPLOYMENT STATUS: NEEDS ATTENTION")
        
        return summary

def main():
    """Main validation execution"""
    validator = ProductionValidator()
    summary = validator.run_full_validation()
    
    # Save results
    with open("production_validation_results.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: production_validation_results.json")
    
    # Return exit code based on results
    return 0 if summary['performance_targets']['production_ready'] else 1

if __name__ == "__main__":
    exit(main())
