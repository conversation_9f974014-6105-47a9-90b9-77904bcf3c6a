#!/usr/bin/env python3
"""
API Documentation Generator
Generates comprehensive API documentation from OpenAPI spec
FAANG+ documentation standards with examples and SDKs
"""

import json
import yaml
import requests
from pathlib import Path
from typing import Dict, Any, List
import argparse


class APIDocumentationGenerator:
    """Generate comprehensive API documentation"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.openapi_spec = None
        self.docs_dir = Path("docs/api")
        self.docs_dir.mkdir(parents=True, exist_ok=True)
    
    def fetch_openapi_spec(self) -> Dict[str, Any]:
        """Fetch OpenAPI specification from the API"""
        try:
            response = requests.get(f"{self.base_url}/openapi.json")
            response.raise_for_status()
            self.openapi_spec = response.json()
            return self.openapi_spec
        except Exception as e:
            print(f"Failed to fetch OpenAPI spec: {e}")
            return {}
    
    def generate_postman_collection(self) -> Dict[str, Any]:
        """Generate Postman collection from OpenAPI spec"""
        if not self.openapi_spec:
            return {}
        
        collection = {
            "info": {
                "name": self.openapi_spec.get("info", {}).get("title", "API"),
                "description": self.openapi_spec.get("info", {}).get("description", ""),
                "version": self.openapi_spec.get("info", {}).get("version", "1.0.0"),
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "auth": {
                "type": "apikey",
                "apikey": [
                    {"key": "key", "value": "X-API-Key", "type": "string"},
                    {"key": "value", "value": "{{api_key}}", "type": "string"},
                    {"key": "in", "value": "header", "type": "string"}
                ]
            },
            "variable": [
                {
                    "key": "base_url",
                    "value": "https://api.cost-optimizer.com",
                    "type": "string"
                },
                {
                    "key": "api_key",
                    "value": "your-api-key-here",
                    "type": "string"
                }
            ],
            "item": []
        }
        
        # Generate requests for each endpoint
        for path, methods in self.openapi_spec.get("paths", {}).items():
            for method, spec in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    request_item = self._create_postman_request(path, method, spec)
                    collection["item"].append(request_item)
        
        return collection
    
    def _create_postman_request(self, path: str, method: str, spec: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Postman request item"""
        request = {
            "name": spec.get("summary", f"{method.upper()} {path}"),
            "request": {
                "method": method.upper(),
                "header": [
                    {
                        "key": "Content-Type",
                        "value": "application/json",
                        "type": "text"
                    }
                ],
                "url": {
                    "raw": f"{{{{base_url}}}}{path}",
                    "host": ["{{base_url}}"],
                    "path": path.strip("/").split("/")
                }
            },
            "response": []
        }
        
        # Add request body for POST/PUT/PATCH
        if method.upper() in ["POST", "PUT", "PATCH"]:
            request_body = spec.get("requestBody", {})
            if request_body:
                content = request_body.get("content", {})
                json_content = content.get("application/json", {})
                schema = json_content.get("schema", {})
                
                # Generate example from schema
                example = self._generate_example_from_schema(schema)
                request["request"]["body"] = {
                    "mode": "raw",
                    "raw": json.dumps(example, indent=2),
                    "options": {
                        "raw": {
                            "language": "json"
                        }
                    }
                }
        
        return request
    
    def _generate_example_from_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate example data from JSON schema"""
        if schema.get("type") == "object":
            properties = schema.get("properties", {})
            example = {}
            
            for prop_name, prop_schema in properties.items():
                if prop_schema.get("type") == "string":
                    if "example" in prop_schema:
                        example[prop_name] = prop_schema["example"]
                    elif prop_name == "prompt":
                        example[prop_name] = "Create a Python function that calculates factorial"
                    elif prop_name == "user_id":
                        example[prop_name] = "user123"
                    else:
                        example[prop_name] = f"example_{prop_name}"
                elif prop_schema.get("type") == "number":
                    example[prop_name] = prop_schema.get("example", 0.8)
                elif prop_schema.get("type") == "integer":
                    example[prop_name] = prop_schema.get("example", 3)
                elif prop_schema.get("type") == "boolean":
                    example[prop_name] = prop_schema.get("example", True)
                elif prop_schema.get("type") == "array":
                    example[prop_name] = []
            
            return example
        
        return {}
    
    def generate_curl_examples(self) -> str:
        """Generate cURL examples for all endpoints"""
        if not self.openapi_spec:
            return ""
        
        curl_examples = "# cURL Examples\n\n"
        
        for path, methods in self.openapi_spec.get("paths", {}).items():
            for method, spec in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    curl_examples += self._create_curl_example(path, method, spec)
                    curl_examples += "\n\n"
        
        return curl_examples
    
    def _create_curl_example(self, path: str, method: str, spec: Dict[str, Any]) -> str:
        """Create a cURL example for an endpoint"""
        summary = spec.get("summary", f"{method.upper()} {path}")
        description = spec.get("description", "").split("\n")[0]
        
        curl = f"## {summary}\n"
        if description:
            curl += f"{description}\n\n"
        
        curl += f"```bash\ncurl -X {method.upper()} \\\n"
        curl += f'  "https://api.cost-optimizer.com{path}" \\\n'
        curl += f'  -H "Content-Type: application/json" \\\n'
        curl += f'  -H "X-API-Key: your-api-key"'
        
        # Add request body for POST/PUT/PATCH
        if method.upper() in ["POST", "PUT", "PATCH"]:
            request_body = spec.get("requestBody", {})
            if request_body:
                content = request_body.get("content", {})
                json_content = content.get("application/json", {})
                schema = json_content.get("schema", {})
                
                example = self._generate_example_from_schema(schema)
                if example:
                    curl += " \\\n  -d '"
                    curl += json.dumps(example, indent=2).replace("'", "\\'")
                    curl += "'"
        
        curl += "\n```"
        
        return curl
    
    def generate_sdk_examples(self) -> Dict[str, str]:
        """Generate SDK examples for different languages"""
        examples = {}
        
        # Python SDK example
        examples["python"] = '''
# Python SDK Example
from cost_optimizer import CostOptimizer

client = CostOptimizer(api_key="your-api-key")

# Single optimization
result = client.optimize(
    prompt="Create a Python function for sorting",
    quality_threshold=0.8,
    optimization_level=3,
    user_id="user123"
)

print(f"Savings: {result.savings_percentage}%")
print(f"Quality: {result.quality_score}")

# Batch optimization
batch_results = client.batch_optimize([
    {
        "prompt": "Create a REST API endpoint",
        "quality_threshold": 0.8,
        "optimization_level": 3,
        "user_id": "user123"
    },
    {
        "prompt": "Implement caching layer",
        "quality_threshold": 0.85,
        "optimization_level": 2,
        "user_id": "user123"
    }
])
'''
        
        # JavaScript SDK example
        examples["javascript"] = '''
// JavaScript SDK Example
const CostOptimizer = require('cost-optimizer-js');

const client = new CostOptimizer('your-api-key');

// Single optimization
const result = await client.optimize({
  prompt: 'Create a JavaScript function for validation',
  qualityThreshold: 0.8,
  optimizationLevel: 3,
  userId: 'user123'
});

console.log(`Savings: ${result.savingsPercentage}%`);
console.log(`Quality: ${result.qualityScore}`);

// Batch optimization
const batchResults = await client.batchOptimize([
  {
    prompt: 'Create a React component',
    qualityThreshold: 0.8,
    optimizationLevel: 3,
    userId: 'user123'
  }
]);
'''
        
        return examples
    
    def generate_all_documentation(self):
        """Generate all documentation files"""
        print("Fetching OpenAPI specification...")
        self.fetch_openapi_spec()
        
        if not self.openapi_spec:
            print("Failed to fetch OpenAPI spec. Exiting.")
            return
        
        print("Generating Postman collection...")
        postman_collection = self.generate_postman_collection()
        with open(self.docs_dir / "postman_collection.json", "w") as f:
            json.dump(postman_collection, f, indent=2)
        
        print("Generating cURL examples...")
        curl_examples = self.generate_curl_examples()
        with open(self.docs_dir / "curl_examples.md", "w") as f:
            f.write(curl_examples)
        
        print("Generating SDK examples...")
        sdk_examples = self.generate_sdk_examples()
        for lang, example in sdk_examples.items():
            with open(self.docs_dir / f"sdk_example_{lang}.md", "w") as f:
                f.write(example)
        
        print("Saving OpenAPI spec...")
        with open(self.docs_dir / "openapi.json", "w") as f:
            json.dump(self.openapi_spec, f, indent=2)
        
        with open(self.docs_dir / "openapi.yaml", "w") as f:
            yaml.dump(self.openapi_spec, f, default_flow_style=False)
        
        print(f"Documentation generated in {self.docs_dir}")


def main():
    parser = argparse.ArgumentParser(description="Generate API documentation")
    parser.add_argument(
        "--base-url",
        default="http://localhost:8000",
        help="Base URL of the API server"
    )
    
    args = parser.parse_args()
    
    generator = APIDocumentationGenerator(args.base_url)
    generator.generate_all_documentation()


if __name__ == "__main__":
    main()
