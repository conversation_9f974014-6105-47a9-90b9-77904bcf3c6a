#!/bin/bash

# OrbStack Deployment Script for Cost Optimization Platform
# Production-grade deployment with internal service discovery
# Optimized for macOS ARM64 with security hardening

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="cost-opt-platform"
NETWORK_NAME="costopt-internal"
COMPOSE_FILE="orbstack-config.yml"
ENV_FILE=".env"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if OrbStack is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker/OrbStack is not running. Please start OrbStack first."
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        error "docker-compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we're on macOS (OrbStack requirement)
    if [[ "$OSTYPE" != "darwin"* ]]; then
        warning "This script is optimized for macOS with OrbStack"
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    local dirs=(
        "data/postgres"
        "data/redis"
        "data/chromadb"
        "data/prometheus"
        "data/grafana"
        "logs"
        "config"
        "monitoring/prometheus"
        "monitoring/grafana/dashboards"
        "monitoring/grafana/datasources"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        log "Created directory: $dir"
    done
    
    # Set proper permissions
    chmod 755 data/
    chmod 755 logs/
    chmod 755 config/
    
    success "Directories created successfully"
}

# Generate secure environment variables
generate_env_vars() {
    log "Generating secure environment variables..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log "Creating new .env file..."
        
        # Generate secure passwords and keys
        POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        SECRET_KEY=$(openssl rand -base64 64 | tr -d "=+/")
        GRAFANA_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)
        
        cat > "$ENV_FILE" << EOF
# Cost Optimization Platform - Production Environment
# Generated on $(date)

# Database Configuration
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# Security Configuration
SECRET_KEY=${SECRET_KEY}
ALLOWED_ORIGINS=http://localhost:8000,https://localhost:8000

# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Monitoring Configuration
GRAFANA_PASSWORD=${GRAFANA_PASSWORD}

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Performance Tuning
WORKERS=4
MAX_WORKERS=8
WORKER_CONNECTIONS=1000

# Service Discovery
SERVICE_DISCOVERY_ENABLED=true
HEALTH_CHECK_INTERVAL=30
EOF
        
        success "Environment file created: $ENV_FILE"
        warning "Please update OPENROUTER_API_KEY in $ENV_FILE with your actual API key"
    else
        log "Using existing environment file: $ENV_FILE"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    log "Creating monitoring configuration..."
    
    # Prometheus configuration
    cat > monitoring/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cost-optimizer-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
EOF

    # Grafana datasource configuration
    mkdir -p monitoring/grafana/datasources
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    success "Monitoring configuration created"
}

# Deploy the stack
deploy_stack() {
    log "Deploying Cost Optimization Platform stack..."
    
    # Pull latest images
    log "Pulling latest images..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # Build custom images
    log "Building application image..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache api
    
    # Start the stack
    log "Starting services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    success "Stack deployed successfully"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to become healthy..."
    
    local max_attempts=60
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        local healthy_services=0
        local total_services=0
        
        # Check each service health
        for service in api db redis chromadb prometheus grafana; do
            total_services=$((total_services + 1))
            
            if docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -q "healthy\|Up"; then
                healthy_services=$((healthy_services + 1))
            fi
        done
        
        log "Healthy services: $healthy_services/$total_services"
        
        if [[ $healthy_services -eq $total_services ]]; then
            success "All services are healthy!"
            return 0
        fi
        
        attempt=$((attempt + 1))
        sleep 5
    done
    
    error "Services did not become healthy within expected time"
    return 1
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check API health
    if curl -f -s http://localhost:8000/health >/dev/null; then
        success "API is responding"
    else
        error "API health check failed"
        return 1
    fi
    
    # Check service discovery
    if curl -f -s http://localhost:8000/api/v1/services/discovery/config >/dev/null; then
        success "Service discovery is working"
    else
        warning "Service discovery endpoint not responding"
    fi
    
    # Display service status
    log "Service status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    success "Deployment verification completed"
}

# Show deployment information
show_deployment_info() {
    log "Deployment Information:"
    echo
    echo "🚀 Cost Optimization Platform is now running!"
    echo
    echo "📊 Main Dashboard: http://localhost:8000"
    echo "🔍 API Documentation: http://localhost:8000/docs"
    echo "📈 Service Discovery: http://localhost:8000/api/v1/services"
    echo
    echo "🔧 Internal Services (accessible via API proxy):"
    echo "   - Grafana: http://localhost:8000/api/v1/proxy/grafana"
    echo "   - Prometheus: http://localhost:8000/api/v1/proxy/prometheus"
    echo "   - Jaeger: http://localhost:8000/api/v1/proxy/jaeger"
    echo
    echo "📁 Data directories:"
    echo "   - Logs: ./logs/"
    echo "   - Data: ./data/"
    echo "   - Config: ./config/"
    echo
    echo "🔐 Security:"
    echo "   - All internal services are isolated in Docker network"
    echo "   - Only port 8000 is exposed externally"
    echo "   - Service discovery provides internal connectivity"
    echo
    echo "🛠️  Management commands:"
    echo "   - View logs: docker-compose -f $COMPOSE_FILE logs -f"
    echo "   - Stop services: docker-compose -f $COMPOSE_FILE down"
    echo "   - Restart: docker-compose -f $COMPOSE_FILE restart"
    echo
}

# Cleanup function
cleanup() {
    log "Cleaning up previous deployment..."
    
    # Stop and remove containers
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans 2>/dev/null || true
    
    # Remove unused networks
    docker network prune -f 2>/dev/null || true
    
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting OrbStack deployment for Cost Optimization Platform"
    
    # Parse command line arguments
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            create_directories
            generate_env_vars
            create_monitoring_config
            deploy_stack
            wait_for_services
            verify_deployment
            show_deployment_info
            ;;
        "cleanup")
            cleanup
            ;;
        "restart")
            cleanup
            main deploy
            ;;
        "status")
            docker-compose -f "$COMPOSE_FILE" ps
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            ;;
        *)
            echo "Usage: $0 {deploy|cleanup|restart|status|logs [service]}"
            echo
            echo "Commands:"
            echo "  deploy  - Deploy the complete stack (default)"
            echo "  cleanup - Stop and remove all containers"
            echo "  restart - Cleanup and redeploy"
            echo "  status  - Show service status"
            echo "  logs    - Show logs (optionally for specific service)"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
