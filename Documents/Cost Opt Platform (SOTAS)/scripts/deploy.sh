#!/bin/bash
# Deployment Script for Cost Optimization Platform
# FAANG+ deployment practices with comprehensive validation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
K8S_DIR="$PROJECT_ROOT/k8s"

# Default values
ENVIRONMENT="${ENVIRONMENT:-production}"
NAMESPACE="${NAMESPACE:-cost-optimization}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
REGISTRY="${REGISTRY:-your-registry.com}"
IMAGE_NAME="${IMAGE_NAME:-cost-optimizer}"
KUBECTL_CONTEXT="${KUBECTL_CONTEXT:-}"
DRY_RUN="${DRY_RUN:-false}"
SKIP_TESTS="${SKIP_TESTS:-false}"
SKIP_BUILD="${SKIP_BUILD:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] [INFO]${NC} $*"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] [WARN]${NC} $*"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $*"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $*"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        error "Deployment failed with exit code $exit_code"
        error "Check the logs above for details"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Validation functions
validate_prerequisites() {
    log "Validating prerequisites..."
    
    # Check required tools
    local required_tools=("kubectl" "docker" "helm")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "Required tool '$tool' is not installed"
            exit 1
        fi
    done
    
    # Check kubectl context
    if [ -n "$KUBECTL_CONTEXT" ]; then
        if ! kubectl config get-contexts "$KUBECTL_CONTEXT" &> /dev/null; then
            error "Kubectl context '$KUBECTL_CONTEXT' not found"
            exit 1
        fi
        kubectl config use-context "$KUBECTL_CONTEXT"
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check namespace
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        warn "Namespace '$NAMESPACE' does not exist, will be created"
    fi
    
    success "Prerequisites validated"
}

validate_configuration() {
    log "Validating configuration..."
    
    # Check required files
    local required_files=(
        "$K8S_DIR/namespace.yaml"
        "$K8S_DIR/deployment.yaml"
        "$K8S_DIR/ingress.yaml"
        "$K8S_DIR/monitoring.yaml"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            error "Required file '$file' not found"
            exit 1
        fi
    done
    
    # Check secrets template
    if [ ! -f "$K8S_DIR/secrets.yaml" ] && [ ! -f "$K8S_DIR/secrets.yaml.template" ]; then
        error "Neither secrets.yaml nor secrets.yaml.template found"
        error "Please create secrets.yaml from the template"
        exit 1
    fi
    
    success "Configuration validated"
}

# Build functions
run_tests() {
    if [ "$SKIP_TESTS" = "true" ]; then
        warn "Skipping tests (SKIP_TESTS=true)"
        return
    fi
    
    log "Running tests..."
    cd "$PROJECT_ROOT"
    
    # Run unit tests
    python -m pytest tests/unit/ -v --tb=short || {
        error "Unit tests failed"
        exit 1
    }
    
    # Run integration tests
    python -m pytest tests/integration/ -v --tb=short || {
        error "Integration tests failed"
        exit 1
    }
    
    success "Tests passed"
}

build_image() {
    if [ "$SKIP_BUILD" = "true" ]; then
        warn "Skipping image build (SKIP_BUILD=true)"
        return
    fi
    
    log "Building Docker image..."
    cd "$PROJECT_ROOT"
    
    # Build arguments
    local build_args=(
        "--build-arg" "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
        "--build-arg" "VERSION=$IMAGE_TAG"
        "--build-arg" "VCS_REF=$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
    )
    
    # Build image
    docker build "${build_args[@]}" -t "$REGISTRY/$IMAGE_NAME:$IMAGE_TAG" . || {
        error "Docker build failed"
        exit 1
    }
    
    # Tag as latest if this is a production build
    if [ "$ENVIRONMENT" = "production" ]; then
        docker tag "$REGISTRY/$IMAGE_NAME:$IMAGE_TAG" "$REGISTRY/$IMAGE_NAME:latest"
    fi
    
    success "Image built successfully"
}

push_image() {
    if [ "$SKIP_BUILD" = "true" ]; then
        warn "Skipping image push (SKIP_BUILD=true)"
        return
    fi
    
    log "Pushing Docker image..."
    
    # Push tagged image
    docker push "$REGISTRY/$IMAGE_NAME:$IMAGE_TAG" || {
        error "Failed to push image"
        exit 1
    }
    
    # Push latest tag for production
    if [ "$ENVIRONMENT" = "production" ]; then
        docker push "$REGISTRY/$IMAGE_NAME:latest"
    fi
    
    success "Image pushed successfully"
}

# Deployment functions
create_namespace() {
    log "Creating namespace..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply -f "$K8S_DIR/namespace.yaml" --dry-run=client -o yaml
    else
        kubectl apply -f "$K8S_DIR/namespace.yaml"
    fi
    
    success "Namespace configuration applied"
}

deploy_secrets() {
    log "Deploying secrets..."
    
    local secrets_file="$K8S_DIR/secrets.yaml"
    
    if [ ! -f "$secrets_file" ]; then
        warn "secrets.yaml not found, skipping secrets deployment"
        warn "Please create secrets manually or from template"
        return
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply -f "$secrets_file" --dry-run=client -o yaml
    else
        kubectl apply -f "$secrets_file"
    fi
    
    success "Secrets deployed"
}

deploy_application() {
    log "Deploying application..."
    
    # Update image tag in deployment
    local temp_deployment="/tmp/deployment-$$.yaml"
    sed "s|image: cost-optimizer:latest|image: $REGISTRY/$IMAGE_NAME:$IMAGE_TAG|g" \
        "$K8S_DIR/deployment.yaml" > "$temp_deployment"
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply -f "$temp_deployment" --dry-run=client -o yaml
    else
        kubectl apply -f "$temp_deployment"
    fi
    
    rm -f "$temp_deployment"
    success "Application deployed"
}

deploy_ingress() {
    log "Deploying ingress..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply -f "$K8S_DIR/ingress.yaml" --dry-run=client -o yaml
    else
        kubectl apply -f "$K8S_DIR/ingress.yaml"
    fi
    
    success "Ingress deployed"
}

deploy_monitoring() {
    log "Deploying monitoring..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply -f "$K8S_DIR/monitoring.yaml" --dry-run=client -o yaml
    else
        kubectl apply -f "$K8S_DIR/monitoring.yaml"
    fi
    
    success "Monitoring deployed"
}

wait_for_deployment() {
    if [ "$DRY_RUN" = "true" ]; then
        log "Skipping deployment wait (dry run mode)"
        return
    fi
    
    log "Waiting for deployment to be ready..."
    
    # Wait for deployment to be ready
    kubectl rollout status deployment/cost-optimizer -n "$NAMESPACE" --timeout=600s || {
        error "Deployment failed to become ready"
        kubectl describe deployment/cost-optimizer -n "$NAMESPACE"
        kubectl logs -l app=cost-optimizer -n "$NAMESPACE" --tail=50
        exit 1
    }
    
    success "Deployment is ready"
}

run_health_checks() {
    if [ "$DRY_RUN" = "true" ]; then
        log "Skipping health checks (dry run mode)"
        return
    fi
    
    log "Running health checks..."
    
    # Get service endpoint
    local service_ip
    service_ip=$(kubectl get service cost-optimizer-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    if [ -z "$service_ip" ]; then
        # Try hostname for cloud providers
        service_ip=$(kubectl get service cost-optimizer-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
    fi
    
    if [ -z "$service_ip" ]; then
        warn "LoadBalancer IP/hostname not available, using port-forward for health check"
        kubectl port-forward service/cost-optimizer-service 8080:80 -n "$NAMESPACE" &
        local port_forward_pid=$!
        sleep 5
        service_ip="localhost:8080"
    fi
    
    # Health check
    local health_url="http://$service_ip/health"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            success "Health check passed"
            break
        fi
        
        log "Health check attempt $attempt/$max_attempts failed, retrying in 10s..."
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error "Health checks failed after $max_attempts attempts"
        exit 1
    fi
    
    # Clean up port-forward if used
    if [ -n "${port_forward_pid:-}" ]; then
        kill $port_forward_pid 2>/dev/null || true
    fi
}

# Main deployment function
main() {
    log "Starting deployment of Cost Optimization Platform"
    log "Environment: $ENVIRONMENT"
    log "Namespace: $NAMESPACE"
    log "Image: $REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    log "Dry run: $DRY_RUN"
    
    # Validation
    validate_prerequisites
    validate_configuration
    
    # Build and test
    run_tests
    build_image
    push_image
    
    # Deploy
    create_namespace
    deploy_secrets
    deploy_application
    deploy_ingress
    deploy_monitoring
    
    # Verify
    wait_for_deployment
    run_health_checks
    
    success "Deployment completed successfully!"
    
    # Display useful information
    log "Useful commands:"
    log "  kubectl get pods -n $NAMESPACE"
    log "  kubectl logs -f deployment/cost-optimizer -n $NAMESPACE"
    log "  kubectl describe deployment/cost-optimizer -n $NAMESPACE"
    log "  kubectl get ingress -n $NAMESPACE"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment|-e)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --namespace|-n)
            NAMESPACE="$2"
            shift 2
            ;;
        --image-tag|-t)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry|-r)
            REGISTRY="$2"
            shift 2
            ;;
        --context|-c)
            KUBECTL_CONTEXT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        --skip-build)
            SKIP_BUILD="true"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --environment, -e    Environment (default: production)"
            echo "  --namespace, -n      Kubernetes namespace (default: cost-optimization)"
            echo "  --image-tag, -t      Docker image tag (default: latest)"
            echo "  --registry, -r       Docker registry (default: your-registry.com)"
            echo "  --context, -c        Kubectl context"
            echo "  --dry-run           Perform dry run"
            echo "  --skip-tests        Skip running tests"
            echo "  --skip-build        Skip building and pushing image"
            echo "  --help, -h          Show this help"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
