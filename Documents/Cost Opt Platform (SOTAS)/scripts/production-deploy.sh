#!/bin/bash

# Production Deployment Script for Cost Optimization Platform
# FAANG+ level deployment automation with zero-downtime and comprehensive validation
# Designed for 100M+ user scale with enterprise-grade reliability

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="cost-opt-platform"
ENVIRONMENT="${ENVIRONMENT:-production}"
COMPOSE_FILE="orbstack-config.yml"
ENV_FILE=".env"
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
ROLLBACK_ENABLED=true

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

# Pre-deployment validation
validate_environment() {
    log "Validating deployment environment..."
    
    # Check if running on macOS (OrbStack requirement)
    if [[ "$OSTYPE" != "darwin"* ]]; then
        error "This deployment script is optimized for macOS with OrbStack"
        exit 1
    fi
    
    # Check Docker/OrbStack
    if ! docker info >/dev/null 2>&1; then
        error "Docker/OrbStack is not running"
        exit 1
    fi
    
    # Check docker-compose
    if ! command -v docker-compose >/dev/null 2>&1; then
        error "docker-compose is not installed"
        exit 1
    fi
    
    # Check required files
    local required_files=("$COMPOSE_FILE" "Dockerfile" "requirements.txt")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file not found: $file"
            exit 1
        fi
    done
    
    # Validate environment variables
    if [[ ! -f "$ENV_FILE" ]]; then
        warning "Environment file not found: $ENV_FILE"
        warning "Creating default environment file..."
        create_production_env
    fi
    
    # Check critical environment variables
    source "$ENV_FILE"
    local required_vars=("SECRET_KEY" "OPENROUTER_API_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error "Required environment variable not set: $var"
            exit 1
        fi
    done
    
    success "Environment validation passed"
}

# Create production environment file
create_production_env() {
    log "Creating production environment configuration..."
    
    # Generate secure secrets
    SECRET_KEY=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    GRAFANA_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)
    
    cat > "$ENV_FILE" << EOF
# Cost Optimization Platform - Production Environment
# Generated on $(date)

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Security
SECRET_KEY=${SECRET_KEY}
ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Database
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# OpenRouter API (REQUIRED - UPDATE THIS)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Monitoring
GRAFANA_PASSWORD=${GRAFANA_PASSWORD}

# Performance
WORKERS=4
MAX_WORKERS=8
WORKER_CONNECTIONS=1000

# Service Discovery
SERVICE_DISCOVERY_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# Cache Configuration
REDIS_MAX_CONNECTIONS=100
CACHE_TTL=3600

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Metrics
PROMETHEUS_ENABLED=true
JAEGER_ENABLED=true
EOF
    
    success "Production environment file created"
    warning "IMPORTANT: Update OPENROUTER_API_KEY in $ENV_FILE with your actual API key"
    warning "IMPORTANT: Update ALLOWED_ORIGINS with your actual domain(s)"
}

# Pre-deployment backup
create_backup() {
    if [[ "$ROLLBACK_ENABLED" == "true" ]]; then
        log "Creating pre-deployment backup..."
        
        mkdir -p "$BACKUP_DIR"
        
        # Backup data volumes
        if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
            log "Backing up data volumes..."
            
            # Backup database
            docker-compose -f "$COMPOSE_FILE" exec -T db pg_dump -U costopt costopt > "$BACKUP_DIR/database.sql" || true
            
            # Backup Redis data
            docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli BGSAVE || true
            
            # Copy data directories
            cp -r data/ "$BACKUP_DIR/" 2>/dev/null || true
            cp -r logs/ "$BACKUP_DIR/" 2>/dev/null || true
        fi
        
        # Backup configuration
        cp "$ENV_FILE" "$BACKUP_DIR/"
        cp "$COMPOSE_FILE" "$BACKUP_DIR/"
        
        success "Backup created: $BACKUP_DIR"
    fi
}

# Build and deploy
deploy_application() {
    log "Starting production deployment..."
    
    # Pull latest images
    log "Pulling latest base images..."
    docker-compose -f "$COMPOSE_FILE" pull --ignore-pull-failures
    
    # Build application image
    log "Building application image..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache api
    
    # Deploy with zero-downtime strategy
    log "Deploying services..."
    
    # Start infrastructure services first
    log "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" up -d db redis chromadb prometheus grafana jaeger
    
    # Wait for infrastructure to be ready
    wait_for_infrastructure
    
    # Deploy application
    log "Deploying application..."
    docker-compose -f "$COMPOSE_FILE" up -d api
    
    success "Deployment completed"
}

# Wait for infrastructure services
wait_for_infrastructure() {
    log "Waiting for infrastructure services to be ready..."
    
    local services=("db:5432" "redis:6379" "chromadb:8000")
    local max_attempts=60
    
    for service in "${services[@]}"; do
        local host=$(echo "$service" | cut -d: -f1)
        local port=$(echo "$service" | cut -d: -f2)
        local attempts=0
        
        log "Waiting for $host:$port..."
        
        while ! docker-compose -f "$COMPOSE_FILE" exec -T "$host" timeout 1 bash -c "echo > /dev/tcp/localhost/$port" 2>/dev/null; do
            attempts=$((attempts + 1))
            if [[ $attempts -ge $max_attempts ]]; then
                error "Timeout waiting for $host:$port"
                return 1
            fi
            sleep 5
        done
        
        success "$host:$port is ready"
    done
}

# Comprehensive health checks
run_health_checks() {
    log "Running comprehensive health checks..."
    
    local api_url="http://localhost:8000"
    local max_attempts=60
    local attempts=0
    
    # Wait for API to be responsive
    log "Waiting for API to be ready..."
    while ! curl -f -s "$api_url/health" >/dev/null 2>&1; do
        attempts=$((attempts + 1))
        if [[ $attempts -ge $max_attempts ]]; then
            error "API health check timeout"
            return 1
        fi
        sleep 5
    done
    
    # Detailed health checks
    log "Running detailed health checks..."
    
    # Basic health check
    local health_response=$(curl -s "$api_url/health" | jq -r '.status' 2>/dev/null || echo "unknown")
    if [[ "$health_response" != "healthy" ]]; then
        error "API health check failed: $health_response"
        return 1
    fi
    
    # Readiness check
    local ready_response=$(curl -s "$api_url/health/ready" | jq -r '.status' 2>/dev/null || echo "unknown")
    if [[ "$ready_response" != "ready" ]]; then
        error "API readiness check failed: $ready_response"
        return 1
    fi
    
    # Service discovery check
    local services_response=$(curl -s "$api_url/api/v1/services/health/summary" | jq -r '.system_status' 2>/dev/null || echo "unknown")
    if [[ "$services_response" != "healthy" ]]; then
        warning "Service discovery health check: $services_response"
    fi
    
    # Database connectivity
    if ! docker-compose -f "$COMPOSE_FILE" exec -T db pg_isready -U costopt >/dev/null 2>&1; then
        error "Database connectivity check failed"
        return 1
    fi
    
    # Redis connectivity
    if ! docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping >/dev/null 2>&1; then
        error "Redis connectivity check failed"
        return 1
    fi
    
    success "All health checks passed"
}

# Performance validation
validate_performance() {
    log "Running performance validation..."
    
    local api_url="http://localhost:8000"
    
    # Test API response time
    log "Testing API response time..."
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$api_url/health")
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
    
    if [[ $response_time_ms -gt 1000 ]]; then
        warning "API response time is high: ${response_time_ms}ms"
    else
        success "API response time: ${response_time_ms}ms"
    fi
    
    # Test concurrent requests
    log "Testing concurrent request handling..."
    for i in {1..10}; do
        curl -s "$api_url/health" >/dev/null &
    done
    wait
    
    success "Performance validation completed"
}

# Security validation
validate_security() {
    log "Running security validation..."
    
    local api_url="http://localhost:8000"
    
    # Check security headers
    log "Validating security headers..."
    local headers=$(curl -I -s "$api_url/health")
    
    local required_headers=(
        "X-Frame-Options"
        "X-Content-Type-Options"
        "X-XSS-Protection"
        "Strict-Transport-Security"
    )
    
    for header in "${required_headers[@]}"; do
        if echo "$headers" | grep -qi "$header"; then
            success "Security header present: $header"
        else
            warning "Security header missing: $header"
        fi
    done
    
    # Check for exposed sensitive endpoints
    log "Checking for exposed sensitive endpoints..."
    local sensitive_endpoints=("/admin" "/debug" "/.env" "/config")
    
    for endpoint in "${sensitive_endpoints[@]}"; do
        local status=$(curl -s -o /dev/null -w '%{http_code}' "$api_url$endpoint")
        if [[ "$status" == "200" ]]; then
            error "Sensitive endpoint exposed: $endpoint"
        else
            success "Sensitive endpoint protected: $endpoint"
        fi
    done
    
    success "Security validation completed"
}

# Post-deployment verification
verify_deployment() {
    log "Running post-deployment verification..."
    
    # Check all services are running
    log "Verifying service status..."
    local services=$(docker-compose -f "$COMPOSE_FILE" ps --services)
    local failed_services=()
    
    for service in $services; do
        local status=$(docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -v "Name" | awk '{print $3}')
        if [[ "$status" == "Up" ]] || [[ "$status" =~ "Up" ]]; then
            success "Service running: $service"
        else
            error "Service failed: $service ($status)"
            failed_services+=("$service")
        fi
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        error "Failed services: ${failed_services[*]}"
        return 1
    fi
    
    # Run health checks
    run_health_checks || return 1
    
    # Validate performance
    validate_performance || return 1
    
    # Validate security
    validate_security || return 1
    
    success "Deployment verification completed successfully"
}

# Rollback function
rollback_deployment() {
    if [[ "$ROLLBACK_ENABLED" == "true" && -d "$BACKUP_DIR" ]]; then
        error "Deployment failed. Initiating rollback..."
        
        # Stop current services
        docker-compose -f "$COMPOSE_FILE" down
        
        # Restore configuration
        cp "$BACKUP_DIR/$ENV_FILE" .
        
        # Restore data if needed
        if [[ -f "$BACKUP_DIR/database.sql" ]]; then
            log "Restoring database..."
            docker-compose -f "$COMPOSE_FILE" up -d db
            sleep 10
            docker-compose -f "$COMPOSE_FILE" exec -T db psql -U costopt -d costopt < "$BACKUP_DIR/database.sql"
        fi
        
        # Restart services
        docker-compose -f "$COMPOSE_FILE" up -d
        
        warning "Rollback completed. Please investigate the deployment failure."
    else
        error "Rollback not available or disabled"
    fi
}

# Display deployment information
show_deployment_info() {
    log "Deployment Information:"
    echo
    echo "🚀 Cost Optimization Platform - Production Deployment"
    echo "=================================================="
    echo
    echo "📊 Main Dashboard: http://localhost:8000"
    echo "🔍 API Documentation: http://localhost:8000/docs"
    echo "📈 Metrics: http://localhost:8000/metrics"
    echo "🏥 Health Check: http://localhost:8000/health"
    echo "🔧 Service Discovery: http://localhost:8000/api/v1/services"
    echo
    echo "📁 Data & Logs:"
    echo "   - Application Logs: ./logs/"
    echo "   - Data Volumes: ./data/"
    echo "   - Backup: $BACKUP_DIR"
    echo
    echo "🔐 Security Features:"
    echo "   - OWASP-compliant security headers"
    echo "   - Rate limiting enabled"
    echo "   - Input validation active"
    echo "   - Internal service isolation"
    echo
    echo "📊 Monitoring:"
    echo "   - Prometheus metrics collection"
    echo "   - Grafana dashboards"
    echo "   - Jaeger distributed tracing"
    echo "   - Comprehensive health checks"
    echo
    echo "🛠️  Management Commands:"
    echo "   - View logs: docker-compose -f $COMPOSE_FILE logs -f"
    echo "   - Check status: docker-compose -f $COMPOSE_FILE ps"
    echo "   - Stop services: docker-compose -f $COMPOSE_FILE down"
    echo "   - Scale API: docker-compose -f $COMPOSE_FILE up -d --scale api=3"
    echo
}

# Main deployment function
main() {
    local command="${1:-deploy}"
    
    case "$command" in
        "deploy")
            log "Starting production deployment for Cost Optimization Platform"
            
            validate_environment
            create_backup
            deploy_application
            
            if verify_deployment; then
                show_deployment_info
                success "Production deployment completed successfully!"
            else
                rollback_deployment
                exit 1
            fi
            ;;
        "rollback")
            rollback_deployment
            ;;
        "verify")
            verify_deployment
            ;;
        "status")
            docker-compose -f "$COMPOSE_FILE" ps
            run_health_checks
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|verify|status|logs [service]}"
            echo
            echo "Commands:"
            echo "  deploy   - Full production deployment (default)"
            echo "  rollback - Rollback to previous version"
            echo "  verify   - Verify current deployment"
            echo "  status   - Show service status and health"
            echo "  logs     - Show logs (optionally for specific service)"
            exit 1
            ;;
    esac
}

# Trap errors for automatic rollback
trap 'if [[ $? -ne 0 ]]; then rollback_deployment; fi' ERR

# Run main function
main "$@"
