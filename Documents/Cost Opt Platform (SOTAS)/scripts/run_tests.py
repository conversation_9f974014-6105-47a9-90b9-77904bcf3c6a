#!/usr/bin/env python3
"""
Test Runner Script
Comprehensive test execution with FAANG+ standards
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


class TestRunner:
    """Production-grade test runner with comprehensive reporting"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.test_results = {}
        
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run unit tests"""
        print("🧪 Running Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/unit/",
            "--tb=short",
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--junit-xml=test-results/unit-tests.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        end_time = time.time()
        
        return {
            "name": "Unit Tests",
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run integration tests"""
        print("🔗 Running Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/integration/",
            "--tb=short",
            "--junit-xml=test-results/integration-tests.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        end_time = time.time()
        
        return {
            "name": "Integration Tests",
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    
    def run_e2e_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run end-to-end tests"""
        print("🌐 Running End-to-End Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/e2e/",
            "--tb=short",
            "--junit-xml=test-results/e2e-tests.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        end_time = time.time()
        
        return {
            "name": "End-to-End Tests",
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run performance tests"""
        print("⚡ Running Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/performance/",
            "--tb=short",
            "--junit-xml=test-results/performance-tests.xml",
            "-m", "not slow"  # Skip slow tests by default
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        end_time = time.time()
        
        return {
            "name": "Performance Tests",
            "success": result.returncode == 0,
            "duration": end_time - start_time,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    
    def run_security_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run security tests"""
        print("🔒 Running Security Tests...")
        
        # Run bandit for security analysis
        bandit_cmd = [
            "bandit", "-r", "src/", "-f", "json", "-o", "test-results/security-report.json"
        ]
        
        start_time = time.time()
        bandit_result = subprocess.run(bandit_cmd, cwd=self.project_root, capture_output=True, text=True)
        
        # Run safety for dependency vulnerability check
        safety_cmd = ["safety", "check", "--json", "--output", "test-results/safety-report.json"]
        safety_result = subprocess.run(safety_cmd, cwd=self.project_root, capture_output=True, text=True)
        
        end_time = time.time()
        
        # Consider success if both tools run without critical issues
        success = bandit_result.returncode in [0, 1] and safety_result.returncode in [0, 1]
        
        return {
            "name": "Security Tests",
            "success": success,
            "duration": end_time - start_time,
            "stdout": f"Bandit: {bandit_result.stdout}\nSafety: {safety_result.stdout}",
            "stderr": f"Bandit: {bandit_result.stderr}\nSafety: {safety_result.stderr}",
            "returncode": max(bandit_result.returncode, safety_result.returncode)
        }
    
    def run_linting(self, verbose: bool = False) -> Dict[str, Any]:
        """Run code linting"""
        print("🧹 Running Code Linting...")
        
        # Run flake8
        flake8_cmd = [
            "flake8", "src/", "tests/",
            "--max-line-length=100",
            "--ignore=E203,W503",
            "--output-file=test-results/flake8-report.txt"
        ]
        
        start_time = time.time()
        flake8_result = subprocess.run(flake8_cmd, cwd=self.project_root, capture_output=True, text=True)
        
        # Run mypy for type checking
        mypy_cmd = [
            "mypy", "src/",
            "--ignore-missing-imports",
            "--no-strict-optional"
        ]
        mypy_result = subprocess.run(mypy_cmd, cwd=self.project_root, capture_output=True, text=True)
        
        end_time = time.time()
        
        success = flake8_result.returncode == 0 and mypy_result.returncode == 0
        
        return {
            "name": "Code Linting",
            "success": success,
            "duration": end_time - start_time,
            "stdout": f"Flake8: {flake8_result.stdout}\nMypy: {mypy_result.stdout}",
            "stderr": f"Flake8: {flake8_result.stderr}\nMypy: {mypy_result.stderr}",
            "returncode": max(flake8_result.returncode, mypy_result.returncode)
        }
    
    def setup_test_environment(self):
        """Setup test environment"""
        print("🔧 Setting up test environment...")
        
        # Create test results directory
        test_results_dir = self.project_root / "test-results"
        test_results_dir.mkdir(exist_ok=True)
        
        # Set environment variables for testing
        os.environ["TESTING"] = "true"
        os.environ["LOG_LEVEL"] = "WARNING"
        
        # Install test dependencies if needed
        requirements_test = self.project_root / "requirements-test.txt"
        if requirements_test.exists():
            subprocess.run([
                "pip", "install", "-r", str(requirements_test)
            ], cwd=self.project_root)
    
    def generate_test_report(self, results: List[Dict[str, Any]]):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 TEST EXECUTION SUMMARY")
        print("="*80)
        
        total_duration = sum(r["duration"] for r in results)
        passed_tests = sum(1 for r in results if r["success"])
        failed_tests = len(results) - passed_tests
        
        print(f"Total Test Suites: {len(results)}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Total Duration: {total_duration:.2f}s")
        print(f"Success Rate: {(passed_tests/len(results)*100):.1f}%")
        
        print("\n📋 DETAILED RESULTS:")
        print("-" * 80)
        
        for result in results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} {result['name']:<20} ({result['duration']:.2f}s)")
            
            if not result["success"] and result["stderr"]:
                print(f"   Error: {result['stderr'][:200]}...")
        
        # Generate HTML report
        self.generate_html_report(results)
        
        print(f"\n📄 Reports generated in: {self.project_root}/test-results/")
        print("="*80)
        
        return passed_tests == len(results)
    
    def generate_html_report(self, results: List[Dict[str, Any]]):
        """Generate HTML test report"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Execution Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .summary { margin: 20px 0; }
                .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
                .pass { background: #d4edda; border-left: 5px solid #28a745; }
                .fail { background: #f8d7da; border-left: 5px solid #dc3545; }
                .details { margin-top: 10px; font-family: monospace; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Cost Optimization Platform - Test Report</h1>
                <p>Generated: {timestamp}</p>
            </div>
            
            <div class="summary">
                <h2>Summary</h2>
                <p>Total Suites: {total_suites}</p>
                <p>Passed: {passed}</p>
                <p>Failed: {failed}</p>
                <p>Success Rate: {success_rate:.1f}%</p>
            </div>
            
            <div class="results">
                <h2>Detailed Results</h2>
                {test_results}
            </div>
        </body>
        </html>
        """
        
        test_results_html = ""
        for result in results:
            status_class = "pass" if result["success"] else "fail"
            status_text = "PASS" if result["success"] else "FAIL"
            
            test_results_html += f"""
            <div class="test-result {status_class}">
                <h3>{result['name']} - {status_text} ({result['duration']:.2f}s)</h3>
                {f'<div class="details">{result["stderr"]}</div>' if not result["success"] and result["stderr"] else ''}
            </div>
            """
        
        total_suites = len(results)
        passed = sum(1 for r in results if r["success"])
        failed = total_suites - passed
        success_rate = (passed / total_suites * 100) if total_suites > 0 else 0
        
        html_content = html_content.format(
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            total_suites=total_suites,
            passed=passed,
            failed=failed,
            success_rate=success_rate,
            test_results=test_results_html
        )
        
        report_path = self.project_root / "test-results" / "test-report.html"
        with open(report_path, "w") as f:
            f.write(html_content)
    
    def run_all_tests(self, test_types: List[str], verbose: bool = False) -> bool:
        """Run all specified test types"""
        self.setup_test_environment()
        
        test_functions = {
            "unit": self.run_unit_tests,
            "integration": self.run_integration_tests,
            "e2e": self.run_e2e_tests,
            "performance": self.run_performance_tests,
            "security": self.run_security_tests,
            "lint": self.run_linting
        }
        
        results = []
        
        for test_type in test_types:
            if test_type in test_functions:
                try:
                    result = test_functions[test_type](verbose)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "name": f"{test_type.title()} Tests",
                        "success": False,
                        "duration": 0,
                        "stdout": "",
                        "stderr": str(e),
                        "returncode": 1
                    })
            else:
                print(f"⚠️  Unknown test type: {test_type}")
        
        return self.generate_test_report(results)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Run test suites for Cost Optimization Platform")
    parser.add_argument(
        "--types",
        nargs="+",
        default=["unit", "integration", "lint"],
        choices=["unit", "integration", "e2e", "performance", "security", "lint", "all"],
        help="Test types to run"
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    if "all" in args.types:
        test_types = ["unit", "integration", "e2e", "performance", "security", "lint"]
    else:
        test_types = args.types
    
    runner = TestRunner(args.project_root)
    success = runner.run_all_tests(test_types, args.verbose)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
