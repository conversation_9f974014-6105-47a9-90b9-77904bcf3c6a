version: '3.8'

services:
  # Main API service - ONLY service exposed externally
  api:
    build: .
    command: ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
    ports:
      - "8000:8000"  # ONLY external port - main dashboard access
    environment:
      - DATABASE_URL=postgresql+asyncpg://costopt:costopt123@db:5432/costopt
      - REDIS_URL=redis://redis:6379
      - CHROMADB_HOST=chromadb
      - CHROMADB_PORT=8000
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-your_openrouter_api_key_here}
      - ENVIRONMENT=production
      - DEBUG=false
      - JAEGER_HOST=jaeger
      - JAEGER_PORT=14268
      - PROMETHEUS_HOST=prometheus
      - PROMETHEUS_PORT=9090
      - GRAFANA_HOST=grafana
      - GRAFANA_PORT=3000
      - N8N_HOST=n8n
      - N8N_PORT=5678
    depends_on:
      - db
      - redis
      - chromadb
      - jaeger
      - prometheus
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - costopt-internal

  # PostgreSQL database with pgvector extension - INTERNAL ONLY
  db:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=costopt
      - POSTGRES_USER=costopt
      - POSTGRES_PASSWORD=costopt123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8
    # NO external ports - internal access only via hostname 'db'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U costopt -d costopt"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - costopt-internal

  # Redis cache - INTERNAL ONLY
  redis:
    image: redis:7-alpine
    # NO external ports - internal access only via hostname 'redis'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --bind 0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - costopt-internal

  # ChromaDB for vector similarity search - INTERNAL ONLY
  chromadb:
    image: chromadb/chroma:latest
    # NO external ports - internal access only via hostname 'chromadb'
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=8000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - costopt-internal

  # Jaeger for distributed tracing - INTERNAL ONLY
  jaeger:
    image: jaegertracing/all-in-one:latest
    # NO external ports - accessible via API proxy
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    restart: unless-stopped
    networks:
      - costopt-internal

  # Prometheus for metrics collection - INTERNAL ONLY
  prometheus:
    image: prom/prometheus:latest
    # NO external ports - accessible via API proxy
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.listen-address=0.0.0.0:9090'
    restart: unless-stopped
    networks:
      - costopt-internal

  # Grafana for metrics visualization - INTERNAL ONLY
  grafana:
    image: grafana/grafana:latest
    # NO external ports - accessible via API proxy
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_SERVER_HTTP_ADDR=0.0.0.0
      - GF_SERVER_HTTP_PORT=3000
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - costopt-internal

  # N8N for workflow automation - INTERNAL ONLY
  n8n:
    image: n8nio/n8n:latest
    # NO external ports - accessible via API proxy
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - WEBHOOK_URL=http://n8n:5678/
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped
    networks:
      - costopt-internal

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chromadb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  n8n_data:
    driver: local

networks:
  costopt-internal:
    name: costopt-internal
    driver: bridge
    internal: false  # Allow external access for API service
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
