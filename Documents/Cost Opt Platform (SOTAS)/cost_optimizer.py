#!/usr/bin/env python3
"""
Cost Optimization Platform built on LiteLLM
Production-ready LLM cost optimization using battle-tested patterns
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

import litellm
from litellm import completion, acompletion, Router
import redis
import sqlite3
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
import uvicorn

# Configure LiteLLM
litellm.set_verbose = True
litellm.drop_params = True  # Drop unsupported params instead of failing

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelTier(str, Enum):
    PREMIUM = "premium"
    STANDARD = "standard"
    BUDGET = "budget"

class TaskComplexity(str, Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"

@dataclass
class OptimizationRequest:
    prompt: str
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7
    quality_threshold: Optional[float] = 0.8
    max_cost: Optional[float] = 0.10
    task_complexity: Optional[TaskComplexity] = TaskComplexity.MEDIUM
    user_id: Optional[str] = None
    prefer_speed: bool = False
    prefer_cost: bool = True

@dataclass
class OptimizationResponse:
    content: str
    model_used: str
    cost_usd: Decimal
    tokens_used: int
    processing_time_ms: float
    quality_score: float
    optimization_savings: Decimal
    cache_hit: bool = False
    request_id: str = ""

class CostOptimizer:
    """Production-ready cost optimizer built on LiteLLM"""
    
    def __init__(self, config_path: str = "litellm_config.yaml"):
        self.config_path = config_path
        self.router = None
        self.redis_client = None
        self.db_path = "data/cost_optimizer.db"
        self.model_costs = {}
        self.model_tiers = {}
        self.optimization_history = []
        
        # Initialize components
        self._setup_database()
        self._setup_redis()
        self._load_model_config()
        self._setup_router()
        
    def _setup_database(self):
        """Setup SQLite database for tracking"""
        os.makedirs("data", exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS optimizations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id TEXT UNIQUE,
                user_id TEXT,
                prompt_hash TEXT,
                model_used TEXT,
                cost_usd REAL,
                tokens_used INTEGER,
                processing_time_ms REAL,
                quality_score REAL,
                optimization_savings REAL,
                cache_hit BOOLEAN,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT,
                avg_quality REAL,
                avg_cost REAL,
                avg_latency REAL,
                success_rate REAL,
                total_requests INTEGER,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
        
    def _setup_redis(self):
        """Setup Redis for caching"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            if redis_url and "localhost" not in redis_url:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                self.redis_client.ping()
                logger.info("Redis connected successfully")
            else:
                logger.info("Redis not configured, caching disabled")
                self.redis_client = None
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}. Caching disabled.")
            self.redis_client = None
            
    def _load_model_config(self):
        """Load model configuration"""
        # Default model costs (per 1K tokens) - based on live testing
        self.model_costs = {
            "claude-4-sonnet": {"input": 0.003, "output": 0.015, "tier": ModelTier.PREMIUM},
            "llama-3-8b": {"input": 0.00018, "output": 0.00018, "tier": ModelTier.BUDGET},
        }
        
        for model, config in self.model_costs.items():
            self.model_tiers[model] = config["tier"]
            
    def _setup_router(self):
        """Setup LiteLLM router with fallbacks"""
        try:
            # Only setup router if API keys are available
            if os.getenv("ANTHROPIC_API_KEY") or os.getenv("OPENROUTER_API_KEY"):
                # Use LiteLLM's Router for intelligent routing
                model_list = []

                if os.getenv("OPENROUTER_API_KEY"):
                    model_list.append({
                        "model_name": "claude-4-sonnet",
                        "litellm_params": {
                            "model": "openrouter/anthropic/claude-3-5-sonnet",
                            "api_key": os.getenv("OPENROUTER_API_KEY")
                        }
                    })

                if os.getenv("OPENROUTER_API_KEY"):
                    model_list.extend([
                        {
                            "model_name": "llama-3-8b",
                            "litellm_params": {
                                "model": "openrouter/meta-llama/llama-3.1-8b-instruct",
                                "api_key": os.getenv("OPENROUTER_API_KEY")
                            }
                        }
                    ])

                if model_list:
                    self.router = Router(model_list=model_list)
                    logger.info("LiteLLM Router initialized successfully")
                else:
                    logger.warning("No API keys found, router disabled")
                    self.router = None
            else:
                logger.info("No API keys configured, router disabled")
                self.router = None
        except Exception as e:
            logger.warning(f"Router setup failed: {e}. Using direct calls.")
            self.router = None
            
    def _calculate_prompt_complexity(self, prompt: str) -> TaskComplexity:
        """Analyze prompt to determine task complexity"""
        prompt_lower = prompt.lower()
        
        # Expert level indicators
        expert_keywords = ["analyze", "design", "architect", "implement", "optimize", "debug", "refactor"]
        complex_keywords = ["explain", "compare", "evaluate", "summarize", "research"]
        simple_keywords = ["what", "who", "when", "where", "define", "list"]
        
        expert_count = sum(1 for keyword in expert_keywords if keyword in prompt_lower)
        complex_count = sum(1 for keyword in complex_keywords if keyword in prompt_lower)
        simple_count = sum(1 for keyword in simple_keywords if keyword in prompt_lower)
        
        if expert_count >= 2 or len(prompt) > 1000:
            return TaskComplexity.EXPERT
        elif expert_count >= 1 or complex_count >= 2 or len(prompt) > 500:
            return TaskComplexity.COMPLEX
        elif complex_count >= 1 or len(prompt) > 200:
            return TaskComplexity.MEDIUM
        else:
            return TaskComplexity.SIMPLE
            
    def _select_optimal_model(self, request: OptimizationRequest) -> str:
        """Select the optimal model balancing quality and cost"""
        complexity = request.task_complexity or self._calculate_prompt_complexity(request.prompt)

        # QUALITY-FIRST approach - use Claude 4 Sonnet for most tasks
        # Only use budget model for very simple tasks when cost is explicitly prioritized

        if complexity == TaskComplexity.EXPERT:
            return "claude-4-sonnet"  # Always use premium for expert tasks
        elif complexity == TaskComplexity.COMPLEX:
            return "claude-4-sonnet"  # Use premium for complex tasks
        elif complexity == TaskComplexity.MEDIUM:
            # Use premium unless explicitly preferring cost AND quality threshold is low
            if request.prefer_cost and request.quality_threshold < 0.7:
                return "llama-3-8b"
            else:
                return "claude-4-sonnet"
        else:  # Simple tasks
            # Use budget model only for very simple tasks with low quality requirements
            if request.prefer_cost and request.quality_threshold < 0.8:
                return "llama-3-8b"
            else:
                return "claude-4-sonnet"
            
    def _get_cache_key(self, prompt: str, model: str, max_tokens: int, temperature: float) -> str:
        """Generate cache key for request"""
        import hashlib
        content = f"{prompt}:{model}:{max_tokens}:{temperature}"
        return f"cost_opt:{hashlib.md5(content.encode()).hexdigest()}"
        
    def _check_cache(self, cache_key: str) -> Optional[Dict]:
        """Check if response is cached"""
        if not self.redis_client:
            return None
            
        try:
            cached = self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Cache check failed: {e}")
        return None
        
    def _cache_response(self, cache_key: str, response_data: Dict, ttl: int = 3600):
        """Cache response"""
        if not self.redis_client:
            return
            
        try:
            self.redis_client.setex(cache_key, ttl, json.dumps(response_data))
        except Exception as e:
            logger.warning(f"Cache store failed: {e}")
            
    def _calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> Decimal:
        """Calculate cost for model usage"""
        if model not in self.model_costs:
            return Decimal("0.01")  # Default cost
            
        costs = self.model_costs[model]
        input_cost = Decimal(str(costs["input"])) * Decimal(str(input_tokens)) / 1000
        output_cost = Decimal(str(costs["output"])) * Decimal(str(output_tokens)) / 1000
        
        return input_cost + output_cost
        
    def _estimate_quality_score(self, model: str, complexity: TaskComplexity) -> float:
        """Estimate quality score based on model and task complexity"""
        base_scores = {
            "claude-4-sonnet": 0.95,  # Premium quality
            "llama-3-8b": 0.65,       # Budget quality (realistic assessment)
        }

        complexity_modifiers = {
            TaskComplexity.SIMPLE: 0.05,
            TaskComplexity.MEDIUM: 0.0,
            TaskComplexity.COMPLEX: -0.05,
            TaskComplexity.EXPERT: -0.10
        }

        base_score = base_scores.get(model, 0.75)
        modifier = complexity_modifiers.get(complexity, 0.0)

        return max(0.1, min(1.0, base_score + modifier))
        
    async def optimize(self, request: OptimizationRequest) -> OptimizationResponse:
        """Main optimization method"""
        start_time = time.time()
        request_id = f"req_{int(time.time() * 1000)}"
        
        # Select optimal model
        selected_model = self._select_optimal_model(request)
        
        # Check cache
        cache_key = self._get_cache_key(
            request.prompt, selected_model, 
            request.max_tokens or 1000, request.temperature or 0.7
        )
        
        cached_response = self._check_cache(cache_key)
        if cached_response:
            logger.info(f"Cache hit for request {request_id}")
            return OptimizationResponse(
                content=cached_response["content"],
                model_used=cached_response["model_used"],
                cost_usd=Decimal(str(cached_response["cost_usd"])),
                tokens_used=cached_response["tokens_used"],
                processing_time_ms=time.time() - start_time * 1000,
                quality_score=cached_response["quality_score"],
                optimization_savings=Decimal(str(cached_response["optimization_savings"])),
                cache_hit=True,
                request_id=request_id
            )
        
        # Make LLM request
        try:
            messages = [{"role": "user", "content": request.prompt}]
            
            if self.router:
                response = await self.router.acompletion(
                    model=selected_model,
                    messages=messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
            else:
                # Fallback to direct LiteLLM call
                response = await acompletion(
                    model=selected_model,
                    messages=messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
                
            # Extract response data
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            input_tokens = response.usage.prompt_tokens
            output_tokens = response.usage.completion_tokens
            
            # Calculate metrics
            processing_time_ms = (time.time() - start_time) * 1000
            cost_usd = self._calculate_cost(selected_model, input_tokens, output_tokens)
            quality_score = self._estimate_quality_score(
                selected_model, 
                request.task_complexity or self._calculate_prompt_complexity(request.prompt)
            )
            
            # Calculate savings (compared to premium model)
            premium_cost = self._calculate_cost("claude-4-sonnet", input_tokens, output_tokens)
            optimization_savings = premium_cost - cost_usd
            
            # Create response
            opt_response = OptimizationResponse(
                content=content,
                model_used=selected_model,
                cost_usd=cost_usd,
                tokens_used=tokens_used,
                processing_time_ms=processing_time_ms,
                quality_score=quality_score,
                optimization_savings=optimization_savings,
                cache_hit=False,
                request_id=request_id
            )
            
            # Cache response
            cache_data = {
                "content": content,
                "model_used": selected_model,
                "cost_usd": float(cost_usd),
                "tokens_used": tokens_used,
                "quality_score": quality_score,
                "optimization_savings": float(optimization_savings)
            }
            self._cache_response(cache_key, cache_data)
            
            # Store in database
            self._store_optimization(request, opt_response)
            
            return opt_response
            
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            raise HTTPException(status_code=500, detail=f"Optimization failed: {str(e)}")
            
    def _store_optimization(self, request: OptimizationRequest, response: OptimizationResponse):
        """Store optimization result in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            import hashlib
            prompt_hash = hashlib.md5(request.prompt.encode()).hexdigest()
            
            cursor.execute("""
                INSERT INTO optimizations 
                (request_id, user_id, prompt_hash, model_used, cost_usd, tokens_used, 
                 processing_time_ms, quality_score, optimization_savings, cache_hit)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                response.request_id, request.user_id, prompt_hash, response.model_used,
                float(response.cost_usd), response.tokens_used, response.processing_time_ms,
                response.quality_score, float(response.optimization_savings), response.cache_hit
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to store optimization: {e}")
            
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total stats
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_requests,
                    SUM(cost_usd) as total_cost,
                    SUM(optimization_savings) as total_savings,
                    AVG(quality_score) as avg_quality,
                    AVG(processing_time_ms) as avg_latency
                FROM optimizations
                WHERE created_at >= datetime('now', '-24 hours')
            """)
            
            stats = cursor.fetchone()
            
            # Model breakdown
            cursor.execute("""
                SELECT 
                    model_used,
                    COUNT(*) as requests,
                    SUM(cost_usd) as cost,
                    AVG(quality_score) as quality
                FROM optimizations
                WHERE created_at >= datetime('now', '-24 hours')
                GROUP BY model_used
            """)
            
            model_stats = cursor.fetchall()
            
            conn.close()
            
            return {
                "total_requests": stats[0] or 0,
                "total_cost_usd": round(stats[1] or 0, 4),
                "total_savings_usd": round(stats[2] or 0, 4),
                "avg_quality_score": round(stats[3] or 0, 3),
                "avg_latency_ms": round(stats[4] or 0, 1),
                "model_breakdown": [
                    {
                        "model": row[0],
                        "requests": row[1],
                        "cost_usd": round(row[2], 4),
                        "avg_quality": round(row[3], 3)
                    }
                    for row in model_stats
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {"error": str(e)}

# Global optimizer instance
optimizer = CostOptimizer()

# FastAPI app
app = FastAPI(
    title="Cost Optimization Platform",
    description="Production-ready LLM cost optimization built on LiteLLM",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class OptimizeRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to optimize")
    max_tokens: Optional[int] = Field(1000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, description="Temperature for generation")
    quality_threshold: Optional[float] = Field(0.8, description="Minimum quality threshold")
    max_cost: Optional[float] = Field(0.10, description="Maximum cost per request")
    task_complexity: Optional[TaskComplexity] = Field(TaskComplexity.MEDIUM, description="Task complexity")
    user_id: Optional[str] = Field(None, description="User identifier")
    prefer_speed: bool = Field(False, description="Prefer faster models")
    prefer_cost: bool = Field(True, description="Prefer cheaper models")

class OptimizeResponseModel(BaseModel):
    content: str
    model_used: str
    cost_usd: float
    tokens_used: int
    processing_time_ms: float
    quality_score: float
    optimization_savings: float
    cache_hit: bool
    request_id: str

@app.post("/api/v1/optimize", response_model=OptimizeResponseModel)
async def optimize_endpoint(request: OptimizeRequest):
    """Optimize LLM request for cost and quality"""
    opt_request = OptimizationRequest(
        prompt=request.prompt,
        max_tokens=request.max_tokens,
        temperature=request.temperature,
        quality_threshold=request.quality_threshold,
        max_cost=request.max_cost,
        task_complexity=request.task_complexity,
        user_id=request.user_id,
        prefer_speed=request.prefer_speed,
        prefer_cost=request.prefer_cost
    )
    
    response = await optimizer.optimize(opt_request)
    
    return OptimizeResponseModel(
        content=response.content,
        model_used=response.model_used,
        cost_usd=float(response.cost_usd),
        tokens_used=response.tokens_used,
        processing_time_ms=response.processing_time_ms,
        quality_score=response.quality_score,
        optimization_savings=float(response.optimization_savings),
        cache_hit=response.cache_hit,
        request_id=response.request_id
    )

@app.get("/api/v1/stats")
async def get_stats():
    """Get usage statistics"""
    return optimizer.get_usage_stats()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Simple dashboard"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Cost Optimization Platform</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .stat { text-align: center; }
            .stat h3 { margin: 0; color: #333; }
            .stat p { font-size: 24px; font-weight: bold; color: #007bff; margin: 10px 0; }
            textarea { width: 100%; height: 100px; margin: 10px 0; }
            button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .response { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚅 Cost Optimization Platform</h1>
            <p>Production-ready LLM cost optimization built on LiteLLM</p>
            
            <div class="card">
                <h2>Test Optimization</h2>
                <textarea id="prompt" placeholder="Enter your prompt here...">What is machine learning and how does it work?</textarea>
                <br>
                <button onclick="optimize()">Optimize Request</button>
                <div id="result"></div>
            </div>
            
            <div class="card">
                <h2>Usage Statistics (Last 24h)</h2>
                <div id="stats" class="stats">
                    <div class="stat">
                        <h3>Total Requests</h3>
                        <p id="total-requests">-</p>
                    </div>
                    <div class="stat">
                        <h3>Total Cost</h3>
                        <p id="total-cost">$-</p>
                    </div>
                    <div class="stat">
                        <h3>Total Savings</h3>
                        <p id="total-savings">$-</p>
                    </div>
                    <div class="stat">
                        <h3>Avg Quality</h3>
                        <p id="avg-quality">-</p>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            async function optimize() {
                const prompt = document.getElementById('prompt').value;
                const resultDiv = document.getElementById('result');
                
                resultDiv.innerHTML = '<p>Optimizing...</p>';
                
                try {
                    const response = await fetch('/api/v1/optimize', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prompt: prompt })
                    });
                    
                    const data = await response.json();
                    
                    resultDiv.innerHTML = `
                        <div class="response">
                            <h3>Response</h3>
                            <p><strong>Content:</strong> ${data.content}</p>
                            <p><strong>Model:</strong> ${data.model_used}</p>
                            <p><strong>Cost:</strong> $${data.cost_usd.toFixed(4)}</p>
                            <p><strong>Savings:</strong> $${data.optimization_savings.toFixed(4)}</p>
                            <p><strong>Quality Score:</strong> ${data.quality_score.toFixed(2)}</p>
                            <p><strong>Processing Time:</strong> ${data.processing_time_ms.toFixed(1)}ms</p>
                            <p><strong>Cache Hit:</strong> ${data.cache_hit ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                }
            }
            
            async function loadStats() {
                try {
                    const response = await fetch('/api/v1/stats');
                    const data = await response.json();
                    
                    document.getElementById('total-requests').textContent = data.total_requests || 0;
                    document.getElementById('total-cost').textContent = '$' + (data.total_cost_usd || 0).toFixed(4);
                    document.getElementById('total-savings').textContent = '$' + (data.total_savings_usd || 0).toFixed(4);
                    document.getElementById('avg-quality').textContent = (data.avg_quality_score || 0).toFixed(2);
                } catch (error) {
                    console.error('Failed to load stats:', error);
                }
            }
            
            // Load stats on page load
            loadStats();
            
            // Refresh stats every 30 seconds
            setInterval(loadStats, 30000);
        </script>
    </body>
    </html>
    """

if __name__ == "__main__":
    uvicorn.run("cost_optimizer:app", host="0.0.0.0", port=8000, reload=True)
