#!/usr/bin/env python3
"""
Startup script for Cost Optimization Platform
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import litellm
        import fastapi
        import redis
        import uvicorn
        print("✅ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install 'litellm[proxy]' fastapi uvicorn redis")
        return False

def setup_environment():
    """Setup environment variables"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from template...")
        env_file.write_text(env_example.read_text())
        print("⚠️  Please edit .env file with your API keys before running")
        return False
    
    # Load environment variables
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    
    return True

def check_api_keys():
    """Check if API keys are configured"""
    required_keys = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY", "OPENROUTER_API_KEY"]
    missing_keys = []
    
    for key in required_keys:
        if not os.getenv(key) or os.getenv(key) == f"your-{key.lower().replace('_', '-')}-here":
            missing_keys.append(key)
    
    if missing_keys:
        print(f"⚠️  Missing API keys: {', '.join(missing_keys)}")
        print("Please configure your API keys in the .env file")
        return False
    
    print("✅ API keys configured")
    return True

def start_redis():
    """Start Redis if available"""
    try:
        import redis
        client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        client.ping()
        print("✅ Redis is running")
        return True
    except:
        print("⚠️  Redis not available - caching will be disabled")
        return False

def run_server():
    """Run the cost optimization server"""
    print("🚀 Starting Cost Optimization Platform...")
    
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", "8000"))
    reload = os.getenv("APP_RELOAD", "true").lower() == "true"
    
    try:
        import uvicorn
        uvicorn.run(
            "cost_optimizer:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main startup function"""
    print("🚅 Cost Optimization Platform")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        print("\n📝 Please configure your .env file and run again")
        sys.exit(1)
    
    # Check API keys (optional for testing)
    api_keys_ok = check_api_keys()
    
    # Check Redis
    redis_ok = start_redis()
    
    if not api_keys_ok:
        response = input("\nContinue without API keys? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🌟 Platform Status:")
    print(f"   Dependencies: ✅")
    print(f"   API Keys: {'✅' if api_keys_ok else '⚠️'}")
    print(f"   Redis Cache: {'✅' if redis_ok else '⚠️'}")
    print("=" * 50)
    
    # Start server
    run_server()

if __name__ == "__main__":
    main()
