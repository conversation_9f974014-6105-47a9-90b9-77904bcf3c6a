#!/usr/bin/env python3
"""
System Health Checker - 99%+ Reliability Validation
Comprehensive debugging and validation for production deployment
"""

import asyncio
import sys
import os
import json
import time
import psutil
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append('/home/<USER>/cost-opt-platform')

class SystemHealthChecker:
    """Comprehensive system health and reliability checker"""
    
    def __init__(self):
        self.health_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_health": "UNKNOWN",
            "reliability_score": 0.0,
            "components": {},
            "performance_metrics": {},
            "errors": [],
            "warnings": [],
            "recommendations": []
        }
    
    async def run_comprehensive_check(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive report"""
        print("🏥 SYSTEM HEALTH CHECKER - 99%+ Reliability Validation")
        print("=" * 70)
        
        checks = [
            ("Infrastructure", self._check_infrastructure),
            ("Dependencies", self._check_dependencies),
            ("Database", self._check_database),
            ("Redis Cache", self._check_redis),
            ("API Endpoints", self._check_api_endpoints),
            ("Vector Databases", self._check_vector_dbs),
            ("OpenRouter Integration", self._check_openrouter),
            ("Performance", self._check_performance),
            ("Security", self._check_security),
            ("Monitoring", self._check_monitoring)
        ]
        
        total_checks = len(checks)
        passed_checks = 0
        
        for check_name, check_func in checks:
            print(f"\n🔍 Checking {check_name}...")
            try:
                result = await check_func()
                self.health_report["components"][check_name] = result
                
                if result.get("status") == "HEALTHY":
                    passed_checks += 1
                    print(f"  ✅ {check_name}: HEALTHY")
                else:
                    print(f"  ⚠️ {check_name}: {result.get('status', 'UNKNOWN')}")
                    if result.get("errors"):
                        for error in result["errors"]:
                            print(f"    ❌ {error}")
                            self.health_report["errors"].append(f"{check_name}: {error}")
                
            except Exception as e:
                print(f"  ❌ {check_name}: FAILED - {e}")
                self.health_report["errors"].append(f"{check_name}: {str(e)}")
                self.health_report["components"][check_name] = {
                    "status": "FAILED",
                    "error": str(e)
                }
        
        # Calculate reliability score
        self.health_report["reliability_score"] = (passed_checks / total_checks) * 100
        
        # Determine overall health
        if self.health_report["reliability_score"] >= 99:
            self.health_report["overall_health"] = "EXCELLENT"
        elif self.health_report["reliability_score"] >= 95:
            self.health_report["overall_health"] = "GOOD"
        elif self.health_report["reliability_score"] >= 80:
            self.health_report["overall_health"] = "FAIR"
        else:
            self.health_report["overall_health"] = "POOR"
        
        self._generate_recommendations()
        self._print_summary()
        
        return self.health_report
    
    async def _check_infrastructure(self) -> Dict[str, Any]:
        """Check system infrastructure"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            # System resources
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            result["details"] = {
                "cpu_usage": f"{cpu_percent}%",
                "memory_usage": f"{memory.percent}%",
                "memory_available": f"{memory.available / (1024**3):.1f}GB",
                "disk_usage": f"{disk.percent}%",
                "disk_free": f"{disk.free / (1024**3):.1f}GB"
            }
            
            # Check thresholds
            if cpu_percent > 80:
                result["errors"].append(f"High CPU usage: {cpu_percent}%")
            if memory.percent > 85:
                result["errors"].append(f"High memory usage: {memory.percent}%")
            if disk.percent > 90:
                result["errors"].append(f"High disk usage: {disk.percent}%")
            
            if result["errors"]:
                result["status"] = "WARNING"
                
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(str(e))
        
        return result
    
    async def _check_dependencies(self) -> Dict[str, Any]:
        """Check Python dependencies and imports"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        critical_imports = [
            "fastapi",
            "sqlalchemy",
            "redis",
            "pydantic",
            "uvicorn",
            "chromadb",
            "celery",
            "prometheus_client",
            "opentelemetry"
        ]
        
        for module in critical_imports:
            try:
                __import__(module)
                result["details"][module] = "✅ Available"
            except ImportError as e:
                result["details"][module] = f"❌ Missing: {e}"
                result["errors"].append(f"Missing dependency: {module}")
        
        if result["errors"]:
            result["status"] = "FAILED"
        
        return result
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity and health"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            from src.core.database import get_database
            
            db = get_database()
            start_time = time.time()
            await db.connect()
            connection_time = (time.time() - start_time) * 1000
            
            result["details"] = {
                "connection_time": f"{connection_time:.2f}ms",
                "database_type": "SQLite with aiosqlite",
                "status": "Connected"
            }
            
            await db.disconnect()
            
            if connection_time > 1000:  # 1 second
                result["errors"].append(f"Slow database connection: {connection_time:.2f}ms")
                result["status"] = "WARNING"
                
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(f"Database connection failed: {e}")
        
        return result
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            import redis
            
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            
            # Test basic operations
            start_time = time.time()
            r.ping()
            ping_time = (time.time() - start_time) * 1000
            
            # Test set/get
            test_key = "health_check_test"
            r.set(test_key, "test_value", ex=60)
            value = r.get(test_key)
            r.delete(test_key)
            
            result["details"] = {
                "ping_time": f"{ping_time:.2f}ms",
                "set_get_test": "✅ Passed" if value == "test_value" else "❌ Failed",
                "redis_version": r.info()["redis_version"]
            }
            
            if ping_time > 100:  # 100ms
                result["errors"].append(f"Slow Redis response: {ping_time:.2f}ms")
                result["status"] = "WARNING"
                
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(f"Redis connection failed: {e}")
        
        return result
    
    async def _check_api_endpoints(self) -> Dict[str, Any]:
        """Check API endpoint health"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        endpoints = [
            ("GET", "http://localhost:8000/health"),
            ("GET", "http://localhost:8000/api/v1/conversations/"),
            ("GET", "http://localhost:8000/metrics")
        ]
        
        for method, url in endpoints:
            try:
                response = requests.get(url, timeout=5)
                endpoint_name = url.split("/")[-1] or "root"
                
                result["details"][endpoint_name] = {
                    "status_code": response.status_code,
                    "response_time": f"{response.elapsed.total_seconds() * 1000:.2f}ms"
                }
                
                if response.status_code not in [200, 404]:  # 404 is OK for some endpoints
                    result["errors"].append(f"{endpoint_name}: HTTP {response.status_code}")
                
            except requests.exceptions.ConnectionError:
                result["details"][endpoint_name] = "❌ Connection refused"
                result["errors"].append(f"{endpoint_name}: Server not running")
            except Exception as e:
                result["details"][endpoint_name] = f"❌ Error: {e}"
                result["errors"].append(f"{endpoint_name}: {e}")
        
        if result["errors"]:
            result["status"] = "FAILED"
        
        return result
    
    async def _check_vector_dbs(self) -> Dict[str, Any]:
        """Check vector database integrations"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            import chromadb
            
            # Test ChromaDB
            client = chromadb.Client()
            collections = client.list_collections()
            
            result["details"]["chromadb"] = {
                "status": "✅ Available",
                "collections": len(collections)
            }
            
        except Exception as e:
            result["details"]["chromadb"] = f"❌ Error: {e}"
            result["errors"].append(f"ChromaDB: {e}")
        
        # Test other vector DBs
        vector_dbs = ["faiss", "pymilvus", "pinecone"]
        for db in vector_dbs:
            try:
                __import__(db)
                result["details"][db] = "✅ Available"
            except ImportError:
                result["details"][db] = "⚠️ Not installed"
        
        if result["errors"]:
            result["status"] = "WARNING"
        
        return result
    
    async def _check_openrouter(self) -> Dict[str, Any]:
        """Check OpenRouter.ai integration"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            from src.core.config import get_settings
            
            settings = get_settings()
            
            if not settings.openrouter_api_key or settings.openrouter_api_key == "your-openrouter-api-key":
                result["errors"].append("OpenRouter API key not configured")
                result["status"] = "FAILED"
            else:
                result["details"]["api_key"] = "✅ Configured"
                result["details"]["base_url"] = settings.openrouter_base_url
            
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(f"OpenRouter config error: {e}")
        
        return result
    
    async def _check_performance(self) -> Dict[str, Any]:
        """Check performance metrics"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            # Test import performance
            start_time = time.time()
            from src.core.config import get_settings
            import_time = (time.time() - start_time) * 1000
            
            result["details"] = {
                "config_import_time": f"{import_time:.2f}ms",
                "target_latency": "<100ms",
                "target_uptime": "99.9%"
            }
            
            if import_time > 100:
                result["errors"].append(f"Slow config import: {import_time:.2f}ms")
                result["status"] = "WARNING"
                
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(f"Performance check failed: {e}")
        
        return result
    
    async def _check_security(self) -> Dict[str, Any]:
        """Check security configuration"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        try:
            from src.core.config import get_settings
            
            settings = get_settings()
            
            security_checks = {
                "secret_key": settings.secret_key != "development-secret-key-32-characters-long",
                "debug_mode": not settings.debug,
                "cors_configured": bool(settings.allowed_origins),
                "rate_limiting": True  # Assume configured
            }
            
            result["details"] = security_checks
            
            for check, passed in security_checks.items():
                if not passed:
                    result["errors"].append(f"Security issue: {check}")
            
            if result["errors"]:
                result["status"] = "WARNING"
                
        except Exception as e:
            result["status"] = "FAILED"
            result["errors"].append(f"Security check failed: {e}")
        
        return result
    
    async def _check_monitoring(self) -> Dict[str, Any]:
        """Check monitoring and observability"""
        result = {"status": "HEALTHY", "details": {}, "errors": []}
        
        monitoring_components = [
            "prometheus_client",
            "opentelemetry",
            "structlog"
        ]
        
        for component in monitoring_components:
            try:
                __import__(component)
                result["details"][component] = "✅ Available"
            except ImportError:
                result["details"][component] = "❌ Missing"
                result["errors"].append(f"Missing monitoring component: {component}")
        
        if result["errors"]:
            result["status"] = "WARNING"
        
        return result
    
    def _generate_recommendations(self):
        """Generate recommendations based on health check results"""
        recommendations = []
        
        if self.health_report["reliability_score"] < 99:
            recommendations.append("System reliability below 99% - review failed components")
        
        for component, details in self.health_report["components"].items():
            if details.get("status") == "FAILED":
                recommendations.append(f"Fix {component} - critical for system operation")
            elif details.get("status") == "WARNING":
                recommendations.append(f"Optimize {component} - performance issues detected")
        
        if not recommendations:
            recommendations.append("System is healthy - maintain current configuration")
        
        self.health_report["recommendations"] = recommendations
    
    def _print_summary(self):
        """Print health check summary"""
        print("\n" + "=" * 70)
        print("📊 SYSTEM HEALTH SUMMARY")
        print("=" * 70)
        print(f"Overall Health: {self.health_report['overall_health']}")
        print(f"Reliability Score: {self.health_report['reliability_score']:.1f}%")
        print(f"Components Checked: {len(self.health_report['components'])}")
        print(f"Errors Found: {len(self.health_report['errors'])}")
        print(f"Warnings: {len(self.health_report['warnings'])}")
        
        if self.health_report["errors"]:
            print(f"\n❌ Critical Issues:")
            for error in self.health_report["errors"][:5]:  # Show first 5
                print(f"  • {error}")
        
        print(f"\n💡 Recommendations:")
        for rec in self.health_report["recommendations"][:3]:  # Show first 3
            print(f"  • {rec}")
        
        if self.health_report["reliability_score"] >= 99:
            print(f"\n🎉 System meets 99%+ reliability target!")
        else:
            print(f"\n⚠️ System reliability needs improvement to reach 99%+ target")

async def main():
    """Run comprehensive system health check"""
    checker = SystemHealthChecker()
    health_report = await checker.run_comprehensive_check()
    
    # Save report
    with open('/home/<USER>/cost-opt-platform/health_report.json', 'w') as f:
        json.dump(health_report, f, indent=2, default=str)
    
    print(f"\n📄 Health report saved to health_report.json")
    
    # Exit with appropriate code
    if health_report["reliability_score"] >= 99:
        print(f"✅ System health check PASSED")
        sys.exit(0)
    else:
        print(f"❌ System health check FAILED")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
