# Testing Requirements for 100% Coverage
# Compatible with Python 3.9+

# Core Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-benchmark>=4.0.0

# HTTP Testing
httpx>=0.25.0
requests-mock>=1.11.0

# Database Testing
fakeredis>=2.20.0

# Code Quality & Coverage
coverage[toml]>=7.3.0
pytest-html>=4.1.0

# Performance Testing
memory-profiler>=0.61.0

# Security Testing
bandit>=1.7.5

# Code Quality
flake8>=6.1.0

# Mocking & Fixtures
factory-boy>=3.3.0
freezegun>=1.2.0
