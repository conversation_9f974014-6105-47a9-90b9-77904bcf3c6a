#!/bin/bash
# Docker entrypoint script for FAANG+ production deployment
# Implements Google reliability practices with comprehensive health checks

set -euo pipefail

# Configuration
export PYTHONPATH="${PYTHONPATH:-/app}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"
export ENVIRONMENT="${ENVIRONMENT:-production}"

# Logging function
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [ENTRYPOINT] $*" >&2
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Signal handlers for graceful shutdown
shutdown_handler() {
    log "Received shutdown signal, initiating graceful shutdown..."
    
    # Send SIGTERM to all child processes
    if [ -n "${MAIN_PID:-}" ]; then
        log "Stopping main process (PID: $MAIN_PID)..."
        kill -TERM "$MAIN_PID" 2>/dev/null || true
        
        # Wait for graceful shutdown
        local timeout=30
        while kill -0 "$MAIN_PID" 2>/dev/null && [ $timeout -gt 0 ]; do
            sleep 1
            timeout=$((timeout - 1))
        done
        
        # Force kill if still running
        if kill -0 "$MAIN_PID" 2>/dev/null; then
            log "Force killing main process..."
            kill -KILL "$MAIN_PID" 2>/dev/null || true
        fi
    fi
    
    log "Graceful shutdown completed"
    exit 0
}

# Set up signal handlers
trap shutdown_handler SIGTERM SIGINT

# Pre-flight checks
preflight_checks() {
    log "Running pre-flight checks..."
    
    # Check required environment variables
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            error_exit "Required environment variable $var is not set"
        fi
    done
    
    # Check database connectivity (temporarily disabled for development)
    log "Skipping database connectivity check for development..."
    # python -c "
# import asyncio
# import sys
# from src.core.database import DatabaseManager

# async def check_db():
#     try:
#         db = DatabaseManager()
#         await db.initialize()
#         await db.health_check()
#         await db.cleanup()
#         print('Database connection successful')
#         return True
#     except Exception as e:
#         print(f'Database connection failed: {e}')
#         return False

# result = asyncio.run(check_db())
# sys.exit(0 if result else 1)
# " || error_exit "Database connectivity check failed"
    
    # Check Redis connectivity
    log "Checking Redis connectivity..."
    python -c "
import redis
import os
import sys

try:
    r = redis.from_url(os.environ['REDIS_URL'])
    r.ping()
    print('Redis connection successful')
    sys.exit(0)
except Exception as e:
    print(f'Redis connection failed: {e}')
    sys.exit(1)
" || error_exit "Redis connectivity check failed"
    
    # Check required directories
    local required_dirs=(
        "/app/logs"
        "/app/data"
        "/app/tmp"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log "Creating directory: $dir"
            mkdir -p "$dir"
        fi
    done
    
    log "Pre-flight checks completed successfully"
}

# Database migrations
run_migrations() {
    if [ "${RUN_MIGRATIONS:-true}" = "true" ]; then
        log "Running database migrations..."
        alembic upgrade head || error_exit "Database migration failed"
        log "Database migrations completed"
    else
        log "Skipping database migrations (RUN_MIGRATIONS=false)"
    fi
}

# Cache warming
warm_caches() {
    if [ "${WARM_CACHES:-true}" = "true" ]; then
        log "Warming caches..."
        python -c "
import asyncio
from src.services.cache_manager import CacheManager

async def warm_cache():
    try:
        cache = CacheManager()
        await cache.initialize()
        # Add cache warming logic here
        await cache.cleanup()
        print('Cache warming completed')
    except Exception as e:
        print(f'Cache warming failed: {e}')

asyncio.run(warm_cache())
" || log "WARNING: Cache warming failed, continuing anyway"
    else
        log "Skipping cache warming (WARM_CACHES=false)"
    fi
}

# Start application
start_application() {
    log "Starting application in $ENVIRONMENT mode..."
    
    case "$ENVIRONMENT" in
        "production")
            log "Starting with Gunicorn for production..."
            exec gunicorn src.main:app \
                --config gunicorn.conf.py \
                --bind "0.0.0.0:${PORT:-8000}" \
                --workers "${WORKERS:-4}" \
                --worker-class "${WORKER_CLASS:-uvicorn.workers.UvicornWorker}" \
                --worker-connections "${WORKER_CONNECTIONS:-1000}" \
                --max-requests "${MAX_REQUESTS:-1000}" \
                --max-requests-jitter "${MAX_REQUESTS_JITTER:-100}" \
                --timeout "${TIMEOUT:-30}" \
                --graceful-timeout "${GRACEFUL_TIMEOUT:-30}" \
                --keepalive "${KEEPALIVE:-2}" \
                --preload \
                --access-logfile - \
                --error-logfile - \
                --log-level "${LOG_LEVEL:-info}" &
            ;;
        "development")
            log "Starting with Uvicorn for development..."
            exec uvicorn src.main:app \
                --host "0.0.0.0" \
                --port "${PORT:-8000}" \
                --reload \
                --log-level "${LOG_LEVEL:-debug}" &
            ;;
        "testing")
            log "Starting in testing mode..."
            exec python -m pytest tests/ -v &
            ;;
        *)
            error_exit "Unknown environment: $ENVIRONMENT"
            ;;
    esac
    
    # Store main process PID for signal handling
    MAIN_PID=$!
    log "Application started with PID: $MAIN_PID"
    
    # Wait for the main process
    wait $MAIN_PID
}

# Health check endpoint setup
setup_health_checks() {
    log "Setting up health check monitoring..."
    
    # Start background health check monitor
    python -c "
import asyncio
import time
import logging
from src.api.v1.routes.health import health_check

async def monitor_health():
    logger = logging.getLogger('health_monitor')
    while True:
        try:
            result = await health_check()
            if result.status != 'healthy':
                logger.warning(f'Health check warning: {result.details}')
        except Exception as e:
            logger.error(f'Health check failed: {e}')
        
        await asyncio.sleep(30)  # Check every 30 seconds

if __name__ == '__main__':
    asyncio.run(monitor_health())
" &
    
    log "Health check monitoring started"
}

# Performance monitoring setup
setup_monitoring() {
    if [ "${ENABLE_MONITORING:-true}" = "true" ]; then
        log "Setting up performance monitoring..."
        
        # Start Prometheus metrics endpoint
        python -c "
import prometheus_client
import threading
import time

def start_metrics_server():
    prometheus_client.start_http_server(9090)
    print('Prometheus metrics server started on port 9090')

if __name__ == '__main__':
    threading.Thread(target=start_metrics_server, daemon=True).start()
    time.sleep(1)  # Give it time to start
" &
        
        log "Performance monitoring setup completed"
    fi
}

# Main execution
main() {
    log "Starting Cost Optimizer application..."
    log "Environment: $ENVIRONMENT"
    log "Python path: $PYTHONPATH"
    log "Log level: $LOG_LEVEL"
    
    # Run initialization steps
    preflight_checks
    run_migrations
    warm_caches
    setup_health_checks
    setup_monitoring
    
    # Start the application
    start_application
}

# Execute main function
main "$@"
