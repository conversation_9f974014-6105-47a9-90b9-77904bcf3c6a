#!/usr/bin/env python3
"""
Simple Cost Optimization Demo using LiteLLM
A working demonstration of cost optimization principles
"""

import asyncio
import time
from decimal import Decimal
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

# Try to import LiteLLM, fallback to mock if not available
try:
    import litellm
    from litellm import completion, acompletion
    LITELLM_AVAILABLE = True
    print("✅ LiteLLM is available")
except ImportError:
    LITELLM_AVAILABLE = False
    print("⚠️  LiteLLM not available, using mock responses")

class ModelTier(str, Enum):
    PREMIUM = "premium"
    STANDARD = "standard" 
    BUDGET = "budget"

class TaskComplexity(str, Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"

@dataclass
class OptimizationRequest:
    prompt: str
    max_tokens: int = 1000
    temperature: float = 0.7
    quality_threshold: float = 0.8
    prefer_cost: bool = True

@dataclass
class OptimizationResult:
    content: str
    model_used: str
    cost_usd: Decimal
    tokens_used: int
    processing_time_ms: float
    quality_score: float
    savings_usd: Decimal

class SimpleCostOptimizer:
    """Simple cost optimizer demonstrating core principles"""
    
    def __init__(self):
        # Model configurations with costs per 1K tokens
        self.models = {
            "claude-4-sonnet": {
                "tier": ModelTier.PREMIUM,
                "input_cost": 0.003,
                "output_cost": 0.015,
                "quality_score": 0.95,
                "litellm_model": "anthropic/claude-3-5-sonnet-20241022"
            },
            "deepseek-v3": {
                "tier": ModelTier.STANDARD,
                "input_cost": 0.002,
                "output_cost": 0.009,
                "quality_score": 0.85,
                "litellm_model": "openrouter/deepseek/deepseek-v3"
            },
            "llama-3-8b": {
                "tier": ModelTier.BUDGET,
                "input_cost": 0.0001,
                "output_cost": 0.0003,
                "quality_score": 0.70,
                "litellm_model": "openrouter/meta-llama/llama-3.1-8b-instruct"
            }
        }
        
        self.optimization_history = []
        
    def analyze_complexity(self, prompt: str) -> TaskComplexity:
        """Analyze prompt complexity"""
        prompt_lower = prompt.lower()
        
        expert_keywords = ["analyze", "design", "architect", "implement", "optimize"]
        complex_keywords = ["explain", "compare", "evaluate", "summarize"]
        
        expert_count = sum(1 for kw in expert_keywords if kw in prompt_lower)
        complex_count = sum(1 for kw in complex_keywords if kw in prompt_lower)
        
        if expert_count >= 2 or len(prompt) > 1000:
            return TaskComplexity.EXPERT
        elif expert_count >= 1 or complex_count >= 2:
            return TaskComplexity.COMPLEX
        elif complex_count >= 1 or len(prompt) > 200:
            return TaskComplexity.MEDIUM
        else:
            return TaskComplexity.SIMPLE
    
    def select_optimal_model(self, request: OptimizationRequest) -> str:
        """Select the most cost-effective model"""
        complexity = self.analyze_complexity(request.prompt)
        
        # Model selection logic
        if complexity == TaskComplexity.EXPERT and request.quality_threshold >= 0.9:
            return "claude-4-sonnet"
        elif complexity == TaskComplexity.COMPLEX and request.quality_threshold >= 0.8:
            return "deepseek-v3" if request.prefer_cost else "claude-4-sonnet"
        elif complexity == TaskComplexity.MEDIUM:
            return "deepseek-v3" if request.prefer_cost else "claude-4-sonnet"
        else:  # Simple tasks
            return "llama-3-8b" if request.prefer_cost else "deepseek-v3"
    
    def calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> Decimal:
        """Calculate cost for model usage"""
        if model not in self.models:
            return Decimal("0.01")
        
        config = self.models[model]
        input_cost = Decimal(str(config["input_cost"])) * input_tokens / 1000
        output_cost = Decimal(str(config["output_cost"])) * output_tokens / 1000
        
        return input_cost + output_cost
    
    def mock_llm_response(self, model: str, prompt: str, max_tokens: int) -> Dict:
        """Mock LLM response for testing"""
        responses = {
            "claude-4-sonnet": "This is a high-quality response from Claude 4 Sonnet. The answer demonstrates deep understanding and comprehensive analysis of the question.",
            "deepseek-v3": "This is a good quality response from DeepSeek v3. It provides accurate information with reasonable detail.",
            "llama-3-8b": "This is a basic response from Llama 3 8B. It answers the question directly and concisely."
        }
        
        content = responses.get(model, "Mock response from " + model)
        
        # Simulate token usage
        input_tokens = len(prompt.split()) * 1.3  # Rough approximation
        output_tokens = len(content.split()) * 1.3
        
        return {
            "content": content,
            "input_tokens": int(input_tokens),
            "output_tokens": int(output_tokens),
            "total_tokens": int(input_tokens + output_tokens)
        }
    
    async def optimize(self, request: OptimizationRequest) -> OptimizationResult:
        """Main optimization method"""
        start_time = time.time()
        
        # Select optimal model
        selected_model = self.select_optimal_model(request)
        print(f"🎯 Selected model: {selected_model}")
        
        # Get response (real or mock)
        if LITELLM_AVAILABLE:
            try:
                # Try real LiteLLM call
                model_config = self.models[selected_model]
                response = await acompletion(
                    model=model_config["litellm_model"],
                    messages=[{"role": "user", "content": request.prompt}],
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
                
                content = response.choices[0].message.content
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens
                total_tokens = response.usage.total_tokens
                
            except Exception as e:
                print(f"⚠️  LiteLLM call failed: {e}, using mock response")
                mock_response = self.mock_llm_response(selected_model, request.prompt, request.max_tokens)
                content = mock_response["content"]
                input_tokens = mock_response["input_tokens"]
                output_tokens = mock_response["output_tokens"]
                total_tokens = mock_response["total_tokens"]
        else:
            # Use mock response
            mock_response = self.mock_llm_response(selected_model, request.prompt, request.max_tokens)
            content = mock_response["content"]
            input_tokens = mock_response["input_tokens"]
            output_tokens = mock_response["output_tokens"]
            total_tokens = mock_response["total_tokens"]
        
        # Calculate metrics
        processing_time_ms = (time.time() - start_time) * 1000
        cost_usd = self.calculate_cost(selected_model, input_tokens, output_tokens)
        quality_score = self.models[selected_model]["quality_score"]
        
        # Calculate savings compared to premium model
        premium_cost = self.calculate_cost("claude-4-sonnet", input_tokens, output_tokens)
        savings_usd = premium_cost - cost_usd
        
        result = OptimizationResult(
            content=content,
            model_used=selected_model,
            cost_usd=cost_usd,
            tokens_used=total_tokens,
            processing_time_ms=processing_time_ms,
            quality_score=quality_score,
            savings_usd=savings_usd
        )
        
        # Store in history
        self.optimization_history.append(result)
        
        return result
    
    def get_stats(self) -> Dict:
        """Get optimization statistics"""
        if not self.optimization_history:
            return {"message": "No optimizations performed yet"}
        
        total_cost = sum(float(r.cost_usd) for r in self.optimization_history)
        total_savings = sum(float(r.savings_usd) for r in self.optimization_history)
        avg_quality = sum(r.quality_score for r in self.optimization_history) / len(self.optimization_history)
        
        model_usage = {}
        for result in self.optimization_history:
            model = result.model_used
            if model not in model_usage:
                model_usage[model] = {"count": 0, "cost": 0}
            model_usage[model]["count"] += 1
            model_usage[model]["cost"] += float(result.cost_usd)
        
        return {
            "total_requests": len(self.optimization_history),
            "total_cost_usd": round(total_cost, 4),
            "total_savings_usd": round(total_savings, 4),
            "avg_quality_score": round(avg_quality, 3),
            "model_usage": model_usage
        }

async def demo():
    """Run cost optimization demo"""
    print("🚅 Cost Optimization Platform Demo")
    print("=" * 50)
    
    optimizer = SimpleCostOptimizer()
    
    # Test cases
    test_cases = [
        OptimizationRequest(
            prompt="What is the capital of France?",
            prefer_cost=True
        ),
        OptimizationRequest(
            prompt="Explain the differences between machine learning and deep learning, including their applications and limitations.",
            quality_threshold=0.85,
            prefer_cost=True
        ),
        OptimizationRequest(
            prompt="Design a scalable microservices architecture for an e-commerce platform that can handle 1 million concurrent users.",
            quality_threshold=0.9,
            prefer_cost=False
        )
    ]
    
    print("\n🧪 Running test cases...")
    
    for i, request in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Prompt: {request.prompt[:60]}...")
        print(f"Quality threshold: {request.quality_threshold}")
        print(f"Prefer cost: {request.prefer_cost}")
        
        try:
            result = await optimizer.optimize(request)
            
            print(f"\n✅ Results:")
            print(f"   Model used: {result.model_used}")
            print(f"   Cost: ${result.cost_usd:.4f}")
            print(f"   Savings: ${result.savings_usd:.4f}")
            print(f"   Quality score: {result.quality_score:.2f}")
            print(f"   Processing time: {result.processing_time_ms:.1f}ms")
            print(f"   Tokens used: {result.tokens_used}")
            print(f"   Response: {result.content[:100]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Show overall stats
    print(f"\n📊 Overall Statistics:")
    stats = optimizer.get_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ Demo completed successfully!")

if __name__ == "__main__":
    asyncio.run(demo())
