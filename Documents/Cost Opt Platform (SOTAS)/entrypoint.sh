#!/bin/bash
# Production Entrypoint for Cost Optimization Platform

set -e

echo "🚅 Starting Cost Optimization Platform..."

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
while ! redis-cli -h redis -p 6379 ping > /dev/null 2>&1; do
    echo "Redis is unavailable - sleeping"
    sleep 2
done
echo "✅ Redis is ready!"

# Create necessary directories
mkdir -p /app/data /app/logs

# Initialize database if needed
if [ ! -f "/app/data/cost_optimizer.db" ]; then
    echo "🗄️ Initializing database..."
    python -c "
from cost_optimizer import create_tables
create_tables()
print('Database initialized successfully!')
"
fi

# Start the application
echo "🚀 Starting Cost Optimization Platform on port 8001..."
exec python -m uvicorn cost_optimizer:app \
    --host 0.0.0.0 \
    --port 8001 \
    --workers 1 \
    --access-log \
    --log-level info
