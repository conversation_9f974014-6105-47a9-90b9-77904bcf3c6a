#!/bin/bash

# Production-Grade Entrypoint for Claude Cost Optimizer
# Optimized for OrbStack deployment with comprehensive error handling

set -euo pipefail  # Exit on error, undefined vars, pipe failures
IFS=$'\n\t'       # Secure Internal Field Separator

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" >&2
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Signal handling for graceful shutdown
cleanup() {
    log "Received shutdown signal, cleaning up..."
    if [ -n "${GUNICORN_PID:-}" ]; then
        kill -TERM "$GUNICORN_PID" 2>/dev/null || true
        wait "$GUNICORN_PID" 2>/dev/null || true
    fi
    log "Cleanup completed"
    exit 0
}

trap cleanup SIGTERM SIGINT SIGQUIT

# Environment variables with validation
export PYTHONPATH="/app"
export ENVIRONMENT="${ENVIRONMENT:-production}"
export PORT="${PORT:-8000}"
export WORKERS="${WORKERS:-4}"
export MAX_WORKERS="${MAX_WORKERS:-8}"
export WORKER_CLASS="${WORKER_CLASS:-gevent}"
export WORKER_CONNECTIONS="${WORKER_CONNECTIONS:-1000}"
export TIMEOUT="${TIMEOUT:-30}"
export KEEPALIVE="${KEEPALIVE:-2}"
export MAX_REQUESTS="${MAX_REQUESTS:-1000}"
export MAX_REQUESTS_JITTER="${MAX_REQUESTS_JITTER:-100}"

log "Starting Claude Cost Optimizer..."
log "Environment: $ENVIRONMENT"
log "Port: $PORT"
log "Workers: $WORKERS"

# Validate environment
if [ ! -d "/app/src" ]; then
    error_exit "Application source directory not found"
fi

if [ ! -f "/app/src/main.py" ]; then
    error_exit "Main application file not found"
fi

# Create required directories with proper permissions
mkdir -p /app/logs /app/data /app/tmp /app/cache
chmod 750 /app/logs /app/data /app/tmp /app/cache

# Health check before starting
log "Performing pre-startup health checks..."

# Check Python environment
python -c "import sys; print(f'Python version: {sys.version}')" || error_exit "Python environment check failed"

# Check critical imports
python -c "
try:
    import fastapi, uvicorn, sqlalchemy, redis, httpx
    print('Critical dependencies available')
except ImportError as e:
    print(f'Missing dependency: {e}')
    exit(1)
" || error_exit "Dependency check failed"

# Database migration (if needed)
if [ "$ENVIRONMENT" = "production" ] && [ -f "/app/alembic.ini" ]; then
    log "Running database migrations..."
    alembic upgrade head || log "WARNING: Database migration failed, continuing..."
fi

# Start the application based on environment
if [ "$ENVIRONMENT" = "development" ]; then
    log "Starting with Uvicorn for development..."
    exec uvicorn src.main:app \
        --host "0.0.0.0" \
        --port "$PORT" \
        --reload \
        --log-level debug \
        --access-log \
        --use-colors
else
    log "Starting with Gunicorn for production..."

    # Production configuration
    exec gunicorn src.main:app \
        --bind "0.0.0.0:$PORT" \
        --workers "$WORKERS" \
        --max-requests "$MAX_REQUESTS" \
        --max-requests-jitter "$MAX_REQUESTS_JITTER" \
        --timeout "$TIMEOUT" \
        --keepalive "$KEEPALIVE" \
        --worker-class "$WORKER_CLASS" \
        --worker-connections "$WORKER_CONNECTIONS" \
        --preload \
        --access-logfile - \
        --error-logfile - \
        --log-level info \
        --capture-output \
        --enable-stdio-inheritance \
        --pid /tmp/gunicorn.pid
fi
