#!/usr/bin/env python3
"""
Complete System Test for Claude Optimizer Platform
Tests all major components to verify 100% functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_database():
    """Test database connectivity and table creation"""
    print("🗄️  Testing Database...")
    try:
        from src.core.database import database
        await database.connect()
        await database.create_tables()
        print("✅ Database: Connected and tables created")
        await database.disconnect()
        return True
    except Exception as e:
        print(f"❌ Database: Failed - {e}")
        return False

async def test_configuration():
    """Test configuration loading"""
    print("⚙️  Testing Configuration...")
    try:
        from src.core.config import get_settings
        settings = get_settings()
        print(f"✅ Configuration: Loaded {settings.environment} environment")
        print(f"   Database: {settings.database_url}")
        print(f"   API Key: {'Set' if settings.openrouter_api_key != 'sk-or-dev-key-for-testing-purposes-only' else 'Using dev key'}")
        return True
    except Exception as e:
        print(f"❌ Configuration: Failed - {e}")
        return False

async def test_compression_engine():
    """Test compression functionality"""
    print("🗜️  Testing Compression Engine...")
    try:
        from src.services.compression_engine import CompressionEngine
        
        engine = CompressionEngine()
        test_text = "This is a very long text that should be compressed significantly to demonstrate the compression capabilities of our advanced system."
        
        # Test basic compression functionality
        compressed, ratio = await engine.compress(test_text, target_ratio=0.7)
        actual_ratio = len(compressed) / len(test_text)
        
        print(f"✅ Compression: {len(test_text)} → {len(compressed)} chars (ratio: {ratio:.2f})")
        return True
    except Exception as e:
        print(f"❌ Compression: Failed - {e}")
        return False

async def test_quality_assessor():
    """Test quality assessment"""
    print("🎯 Testing Quality Assessor...")
    try:
        from src.services.quality_assessor import QualityAssessor
        
        assessor = QualityAssessor()
        await assessor.initialize()
        
        original = "Explain quantum computing"
        response = "Quantum computing uses quantum mechanics principles"
        
        quality = await assessor.assess_quality(original, response, 0.8)
        print(f"✅ Quality Assessment: Score {quality.overall_score:.2f}")
        return True
    except Exception as e:
        print(f"❌ Quality Assessment: Failed - {e}")
        return False

async def test_cache_manager():
    """Test cache functionality"""
    print("💾 Testing Cache Manager...")
    try:
        from src.services.cache_manager import CacheManager

        cache = CacheManager()
        
        # Test memory cache
        test_key = "test_optimization"
        test_value = {"result": "test response", "cost": 0.05}
        
        await cache.cache_optimization_result(test_key, test_value)
        cached = await cache.get_cached_optimization(test_key)
        
        if cached:
            print("✅ Cache: Memory cache working")
            return True
        else:
            print("⚠️  Cache: Memory cache not returning results")
            return False
    except Exception as e:
        print(f"❌ Cache: Failed - {e}")
        return False

async def test_api_endpoints():
    """Test API endpoint structure"""
    print("🌐 Testing API Endpoints...")
    try:
        from src.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            print("✅ API: Health endpoint working")
        else:
            print(f"⚠️  API: Health endpoint returned {response.status_code}")
        
        # Test dashboard endpoint
        response = client.get("/")
        if response.status_code == 200:
            print("✅ API: Dashboard endpoint working")
        else:
            print(f"⚠️  API: Dashboard returned {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ API: Failed - {e}")
        return False

def test_static_files():
    """Test static file availability"""
    print("📁 Testing Static Files...")
    try:
        static_dir = Path("src/static")
        required_files = ["index.html", "css/dashboard.css", "js/dashboard.js"]
        
        missing_files = []
        for file_path in required_files:
            if not (static_dir / file_path).exists():
                missing_files.append(file_path)
        
        if not missing_files:
            print("✅ Static Files: All required files present")
            return True
        else:
            print(f"❌ Static Files: Missing {missing_files}")
            return False
    except Exception as e:
        print(f"❌ Static Files: Failed - {e}")
        return False

def test_requirements():
    """Test that all required packages are installed"""
    print("📦 Testing Requirements...")
    try:
        required_packages = [
            "fastapi", "uvicorn", "sqlalchemy", "aiosqlite", 
            "pydantic", "prometheus_client", "sentence_transformers",
            "opentelemetry-api", "redis", "httpx"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if not missing_packages:
            print("✅ Requirements: All packages installed")
            return True
        else:
            print(f"❌ Requirements: Missing {missing_packages}")
            return False
    except Exception as e:
        print(f"❌ Requirements: Failed - {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Claude Optimizer Platform - System Test")
    print("=" * 50)
    
    tests = [
        ("Requirements", test_requirements),
        ("Configuration", test_configuration),
        ("Static Files", test_static_files),
        ("Database", test_database),
        ("Compression", test_compression_engine),
        ("Quality Assessment", test_quality_assessor),
        ("Cache Manager", test_cache_manager),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}: Exception - {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your Claude Optimizer Platform is ready to use!")
        print("\nNext steps:")
        print("1. Set your OpenRouter API key: export OPENROUTER_API_KEY='your_key'")
        print("2. Start the server: python -m uvicorn src.main:app --port 8000")
        print("3. Open dashboard: http://localhost:8000")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please fix the issues above.")
        print("Check the SETUP_GUIDE.md for troubleshooting steps.")

if __name__ == "__main__":
    asyncio.run(main())
