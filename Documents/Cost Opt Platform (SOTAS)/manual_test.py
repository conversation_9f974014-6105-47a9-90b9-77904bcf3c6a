#!/usr/bin/env python3
"""
Manual Testing Script for Cost Optimization Platform
Interactive testing with different scenarios
"""

import requests
import json

def test_optimization(prompt, prefer_cost=True, quality_threshold=0.7, max_tokens=200):
    """Test optimization with custom parameters"""
    print(f"\n🧪 Testing: {prompt[:50]}...")
    print(f"   Prefer Cost: {prefer_cost}")
    print(f"   Quality Threshold: {quality_threshold}")
    
    payload = {
        "prompt": prompt,
        "max_tokens": max_tokens,
        "temperature": 0.7,
        "prefer_cost": prefer_cost,
        "quality_threshold": quality_threshold
    }
    
    try:
        response = requests.post("http://localhost:8001/api/v1/optimize", json=payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS:")
            print(f"   Model Used: {data.get('model_used')}")
            print(f"   Cost: ${data.get('cost_usd', 0):.6f}")
            print(f"   Quality Score: {data.get('quality_score', 0):.2f}")
            print(f"   Tokens Used: {data.get('tokens_used', 0)}")
            print(f"   Savings: ${data.get('optimization_savings', 0):.6f}")
            print(f"   Response: {data.get('content', '')[:100]}...")
            return data
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None

def main():
    """Run manual tests"""
    print("🚅 MANUAL TESTING - COST OPTIMIZATION PLATFORM")
    print("=" * 60)
    
    # Test 1: Simple task with cost preference
    print("\n" + "="*60)
    print("TEST 1: Simple Task - Cost Optimized")
    test_optimization(
        prompt="What is 2+2?",
        prefer_cost=True,
        quality_threshold=0.6,
        max_tokens=50
    )
    
    # Test 2: Simple task with quality preference
    print("\n" + "="*60)
    print("TEST 2: Simple Task - Quality Optimized")
    test_optimization(
        prompt="What is the capital of Japan?",
        prefer_cost=False,
        quality_threshold=0.9,
        max_tokens=100
    )
    
    # Test 3: Complex task with cost preference
    print("\n" + "="*60)
    print("TEST 3: Complex Task - Cost Optimized")
    test_optimization(
        prompt="Explain the basics of machine learning",
        prefer_cost=True,
        quality_threshold=0.7,
        max_tokens=200
    )
    
    # Test 4: Complex task with quality preference
    print("\n" + "="*60)
    print("TEST 4: Complex Task - Quality Optimized")
    test_optimization(
        prompt="Explain quantum computing and its applications",
        prefer_cost=False,
        quality_threshold=0.9,
        max_tokens=300
    )
    
    # Test 5: Expert task (should always use premium)
    print("\n" + "="*60)
    print("TEST 5: Expert Task - Premium Required")
    test_optimization(
        prompt="Design a distributed microservices architecture for handling 1 million users with fault tolerance, load balancing, and auto-scaling",
        prefer_cost=True,  # Even with cost preference, should use premium
        quality_threshold=0.95,
        max_tokens=500
    )
    
    # Show current stats
    print("\n" + "="*60)
    print("CURRENT PLATFORM STATISTICS")
    try:
        response = requests.get("http://localhost:8001/api/v1/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"Total Requests: {stats.get('total_requests', 0)}")
            print(f"Total Cost: ${stats.get('total_cost_usd', 0):.4f}")
            print(f"Total Savings: ${stats.get('total_savings_usd', 0):.4f}")
            print(f"Average Quality: {stats.get('avg_quality_score', 0):.2f}")
            print(f"Average Latency: {stats.get('avg_latency_ms', 0):.1f}ms")
            
            print("\nModel Breakdown:")
            for model_stat in stats.get('model_breakdown', []):
                print(f"  {model_stat['model']}: {model_stat['requests']} requests, ${model_stat['cost_usd']:.4f}")
        else:
            print("❌ Could not fetch stats")
    except Exception as e:
        print(f"❌ Stats error: {e}")
    
    print("\n" + "="*60)
    print("🎯 TESTING COMPLETE!")
    print("You can also test via:")
    print("1. Web Dashboard: http://localhost:8001")
    print("2. Direct API calls using curl or Postman")
    print("3. The interactive web interface")

if __name__ == "__main__":
    main()
