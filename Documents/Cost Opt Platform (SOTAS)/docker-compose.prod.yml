# Cost Optimization Platform - Production Deployment
# Simplified for current implementation with SQLite + Redis

version: '3.8'

services:
  # Redis Cache Service - INTERNAL ONLY
  redis:
    image: redis:8.0-alpine
    container_name: cost-opt-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --bind 0.0.0.0
    volumes:
      - redis_data:/data
    networks:
      - cost-opt-internal
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Main Cost Optimization API - ONLY EXTERNAL SERVICE
  cost-optimizer:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cost-opt-app
    restart: unless-stopped
    ports:
      - "8001:8001"  # ONLY external port exposed
    environment:
      # Application Configuration
      - HOST=0.0.0.0
      - PORT=8001
      - DEBUG=false
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
      
      # Database Configuration (SQLite for simplicity)
      - DATABASE_URL=sqlite:///./data/cost_optimizer.db
      
      # Redis Configuration (internal networking)
      - REDIS_URL=redis://redis:6379/0
      - CACHE_ENABLED=true
      - CACHE_TTL=3600
      
      # API Keys (from environment)
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      
      # Security Configuration
      - CORS_ORIGINS=*
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_REQUESTS=1000
      - RATE_LIMIT_WINDOW=3600
      
      # Performance Configuration
      - MAX_CONCURRENT_REQUESTS=100
      - REQUEST_TIMEOUT=60
      
      # Cost Optimization Settings
      - DEFAULT_QUALITY_THRESHOLD=0.8
      - DEFAULT_PREFER_COST=true
      - COST_TRACKING_ENABLED=true
      
      # Monitoring Configuration
      - METRICS_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30
      
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    networks:
      - cost-opt-internal
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Prometheus Monitoring (Optional - Internal Only)
  prometheus:
    image: prom/prometheus:latest
    container_name: cost-opt-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.listen-address=0.0.0.0:9090'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - cost-opt-internal
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

# Internal network for service discovery
networks:
  cost-opt-internal:
    name: cost-opt-internal
    driver: bridge
    internal: false  # Allow external access for main API
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********

# Persistent volumes
volumes:
  redis_data:
    driver: local
  app_data:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
    driver: local
