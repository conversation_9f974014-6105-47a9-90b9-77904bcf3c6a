"""
Enhanced CORS Security Middleware
Production-grade CORS configuration with security hardening
"""

import logging
import re
from typing import List, Set, Optional
from urllib.parse import urlparse

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.cors import CORSMiddleware
from prometheus_client import Counter

from src.core.config import get_settings

logger = logging.getLogger(__name__)

# CORS security metrics
CORS_EVENTS = Counter(
    'cors_events_total',
    'Total CORS events',
    ['event_type', 'result']
)

ORIGIN_VALIDATION = Counter(
    'origin_validation_total',
    'Origin validation attempts',
    ['validation_result']
)


class SecureCORSMiddleware(BaseHTTPMiddleware):
    """
    Enhanced CORS middleware with security validation
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        
        # Parse allowed origins
        self.allowed_origins = self._parse_allowed_origins()
        
        # Compile regex patterns for origin validation
        self.origin_patterns = self._compile_origin_patterns()
        
        # Security headers for CORS responses
        self.cors_security_headers = {
            "Vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
            "Access-Control-Max-Age": "86400",  # 24 hours
        }
        
        # Dangerous origins to always block
        self.blocked_origins = {
            "null",
            "file://",
            "data:",
            "javascript:",
            "vbscript:",
        }
        
        # Methods allowed for CORS
        self.allowed_methods = {
            "GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"
        }
        
        # Headers allowed for CORS
        self.allowed_headers = {
            "accept",
            "accept-encoding",
            "authorization",
            "content-type",
            "dnt",
            "origin",
            "user-agent",
            "x-csrftoken",
            "x-requested-with",
            "x-api-key",
            "x-request-id",
            "cache-control",
            "pragma"
        }
        
        # Headers exposed to the client
        self.exposed_headers = {
            "x-request-id",
            "x-ratelimit-limit",
            "x-ratelimit-remaining",
            "x-ratelimit-reset"
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process CORS with enhanced security validation"""
        
        origin = request.headers.get("origin")
        method = request.method
        
        # Handle preflight requests
        if method == "OPTIONS":
            return await self._handle_preflight(request, origin)
        
        # Validate origin for actual requests
        if origin:
            if not self._is_origin_allowed(origin):
                ORIGIN_VALIDATION.labels(validation_result="blocked").inc()
                logger.warning(f"Blocked request from unauthorized origin: {origin}")
                
                # Return 403 for unauthorized origins
                return Response(
                    content="Origin not allowed",
                    status_code=403,
                    headers={"X-CORS-Error": "origin_not_allowed"}
                )
            
            ORIGIN_VALIDATION.labels(validation_result="allowed").inc()
        
        # Process the request
        response = await call_next(request)
        
        # Add CORS headers to response
        if origin and self._is_origin_allowed(origin):
            self._add_cors_headers(response, origin, method)
        
        return response
    
    async def _handle_preflight(self, request: Request, origin: Optional[str]) -> Response:
        """Handle CORS preflight requests"""
        
        if not origin:
            CORS_EVENTS.labels(event_type="preflight", result="no_origin").inc()
            return Response(
                content="Origin header required for preflight",
                status_code=400
            )
        
        if not self._is_origin_allowed(origin):
            CORS_EVENTS.labels(event_type="preflight", result="origin_blocked").inc()
            return Response(
                content="Origin not allowed",
                status_code=403,
                headers={"X-CORS-Error": "origin_not_allowed"}
            )
        
        # Validate requested method
        requested_method = request.headers.get("access-control-request-method")
        if requested_method and requested_method not in self.allowed_methods:
            CORS_EVENTS.labels(event_type="preflight", result="method_blocked").inc()
            return Response(
                content="Method not allowed",
                status_code=405,
                headers={"X-CORS-Error": "method_not_allowed"}
            )
        
        # Validate requested headers
        requested_headers = request.headers.get("access-control-request-headers")
        if requested_headers:
            headers_list = [h.strip().lower() for h in requested_headers.split(",")]
            for header in headers_list:
                if header not in self.allowed_headers:
                    CORS_EVENTS.labels(event_type="preflight", result="header_blocked").inc()
                    return Response(
                        content="Header not allowed",
                        status_code=400,
                        headers={"X-CORS-Error": "header_not_allowed"}
                    )
        
        # Create successful preflight response
        headers = {
            "Access-Control-Allow-Origin": origin,
            "Access-Control-Allow-Methods": ", ".join(self.allowed_methods),
            "Access-Control-Allow-Headers": ", ".join(self.allowed_headers),
            "Access-Control-Expose-Headers": ", ".join(self.exposed_headers),
            "Access-Control-Allow-Credentials": "true",
            **self.cors_security_headers
        }
        
        CORS_EVENTS.labels(event_type="preflight", result="success").inc()
        
        return Response(
            content="",
            status_code=200,
            headers=headers
        )
    
    def _add_cors_headers(self, response: Response, origin: str, method: str):
        """Add CORS headers to response"""
        
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Expose-Headers"] = ", ".join(self.exposed_headers)
        
        # Add security headers
        for header, value in self.cors_security_headers.items():
            response.headers[header] = value
        
        CORS_EVENTS.labels(event_type="response", result="headers_added").inc()
    
    def _is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed with enhanced security validation"""
        
        if not origin:
            return False
        
        # Block dangerous origins
        origin_lower = origin.lower()
        for blocked in self.blocked_origins:
            if origin_lower.startswith(blocked):
                return False
        
        # Validate origin format
        if not self._is_valid_origin_format(origin):
            return False
        
        # Check against allowed origins
        if "*" in self.allowed_origins:
            # Wildcard allowed (development only)
            if self.settings.environment == "production":
                logger.warning("Wildcard CORS origin in production - security risk!")
            return True
        
        # Exact match
        if origin in self.allowed_origins:
            return True
        
        # Pattern match
        for pattern in self.origin_patterns:
            if pattern.match(origin):
                return True
        
        return False
    
    def _is_valid_origin_format(self, origin: str) -> bool:
        """Validate origin format"""
        
        try:
            parsed = urlparse(origin)
            
            # Must have scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Scheme must be http or https
            if parsed.scheme not in ["http", "https"]:
                return False
            
            # No path, query, or fragment allowed in origin
            if parsed.path or parsed.query or parsed.fragment:
                return False
            
            # Validate hostname format
            hostname = parsed.hostname
            if not hostname:
                return False
            
            # Basic hostname validation
            if not re.match(r'^[a-zA-Z0-9.-]+$', hostname):
                return False
            
            # Check for suspicious patterns
            suspicious_patterns = [
                r'\.\.', r'--', r'__', r'\.{3,}',
                r'[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'  # IP addresses
            ]
            
            for pattern in suspicious_patterns:
                if re.search(pattern, hostname):
                    logger.warning(f"Suspicious hostname pattern in origin: {hostname}")
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to parse origin {origin}: {e}")
            return False
    
    def _parse_allowed_origins(self) -> Set[str]:
        """Parse and validate allowed origins from configuration"""
        
        origins = set()
        
        for origin in self.settings.allowed_origins:
            origin = origin.strip()
            
            if not origin:
                continue
            
            # Validate origin format (except for wildcard)
            if origin != "*" and not self._is_valid_origin_format(origin):
                logger.warning(f"Invalid origin format in configuration: {origin}")
                continue
            
            origins.add(origin)
        
        logger.info(f"Configured {len(origins)} allowed CORS origins")
        return origins
    
    def _compile_origin_patterns(self) -> List[re.Pattern]:
        """Compile regex patterns for origin matching"""
        
        patterns = []
        
        for origin in self.allowed_origins:
            if "*" in origin and origin != "*":
                # Convert wildcard pattern to regex
                # e.g., "https://*.example.com" -> "https://.*\.example\.com"
                pattern = origin.replace(".", r"\.")
                pattern = pattern.replace("*", ".*")
                pattern = f"^{pattern}$"
                
                try:
                    compiled_pattern = re.compile(pattern, re.IGNORECASE)
                    patterns.append(compiled_pattern)
                    logger.info(f"Compiled CORS origin pattern: {pattern}")
                except re.error as e:
                    logger.error(f"Failed to compile origin pattern {pattern}: {e}")
        
        return patterns


def create_cors_middleware(app):
    """Create and configure CORS middleware"""
    
    settings = get_settings()
    
    # Use secure CORS middleware for production
    if settings.environment == "production":
        return SecureCORSMiddleware(app)
    
    # Use standard CORS middleware for development
    return CORSMiddleware(
        app,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["x-request-id", "x-ratelimit-limit", "x-ratelimit-remaining"]
    )
