"""
Monitoring Middleware
Automatic instrumentation for HTTP requests with FAANG+ observability standards
"""

import time
import logging
from typing import Callable, Optional
from uuid import uuid4

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from opentelemetry import trace

logger = logging.getLogger(__name__)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Production-grade monitoring middleware for automatic instrumentation
    Captures metrics, traces, and logs for all HTTP requests
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/openapi.json"]
        self.tracer = trace.get_tracer(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process HTTP request with comprehensive monitoring
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response object with monitoring data
        """
        # Skip monitoring for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Generate request ID for tracing
        request_id = str(uuid4())
        
        # Start timing
        start_time = time.time()
        
        # Extract request information
        method = request.method
        path = request.url.path
        user_agent = request.headers.get("user-agent", "unknown")
        client_ip = self._get_client_ip(request)
        
        # Calculate request size
        request_size = self._get_request_size(request)
        
        # Start distributed trace
        with self.tracer.start_as_current_span(
            f"{method} {path}",
            attributes={
                "http.method": method,
                "http.url": str(request.url),
                "http.scheme": request.url.scheme,
                "http.host": request.url.hostname,
                "http.target": path,
                "http.user_agent": user_agent,
                "http.client_ip": client_ip,
                "http.request_id": request_id,
                "http.request_size": request_size
            }
        ) as span:
            
            # Add request ID to request state for downstream use
            request.state.request_id = request_id
            request.state.start_time = start_time
            
            # Log request start
            logger.info(
                f"Request started: {method} {path}",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "path": path,
                    "client_ip": client_ip,
                    "user_agent": user_agent
                }
            )
            
            try:
                # Process request
                response = await call_next(request)
                
                # Calculate processing time
                processing_time = time.time() - start_time
                
                # Get response size
                response_size = self._get_response_size(response)
                
                # Update span with response information
                span.set_attributes({
                    "http.status_code": response.status_code,
                    "http.response_size": response_size,
                    "http.duration_ms": processing_time * 1000
                })
                
                # Set span status based on HTTP status code
                if response.status_code >= 400:
                    span.set_status(
                        trace.Status(
                            trace.StatusCode.ERROR,
                            f"HTTP {response.status_code}"
                        )
                    )
                else:
                    span.set_status(trace.Status(trace.StatusCode.OK))
                
                # Record metrics
                self._record_metrics(
                    method=method,
                    endpoint=self._normalize_endpoint(path),
                    status_code=response.status_code,
                    duration_seconds=processing_time,
                    request_size_bytes=request_size,
                    response_size_bytes=response_size
                )
                
                # Log request completion
                logger.info(
                    f"Request completed: {method} {path} - {response.status_code} in {processing_time:.3f}s",
                    extra={
                        "request_id": request_id,
                        "method": method,
                        "path": path,
                        "status_code": response.status_code,
                        "duration_ms": processing_time * 1000,
                        "request_size": request_size,
                        "response_size": response_size
                    }
                )
                
                # Add monitoring headers to response
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Processing-Time"] = f"{processing_time:.3f}"
                
                return response
                
            except Exception as e:
                # Calculate processing time for failed requests
                processing_time = time.time() - start_time
                
                # Record exception in span
                span.record_exception(e)
                span.set_status(
                    trace.Status(
                        trace.StatusCode.ERROR,
                        f"Exception: {str(e)}"
                    )
                )
                
                # Record error metrics
                self._record_metrics(
                    method=method,
                    endpoint=self._normalize_endpoint(path),
                    status_code=500,
                    duration_seconds=processing_time,
                    request_size_bytes=request_size,
                    response_size_bytes=0
                )
                
                # Log error
                logger.error(
                    f"Request failed: {method} {path} - {str(e)}",
                    extra={
                        "request_id": request_id,
                        "method": method,
                        "path": path,
                        "error": str(e),
                        "duration_ms": processing_time * 1000
                    },
                    exc_info=True
                )
                
                # Re-raise exception
                raise
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request"""
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    def _get_request_size(self, request: Request) -> int:
        """Calculate request size in bytes"""
        try:
            content_length = request.headers.get("content-length")
            if content_length:
                return int(content_length)
            
            # Estimate size from headers and URL
            header_size = sum(len(k) + len(v) for k, v in request.headers.items())
            url_size = len(str(request.url))
            return header_size + url_size
            
        except Exception:
            return 0
    
    def _get_response_size(self, response: Response) -> int:
        """Calculate response size in bytes"""
        try:
            # Check if response has content-length header
            content_length = response.headers.get("content-length")
            if content_length:
                return int(content_length)
            
            # Estimate from headers
            header_size = sum(len(k) + len(v) for k, v in response.headers.items())
            
            # If response has body, try to estimate
            if hasattr(response, 'body') and response.body:
                return len(response.body) + header_size
            
            return header_size
            
        except Exception:
            return 0
    
    def _normalize_endpoint(self, path: str) -> str:
        """
        Normalize endpoint path for metrics
        Replaces dynamic segments with placeholders to reduce cardinality
        """
        # Common patterns to normalize
        import re
        
        # Replace UUIDs
        path = re.sub(
            r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
            '/{uuid}',
            path,
            flags=re.IGNORECASE
        )
        
        # Replace numeric IDs
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Replace API keys (if they appear in path)
        path = re.sub(r'/[a-zA-Z0-9]{32,}', '/{api_key}', path)
        
        return path
    
    def _record_metrics(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration_seconds: float,
        request_size_bytes: int,
        response_size_bytes: int
    ):
        """Record HTTP metrics"""
        try:
            # Import here to avoid circular imports
            from src.monitoring.setup import get_monitoring_manager
            
            manager = get_monitoring_manager()
            manager.record_http_metrics(
                method=method,
                endpoint=endpoint,
                status_code=status_code,
                duration_seconds=duration_seconds,
                request_size_bytes=request_size_bytes,
                response_size_bytes=response_size_bytes
            )
            
        except Exception as e:
            # Don't fail request if metrics recording fails
            logger.debug(f"Failed to record HTTP metrics: {e}")


class RequestContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add request context information
    Useful for correlation across services and debugging
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add request context to request state"""
        # Generate or extract correlation ID
        correlation_id = request.headers.get("x-correlation-id", str(uuid4()))
        
        # Add context to request state
        request.state.correlation_id = correlation_id
        request.state.request_start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Add correlation ID to response
        response.headers["X-Correlation-ID"] = correlation_id
        
        return response
