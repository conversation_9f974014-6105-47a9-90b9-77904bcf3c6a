"""
Logging Middleware
Structured logging with correlation IDs and performance metrics
"""

import json
import logging
import time
from typing import Callable
from uuid import uuid4

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Production-grade logging middleware with structured logging
    """
    
    def __init__(self, app):
        super().__init__(app)
        
        # Paths to exclude from detailed logging (to reduce noise)
        self.excluded_paths = {
            "/health",
            "/health/live", 
            "/health/ready",
            "/metrics"
        }
        
        # Sensitive headers to redact
        self.sensitive_headers = {
            "authorization",
            "x-api-key",
            "cookie",
            "x-auth-token"
        }
        
        # Request size limit for logging body (1MB)
        self.max_body_size = 1024 * 1024
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with comprehensive logging"""
        
        # Generate correlation ID if not present
        correlation_id = request.headers.get("X-Correlation-ID") or str(uuid4())
        request.state.correlation_id = correlation_id
        
        # Skip detailed logging for excluded paths
        if request.url.path in self.excluded_paths:
            return await self._simple_logging(request, call_next, correlation_id)
        
        # Detailed logging for API endpoints
        return await self._detailed_logging(request, call_next, correlation_id)
    
    async def _simple_logging(self, request: Request, call_next: Callable, correlation_id: str) -> Response:
        """Simple logging for health checks and metrics"""
        
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Log only if there's an error
            if response.status_code >= 400:
                processing_time = time.time() - start_time
                logger.warning(
                    "Request completed with error",
                    correlation_id=correlation_id,
                    method=request.method,
                    path=request.url.path,
                    status_code=response.status_code,
                    processing_time_ms=round(processing_time * 1000, 2)
                )
            
            # Add correlation ID to response
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                "Request failed",
                correlation_id=correlation_id,
                method=request.method,
                path=request.url.path,
                error=str(e),
                processing_time_ms=round(processing_time * 1000, 2),
                exc_info=True
            )
            raise
    
    async def _detailed_logging(self, request: Request, call_next: Callable, correlation_id: str) -> Response:
        """Detailed logging for API endpoints"""
        
        start_time = time.time()
        
        # Extract request information
        request_info = await self._extract_request_info(request)
        
        # Log request start
        logger.info(
            "Request started",
            correlation_id=correlation_id,
            **request_info
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            processing_time = time.time() - start_time
            
            # Extract response information
            response_info = self._extract_response_info(response, processing_time)
            
            # Log request completion
            log_level = "info"
            if response.status_code >= 500:
                log_level = "error"
            elif response.status_code >= 400:
                log_level = "warning"
            
            getattr(logger, log_level)(
                "Request completed",
                correlation_id=correlation_id,
                **request_info,
                **response_info
            )
            
            # Add correlation ID to response
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            logger.error(
                "Request failed with exception",
                correlation_id=correlation_id,
                **request_info,
                error=str(e),
                error_type=type(e).__name__,
                processing_time_ms=round(processing_time * 1000, 2),
                exc_info=True
            )
            raise
    
    async def _extract_request_info(self, request: Request) -> dict:
        """Extract request information for logging"""
        
        # Basic request info
        info = {
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params) if request.query_params else None,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "content_type": request.headers.get("content-type"),
            "content_length": request.headers.get("content-length")
        }
        
        # Add sanitized headers
        info["headers"] = self._sanitize_headers(dict(request.headers))
        
        # Add request body for POST/PUT requests (if not too large)
        if request.method in ["POST", "PUT", "PATCH"]:
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) <= self.max_body_size:
                try:
                    # Note: This is a simplified approach
                    # In production, you'd need to handle body reading more carefully
                    # to avoid consuming the request body
                    pass
                except Exception as e:
                    info["body_read_error"] = str(e)
        
        # Add API key info if available
        if hasattr(request.state, "api_key_info"):
            api_key_info = request.state.api_key_info
            info["api_key_name"] = api_key_info.get("name")
            info["api_key_permissions"] = api_key_info.get("permissions")
        
        return {k: v for k, v in info.items() if v is not None}
    
    def _extract_response_info(self, response: Response, processing_time: float) -> dict:
        """Extract response information for logging"""
        
        return {
            "status_code": response.status_code,
            "response_headers": self._sanitize_headers(dict(response.headers)),
            "processing_time_ms": round(processing_time * 1000, 2),
            "response_size": response.headers.get("content-length")
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _sanitize_headers(self, headers: dict) -> dict:
        """Sanitize headers by redacting sensitive information"""
        
        sanitized = {}
        
        for key, value in headers.items():
            key_lower = key.lower()
            
            if key_lower in self.sensitive_headers:
                # Redact sensitive headers
                if value:
                    sanitized[key] = f"{value[:4]}***" if len(value) > 4 else "***"
                else:
                    sanitized[key] = "***"
            else:
                sanitized[key] = value
        
        return sanitized


class PerformanceLoggingMiddleware(BaseHTTPMiddleware):
    """
    Performance-focused logging middleware for metrics collection
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.performance_logger = structlog.get_logger("performance")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Log performance metrics"""
        
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            processing_time = time.time() - start_time
            
            # Log performance metrics
            self.performance_logger.info(
                "request_performance",
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                processing_time_ms=round(processing_time * 1000, 2),
                timestamp=start_time
            )
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            self.performance_logger.error(
                "request_error",
                method=request.method,
                path=request.url.path,
                error_type=type(e).__name__,
                processing_time_ms=round(processing_time * 1000, 2),
                timestamp=start_time
            )
            raise


# Utility functions for application logging
def get_correlation_id(request: Request) -> str:
    """Get correlation ID from request"""
    return getattr(request.state, "correlation_id", "unknown")


def log_optimization_event(
    correlation_id: str,
    event_type: str,
    **kwargs
):
    """Log optimization-specific events"""
    
    optimization_logger = structlog.get_logger("optimization")
    
    optimization_logger.info(
        event_type,
        correlation_id=correlation_id,
        **kwargs
    )


def log_cache_event(
    correlation_id: str,
    cache_type: str,
    hit: bool,
    **kwargs
):
    """Log cache-specific events"""
    
    cache_logger = structlog.get_logger("cache")
    
    cache_logger.info(
        "cache_access",
        correlation_id=correlation_id,
        cache_type=cache_type,
        hit=hit,
        **kwargs
    )


def log_model_event(
    correlation_id: str,
    model_name: str,
    event_type: str,
    **kwargs
):
    """Log model-specific events"""
    
    model_logger = structlog.get_logger("model")
    
    model_logger.info(
        event_type,
        correlation_id=correlation_id,
        model_name=model_name,
        **kwargs
    )
