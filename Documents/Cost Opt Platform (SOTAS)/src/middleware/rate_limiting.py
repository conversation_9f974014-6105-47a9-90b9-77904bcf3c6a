"""
Rate Limiting Middleware
Production-grade rate limiting with Redis backend and intelligent abuse detection
"""

import asyncio
import logging
import time
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, timedelta

import redis.asyncio as aioredis
from fastapi import HTTPException, Request
from starlette.middleware.base import BaseHTTPMiddleware

from src.core.config import get_settings

logger = logging.getLogger(__name__)


class RateLimiter:
    """
    Standalone rate limiter for use in services and tests
    """

    def __init__(self, redis_client: Optional[aioredis.Redis] = None):
        self.settings = get_settings()
        self.redis_client = redis_client

        # Rate limiting configuration
        self.rate_limits = {
            "requests_per_minute": self.settings.rate_limit_requests_per_minute,
            "burst_size": self.settings.rate_limit_burst_size,
            "requests_per_hour": self.settings.rate_limit_requests_per_minute * 60,
            "requests_per_day": self.settings.rate_limit_requests_per_minute * 60 * 24,
        }

    async def check_rate_limit(self, identifier: str, limit_type: str = "requests_per_minute") -> Tuple[bool, Dict]:
        """
        Check if request is within rate limits

        Returns:
            Tuple of (allowed: bool, info: dict)
        """
        if not self.redis_client:
            # If no Redis, allow all requests (fallback mode)
            return True, {"allowed": True, "remaining": 1000, "reset_time": time.time() + 60}

        try:
            limit = self.rate_limits.get(limit_type, 60)
            window = 60 if "minute" in limit_type else (3600 if "hour" in limit_type else 86400)

            key = f"rate_limit:{identifier}:{limit_type}"
            current_time = int(time.time())
            window_start = current_time - window

            # Use Redis sorted set for sliding window
            pipe = self.redis_client.pipeline()

            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)

            # Count current requests
            pipe.zcard(key)

            # Add current request
            pipe.zadd(key, {str(current_time): current_time})

            # Set expiration
            pipe.expire(key, window)

            results = await pipe.execute()
            current_count = results[1]

            allowed = current_count < limit
            remaining = max(0, limit - current_count - 1)
            reset_time = current_time + window

            return allowed, {
                "allowed": allowed,
                "remaining": remaining,
                "reset_time": reset_time,
                "limit": limit,
                "current": current_count
            }

        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # On error, allow request (fail open)
            return True, {"allowed": True, "remaining": 1000, "reset_time": time.time() + 60}

    async def is_rate_limited(self, identifier: str) -> bool:
        """Simple check if identifier is rate limited"""
        allowed, _ = await self.check_rate_limit(identifier)
        return not allowed


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """
    Advanced rate limiting middleware with multiple algorithms and abuse detection
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.redis_client: Optional[aioredis.Redis] = None
        
        # Rate limiting configuration
        self.rate_limits = {
            # Per-minute limits
            "requests_per_minute": self.settings.rate_limit_requests_per_minute,
            "burst_size": self.settings.rate_limit_burst_size,
            
            # Per-hour limits for additional protection
            "requests_per_hour": self.settings.rate_limit_requests_per_minute * 60,
            
            # Per-day limits
            "requests_per_day": self.settings.rate_limit_requests_per_minute * 60 * 24,
        }
        
        # Endpoints with different rate limits
        self.endpoint_limits = {
            "/api/v1/optimize": {
                "requests_per_minute": 30,
                "burst_size": 10
            },
            "/api/v1/optimize/batch": {
                "requests_per_minute": 5,
                "burst_size": 2
            },
            "/health": {
                "requests_per_minute": 300,  # Health checks can be frequent
                "burst_size": 50
            }
        }
        
        # Abuse detection thresholds
        self.abuse_thresholds = {
            "suspicious_requests_per_minute": 100,
            "error_rate_threshold": 0.5,  # 50% error rate
            "ban_duration_minutes": 60
        }
        
        # Whitelist for internal services
        self.whitelist_ips = {
            "127.0.0.1",
            "::1",
            "localhost"
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting"""
        
        # Initialize Redis connection if needed
        if not self.redis_client:
            await self._init_redis()
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Check if client is whitelisted
        if self._is_whitelisted(request, client_id):
            return await call_next(request)
        
        # Check rate limits
        try:
            await self._check_rate_limits(request, client_id)
        except HTTPException:
            # Rate limit exceeded
            await self._record_rate_limit_violation(client_id)
            raise
        
        # Process request
        start_time = time.time()
        try:
            response = await call_next(request)
            
            # Record successful request
            await self._record_request(client_id, success=True)
            
            return response
            
        except Exception as e:
            # Record failed request
            await self._record_request(client_id, success=False)
            
            # Check for abuse patterns
            await self._check_abuse_patterns(client_id)
            
            raise
    
    async def _init_redis(self):
        """Initialize Redis connection for rate limiting"""
        try:
            self.redis_client = aioredis.from_url(
                self.settings.redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            await self.redis_client.ping()
            logger.info("Rate limiting Redis connection established")
        except Exception as e:
            logger.error(f"Failed to connect to Redis for rate limiting: {e}")
            # Continue without Redis - use in-memory fallback
            self.redis_client = None
    
    def _get_client_id(self, request: Request) -> str:
        """Get unique client identifier"""
        # Try to get API key first
        api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization")
        if api_key:
            return f"api_key:{api_key[:10]}..."  # Use first 10 chars for privacy
        
        # Fallback to IP address
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _is_whitelisted(self, request: Request, client_id: str) -> bool:
        """Check if client is whitelisted"""
        client_ip = self._get_client_ip(request)
        return client_ip in self.whitelist_ips
    
    async def _check_rate_limits(self, request: Request, client_id: str):
        """Check all rate limits for the client"""
        
        # Get endpoint-specific limits
        endpoint = request.url.path
        limits = self.endpoint_limits.get(endpoint, self.rate_limits)
        
        # Check different time windows
        checks = [
            ("minute", 60, limits.get("requests_per_minute", self.rate_limits["requests_per_minute"])),
            ("hour", 3600, limits.get("requests_per_hour", self.rate_limits["requests_per_hour"])),
            ("day", 86400, limits.get("requests_per_day", self.rate_limits["requests_per_day"]))
        ]
        
        for window_name, window_seconds, limit in checks:
            current_count = await self._get_request_count(client_id, window_seconds)
            
            if current_count >= limit:
                logger.warning(
                    f"Rate limit exceeded for {client_id}: "
                    f"{current_count}/{limit} requests per {window_name}"
                )
                
                # Calculate retry after
                retry_after = await self._calculate_retry_after(client_id, window_seconds)
                
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "rate_limit_exceeded",
                        "message": f"Rate limit exceeded: {limit} requests per {window_name}",
                        "retry_after": retry_after,
                        "current_count": current_count,
                        "limit": limit,
                        "window": window_name
                    },
                    headers={"Retry-After": str(retry_after)}
                )
        
        # Check burst limits
        burst_limit = limits.get("burst_size", self.rate_limits["burst_size"])
        burst_count = await self._get_burst_count(client_id)
        
        if burst_count >= burst_limit:
            logger.warning(f"Burst limit exceeded for {client_id}: {burst_count}/{burst_limit}")
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "burst_limit_exceeded",
                    "message": f"Too many requests in short time: {burst_limit} max",
                    "retry_after": 60,
                    "current_count": burst_count,
                    "limit": burst_limit
                },
                headers={"Retry-After": "60"}
            )
    
    async def _get_request_count(self, client_id: str, window_seconds: int) -> int:
        """Get request count for time window"""
        if not self.redis_client:
            return 0  # Fallback when Redis unavailable
        
        try:
            key = f"rate_limit:{client_id}:{window_seconds}"
            count = await self.redis_client.get(key)
            return int(count) if count else 0
        except Exception as e:
            logger.error(f"Failed to get request count: {e}")
            return 0
    
    async def _get_burst_count(self, client_id: str) -> int:
        """Get burst request count (last 10 seconds)"""
        if not self.redis_client:
            return 0
        
        try:
            key = f"burst:{client_id}"
            count = await self.redis_client.get(key)
            return int(count) if count else 0
        except Exception as e:
            logger.error(f"Failed to get burst count: {e}")
            return 0
    
    async def _record_request(self, client_id: str, success: bool):
        """Record request for rate limiting and abuse detection"""
        if not self.redis_client:
            return
        
        try:
            current_time = int(time.time())
            
            # Record in different time windows
            windows = [60, 3600, 86400]  # 1 minute, 1 hour, 1 day
            
            pipe = self.redis_client.pipeline()
            
            for window in windows:
                key = f"rate_limit:{client_id}:{window}"
                pipe.incr(key)
                pipe.expire(key, window)
            
            # Record burst count (10 second window)
            burst_key = f"burst:{client_id}"
            pipe.incr(burst_key)
            pipe.expire(burst_key, 10)
            
            # Record for abuse detection
            abuse_key = f"abuse:{client_id}:{current_time // 60}"  # Per minute
            pipe.hincrby(abuse_key, "total", 1)
            if not success:
                pipe.hincrby(abuse_key, "errors", 1)
            pipe.expire(abuse_key, 300)  # Keep for 5 minutes
            
            await pipe.execute()
            
        except Exception as e:
            logger.error(f"Failed to record request: {e}")
    
    async def _record_rate_limit_violation(self, client_id: str):
        """Record rate limit violation for abuse tracking"""
        if not self.redis_client:
            return
        
        try:
            key = f"violations:{client_id}"
            await self.redis_client.incr(key)
            await self.redis_client.expire(key, 3600)  # Track for 1 hour
        except Exception as e:
            logger.error(f"Failed to record violation: {e}")
    
    async def _check_abuse_patterns(self, client_id: str):
        """Check for abuse patterns and ban if necessary"""
        if not self.redis_client:
            return
        
        try:
            current_minute = int(time.time()) // 60
            abuse_key = f"abuse:{client_id}:{current_minute}"
            
            abuse_data = await self.redis_client.hgetall(abuse_key)
            if not abuse_data:
                return
            
            total_requests = int(abuse_data.get("total", 0))
            error_requests = int(abuse_data.get("errors", 0))
            
            # Check for suspicious activity
            if total_requests > self.abuse_thresholds["suspicious_requests_per_minute"]:
                await self._ban_client(client_id, "excessive_requests")
                return
            
            # Check error rate
            if total_requests > 10:  # Only check if enough requests
                error_rate = error_requests / total_requests
                if error_rate > self.abuse_thresholds["error_rate_threshold"]:
                    await self._ban_client(client_id, "high_error_rate")
                    return
            
        except Exception as e:
            logger.error(f"Failed to check abuse patterns: {e}")
    
    async def _ban_client(self, client_id: str, reason: str):
        """Ban client for abuse"""
        if not self.redis_client:
            return
        
        try:
            ban_key = f"banned:{client_id}"
            ban_duration = self.abuse_thresholds["ban_duration_minutes"] * 60
            
            await self.redis_client.setex(
                ban_key, 
                ban_duration, 
                f"banned:{reason}:{int(time.time())}"
            )
            
            logger.warning(f"Banned client {client_id} for {reason}")
            
        except Exception as e:
            logger.error(f"Failed to ban client: {e}")
    
    async def _calculate_retry_after(self, client_id: str, window_seconds: int) -> int:
        """Calculate retry-after header value"""
        # Simple calculation - return the window size
        return min(window_seconds, 300)  # Max 5 minutes


async def check_rate_limit(request: Request) -> bool:
    """Dependency function for rate limit checking"""
    # This is called by the middleware, so just return True
    # The actual checking is done in the middleware
    return True
