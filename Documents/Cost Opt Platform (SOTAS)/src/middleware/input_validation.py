"""
Enhanced Input Validation Middleware
Production-grade input validation and sanitization for 100M+ users
"""

import logging
import re
import json
from typing import Dict, Any, List, Optional
from urllib.parse import unquote

from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter

logger = logging.getLogger(__name__)

# Validation metrics
VALIDATION_EVENTS = Counter(
    'input_validation_events_total',
    'Total input validation events',
    ['validation_type', 'result']
)

SANITIZATION_EVENTS = Counter(
    'input_sanitization_events_total',
    'Total input sanitization events',
    ['sanitization_type']
)


class InputValidator:
    """
    Standalone input validator for use in services and tests
    """

    def __init__(self):
        # Compile regex patterns for performance
        self.patterns = {
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'sql_injection': re.compile(
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|"
                r"(--|#|/\*|\*/|;|'|\"|`|xp_|sp_)",
                re.IGNORECASE
            ),
            'xss': re.compile(
                r"(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|"
                r"onmouseover=|data:text/html|<iframe|<object|<embed|<link|<meta|"
                r"<style|expression\(|@import|behavior:|binding:|mocha:|livescript:|"
                r"eval\(|setTimeout|setInterval)",
                re.IGNORECASE
            ),
            'path_traversal': re.compile(r'(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)', re.IGNORECASE),
            'command_injection': re.compile(
                r'(\||&|;|`|\$\(|\${|<|>|\n|\r)',
                re.IGNORECASE
            )
        }

        # Dangerous file extensions
        self.dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl'
        }

    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        if not email or len(email) > 254:
            return False
        return bool(self.patterns['email'].match(email))

    def detect_sql_injection(self, text: str) -> bool:
        """Detect potential SQL injection attempts"""
        if not text:
            return False
        return bool(self.patterns['sql_injection'].search(text))

    def detect_xss(self, text: str) -> bool:
        """Detect potential XSS attempts"""
        if not text:
            return False
        return bool(self.patterns['xss'].search(text))

    def detect_path_traversal(self, path: str) -> bool:
        """Detect path traversal attempts"""
        if not path:
            return False
        return bool(self.patterns['path_traversal'].search(path))

    def validate_input(self, data: Any, max_length: int = 10000) -> Dict[str, Any]:
        """
        Comprehensive input validation

        Returns:
            Dict with 'valid' boolean and 'errors' list
        """
        errors = []

        if isinstance(data, str):
            if len(data) > max_length:
                errors.append(f"Input too long: {len(data)} > {max_length}")

            if self.detect_sql_injection(data):
                errors.append("Potential SQL injection detected")

            if self.detect_xss(data):
                errors.append("Potential XSS attack detected")

            if self.detect_path_traversal(data):
                errors.append("Path traversal attempt detected")

        elif isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (str, dict, list)):
                    result = self.validate_input(value, max_length)
                    if not result['valid']:
                        errors.extend([f"{key}: {error}" for error in result['errors']])

        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, (str, dict, list)):
                    result = self.validate_input(item, max_length)
                    if not result['valid']:
                        errors.extend([f"[{i}]: {error}" for error in result['errors']])

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }


class InputValidationMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive input validation and sanitization middleware
    """
    
    def __init__(self, app):
        super().__init__(app)
        
        # Compile regex patterns for performance
        self.patterns = {
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'sql_injection': re.compile(
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|"
                r"(--|#|/\*|\*/|;|'|\"|`|xp_|sp_)",
                re.IGNORECASE
            ),
            'xss': re.compile(
                r"(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|"
                r"onmouseover=|data:text/html|<iframe|<object|<embed|<link|<meta|"
                r"<form|<input|<textarea|<select|<option)",
                re.IGNORECASE
            ),
            'path_traversal': re.compile(
                r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c|%252e%252e%252f|"
                r"\.\.%2f|\.\.%5c|%2e%2e/|%2e%2e\\)",
                re.IGNORECASE
            ),
            'command_injection': re.compile(
                r"(\||&|;|`|\$\(|\${|<|>|\n|\r)",
                re.IGNORECASE
            ),
            'ldap_injection': re.compile(
                r"(\*|\(|\)|\\|/|NUL|null)",
                re.IGNORECASE
            ),
            'nosql_injection': re.compile(
                r"(\$where|\$ne|\$in|\$nin|\$gt|\$lt|\$gte|\$lte|\$exists|\$regex)",
                re.IGNORECASE
            )
        }
        
        # HTML entities for sanitization
        self.html_entities = {
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '&': '&amp;',
            '/': '&#x2F;',
            '`': '&#x60;',
            '=': '&#x3D;'
        }
        
        # Maximum lengths for different input types
        self.max_lengths = {
            'prompt': 100000,  # 100KB for prompts
            'model_name': 100,
            'api_key': 200,
            'user_id': 50,
            'session_id': 100,
            'general_string': 1000,
            'url': 2048,
            'email': 254,
            'username': 50
        }
        
        # Paths that require strict validation
        self.strict_validation_paths = {
            '/api/v1/optimize',
            '/api/v1/optimize/batch',
            '/api/v1/proxy'
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with input validation"""
        
        # Skip validation for health checks and static files
        if self._should_skip_validation(request):
            return await call_next(request)
        
        try:
            # Validate request body if present
            if request.method in ['POST', 'PUT', 'PATCH']:
                await self._validate_request_body(request)
            
            # Validate query parameters
            await self._validate_query_parameters(request)
            
            # Validate headers
            await self._validate_headers(request)
            
            # Process request
            response = await call_next(request)
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Input validation error: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="Input validation failed"
            )
    
    def _should_skip_validation(self, request: Request) -> bool:
        """Check if validation should be skipped for this request"""
        skip_paths = {
            '/health', '/health/', '/health/live', '/health/ready',
            '/metrics', '/static', '/docs', '/redoc', '/openapi.json'
        }
        
        path = request.url.path
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    async def _validate_request_body(self, request: Request):
        """Validate request body content"""
        
        try:
            # Get request body
            body = await request.body()
            if not body:
                return
            
            # Parse JSON body
            try:
                json_data = json.loads(body)
            except json.JSONDecodeError:
                VALIDATION_EVENTS.labels(
                    validation_type='json_parse',
                    result='failed'
                ).inc()
                raise HTTPException(
                    status_code=400,
                    detail="Invalid JSON in request body"
                )
            
            # Validate based on endpoint
            if request.url.path in self.strict_validation_paths:
                await self._validate_optimization_request(json_data)
            else:
                await self._validate_general_json(json_data)
            
            VALIDATION_EVENTS.labels(
                validation_type='request_body',
                result='success'
            ).inc()
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Request body validation error: {e}")
            raise HTTPException(
                status_code=400,
                detail="Request body validation failed"
            )
    
    async def _validate_optimization_request(self, data: Dict[str, Any]):
        """Validate optimization request data"""
        
        # Validate prompt
        if 'prompt' in data:
            prompt = data['prompt']
            if not isinstance(prompt, str):
                raise HTTPException(
                    status_code=400,
                    detail="Prompt must be a string"
                )
            
            if len(prompt) > self.max_lengths['prompt']:
                raise HTTPException(
                    status_code=413,
                    detail=f"Prompt too long (max {self.max_lengths['prompt']} characters)"
                )
            
            # Check for injection attacks
            if self._contains_malicious_content(prompt):
                VALIDATION_EVENTS.labels(
                    validation_type='malicious_content',
                    result='blocked'
                ).inc()
                raise HTTPException(
                    status_code=400,
                    detail="Malicious content detected in prompt"
                )
        
        # Validate model name
        if 'model' in data:
            model = data['model']
            if not isinstance(model, str):
                raise HTTPException(
                    status_code=400,
                    detail="Model must be a string"
                )
            
            if len(model) > self.max_lengths['model_name']:
                raise HTTPException(
                    status_code=400,
                    detail="Model name too long"
                )
            
            # Validate model name format
            if not re.match(r'^[a-zA-Z0-9\-_.]+$', model):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid model name format"
                )
        
        # Validate quality threshold
        if 'quality_threshold' in data:
            threshold = data['quality_threshold']
            if not isinstance(threshold, (int, float)):
                raise HTTPException(
                    status_code=400,
                    detail="Quality threshold must be a number"
                )
            
            if not 0.0 <= threshold <= 1.0:
                raise HTTPException(
                    status_code=400,
                    detail="Quality threshold must be between 0.0 and 1.0"
                )
    
    async def _validate_general_json(self, data: Any):
        """Validate general JSON data"""
        
        if isinstance(data, dict):
            for key, value in data.items():
                # Validate key
                if not isinstance(key, str):
                    raise HTTPException(
                        status_code=400,
                        detail="JSON keys must be strings"
                    )
                
                if len(key) > 100:
                    raise HTTPException(
                        status_code=400,
                        detail="JSON key too long"
                    )
                
                # Recursively validate value
                await self._validate_general_json(value)
        
        elif isinstance(data, list):
            if len(data) > 1000:  # Limit array size
                raise HTTPException(
                    status_code=400,
                    detail="Array too large"
                )
            
            for item in data:
                await self._validate_general_json(item)
        
        elif isinstance(data, str):
            if len(data) > self.max_lengths['general_string']:
                raise HTTPException(
                    status_code=400,
                    detail="String value too long"
                )
            
            # Check for malicious content in strings
            if self._contains_malicious_content(data):
                VALIDATION_EVENTS.labels(
                    validation_type='malicious_content',
                    result='blocked'
                ).inc()
                raise HTTPException(
                    status_code=400,
                    detail="Malicious content detected"
                )
    
    async def _validate_query_parameters(self, request: Request):
        """Validate query parameters"""
        
        for key, value in request.query_params.items():
            # Validate parameter name
            if len(key) > 100:
                raise HTTPException(
                    status_code=400,
                    detail="Query parameter name too long"
                )
            
            # Validate parameter value
            if len(value) > self.max_lengths['general_string']:
                raise HTTPException(
                    status_code=400,
                    detail="Query parameter value too long"
                )
            
            # Check for malicious content
            if self._contains_malicious_content(value):
                VALIDATION_EVENTS.labels(
                    validation_type='malicious_query_param',
                    result='blocked'
                ).inc()
                raise HTTPException(
                    status_code=400,
                    detail="Malicious content in query parameters"
                )
    
    async def _validate_headers(self, request: Request):
        """Validate request headers"""
        
        # Check for oversized headers
        total_size = sum(len(name) + len(value) for name, value in request.headers.items())
        if total_size > 8192:  # 8KB limit
            raise HTTPException(
                status_code=431,
                detail="Request headers too large"
            )
        
        # Validate specific headers
        for name, value in request.headers.items():
            if len(name) > 100 or len(value) > 1000:
                raise HTTPException(
                    status_code=400,
                    detail="Header name or value too long"
                )
    
    def _contains_malicious_content(self, content: str) -> bool:
        """Check if content contains malicious patterns"""
        
        # Decode URL encoding
        try:
            decoded_content = unquote(content)
        except Exception:
            decoded_content = content
        
        # Check against all patterns
        for pattern_name, pattern in self.patterns.items():
            if pattern.search(decoded_content):
                logger.warning(f"Malicious pattern detected: {pattern_name} in content")
                return True
        
        return False
    
    def _sanitize_string(self, value: str) -> str:
        """Sanitize string by escaping HTML entities"""
        
        if not isinstance(value, str):
            return value
        
        # Escape HTML entities
        for char, entity in self.html_entities.items():
            value = value.replace(char, entity)
        
        SANITIZATION_EVENTS.labels(
            sanitization_type='html_escape'
        ).inc()
        
        return value
