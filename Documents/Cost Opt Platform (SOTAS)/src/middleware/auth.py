"""
Authentication Middleware
Production-grade JWT and API key authentication with OWASP compliance
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from uuid import uuid4

import jwt
from fastapi import HTTPException, Request, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from prometheus_client import Counter, Histogram
from passlib.context import CryptContext

from src.core.config import get_settings

logger = logging.getLogger(__name__)

# Prometheus metrics for authentication monitoring
AUTH_ATTEMPTS = Counter(
    'auth_attempts_total',
    'Total authentication attempts',
    ['method', 'status']
)

AUTH_LATENCY = Histogram(
    'auth_latency_seconds',
    'Authentication processing latency',
    ['method']
)

FAILED_LOGINS = Counter(
    'failed_logins_total',
    'Failed login attempts by IP',
    ['ip_address', 'reason']
)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security scheme for authentication
security = HTTPBearer(auto_error=False)

# Rate limiting for authentication attempts
auth_rate_limits = {}
MAX_AUTH_ATTEMPTS = 5
AUTH_LOCKOUT_DURATION = 300  # 5 minutes


class AuthenticationError(Exception):
    """Custom authentication error"""
    def __init__(self, message: str, error_code: str = "auth_failed"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class JWTManager:
    """
    Production-grade JWT token management with security best practices
    """

    def __init__(self):
        self.settings = get_settings()
        self.secret_key = self.settings.secret_key
        self.algorithm = self.settings.jwt_algorithm
        self.expiration_hours = self.settings.jwt_expiration_hours

        # Token blacklist for logout/revocation
        self.blacklisted_tokens = set()

        # Refresh token storage (in production, use Redis)
        self.refresh_tokens = {}

    def create_access_token(
        self,
        subject: str,
        user_id: str,
        permissions: List[str] = None,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token with comprehensive claims"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(hours=self.expiration_hours)

        payload = {
            "sub": subject,
            "user_id": user_id,
            "iat": datetime.utcnow(),
            "exp": expire,
            "nbf": datetime.utcnow(),
            "jti": str(uuid4()),
            "iss": "cost-optimizer",
            "aud": "cost-optimizer-api",
            "permissions": permissions or [],
            "token_type": "access"
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token with comprehensive validation"""
        try:
            if token in self.blacklisted_tokens:
                raise AuthenticationError("Token has been revoked", "token_revoked")

            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_nbf": True,
                    "verify_iat": True,
                    "verify_aud": True,
                    "verify_iss": True
                },
                audience="cost-optimizer-api",
                issuer="cost-optimizer"
            )

            if payload.get("token_type") != "access":
                raise AuthenticationError("Invalid token type", "invalid_token_type")

            return payload

        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired", "token_expired")
        except jwt.InvalidTokenError as e:
            raise AuthenticationError(f"Invalid token: {str(e)}", "invalid_token")

    def revoke_token(self, token: str):
        """Revoke token by adding to blacklist"""
        self.blacklisted_tokens.add(token)


# Global JWT manager instance
jwt_manager = JWTManager()


class APIKeyAuth:
    """API Key authentication handler"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # In production, these would be stored in a database
        # For now, we'll use environment variables or config
        self.valid_api_keys = {
            "demo-key-12345": {
                "name": "Demo API Key",
                "rate_limit_multiplier": 1.0,
                "permissions": ["read", "write"],
                "active": True
            },
            "admin-key-67890": {
                "name": "Admin API Key", 
                "rate_limit_multiplier": 10.0,
                "permissions": ["read", "write", "admin"],
                "active": True
            }
        }
    
    async def verify_api_key(self, api_key: str) -> dict:
        """Verify API key and return key info"""
        
        if not api_key:
            raise HTTPException(
                status_code=401,
                detail={
                    "error": "missing_api_key",
                    "message": "API key is required"
                }
            )
        
        # Remove 'Bearer ' prefix if present
        if api_key.startswith("Bearer "):
            api_key = api_key[7:]
        
        # Check if key exists and is active
        key_info = self.valid_api_keys.get(api_key)
        if not key_info or not key_info.get("active", False):
            logger.warning(f"Invalid API key attempted: {api_key[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={
                    "error": "invalid_api_key",
                    "message": "Invalid or inactive API key"
                }
            )
        
        logger.debug(f"API key verified: {key_info['name']}")
        return key_info
    
    async def check_permissions(self, api_key_info: dict, required_permission: str) -> bool:
        """Check if API key has required permission"""
        permissions = api_key_info.get("permissions", [])
        return required_permission in permissions or "admin" in permissions


# Global auth instance
api_key_auth = APIKeyAuth()


async def verify_api_key(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> str:
    """
    Dependency to verify API key from request
    
    Checks for API key in:
    1. Authorization header (Bearer token)
    2. X-API-Key header
    3. Query parameter (for development only)
    """
    
    api_key = None
    
    # Method 1: Authorization header (Bearer token)
    if credentials:
        api_key = credentials.credentials
    
    # Method 2: X-API-Key header
    if not api_key:
        api_key = request.headers.get("X-API-Key")
    
    # Method 3: Query parameter (development only)
    if not api_key and get_settings().debug:
        api_key = request.query_params.get("api_key")
    
    # Verify the API key
    key_info = await api_key_auth.verify_api_key(api_key)
    
    # Store key info in request state for use by other middleware/endpoints
    request.state.api_key_info = key_info
    
    return api_key


async def verify_admin_permission(
    request: Request,
    api_key: str = Depends(verify_api_key)
) -> str:
    """Dependency to verify admin permissions"""
    
    key_info = getattr(request.state, "api_key_info", {})
    
    if not await api_key_auth.check_permissions(key_info, "admin"):
        raise HTTPException(
            status_code=403,
            detail={
                "error": "insufficient_permissions",
                "message": "Admin permissions required"
            }
        )
    
    return api_key


async def verify_write_permission(
    request: Request,
    api_key: str = Depends(verify_api_key)
) -> str:
    """Dependency to verify write permissions"""
    
    key_info = getattr(request.state, "api_key_info", {})
    
    if not await api_key_auth.check_permissions(key_info, "write"):
        raise HTTPException(
            status_code=403,
            detail={
                "error": "insufficient_permissions", 
                "message": "Write permissions required"
            }
        )
    
    return api_key


# Optional authentication for public endpoints
async def optional_api_key(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """
    Optional API key verification for public endpoints
    Returns None if no API key provided, otherwise verifies it
    """
    
    api_key = None
    
    # Try to get API key
    if credentials:
        api_key = credentials.credentials
    
    if not api_key:
        api_key = request.headers.get("X-API-Key")
    
    if not api_key and get_settings().debug:
        api_key = request.query_params.get("api_key")
    
    # If no API key provided, that's okay for optional auth
    if not api_key:
        return None
    
    # If API key is provided, verify it
    try:
        key_info = await api_key_auth.verify_api_key(api_key)
        request.state.api_key_info = key_info
        return api_key
    except HTTPException:
        # For optional auth, invalid keys are treated as no auth
        return None


class AuthenticationMiddleware:
    """
    Authentication middleware for request-level auth handling
    """
    
    def __init__(self):
        self.settings = get_settings()
        
        # Endpoints that don't require authentication
        self.public_endpoints = {
            "/",
            "/health",
            "/health/",
            "/health/live", 
            "/health/ready",
            "/docs",
            "/redoc",
            "/openapi.json"
        }
        
        # Endpoints that require admin permissions
        self.admin_endpoints = {
            "/api/v1/admin",
            "/api/v1/metrics/detailed"
        }
    
    def is_public_endpoint(self, path: str) -> bool:
        """Check if endpoint is public"""
        return path in self.public_endpoints or path.startswith("/health/")
    
    def requires_admin(self, path: str) -> bool:
        """Check if endpoint requires admin permissions"""
        return any(path.startswith(admin_path) for admin_path in self.admin_endpoints)
    
    async def authenticate_request(self, request: Request) -> Optional[dict]:
        """Authenticate request and return API key info"""
        
        # Skip authentication for public endpoints
        if self.is_public_endpoint(request.url.path):
            return None
        
        # Get API key from request
        api_key = None
        
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            api_key = auth_header[7:]
        
        # Check X-API-Key header
        if not api_key:
            api_key = request.headers.get("X-API-Key")
        
        # Check query parameter (development only)
        if not api_key and self.settings.debug:
            api_key = request.query_params.get("api_key")
        
        # Verify API key
        if api_key:
            try:
                key_info = await api_key_auth.verify_api_key(api_key)
                
                # Check admin permissions if required
                if self.requires_admin(request.url.path):
                    if not await api_key_auth.check_permissions(key_info, "admin"):
                        raise HTTPException(
                            status_code=403,
                            detail="Admin permissions required"
                        )
                
                return key_info
                
            except HTTPException:
                raise
        else:
            # No API key provided for protected endpoint
            raise HTTPException(
                status_code=401,
                detail="API key required"
            )


# Global authentication middleware instance
auth_middleware = AuthenticationMiddleware()
