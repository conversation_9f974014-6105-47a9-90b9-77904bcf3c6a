"""
Model Router Service
FAANG+ implementation with OpenRouter.ai integration for intelligent routing
Provides circuit breaker protection, health monitoring, and cost optimization
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from opentelemetry import trace
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings
from src.core.models import TaskComplexity
from src.services.openrouter_client import OpenRouterClient, TaskType, ModelTier, OpenRouterResponse

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for model routing
MODEL_ROUTING_REQUESTS = Counter(
    'model_routing_requests_total',
    'Total model routing requests',
    ['selected_model', 'task_type', 'routing_reason']
)

MODEL_ROUTING_LATENCY = Histogram(
    'model_routing_latency_seconds',
    'Model routing decision latency',
    ['task_complexity']
)

MODEL_HEALTH_STATUS = Gauge(
    'model_health_status',
    'Model health status (1=healthy, 0.5=degraded, 0=unhealthy)',
    ['model_id', 'provider']
)


class ModelHealth(Enum):
    """Model health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class RoutingStrategy(Enum):
    """Model routing strategies"""
    COST_OPTIMIZED = "cost_optimized"
    QUALITY_FIRST = "quality_first"
    SPEED_OPTIMIZED = "speed_optimized"
    BALANCED = "balanced"


class ModelRoutingDecision:
    """Model routing decision with metadata"""

    def __init__(
        self,
        selected_model: str,
        task_type: TaskType,
        routing_reason: str,
        estimated_cost: float,
        quality_score: float,
        fallback_models: List[str] = None
    ):
        self.selected_model = selected_model
        self.task_type = task_type
        self.routing_reason = routing_reason
        self.estimated_cost = estimated_cost
        self.quality_score = quality_score
        self.fallback_models = fallback_models or []
        self.decision_time = datetime.utcnow()


class CircuitBreaker:
    """Circuit breaker for model health management"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open

    def record_success(self):
        """Record successful request"""
        self.failure_count = 0
        self.state = "closed"

    def record_failure(self):
        """Record failed request"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = "open"

    def can_execute(self) -> bool:
        """Check if request can be executed"""
        if self.state == "closed":
            return True

        if self.state == "open":
            if (time.time() - self.last_failure_time) > self.recovery_timeout:
                self.state = "half_open"
                return True
            return False

        # half_open state - allow one request to test
        return True


class ModelRouter:
    """
    Production-grade model router with OpenRouter.ai integration
    Implements FAANG+ standards for intelligent routing, health monitoring, and cost optimization
    """

    def __init__(self):
        self.settings = get_settings()
        self.openrouter_client = OpenRouterClient()

        # Circuit breakers for each model
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}

        # Model health tracking
        self.model_health: Dict[str, ModelHealth] = {}
        self.model_metrics: Dict[str, Dict[str, Any]] = {}

        # Routing configuration
        self.routing_strategy = RoutingStrategy.COST_OPTIMIZED
        self.quality_threshold = 0.8
        self.max_cost_per_token = None

        # Task type mapping from complexity
        self.complexity_to_task_type = {
            TaskComplexity.SIMPLE: TaskType.SUMMARIZATION,
            TaskComplexity.MEDIUM: TaskType.TEXT_OPTIMIZATION,
            TaskComplexity.COMPLEX: TaskType.REASONING,
            TaskComplexity.EXPERT: TaskType.ANALYSIS
        }
    
    async def initialize(self):
        """Initialize OpenRouter client and model health monitoring"""
        with tracer.start_as_current_span("model_router_initialization"):
            logger.info("Initializing Model Router with OpenRouter.ai...")

            # Initialize OpenRouter client
            await self.openrouter_client.initialize()

            # Initialize circuit breakers for available models
            available_models = await self.openrouter_client.get_available_models()
            for model in available_models:
                model_id = model.get('id', '')
                if model_id:
                    self.circuit_breakers[model_id] = CircuitBreaker(
                        failure_threshold=5,
                        recovery_timeout=60
                    )
                    self.model_health[model_id] = ModelHealth.HEALTHY
                    self.model_metrics[model_id] = {
                        'total_requests': 0,
                        'successful_requests': 0,
                        'failed_requests': 0,
                        'avg_response_time': 0.0,
                        'last_request_time': None,
                        'total_cost': 0.0
                    }

            # Start health monitoring task
            asyncio.create_task(self._health_monitoring_loop())

            logger.info("Model Router initialized successfully")
    
    async def route_request(
        self,
        prompt: str,
        task_complexity: TaskComplexity,
        quality_threshold: float = 0.85,
        preferred_model: Optional[str] = None,
        max_cost_per_token: Optional[float] = None,
        prefer_speed: bool = False
    ) -> ModelRoutingDecision:
        """
        Route request to optimal model using OpenRouter.ai with intelligent selection
        """
        start_time = time.time()

        with tracer.start_as_current_span("model_routing") as span:
            span.set_attribute("task_complexity", task_complexity.value)
            span.set_attribute("quality_threshold", quality_threshold)

            # Map task complexity to task type
            task_type = self.complexity_to_task_type.get(
                task_complexity, TaskType.TEXT_OPTIMIZATION
            )
            span.set_attribute("task_type", task_type.value)

            # Select optimal model using OpenRouter client
            selected_model = self.openrouter_client.model_selector.select_model(
                task_type=task_type,
                quality_threshold=quality_threshold,
                max_cost_per_token=max_cost_per_token or self.max_cost_per_token,
                context_length_required=len(prompt) * 2,  # Rough estimate
                prefer_speed=prefer_speed
            )

            # Override with preferred model if specified and available
            if preferred_model and preferred_model in self.openrouter_client.model_selector.models:
                preferred_model_info = self.openrouter_client.model_selector.models[preferred_model]
                if (task_type in preferred_model_info.capabilities and
                    preferred_model_info.quality_scores.get(task_type, 0.0) >= quality_threshold):
                    selected_model = preferred_model_info
                    routing_reason = "user_preference"
                else:
                    routing_reason = "preferred_model_unsuitable"
            else:
                routing_reason = self._get_routing_reason(selected_model, task_complexity)

            # Get fallback models
            fallback_models = self.openrouter_client.model_selector.get_fallback_chain(
                selected_model.id, task_type
            )

            # Calculate estimated cost
            estimated_cost = self._estimate_cost(prompt, selected_model)

            # Create routing decision
            decision = ModelRoutingDecision(
                selected_model=selected_model.id,
                task_type=task_type,
                routing_reason=routing_reason,
                estimated_cost=estimated_cost,
                quality_score=selected_model.quality_scores.get(task_type, 0.0),
                fallback_models=fallback_models
            )

            # Record metrics
            routing_latency = time.time() - start_time
            MODEL_ROUTING_LATENCY.labels(task_complexity=task_complexity.value).observe(routing_latency)
            MODEL_ROUTING_REQUESTS.labels(
                selected_model=selected_model.id,
                task_type=task_type.value,
                routing_reason=routing_reason
            ).inc()

            span.set_attribute("selected_model", selected_model.id)
            span.set_attribute("routing_reason", routing_reason)
            span.set_attribute("estimated_cost", estimated_cost)
            span.set_attribute("routing_latency_ms", routing_latency * 1000)

            logger.info(
                f"Routed {task_complexity.value} task to {selected_model.id} "
                f"(reason: {routing_reason}, cost: ${estimated_cost:.6f})"
            )

            return decision

    def _estimate_cost(self, prompt: str, model_info) -> float:
        """Estimate cost for the request"""
        # Rough token estimation (4 chars per token average)
        estimated_input_tokens = len(prompt) / 4
        estimated_output_tokens = estimated_input_tokens * 0.3  # Assume 30% output ratio

        total_cost = (
            estimated_input_tokens * model_info.input_cost_per_token +
            estimated_output_tokens * model_info.output_cost_per_token
        )

        return total_cost

    def _get_routing_reason(self, model_info, task_complexity: TaskComplexity) -> str:
        """Get human-readable routing reason"""
        if model_info.tier == ModelTier.BUDGET:
            return f"budget_model_for_{task_complexity.value}_task"
        elif model_info.tier == ModelTier.STANDARD:
            return f"standard_model_for_{task_complexity.value}_task"
        elif model_info.tier == ModelTier.PREMIUM:
            return f"premium_model_for_{task_complexity.value}_quality"
        elif model_info.tier == ModelTier.ULTRA_PREMIUM:
            return f"ultra_premium_for_{task_complexity.value}_excellence"
        else:
            return "optimal_cost_effectiveness"

    async def execute_request(
        self,
        prompt: str,
        task_complexity: TaskComplexity,
        quality_threshold: float = 0.85,
        preferred_model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        user_id: Optional[str] = None
    ) -> OpenRouterResponse:
        """Execute request through OpenRouter with routing and monitoring"""

        # Get routing decision
        decision = await self.route_request(
            prompt=prompt,
            task_complexity=task_complexity,
            quality_threshold=quality_threshold,
            preferred_model=preferred_model
        )

        # Execute request through OpenRouter
        start_time = time.time()
        try:
            response = await self.openrouter_client.generate(
                prompt=prompt,
                task_type=decision.task_type,
                quality_threshold=quality_threshold,
                max_tokens=max_tokens,
                temperature=temperature,
                user_id=user_id,
                model_override=decision.selected_model
            )

            # Record success
            await self.record_request_result(
                model_id=decision.selected_model,
                success=response.error is None,
                response_time=response.latency_ms / 1000,
                cost=response.cost
            )

            return response

        except Exception as e:
            # Record failure
            response_time = time.time() - start_time
            await self.record_request_result(
                model_id=decision.selected_model,
                success=False,
                response_time=response_time,
                cost=0.0
            )
            raise

    def _is_model_available(self, model_id: str) -> bool:
        """Check if model is available (not circuit broken)"""
        if model_id not in self.circuit_breakers:
            return True  # New model, assume available

        return (
            self.circuit_breakers[model_id].can_execute() and
            self.model_health.get(model_id, ModelHealth.HEALTHY) != ModelHealth.UNHEALTHY
        )

    async def record_request_result(
        self,
        model_id: str,
        success: bool,
        response_time: float,
        cost: float = 0.0
    ):
        """Record request result for health monitoring"""
        if model_id not in self.model_metrics:
            self.model_metrics[model_id] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'avg_response_time': 0.0,
                'last_request_time': None,
                'total_cost': 0.0
            }

        metrics = self.model_metrics[model_id]
        circuit_breaker = self.circuit_breakers.get(model_id)

        metrics['total_requests'] += 1
        metrics['last_request_time'] = datetime.utcnow()
        metrics['total_cost'] += cost

        if success:
            metrics['successful_requests'] += 1
            if circuit_breaker:
                circuit_breaker.record_success()

            # Update average response time
            if metrics['avg_response_time'] == 0:
                metrics['avg_response_time'] = response_time
            else:
                metrics['avg_response_time'] = (
                    metrics['avg_response_time'] * 0.9 + response_time * 0.1
                )
        else:
            metrics['failed_requests'] += 1
            if circuit_breaker:
                circuit_breaker.record_failure()

        # Update model health
        self._update_model_health(model_id)

    def _update_model_health(self, model_id: str):
        """Update model health based on recent performance"""
        metrics = self.model_metrics.get(model_id, {})

        if metrics.get('total_requests', 0) < 10:
            # Not enough data
            return

        success_rate = metrics['successful_requests'] / metrics['total_requests']

        if success_rate >= 0.95:
            self.model_health[model_id] = ModelHealth.HEALTHY
            health_value = 1.0
        elif success_rate >= 0.80:
            self.model_health[model_id] = ModelHealth.DEGRADED
            health_value = 0.5
        else:
            self.model_health[model_id] = ModelHealth.UNHEALTHY
            health_value = 0.0

        # Update Prometheus metric
        model_info = self.openrouter_client.model_selector.models.get(model_id)
        provider = model_info.provider if model_info else "unknown"
        MODEL_HEALTH_STATUS.labels(model_id=model_id, provider=provider).set(health_value)
    
    async def _health_monitoring_loop(self):
        """Background task for model health monitoring"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._perform_health_checks()
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")

    async def _perform_health_checks(self):
        """Perform health checks on all models"""
        try:
            # Get latest model availability from OpenRouter
            available_models = await self.openrouter_client.get_available_models()

            # Update model availability
            available_model_ids = {model.get('id', '') for model in available_models}

            for model_id in self.model_health.keys():
                if model_id not in available_model_ids:
                    self.model_health[model_id] = ModelHealth.UNHEALTHY
                    logger.warning(f"Model {model_id} no longer available")

            # Add new models
            for model in available_models:
                model_id = model.get('id', '')
                if model_id and model_id not in self.circuit_breakers:
                    self.circuit_breakers[model_id] = CircuitBreaker()
                    self.model_health[model_id] = ModelHealth.HEALTHY
                    logger.info(f"New model {model_id} detected and added")

        except Exception as e:
            logger.error(f"Health check failed: {e}")

    async def get_model_stats(self) -> Dict[str, Any]:
        """Get comprehensive model performance statistics"""
        stats = {}

        for model_id, metrics in self.model_metrics.items():
            circuit_breaker = self.circuit_breakers.get(model_id)
            model_info = self.openrouter_client.model_selector.models.get(model_id)

            stats[model_id] = {
                'health': self.model_health.get(model_id, ModelHealth.HEALTHY).value,
                'available': self._is_model_available(model_id),
                'circuit_breaker_state': circuit_breaker.state if circuit_breaker else "unknown",
                'total_requests': metrics['total_requests'],
                'success_rate': (
                    metrics['successful_requests'] / metrics['total_requests']
                    if metrics['total_requests'] > 0 else 0
                ),
                'avg_response_time': metrics['avg_response_time'],
                'total_cost': metrics['total_cost'],
                'last_request_time': (
                    metrics['last_request_time'].isoformat()
                    if metrics['last_request_time'] else None
                ),
                'provider': model_info.provider if model_info else "unknown",
                'tier': model_info.tier.value if model_info else "unknown"
            }

        # Add OpenRouter client stats
        openrouter_stats = await self.openrouter_client.get_usage_stats()

        return {
            'models': stats,
            'openrouter': openrouter_stats,
            'routing_strategy': self.routing_strategy.value,
            'quality_threshold': self.quality_threshold
        }

    async def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status"""
        total_models = len(self.model_health)
        healthy_models = sum(
            1 for health in self.model_health.values()
            if health == ModelHealth.HEALTHY
        )

        return {
            'overall_health': "healthy" if healthy_models > total_models * 0.8 else "degraded",
            'total_models': total_models,
            'healthy_models': healthy_models,
            'degraded_models': sum(
                1 for health in self.model_health.values()
                if health == ModelHealth.DEGRADED
            ),
            'unhealthy_models': sum(
                1 for health in self.model_health.values()
                if health == ModelHealth.UNHEALTHY
            ),
            'openrouter_connected': self.openrouter_client.session is not None
        }

    async def cleanup(self):
        """Cleanup OpenRouter client and resources"""
        await self.openrouter_client.cleanup()

        logger.info("Model Router cleanup completed")
