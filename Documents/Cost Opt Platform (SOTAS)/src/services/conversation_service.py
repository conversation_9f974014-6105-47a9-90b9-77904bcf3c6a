"""
Conversation Service
ChatGPT/Claude Desktop style conversation management
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError

from src.models.conversation import (
    Conversation, Message, ConversationCreate, ConversationUpdate,
    MessageCreate, ConversationResponse, MessageResponse,
    ConversationWithMessages, ConversationListResponse,
    MessageSearchRequest, ConversationStats
)
from src.core.database import get_db_session

logger = logging.getLogger(__name__)

class ConversationService:
    """Service for managing conversations and messages"""
    
    def __init__(self):
        self.logger = logger
    
    async def create_conversation(
        self, 
        conversation_data: ConversationCreate,
        db: Session = None
    ) -> ConversationResponse:
        """Create a new conversation"""
        if db is None:
            async with get_db_session() as db:
                return await self._create_conversation_impl(conversation_data, db)
        else:
            return await self._create_conversation_impl(conversation_data, db)
    
    async def _create_conversation_impl(
        self, 
        conversation_data: ConversationCreate,
        db: Session
    ) -> ConversationResponse:
        """Implementation for creating conversation"""
        try:
            conversation = Conversation(
                title=conversation_data.title,
                user_id=conversation_data.user_id,
                metadata=conversation_data.metadata or {}
            )
            
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
            
            self.logger.info(f"Created conversation {conversation.id}")
            
            return ConversationResponse(
                id=conversation.id,
                title=conversation.title,
                created_at=conversation.created_at,
                updated_at=conversation.updated_at,
                user_id=conversation.user_id,
                is_archived=conversation.is_archived,
                metadata=conversation.metadata,
                message_count=0,
                total_cost="0.00"
            )
            
        except SQLAlchemyError as e:
            db.rollback()
            self.logger.error(f"Error creating conversation: {e}")
            raise
    
    async def get_conversation(
        self, 
        conversation_id: str,
        include_messages: bool = False,
        db: Session = None
    ) -> Optional[ConversationWithMessages]:
        """Get a conversation by ID"""
        if db is None:
            async with get_db_session() as db:
                return await self._get_conversation_impl(conversation_id, include_messages, db)
        else:
            return await self._get_conversation_impl(conversation_id, include_messages, db)
    
    async def _get_conversation_impl(
        self, 
        conversation_id: str,
        include_messages: bool,
        db: Session
    ) -> Optional[ConversationWithMessages]:
        """Implementation for getting conversation"""
        try:
            conversation = db.query(Conversation).filter(
                Conversation.id == conversation_id
            ).first()
            
            if not conversation:
                return None
            
            # Calculate stats
            message_count = db.query(func.count(Message.id)).filter(
                Message.conversation_id == conversation_id
            ).scalar() or 0
            
            total_cost = db.query(func.sum(Message.cost_usd.cast(float))).filter(
                Message.conversation_id == conversation_id
            ).scalar() or 0.0
            
            messages = []
            if include_messages:
                message_records = db.query(Message).filter(
                    Message.conversation_id == conversation_id
                ).order_by(asc(Message.created_at)).all()
                
                messages = [
                    MessageResponse(
                        id=msg.id,
                        conversation_id=msg.conversation_id,
                        role=msg.role,
                        content=msg.content,
                        created_at=msg.created_at,
                        token_count=msg.token_count,
                        cost_usd=msg.cost_usd,
                        model_used=msg.model_used,
                        metadata=msg.metadata
                    ) for msg in message_records
                ]
            
            return ConversationWithMessages(
                id=conversation.id,
                title=conversation.title,
                created_at=conversation.created_at,
                updated_at=conversation.updated_at,
                user_id=conversation.user_id,
                is_archived=conversation.is_archived,
                metadata=conversation.metadata,
                message_count=message_count,
                total_cost=f"{total_cost:.2f}",
                messages=messages
            )
            
        except SQLAlchemyError as e:
            self.logger.error(f"Error getting conversation {conversation_id}: {e}")
            raise
    
    async def list_conversations(
        self,
        user_id: Optional[str] = None,
        include_archived: bool = False,
        page: int = 1,
        page_size: int = 20,
        db: Session = None
    ) -> ConversationListResponse:
        """List conversations with pagination"""
        if db is None:
            async with get_db_session() as db:
                return await self._list_conversations_impl(
                    user_id, include_archived, page, page_size, db
                )
        else:
            return await self._list_conversations_impl(
                user_id, include_archived, page, page_size, db
            )
    
    async def _list_conversations_impl(
        self,
        user_id: Optional[str],
        include_archived: bool,
        page: int,
        page_size: int,
        db: Session
    ) -> ConversationListResponse:
        """Implementation for listing conversations"""
        try:
            query = db.query(Conversation)
            
            # Apply filters
            if user_id:
                query = query.filter(Conversation.user_id == user_id)
            
            if not include_archived:
                query = query.filter(Conversation.is_archived == False)
            
            # Get total count
            total = query.count()
            
            # Apply pagination and ordering
            conversations = query.order_by(desc(Conversation.updated_at)).offset(
                (page - 1) * page_size
            ).limit(page_size).all()
            
            # Build response with stats
            conversation_responses = []
            for conv in conversations:
                message_count = db.query(func.count(Message.id)).filter(
                    Message.conversation_id == conv.id
                ).scalar() or 0
                
                total_cost = db.query(func.sum(Message.cost_usd.cast(float))).filter(
                    Message.conversation_id == conv.id
                ).scalar() or 0.0
                
                conversation_responses.append(ConversationResponse(
                    id=conv.id,
                    title=conv.title,
                    created_at=conv.created_at,
                    updated_at=conv.updated_at,
                    user_id=conv.user_id,
                    is_archived=conv.is_archived,
                    metadata=conv.metadata,
                    message_count=message_count,
                    total_cost=f"{total_cost:.2f}"
                ))
            
            return ConversationListResponse(
                conversations=conversation_responses,
                total=total,
                page=page,
                page_size=page_size,
                has_next=page * page_size < total,
                has_prev=page > 1
            )
            
        except SQLAlchemyError as e:
            self.logger.error(f"Error listing conversations: {e}")
            raise

    async def add_message(
        self,
        conversation_id: str,
        message_data: MessageCreate,
        db: Session = None
    ) -> MessageResponse:
        """Add a message to a conversation"""
        if db is None:
            async with get_db_session() as db:
                return await self._add_message_impl(conversation_id, message_data, db)
        else:
            return await self._add_message_impl(conversation_id, message_data, db)

    async def _add_message_impl(
        self,
        conversation_id: str,
        message_data: MessageCreate,
        db: Session
    ) -> MessageResponse:
        """Implementation for adding message"""
        try:
            # Verify conversation exists
            conversation = db.query(Conversation).filter(
                Conversation.id == conversation_id
            ).first()

            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")

            # Create message
            message = Message(
                conversation_id=conversation_id,
                role=message_data.role,
                content=message_data.content,
                token_count=message_data.token_count or 0,
                cost_usd=message_data.cost_usd or "0.00",
                model_used=message_data.model_used,
                metadata=message_data.metadata or {}
            )

            db.add(message)

            # Update conversation timestamp
            conversation.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(message)

            self.logger.info(f"Added message {message.id} to conversation {conversation_id}")

            return MessageResponse(
                id=message.id,
                conversation_id=message.conversation_id,
                role=message.role,
                content=message.content,
                created_at=message.created_at,
                token_count=message.token_count,
                cost_usd=message.cost_usd,
                model_used=message.model_used,
                metadata=message.metadata
            )

        except SQLAlchemyError as e:
            db.rollback()
            self.logger.error(f"Error adding message to conversation {conversation_id}: {e}")
            raise

    async def update_conversation(
        self,
        conversation_id: str,
        update_data: ConversationUpdate,
        db: Session = None
    ) -> Optional[ConversationResponse]:
        """Update a conversation"""
        if db is None:
            async with get_db_session() as db:
                return await self._update_conversation_impl(conversation_id, update_data, db)
        else:
            return await self._update_conversation_impl(conversation_id, update_data, db)

    async def _update_conversation_impl(
        self,
        conversation_id: str,
        update_data: ConversationUpdate,
        db: Session
    ) -> Optional[ConversationResponse]:
        """Implementation for updating conversation"""
        try:
            conversation = db.query(Conversation).filter(
                Conversation.id == conversation_id
            ).first()

            if not conversation:
                return None

            # Update fields
            if update_data.title is not None:
                conversation.title = update_data.title
            if update_data.is_archived is not None:
                conversation.is_archived = update_data.is_archived
            if update_data.metadata is not None:
                conversation.metadata = update_data.metadata

            conversation.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(conversation)

            # Get stats
            message_count = db.query(func.count(Message.id)).filter(
                Message.conversation_id == conversation_id
            ).scalar() or 0

            total_cost = db.query(func.sum(Message.cost_usd.cast(float))).filter(
                Message.conversation_id == conversation_id
            ).scalar() or 0.0

            return ConversationResponse(
                id=conversation.id,
                title=conversation.title,
                created_at=conversation.created_at,
                updated_at=conversation.updated_at,
                user_id=conversation.user_id,
                is_archived=conversation.is_archived,
                metadata=conversation.metadata,
                message_count=message_count,
                total_cost=f"{total_cost:.2f}"
            )

        except SQLAlchemyError as e:
            db.rollback()
            self.logger.error(f"Error updating conversation {conversation_id}: {e}")
            raise

    async def delete_conversation(
        self,
        conversation_id: str,
        db: Session = None
    ) -> bool:
        """Delete a conversation and all its messages"""
        if db is None:
            async with get_db_session() as db:
                return await self._delete_conversation_impl(conversation_id, db)
        else:
            return await self._delete_conversation_impl(conversation_id, db)

    async def _delete_conversation_impl(
        self,
        conversation_id: str,
        db: Session
    ) -> bool:
        """Implementation for deleting conversation"""
        try:
            conversation = db.query(Conversation).filter(
                Conversation.id == conversation_id
            ).first()

            if not conversation:
                return False

            # Delete conversation (messages will be deleted via cascade)
            db.delete(conversation)
            db.commit()

            self.logger.info(f"Deleted conversation {conversation_id}")
            return True

        except SQLAlchemyError as e:
            db.rollback()
            self.logger.error(f"Error deleting conversation {conversation_id}: {e}")
            raise

    async def get_messages(
        self,
        conversation_id: str,
        limit: int = 100,
        offset: int = 0,
        db: Session = None
    ) -> List[MessageResponse]:
        """Get messages from a conversation"""
        if db is None:
            async with get_db_session() as db:
                return await self._get_messages_impl(conversation_id, limit, offset, db)
        else:
            return await self._get_messages_impl(conversation_id, limit, offset, db)

    async def _get_messages_impl(
        self,
        conversation_id: str,
        limit: int,
        offset: int,
        db: Session
    ) -> List[MessageResponse]:
        """Implementation for getting messages"""
        try:
            messages = db.query(Message).filter(
                Message.conversation_id == conversation_id
            ).order_by(asc(Message.created_at)).offset(offset).limit(limit).all()

            return [
                MessageResponse(
                    id=msg.id,
                    conversation_id=msg.conversation_id,
                    role=msg.role,
                    content=msg.content,
                    created_at=msg.created_at,
                    token_count=msg.token_count,
                    cost_usd=msg.cost_usd,
                    model_used=msg.model_used,
                    metadata=msg.metadata
                ) for msg in messages
            ]

        except SQLAlchemyError as e:
            self.logger.error(f"Error getting messages for conversation {conversation_id}: {e}")
            raise

    async def search_messages(
        self,
        search_request: MessageSearchRequest,
        db: Session = None
    ) -> List[MessageResponse]:
        """Search messages across conversations"""
        if db is None:
            async with get_db_session() as db:
                return await self._search_messages_impl(search_request, db)
        else:
            return await self._search_messages_impl(search_request, db)

    async def _search_messages_impl(
        self,
        search_request: MessageSearchRequest,
        db: Session
    ) -> List[MessageResponse]:
        """Implementation for searching messages"""
        try:
            query = db.query(Message)

            # Apply filters
            if search_request.conversation_id:
                query = query.filter(Message.conversation_id == search_request.conversation_id)

            if search_request.role:
                query = query.filter(Message.role == search_request.role)

            if search_request.start_date:
                query = query.filter(Message.created_at >= search_request.start_date)

            if search_request.end_date:
                query = query.filter(Message.created_at <= search_request.end_date)

            # Text search in content
            if search_request.query:
                query = query.filter(Message.content.contains(search_request.query))

            # Apply limit and ordering
            messages = query.order_by(desc(Message.created_at)).limit(
                search_request.limit or 50
            ).all()

            return [
                MessageResponse(
                    id=msg.id,
                    conversation_id=msg.conversation_id,
                    role=msg.role,
                    content=msg.content,
                    created_at=msg.created_at,
                    token_count=msg.token_count,
                    cost_usd=msg.cost_usd,
                    model_used=msg.model_used,
                    metadata=msg.metadata
                ) for msg in messages
            ]

        except SQLAlchemyError as e:
            self.logger.error(f"Error searching messages: {e}")
            raise

    async def get_conversation_stats(
        self,
        user_id: Optional[str] = None,
        db: Session = None
    ) -> ConversationStats:
        """Get conversation statistics"""
        if db is None:
            async with get_db_session() as db:
                return await self._get_conversation_stats_impl(user_id, db)
        else:
            return await self._get_conversation_stats_impl(user_id, db)

    async def _get_conversation_stats_impl(
        self,
        user_id: Optional[str],
        db: Session
    ) -> ConversationStats:
        """Implementation for getting conversation stats"""
        try:
            # Base queries
            conv_query = db.query(Conversation)
            msg_query = db.query(Message)

            if user_id:
                conv_query = conv_query.filter(Conversation.user_id == user_id)
                msg_query = msg_query.join(Conversation).filter(Conversation.user_id == user_id)

            # Basic counts
            total_conversations = conv_query.count()
            total_messages = msg_query.count()

            # Cost calculations
            total_cost = db.query(func.sum(Message.cost_usd.cast(float))).scalar() or 0.0

            # Average messages per conversation
            avg_messages = total_messages / total_conversations if total_conversations > 0 else 0

            # Most used model
            most_used_model_result = db.query(
                Message.model_used, func.count(Message.id)
            ).filter(
                Message.model_used.isnot(None)
            ).group_by(Message.model_used).order_by(
                desc(func.count(Message.id))
            ).first()

            most_used_model = most_used_model_result[0] if most_used_model_result else None

            # Cost by model
            cost_by_model_results = db.query(
                Message.model_used, func.sum(Message.cost_usd.cast(float))
            ).filter(
                Message.model_used.isnot(None)
            ).group_by(Message.model_used).all()

            cost_by_model = {
                model: f"{cost:.2f}" for model, cost in cost_by_model_results if cost
            }

            # Conversations by date (last 30 days)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)

            conversations_by_date_results = db.query(
                func.date(Conversation.created_at), func.count(Conversation.id)
            ).filter(
                Conversation.created_at >= thirty_days_ago
            ).group_by(func.date(Conversation.created_at)).all()

            conversations_by_date = {
                str(date): count for date, count in conversations_by_date_results
            }

            return ConversationStats(
                total_conversations=total_conversations,
                total_messages=total_messages,
                total_cost_usd=f"{total_cost:.2f}",
                avg_messages_per_conversation=round(avg_messages, 2),
                most_used_model=most_used_model,
                cost_by_model=cost_by_model,
                conversations_by_date=conversations_by_date
            )

        except SQLAlchemyError as e:
            self.logger.error(f"Error getting conversation stats: {e}")
            raise
