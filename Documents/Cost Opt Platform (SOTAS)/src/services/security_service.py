"""
Security & Compliance Service
FAANG+ implementation with zero trust architecture, threat detection, and compliance frameworks
Implements Google security practices with enterprise-grade protection
"""

import asyncio
import hashlib
import hmac
import logging
import time
import re
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import uuid
import json
from datetime import datetime, timedelta

from opentelemetry import trace
from src.monitoring.metrics_registry import get_counter, get_histogram, get_gauge
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for security monitoring
SECURITY_EVENTS = get_counter(
    'security_events_total',
    'Total security events',
    ['event_type', 'severity', 'source']
)

AUTHENTICATION_ATTEMPTS = get_counter(
    'authentication_attempts_total',
    'Authentication attempts',
    ['method', 'status']
)

THREAT_DETECTIONS = get_counter(
    'threat_detections_total',
    'Threat detections',
    ['threat_type', 'action']
)

COMPLIANCE_VIOLATIONS = get_counter(
    'compliance_violations_total',
    'Compliance violations detected',
    ['framework', 'violation_type']
)


class SecurityEventType(Enum):
    """Types of security events"""
    AUTHENTICATION_SUCCESS = "auth_success"
    AUTHENTICATION_FAILURE = "auth_failure"
    AUTHORIZATION_FAILURE = "authz_failure"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INPUT_VALIDATION_FAILURE = "input_validation_failure"
    ENCRYPTION_FAILURE = "encryption_failure"
    COMPLIANCE_VIOLATION = "compliance_violation"
    THREAT_DETECTED = "threat_detected"


class ThreatType(Enum):
    """Types of security threats"""
    SQL_INJECTION = "sql_injection"
    XSS_ATTEMPT = "xss_attempt"
    CSRF_ATTEMPT = "csrf_attempt"
    BRUTE_FORCE = "brute_force"
    DDOS_ATTEMPT = "ddos_attempt"
    MALICIOUS_PAYLOAD = "malicious_payload"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_EXFILTRATION = "data_exfiltration"


class ComplianceFramework(Enum):
    """Supported compliance frameworks"""
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO27001 = "iso27001"


class ThreatLevel(Enum):
    """Threat severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityThreat:
    """Security threat detection result"""
    threat_id: str
    threat_type: str
    severity: str
    description: str
    source_ip: str
    user_id: Optional[str]
    detected_at: datetime
    mitigated: bool = False


@dataclass
class SecurityEvent:
    """Security event record"""
    id: str
    event_type: SecurityEventType
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    source_ip: str
    user_id: Optional[str]
    timestamp: datetime
    details: Dict[str, Any]
    threat_type: Optional[ThreatType] = None
    compliance_framework: Optional[ComplianceFramework] = None
    action_taken: Optional[str] = None


class EncryptionService:
    """Production-grade encryption service with key rotation"""
    
    def __init__(self):
        self.settings = get_settings()
        self.primary_key: Optional[Fernet] = None
        self.rotation_keys: List[Fernet] = []
        self.key_rotation_interval = 86400  # 24 hours
        self.last_rotation = 0
    
    async def initialize(self):
        """Initialize encryption service with key management"""
        try:
            # Generate or load primary encryption key
            if hasattr(self.settings, 'encryption_key') and self.settings.encryption_key:
                key = self.settings.encryption_key.encode()
            else:
                # Generate new key from password
                password = getattr(self.settings, 'encryption_password', 'default-password').encode()
                salt = b'salt_1234567890'  # In production, use random salt stored securely
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
            
            self.primary_key = Fernet(key)
            self.last_rotation = time.time()
            
            logger.info("Encryption service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption service: {e}")
            raise
    
    async def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not self.primary_key:
            raise RuntimeError("Encryption service not initialized")
        
        try:
            encrypted_data = self.primary_key.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            SECURITY_EVENTS.labels(
                event_type=SecurityEventType.ENCRYPTION_FAILURE.value,
                severity='HIGH',
                source='encryption_service'
            ).inc()
            logger.error(f"Encryption failed: {e}")
            raise
    
    async def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data with key rotation support"""
        if not self.primary_key:
            raise RuntimeError("Encryption service not initialized")
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            
            # Try primary key first
            try:
                decrypted_data = self.primary_key.decrypt(encrypted_bytes)
                return decrypted_data.decode()
            except Exception:
                # Try rotation keys
                for rotation_key in self.rotation_keys:
                    try:
                        decrypted_data = rotation_key.decrypt(encrypted_bytes)
                        return decrypted_data.decode()
                    except Exception:
                        continue
                
                raise ValueError("Unable to decrypt with any available key")
                
        except Exception as e:
            SECURITY_EVENTS.labels(
                event_type=SecurityEventType.ENCRYPTION_FAILURE.value,
                severity='HIGH',
                source='encryption_service'
            ).inc()
            logger.error(f"Decryption failed: {e}")
            raise
    
    async def rotate_keys(self):
        """Rotate encryption keys"""
        if time.time() - self.last_rotation < self.key_rotation_interval:
            return
        
        try:
            # Move current primary to rotation keys
            if self.primary_key:
                self.rotation_keys.append(self.primary_key)
            
            # Keep only last 3 rotation keys
            self.rotation_keys = self.rotation_keys[-3:]
            
            # Generate new primary key
            new_key = Fernet.generate_key()
            self.primary_key = Fernet(new_key)
            self.last_rotation = time.time()
            
            logger.info("Encryption keys rotated successfully")
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")


class ThreatDetectionEngine:
    """Advanced threat detection with ML-based analysis"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Threat patterns
        self.sql_injection_patterns = [
            r"(\bunion\b.*\bselect\b)",
            r"(\bselect\b.*\bfrom\b.*\bwhere\b)",
            r"(\bdrop\b.*\btable\b)",
            r"(\binsert\b.*\binto\b)",
            r"(\bupdate\b.*\bset\b)",
            r"(\bdelete\b.*\bfrom\b)",
            r"(\bexec\b.*\bsp_)",
            r"(\bor\b.*1\s*=\s*1)",
            r"(\band\b.*1\s*=\s*1)",
            r"(\bor\b.*'.*'.*=.*'.*')",
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"onload\s*=",
            r"onerror\s*=",
            r"onclick\s*=",
            r"onmouseover\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
        ]
        
        # Behavioral analysis
        self.user_behavior: Dict[str, Dict] = {}
        self.ip_reputation: Dict[str, Dict] = {}
        
        # Threat thresholds
        self.brute_force_threshold = 10  # attempts per minute
        self.suspicious_activity_threshold = 50  # requests per minute
    
    async def analyze_request(
        self, 
        source_ip: str, 
        user_id: Optional[str], 
        request_data: Dict[str, Any]
    ) -> List[SecurityEvent]:
        """Analyze request for security threats"""
        threats = []
        
        with tracer.start_as_current_span("threat_analysis") as span:
            span.set_attribute("source_ip", source_ip)
            span.set_attribute("user_id", user_id or "anonymous")
            
            # SQL Injection detection
            sql_threats = await self._detect_sql_injection(request_data, source_ip, user_id)
            threats.extend(sql_threats)
            
            # XSS detection
            xss_threats = await self._detect_xss(request_data, source_ip, user_id)
            threats.extend(xss_threats)
            
            # Brute force detection
            brute_force_threats = await self._detect_brute_force(source_ip, user_id)
            threats.extend(brute_force_threats)
            
            # Behavioral analysis
            behavioral_threats = await self._analyze_behavior(source_ip, user_id, request_data)
            threats.extend(behavioral_threats)
            
            span.set_attribute("threats_detected", len(threats))
            
            # Record threat metrics
            for threat in threats:
                THREAT_DETECTIONS.labels(
                    threat_type=threat.threat_type.value if threat.threat_type else 'unknown',
                    action=threat.action_taken or 'logged'
                ).inc()
        
        return threats


class ComplianceEngine:
    """Compliance monitoring and enforcement engine"""

    def __init__(self):
        self.settings = get_settings()
        self.enabled_frameworks = [
            ComplianceFramework.SOC2,
            ComplianceFramework.GDPR,
            ComplianceFramework.ISO27001
        ]

        # Data retention policies
        self.retention_policies = {
            'optimization_requests': 2555,  # 7 years in days (SOC2)
            'user_data': 2555,  # 7 years
            'security_logs': 2555,  # 7 years
            'audit_logs': 2555,  # 7 years
        }

        # PII detection patterns
        self.pii_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',
            'phone': r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            'ip_address': r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
        }

    async def scan_for_pii(self, data: Dict[str, Any]) -> List[SecurityEvent]:
        """Scan data for PII and compliance violations"""
        violations = []

        for key, value in data.items():
            if isinstance(value, str):
                for pii_type, pattern in self.pii_patterns.items():
                    matches = re.findall(pattern, value)
                    if matches:
                        violation = SecurityEvent(
                            id=str(uuid.uuid4()),
                            event_type=SecurityEventType.COMPLIANCE_VIOLATION,
                            severity='HIGH',
                            source_ip='internal',
                            user_id=None,
                            timestamp=datetime.utcnow(),
                            compliance_framework=ComplianceFramework.GDPR,
                            details={
                                'pii_type': pii_type,
                                'field': key,
                                'matches_count': len(matches),
                                'violation': 'unencrypted_pii_detected'
                            },
                            action_taken='flagged_for_encryption'
                        )
                        violations.append(violation)

                        COMPLIANCE_VIOLATIONS.labels(
                            framework=ComplianceFramework.GDPR.value,
                            violation_type='unencrypted_pii'
                        ).inc()

        return violations

    async def validate_data_retention(self, data_type: str, created_at: datetime) -> bool:
        """Validate data retention compliance"""
        if data_type not in self.retention_policies:
            return True  # No policy defined, allow

        retention_days = self.retention_policies[data_type]
        age_days = (datetime.utcnow() - created_at).days

        if age_days > retention_days:
            COMPLIANCE_VIOLATIONS.labels(
                framework=ComplianceFramework.SOC2.value,
                violation_type='data_retention_exceeded'
            ).inc()
            return False

        return True

    async def audit_access(
        self,
        user_id: str,
        resource: str,
        action: str,
        success: bool
    ) -> SecurityEvent:
        """Create audit log entry for compliance"""
        audit_event = SecurityEvent(
            id=str(uuid.uuid4()),
            event_type=SecurityEventType.AUTHENTICATION_SUCCESS if success else SecurityEventType.AUTHORIZATION_FAILURE,
            severity='LOW' if success else 'MEDIUM',
            source_ip='internal',
            user_id=user_id,
            timestamp=datetime.utcnow(),
            compliance_framework=ComplianceFramework.SOC2,
            details={
                'resource': resource,
                'action': action,
                'success': success,
                'audit_type': 'access_control'
            }
        )

        return audit_event


class SecurityService:
    """
    Production-grade security service implementing FAANG+ standards
    Provides zero trust architecture, threat detection, and compliance monitoring
    """

    def __init__(self):
        self.settings = get_settings()
        self.encryption_service = EncryptionService()
        self.threat_detection = ThreatDetectionEngine()
        self.compliance_engine = ComplianceEngine()

        # Security event storage
        self.security_events: List[SecurityEvent] = []
        self.max_events_in_memory = 10000

        # Rate limiting for security events
        self.event_rate_limiter: Dict[str, List[float]] = {}

    async def initialize(self):
        """Initialize security service"""
        logger.info("Initializing Security Service...")

        await self.encryption_service.initialize()

        logger.info("Security Service initialized successfully")

    async def validate_request(
        self,
        source_ip: str,
        user_id: Optional[str],
        request_data: Dict[str, Any]
    ) -> Tuple[bool, List[SecurityEvent]]:
        """Comprehensive request validation"""
        events = []

        with tracer.start_as_current_span("security_validation") as span:
            span.set_attribute("source_ip", source_ip)
            span.set_attribute("user_id", user_id or "anonymous")

            # Threat detection
            threats = await self.threat_detection.analyze_request(source_ip, user_id, request_data)
            events.extend(threats)

            # PII compliance check
            pii_violations = await self.compliance_engine.scan_for_pii(request_data)
            events.extend(pii_violations)

            # Check if any critical threats were detected
            critical_threats = [
                event for event in events
                if event.severity == 'CRITICAL' or event.action_taken == 'blocked'
            ]

            # Store security events
            await self._store_security_events(events)

            span.set_attribute("threats_detected", len(threats))
            span.set_attribute("pii_violations", len(pii_violations))
            span.set_attribute("critical_threats", len(critical_threats))

            # Return validation result
            is_valid = len(critical_threats) == 0
            return is_valid, events

    async def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return await self.encryption_service.encrypt(data)

    async def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return await self.encryption_service.decrypt(encrypted_data)

    async def audit_access(
        self,
        user_id: str,
        resource: str,
        action: str,
        success: bool
    ) -> None:
        """Audit access for compliance"""
        audit_event = await self.compliance_engine.audit_access(user_id, resource, action, success)
        await self._store_security_events([audit_event])

    async def _store_security_events(self, events: List[SecurityEvent]):
        """Store security events with rate limiting"""
        current_time = time.time()

        for event in events:
            # Rate limit security events per IP
            if event.source_ip not in self.event_rate_limiter:
                self.event_rate_limiter[event.source_ip] = []

            # Clean old events (older than 1 minute)
            self.event_rate_limiter[event.source_ip] = [
                event_time for event_time in self.event_rate_limiter[event.source_ip]
                if current_time - event_time < 60
            ]

            # Add current event
            self.event_rate_limiter[event.source_ip].append(current_time)

            # Store event
            self.security_events.append(event)

            # Record metrics
            SECURITY_EVENTS.labels(
                event_type=event.event_type.value,
                severity=event.severity,
                source=event.source_ip
            ).inc()

            # Log security event
            logger.warning(
                f"Security Event: {event.event_type.value} - {event.severity} - "
                f"IP: {event.source_ip} - User: {event.user_id} - "
                f"Details: {json.dumps(event.details)}"
            )

        # Cleanup old events to prevent memory issues
        if len(self.security_events) > self.max_events_in_memory:
            self.security_events = self.security_events[-self.max_events_in_memory:]

    async def get_security_stats(self) -> Dict[str, Any]:
        """Get comprehensive security statistics"""
        current_time = time.time()

        # Recent events (last hour)
        recent_events = [
            event for event in self.security_events
            if (current_time - event.timestamp.timestamp()) < 3600
        ]

        # Group by event type
        event_counts = {}
        for event in recent_events:
            event_type = event.event_type.value
            event_counts[event_type] = event_counts.get(event_type, 0) + 1

        # Threat statistics
        threat_counts = {}
        for event in recent_events:
            if event.threat_type:
                threat_type = event.threat_type.value
                threat_counts[threat_type] = threat_counts.get(threat_type, 0) + 1

        return {
            'total_events': len(self.security_events),
            'recent_events_1h': len(recent_events),
            'event_types': event_counts,
            'threat_types': threat_counts,
            'encryption_service': {
                'initialized': self.encryption_service.primary_key is not None,
                'last_key_rotation': self.encryption_service.last_rotation
            },
            'compliance_frameworks': [fw.value for fw in self.compliance_engine.enabled_frameworks]
        }

    async def health_check(self) -> bool:
        """Check security service health"""
        try:
            # Check encryption service
            if not self.encryption_service.primary_key:
                return False

            # Test encryption/decryption
            test_data = "health_check_test"
            encrypted = await self.encryption_service.encrypt(test_data)
            decrypted = await self.encryption_service.decrypt(encrypted)

            if decrypted != test_data:
                return False

            return True

        except Exception as e:
            logger.error(f"Security service health check failed: {e}")
            return False

    async def cleanup(self):
        """Cleanup security service resources"""
        # Clear sensitive data from memory
        self.security_events.clear()
        self.event_rate_limiter.clear()

        logger.info("Security Service cleanup completed")
    
    async def _detect_sql_injection(
        self, 
        request_data: Dict[str, Any], 
        source_ip: str, 
        user_id: Optional[str]
    ) -> List[SecurityEvent]:
        """Detect SQL injection attempts"""
        threats = []
        
        # Check all string values in request
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in self.sql_injection_patterns:
                    if re.search(pattern, value.lower()):
                        threat = SecurityEvent(
                            id=str(uuid.uuid4()),
                            event_type=SecurityEventType.THREAT_DETECTED,
                            severity='HIGH',
                            source_ip=source_ip,
                            user_id=user_id,
                            timestamp=datetime.utcnow(),
                            threat_type=ThreatType.SQL_INJECTION,
                            details={
                                'field': key,
                                'pattern_matched': pattern,
                                'suspicious_value': value[:100]  # Truncate for logging
                            },
                            action_taken='blocked'
                        )
                        threats.append(threat)
                        break
        
        return threats
    
    async def _detect_xss(
        self, 
        request_data: Dict[str, Any], 
        source_ip: str, 
        user_id: Optional[str]
    ) -> List[SecurityEvent]:
        """Detect XSS attempts"""
        threats = []
        
        # Check all string values in request
        for key, value in request_data.items():
            if isinstance(value, str):
                for pattern in self.xss_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        threat = SecurityEvent(
                            id=str(uuid.uuid4()),
                            event_type=SecurityEventType.THREAT_DETECTED,
                            severity='HIGH',
                            source_ip=source_ip,
                            user_id=user_id,
                            timestamp=datetime.utcnow(),
                            threat_type=ThreatType.XSS_ATTEMPT,
                            details={
                                'field': key,
                                'pattern_matched': pattern,
                                'suspicious_value': value[:100]
                            },
                            action_taken='blocked'
                        )
                        threats.append(threat)
                        break
        
        return threats
    
    async def _detect_brute_force(
        self, 
        source_ip: str, 
        user_id: Optional[str]
    ) -> List[SecurityEvent]:
        """Detect brute force attacks"""
        threats = []
        current_time = time.time()
        
        # Track IP-based attempts
        if source_ip not in self.ip_reputation:
            self.ip_reputation[source_ip] = {
                'attempts': [],
                'last_attempt': current_time
            }
        
        ip_data = self.ip_reputation[source_ip]
        
        # Clean old attempts (older than 1 minute)
        ip_data['attempts'] = [
            attempt for attempt in ip_data['attempts']
            if current_time - attempt < 60
        ]
        
        # Add current attempt
        ip_data['attempts'].append(current_time)
        ip_data['last_attempt'] = current_time
        
        # Check if threshold exceeded
        if len(ip_data['attempts']) > self.brute_force_threshold:
            threat = SecurityEvent(
                id=str(uuid.uuid4()),
                event_type=SecurityEventType.THREAT_DETECTED,
                severity='CRITICAL',
                source_ip=source_ip,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                threat_type=ThreatType.BRUTE_FORCE,
                details={
                    'attempts_count': len(ip_data['attempts']),
                    'time_window': '60_seconds'
                },
                action_taken='rate_limited'
            )
            threats.append(threat)
        
        return threats
    
    async def _analyze_behavior(
        self, 
        source_ip: str, 
        user_id: Optional[str], 
        request_data: Dict[str, Any]
    ) -> List[SecurityEvent]:
        """Analyze behavioral patterns for anomalies"""
        threats = []
        
        # Simple behavioral analysis - can be enhanced with ML
        current_time = time.time()
        
        # Track user behavior
        if user_id:
            if user_id not in self.user_behavior:
                self.user_behavior[user_id] = {
                    'request_times': [],
                    'request_patterns': {}
                }
            
            user_data = self.user_behavior[user_id]
            
            # Clean old requests (older than 1 minute)
            user_data['request_times'] = [
                req_time for req_time in user_data['request_times']
                if current_time - req_time < 60
            ]
            
            # Add current request
            user_data['request_times'].append(current_time)
            
            # Check for suspicious activity
            if len(user_data['request_times']) > self.suspicious_activity_threshold:
                threat = SecurityEvent(
                    id=str(uuid.uuid4()),
                    event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                    severity='MEDIUM',
                    source_ip=source_ip,
                    user_id=user_id,
                    timestamp=datetime.utcnow(),
                    details={
                        'requests_per_minute': len(user_data['request_times']),
                        'threshold': self.suspicious_activity_threshold
                    },
                    action_taken='monitored'
                )
                threats.append(threat)
        
        return threats
