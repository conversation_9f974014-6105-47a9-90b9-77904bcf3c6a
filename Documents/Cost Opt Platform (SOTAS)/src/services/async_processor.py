"""
Async Processing & Queue Management Service
FAANG+ implementation with backpressure control, load shedding, and graceful shutdown
Designed for 100M+ user scale with TikTok-level performance optimization
"""

import asyncio
import logging
import time
import signal
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import uuid
import json

from opentelemetry import trace
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, Summary

from src.core.config import get_settings
from src.core.models import OptimizationRequest, OptimizationResponse

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)


class TaskStatus(Enum):
    """Task processing status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ProcessingTask:
    """Represents a processing task"""
    task_id: str
    task_type: str
    payload: Dict[str, Any]
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

# Prometheus metrics for async processing monitoring
QUEUE_SIZE = Gauge(
    'async_queue_size',
    'Current queue size',
    ['queue_name', 'priority']
)

PROCESSING_LATENCY = Histogram(
    'async_processing_latency_seconds',
    'Task processing latency',
    ['task_type', 'priority']
)

TASKS_PROCESSED = Counter(
    'async_tasks_processed_total',
    'Total tasks processed',
    ['task_type', 'status']
)

BACKPRESSURE_EVENTS = Counter(
    'async_backpressure_events_total',
    'Backpressure events triggered',
    ['queue_name', 'reason']
)

WORKER_UTILIZATION = Gauge(
    'async_worker_utilization',
    'Worker utilization percentage',
    ['worker_pool']
)


class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 0    # Health checks, system critical
    HIGH = 1        # Real-time optimization requests
    MEDIUM = 2      # Batch processing
    LOW = 3         # Background tasks, analytics
    BULK = 4        # Bulk operations, cleanup


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


@dataclass
class AsyncTask:
    """Async task with metadata and execution context"""
    id: str
    task_type: str
    priority: TaskPriority
    payload: Dict[str, Any]
    callback: Optional[Callable] = None
    timeout: float = 30.0
    max_retries: int = 3
    retry_count: int = 0
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    
    def get_age(self) -> float:
        """Get task age in seconds"""
        return time.time() - self.created_at
    
    def get_processing_time(self) -> Optional[float]:
        """Get processing time if completed"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class BackpressureController:
    """Intelligent backpressure control for queue management"""
    
    def __init__(self, max_queue_size: int = 10000, load_threshold: float = 0.8):
        self.max_queue_size = max_queue_size
        self.load_threshold = load_threshold
        self.current_load = 0.0
        self.shed_probability = 0.0
        
        # Adaptive thresholds
        self.high_priority_threshold = int(max_queue_size * 0.6)
        self.medium_priority_threshold = int(max_queue_size * 0.4)
        self.low_priority_threshold = int(max_queue_size * 0.2)
    
    def should_accept_task(self, task: AsyncTask, current_queue_size: int) -> bool:
        """Determine if task should be accepted based on backpressure"""
        
        # Always accept critical tasks
        if task.priority == TaskPriority.CRITICAL:
            return True
        
        # Check queue size limits by priority
        if task.priority == TaskPriority.HIGH and current_queue_size >= self.high_priority_threshold:
            BACKPRESSURE_EVENTS.labels(queue_name='main', reason='high_priority_limit').inc()
            return False
        
        if task.priority == TaskPriority.MEDIUM and current_queue_size >= self.medium_priority_threshold:
            BACKPRESSURE_EVENTS.labels(queue_name='main', reason='medium_priority_limit').inc()
            return False
        
        if task.priority in [TaskPriority.LOW, TaskPriority.BULK] and current_queue_size >= self.low_priority_threshold:
            BACKPRESSURE_EVENTS.labels(queue_name='main', reason='low_priority_limit').inc()
            return False
        
        # Probabilistic load shedding for non-critical tasks
        if self.current_load > self.load_threshold and task.priority != TaskPriority.CRITICAL:
            if self.shed_probability > 0 and time.time() % 1.0 < self.shed_probability:
                BACKPRESSURE_EVENTS.labels(queue_name='main', reason='load_shedding').inc()
                return False
        
        return True
    
    def update_load_metrics(self, queue_size: int, active_workers: int, total_workers: int):
        """Update load metrics for backpressure calculation"""
        queue_load = queue_size / self.max_queue_size
        worker_load = active_workers / total_workers if total_workers > 0 else 0
        
        # Combined load metric
        self.current_load = max(queue_load, worker_load)
        
        # Calculate shed probability based on load
        if self.current_load > self.load_threshold:
            excess_load = self.current_load - self.load_threshold
            self.shed_probability = min(0.5, excess_load * 2)  # Max 50% shedding
        else:
            self.shed_probability = 0.0


class AsyncTaskQueue:
    """Priority-based async task queue with intelligent scheduling"""
    
    def __init__(self, name: str, max_size: int = 10000):
        self.name = name
        self.max_size = max_size
        
        # Priority queues (lower number = higher priority)
        self.queues: Dict[TaskPriority, asyncio.Queue] = {
            priority: asyncio.Queue(maxsize=max_size // len(TaskPriority))
            for priority in TaskPriority
        }
        
        # Task tracking
        self.pending_tasks: Dict[str, AsyncTask] = {}
        self.running_tasks: Dict[str, AsyncTask] = {}
        self.completed_tasks: Dict[str, AsyncTask] = {}
        
        # Backpressure controller
        self.backpressure = BackpressureController(max_size)
        
        # Statistics
        self.stats = {
            'total_enqueued': 0,
            'total_processed': 0,
            'total_failed': 0,
            'total_rejected': 0
        }
    
    async def enqueue(self, task: AsyncTask) -> bool:
        """Enqueue task with backpressure control"""
        current_size = self.get_total_size()
        
        # Check backpressure
        if not self.backpressure.should_accept_task(task, current_size):
            self.stats['total_rejected'] += 1
            logger.warning(f"Task {task.id} rejected due to backpressure")
            return False
        
        try:
            # Add to appropriate priority queue
            await self.queues[task.priority].put(task)
            self.pending_tasks[task.id] = task
            self.stats['total_enqueued'] += 1
            
            # Update metrics
            QUEUE_SIZE.labels(queue_name=self.name, priority=task.priority.name).set(
                self.queues[task.priority].qsize()
            )
            
            logger.debug(f"Task {task.id} enqueued with priority {task.priority.name}")
            return True
            
        except asyncio.QueueFull:
            self.stats['total_rejected'] += 1
            logger.warning(f"Queue full, rejected task {task.id}")
            return False
    
    async def dequeue(self) -> Optional[AsyncTask]:
        """Dequeue highest priority task"""
        # Check queues in priority order
        for priority in TaskPriority:
            queue = self.queues[priority]
            if not queue.empty():
                try:
                    task = await asyncio.wait_for(queue.get(), timeout=0.1)
                    
                    # Move from pending to running
                    if task.id in self.pending_tasks:
                        del self.pending_tasks[task.id]
                    self.running_tasks[task.id] = task
                    task.status = TaskStatus.RUNNING
                    task.started_at = time.time()
                    
                    # Update metrics
                    QUEUE_SIZE.labels(queue_name=self.name, priority=priority.name).set(queue.qsize())
                    
                    return task
                    
                except asyncio.TimeoutError:
                    continue
        
        return None
    
    def complete_task(self, task: AsyncTask, result: Any = None, error: str = None):
        """Mark task as completed"""
        task.completed_at = time.time()
        task.result = result
        task.error = error
        task.status = TaskStatus.COMPLETED if error is None else TaskStatus.FAILED
        
        # Move from running to completed
        if task.id in self.running_tasks:
            del self.running_tasks[task.id]
        self.completed_tasks[task.id] = task
        
        # Update statistics
        if error is None:
            self.stats['total_processed'] += 1
            TASKS_PROCESSED.labels(task_type=task.task_type, status='completed').inc()
        else:
            self.stats['total_failed'] += 1
            TASKS_PROCESSED.labels(task_type=task.task_type, status='failed').inc()
        
        # Record processing latency
        processing_time = task.get_processing_time()
        if processing_time:
            PROCESSING_LATENCY.labels(
                task_type=task.task_type, 
                priority=task.priority.name
            ).observe(processing_time)
    
    def get_total_size(self) -> int:
        """Get total queue size across all priorities"""
        return sum(queue.qsize() for queue in self.queues.values())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        return {
            'name': self.name,
            'total_size': self.get_total_size(),
            'pending_tasks': len(self.pending_tasks),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len(self.completed_tasks),
            'queue_sizes': {
                priority.name: queue.qsize() 
                for priority, queue in self.queues.items()
            },
            'stats': self.stats.copy(),
            'backpressure': {
                'current_load': self.backpressure.current_load,
                'shed_probability': self.backpressure.shed_probability
            }
        }


class AsyncProcessor:
    """
    Production-grade async processor implementing FAANG+ standards
    Supports backpressure control, load shedding, and graceful shutdown
    """

    def __init__(self, max_workers: int = None, max_queue_size: int = 10000):
        self.settings = get_settings()
        self.max_workers = max_workers or self.settings.max_concurrent_optimizations
        self.max_queue_size = max_queue_size

        # Task queue
        self.task_queue = AsyncTaskQueue("main", max_queue_size)

        # Worker management
        self.workers: List[asyncio.Task] = []
        self.worker_semaphore = asyncio.Semaphore(self.max_workers)
        self.active_workers = 0

        # Shutdown management
        self.shutdown_event = asyncio.Event()
        self.graceful_shutdown_timeout = 30.0

        # Task handlers
        self.task_handlers: Dict[str, Callable] = {}

        # Thread pool for CPU-intensive tasks
        self.thread_pool = ThreadPoolExecutor(
            max_workers=min(32, (self.max_workers or 4) * 2),
            thread_name_prefix="async_processor"
        )

    async def initialize(self):
        """Initialize the async processor"""
        logger.info(f"Initializing AsyncProcessor with {self.max_workers} workers")

        # Start worker tasks
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self.workers.append(worker)

        # Register signal handlers for graceful shutdown
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, self._signal_handler)

        logger.info("AsyncProcessor initialized successfully")

    def register_handler(self, task_type: str, handler: Callable):
        """Register a task handler"""
        self.task_handlers[task_type] = handler
        logger.debug(f"Registered handler for task type: {task_type}")

    async def submit_task(
        self,
        task_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        timeout: float = 30.0,
        callback: Optional[Callable] = None
    ) -> str:
        """Submit a task for async processing"""

        task = AsyncTask(
            id=str(uuid.uuid4()),
            task_type=task_type,
            priority=priority,
            payload=payload,
            timeout=timeout,
            callback=callback
        )

        success = await self.task_queue.enqueue(task)
        if not success:
            raise RuntimeError(f"Failed to enqueue task {task.id} due to backpressure")

        return task.id

    async def get_task_result(self, task_id: str, timeout: float = None) -> Any:
        """Get result of a completed task"""
        start_time = time.time()

        while True:
            # Check if task is completed
            if task_id in self.task_queue.completed_tasks:
                task = self.task_queue.completed_tasks[task_id]
                if task.error:
                    raise RuntimeError(f"Task {task_id} failed: {task.error}")
                return task.result

            # Check timeout
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {task_id} result timeout")

            # Wait a bit before checking again
            await asyncio.sleep(0.1)

    async def _worker_loop(self, worker_name: str):
        """Main worker loop"""
        logger.debug(f"Worker {worker_name} started")

        while not self.shutdown_event.is_set():
            try:
                # Get next task
                task = await self.task_queue.dequeue()
                if not task:
                    await asyncio.sleep(0.1)
                    continue

                # Process task with semaphore
                async with self.worker_semaphore:
                    self.active_workers += 1
                    WORKER_UTILIZATION.labels(worker_pool='main').set(
                        self.active_workers / self.max_workers
                    )

                    try:
                        await self._process_task(task, worker_name)
                    finally:
                        self.active_workers -= 1
                        WORKER_UTILIZATION.labels(worker_pool='main').set(
                            self.active_workers / self.max_workers
                        )

            except asyncio.CancelledError:
                logger.info(f"Worker {worker_name} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}", exc_info=True)
                await asyncio.sleep(1)  # Prevent tight error loops

        logger.debug(f"Worker {worker_name} stopped")

    async def _process_task(self, task: AsyncTask, worker_name: str):
        """Process a single task"""
        with tracer.start_as_current_span("async_task_processing") as span:
            span.set_attribute("task_id", task.id)
            span.set_attribute("task_type", task.task_type)
            span.set_attribute("worker_name", worker_name)
            span.set_attribute("priority", task.priority.name)

            try:
                # Check if handler exists
                if task.task_type not in self.task_handlers:
                    raise ValueError(f"No handler registered for task type: {task.task_type}")

                handler = self.task_handlers[task.task_type]

                # Execute with timeout
                result = await asyncio.wait_for(
                    handler(task.payload),
                    timeout=task.timeout
                )

                # Mark as completed
                self.task_queue.complete_task(task, result=result)

                # Execute callback if provided
                if task.callback:
                    try:
                        await task.callback(task.id, result, None)
                    except Exception as e:
                        logger.error(f"Task callback failed: {e}")

                span.set_attribute("status", "completed")
                logger.debug(f"Task {task.id} completed successfully")

            except asyncio.TimeoutError:
                error_msg = f"Task {task.id} timed out after {task.timeout}s"
                self.task_queue.complete_task(task, error=error_msg)
                span.set_attribute("status", "timeout")
                logger.warning(error_msg)

            except Exception as e:
                error_msg = f"Task {task.id} failed: {str(e)}"
                self.task_queue.complete_task(task, error=error_msg)
                span.record_exception(e)
                span.set_attribute("status", "failed")
                logger.error(error_msg, exc_info=True)

                # Execute error callback if provided
                if task.callback:
                    try:
                        await task.callback(task.id, None, error_msg)
                    except Exception as callback_error:
                        logger.error(f"Task error callback failed: {callback_error}")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        asyncio.create_task(self.shutdown())

    async def shutdown(self):
        """Graceful shutdown of the async processor"""
        logger.info("Starting graceful shutdown...")

        # Set shutdown event
        self.shutdown_event.set()

        # Wait for workers to finish current tasks
        if self.workers:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.workers, return_exceptions=True),
                    timeout=self.graceful_shutdown_timeout
                )
            except asyncio.TimeoutError:
                logger.warning("Graceful shutdown timeout, cancelling workers")
                for worker in self.workers:
                    worker.cancel()

        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)

        logger.info("AsyncProcessor shutdown completed")

    async def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive processor statistics"""
        queue_stats = self.task_queue.get_stats()

        return {
            'processor': {
                'max_workers': self.max_workers,
                'active_workers': self.active_workers,
                'worker_utilization': self.active_workers / self.max_workers,
                'registered_handlers': list(self.task_handlers.keys()),
                'shutdown_initiated': self.shutdown_event.is_set()
            },
            'queue': queue_stats,
            'thread_pool': {
                'max_workers': self.thread_pool._max_workers,
                'active_threads': self.thread_pool._threads,
            }
        }

    async def health_check(self) -> bool:
        """Check processor health"""
        try:
            # Check if workers are running
            if not self.workers or self.shutdown_event.is_set():
                return False

            # Check if queue is not completely full
            if self.task_queue.get_total_size() >= self.max_queue_size:
                return False

            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
