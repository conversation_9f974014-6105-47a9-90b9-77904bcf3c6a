"""
Production Monitoring & Observability Service
FAANG+ implementation with distributed tracing, real-time metrics, and intelligent alerting
Implements Google SRE practices with TikTok performance monitoring
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import uuid
import psutil
import os

from opentelemetry import trace, metrics
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, Summary

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Core monitoring metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

OPTIMIZATION_METRICS = Summary(
    'optimization_performance',
    'Optimization performance metrics',
    ['optimization_type', 'model']
)

SYSTEM_METRICS = Gauge(
    'system_resource_usage',
    'System resource usage',
    ['resource_type']
)

ERROR_RATE = Counter(
    'errors_total',
    'Total errors',
    ['error_type', 'component', 'severity']
)

SLA_VIOLATIONS = Counter(
    'sla_violations_total',
    'SLA violations',
    ['sla_type', 'severity']
)


class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SLAType(Enum):
    """Types of SLA monitoring"""
    LATENCY = "latency"
    AVAILABILITY = "availability"
    ERROR_RATE = "error_rate"
    THROUGHPUT = "throughput"
    COST_EFFICIENCY = "cost_efficiency"


@dataclass
class Alert:
    """Alert definition with escalation policies"""
    id: str
    title: str
    description: str
    severity: AlertSeverity
    component: str
    timestamp: datetime
    metrics: Dict[str, float]
    threshold_breached: str
    escalation_level: int = 0
    acknowledged: bool = False
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class SLATarget:
    """SLA target definition"""
    name: str
    sla_type: SLAType
    target_value: float
    measurement_window: int  # seconds
    alert_threshold: float
    critical_threshold: float


class PerformanceProfiler:
    """Real-time performance profiling with bottleneck detection"""
    
    def __init__(self):
        self.active_traces: Dict[str, Dict[str, Any]] = {}
        self.bottleneck_patterns: List[Dict[str, Any]] = []
        self.performance_baselines: Dict[str, float] = {}
    
    async def start_trace(self, operation: str, metadata: Dict[str, Any] = None) -> str:
        """Start performance trace"""
        trace_id = str(uuid.uuid4())
        
        self.active_traces[trace_id] = {
            'operation': operation,
            'start_time': time.time(),
            'metadata': metadata or {},
            'checkpoints': []
        }
        
        return trace_id
    
    async def add_checkpoint(self, trace_id: str, checkpoint_name: str, metadata: Dict[str, Any] = None):
        """Add checkpoint to active trace"""
        if trace_id in self.active_traces:
            checkpoint_time = time.time()
            self.active_traces[trace_id]['checkpoints'].append({
                'name': checkpoint_name,
                'timestamp': checkpoint_time,
                'elapsed': checkpoint_time - self.active_traces[trace_id]['start_time'],
                'metadata': metadata or {}
            })
    
    async def end_trace(self, trace_id: str) -> Dict[str, Any]:
        """End trace and analyze performance"""
        if trace_id not in self.active_traces:
            return {}
        
        trace_data = self.active_traces.pop(trace_id)
        end_time = time.time()
        total_duration = end_time - trace_data['start_time']
        
        # Analyze for bottlenecks
        bottlenecks = await self._detect_bottlenecks(trace_data, total_duration)
        
        performance_report = {
            'trace_id': trace_id,
            'operation': trace_data['operation'],
            'total_duration': total_duration,
            'checkpoints': trace_data['checkpoints'],
            'bottlenecks': bottlenecks,
            'baseline_comparison': self._compare_to_baseline(trace_data['operation'], total_duration)
        }
        
        # Update baselines
        self._update_baseline(trace_data['operation'], total_duration)
        
        return performance_report
    
    async def _detect_bottlenecks(self, trace_data: Dict[str, Any], total_duration: float) -> List[Dict[str, Any]]:
        """Detect performance bottlenecks in trace"""
        bottlenecks = []
        checkpoints = trace_data['checkpoints']
        
        if not checkpoints:
            return bottlenecks
        
        # Analyze checkpoint durations
        for i, checkpoint in enumerate(checkpoints):
            checkpoint_duration = checkpoint['elapsed']
            if i > 0:
                checkpoint_duration = checkpoint['elapsed'] - checkpoints[i-1]['elapsed']
            
            # Flag as bottleneck if takes >30% of total time
            if checkpoint_duration > (total_duration * 0.3):
                bottlenecks.append({
                    'checkpoint': checkpoint['name'],
                    'duration': checkpoint_duration,
                    'percentage_of_total': (checkpoint_duration / total_duration) * 100,
                    'severity': 'high' if checkpoint_duration > (total_duration * 0.5) else 'medium'
                })
        
        return bottlenecks
    
    def _compare_to_baseline(self, operation: str, duration: float) -> Dict[str, Any]:
        """Compare performance to baseline"""
        if operation not in self.performance_baselines:
            return {'status': 'no_baseline'}
        
        baseline = self.performance_baselines[operation]
        deviation = ((duration - baseline) / baseline) * 100
        
        return {
            'baseline_duration': baseline,
            'current_duration': duration,
            'deviation_percentage': deviation,
            'status': 'regression' if deviation > 20 else 'normal'
        }
    
    def _update_baseline(self, operation: str, duration: float):
        """Update performance baseline with exponential moving average"""
        if operation not in self.performance_baselines:
            self.performance_baselines[operation] = duration
        else:
            # Exponential moving average with alpha=0.1
            self.performance_baselines[operation] = (
                0.9 * self.performance_baselines[operation] + 0.1 * duration
            )


class AlertManager:
    """Intelligent alerting with noise reduction and escalation"""
    
    def __init__(self):
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.escalation_policies: Dict[str, List[Callable]] = {}
        self.noise_reduction_rules: List[Dict[str, Any]] = []
        
        # Alert thresholds
        self.alert_thresholds = {
            'error_rate': 0.05,  # 5%
            'latency_p99': 100,  # 100ms
            'cpu_usage': 80,     # 80%
            'memory_usage': 85,  # 85%
            'disk_usage': 90     # 90%
        }
    
    async def evaluate_alert_conditions(self, metrics: Dict[str, float]) -> List[Alert]:
        """Evaluate metrics against alert conditions"""
        new_alerts = []
        
        # Check error rate
        if metrics.get('error_rate', 0) > self.alert_thresholds['error_rate']:
            alert = Alert(
                id=str(uuid.uuid4()),
                title="High Error Rate Detected",
                description=f"Error rate {metrics['error_rate']:.2%} exceeds threshold {self.alert_thresholds['error_rate']:.2%}",
                severity=AlertSeverity.HIGH,
                component="application",
                timestamp=datetime.utcnow(),
                metrics=metrics,
                threshold_breached="error_rate"
            )
            new_alerts.append(alert)
        
        # Check latency
        if metrics.get('latency_p99', 0) > self.alert_thresholds['latency_p99']:
            alert = Alert(
                id=str(uuid.uuid4()),
                title="High Latency Detected",
                description=f"P99 latency {metrics['latency_p99']:.1f}ms exceeds threshold {self.alert_thresholds['latency_p99']}ms",
                severity=AlertSeverity.MEDIUM,
                component="performance",
                timestamp=datetime.utcnow(),
                metrics=metrics,
                threshold_breached="latency_p99"
            )
            new_alerts.append(alert)
        
        # Check system resources
        for resource in ['cpu_usage', 'memory_usage', 'disk_usage']:
            if metrics.get(resource, 0) > self.alert_thresholds[resource]:
                severity = AlertSeverity.CRITICAL if metrics[resource] > 95 else AlertSeverity.HIGH
                alert = Alert(
                    id=str(uuid.uuid4()),
                    title=f"High {resource.replace('_', ' ').title()} Detected",
                    description=f"{resource.replace('_', ' ').title()} {metrics[resource]:.1f}% exceeds threshold {self.alert_thresholds[resource]}%",
                    severity=severity,
                    component="infrastructure",
                    timestamp=datetime.utcnow(),
                    metrics=metrics,
                    threshold_breached=resource
                )
                new_alerts.append(alert)
        
        # Apply noise reduction
        filtered_alerts = await self._apply_noise_reduction(new_alerts)
        
        # Store and escalate alerts
        for alert in filtered_alerts:
            self.active_alerts[alert.id] = alert
            await self._escalate_alert(alert)
        
        return filtered_alerts
    
    async def _apply_noise_reduction(self, alerts: List[Alert]) -> List[Alert]:
        """Apply noise reduction rules to filter alerts"""
        filtered_alerts = []
        
        for alert in alerts:
            # Check for duplicate alerts in last 5 minutes
            recent_similar = [
                a for a in self.alert_history
                if (a.threshold_breached == alert.threshold_breached and
                    (datetime.utcnow() - a.timestamp).total_seconds() < 300)
            ]
            
            if len(recent_similar) < 3:  # Allow up to 3 similar alerts in 5 minutes
                filtered_alerts.append(alert)
            else:
                logger.debug(f"Alert suppressed due to noise reduction: {alert.title}")
        
        return filtered_alerts
    
    async def _escalate_alert(self, alert: Alert):
        """Escalate alert based on severity and policies"""
        component_handlers = self.escalation_policies.get(alert.component, [])
        
        if alert.severity == AlertSeverity.CRITICAL:
            # Immediate escalation for critical alerts
            for handler in component_handlers:
                try:
                    await handler(alert)
                except Exception as e:
                    logger.error(f"Alert escalation failed: {e}")
        
        # Record alert
        self.alert_history.append(alert)
        
        # Cleanup old history (keep last 1000 alerts)
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge an active alert"""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].acknowledged = True
            logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
            return True
        return False
    
    async def resolve_alert(self, alert_id: str, resolved_by: str) -> bool:
        """Resolve an active alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolution_time = datetime.utcnow()
            
            # Move to history
            self.alert_history.append(alert)
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert {alert_id} resolved by {resolved_by}")
            return True
        return False
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """Get alert statistics"""
        total_alerts = len(self.alert_history)
        active_count = len(self.active_alerts)
        
        # Calculate MTTR (Mean Time To Resolution)
        resolved_alerts = [a for a in self.alert_history if a.resolved and a.resolution_time]
        if resolved_alerts:
            resolution_times = [
                (a.resolution_time - a.timestamp).total_seconds()
                for a in resolved_alerts
            ]
            mttr = sum(resolution_times) / len(resolution_times)
        else:
            mttr = 0
        
        return {
            'total_alerts': total_alerts,
            'active_alerts': active_count,
            'mttr_seconds': mttr,
            'alert_rate_24h': len([
                a for a in self.alert_history
                if (datetime.utcnow() - a.timestamp).total_seconds() < 86400
            ])
        }


class SLAMonitor:
    """SLA monitoring with real-time tracking and violation detection"""

    def __init__(self):
        self.sla_targets = {
            'latency_p99': SLATarget(
                name="P99 Latency",
                sla_type=SLAType.LATENCY,
                target_value=50.0,  # 50ms
                measurement_window=300,  # 5 minutes
                alert_threshold=60.0,
                critical_threshold=100.0
            ),
            'availability': SLATarget(
                name="Service Availability",
                sla_type=SLAType.AVAILABILITY,
                target_value=99.9,  # 99.9%
                measurement_window=3600,  # 1 hour
                alert_threshold=99.5,
                critical_threshold=99.0
            ),
            'error_rate': SLATarget(
                name="Error Rate",
                sla_type=SLAType.ERROR_RATE,
                target_value=1.0,  # 1%
                measurement_window=300,  # 5 minutes
                alert_threshold=2.0,
                critical_threshold=5.0
            ),
            'throughput': SLATarget(
                name="Request Throughput",
                sla_type=SLAType.THROUGHPUT,
                target_value=1000.0,  # 1000 RPS
                measurement_window=300,  # 5 minutes
                alert_threshold=800.0,
                critical_threshold=500.0
            )
        }

        self.sla_violations: List[Dict[str, Any]] = []
        self.measurement_history: Dict[str, List[Dict[str, Any]]] = {}

    async def check_sla_compliance(self, metrics: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check SLA compliance and detect violations"""
        violations = []
        current_time = datetime.utcnow()

        for sla_name, target in self.sla_targets.items():
            metric_value = metrics.get(sla_name, 0)

            # Record measurement
            if sla_name not in self.measurement_history:
                self.measurement_history[sla_name] = []

            self.measurement_history[sla_name].append({
                'timestamp': current_time,
                'value': metric_value
            })

            # Clean old measurements outside window
            cutoff_time = current_time - timedelta(seconds=target.measurement_window)
            self.measurement_history[sla_name] = [
                m for m in self.measurement_history[sla_name]
                if m['timestamp'] > cutoff_time
            ]

            # Check for violations
            violation_detected = False
            severity = None

            if target.sla_type == SLAType.LATENCY:
                if metric_value > target.critical_threshold:
                    violation_detected = True
                    severity = 'critical'
                elif metric_value > target.alert_threshold:
                    violation_detected = True
                    severity = 'warning'

            elif target.sla_type == SLAType.AVAILABILITY:
                if metric_value < target.critical_threshold:
                    violation_detected = True
                    severity = 'critical'
                elif metric_value < target.alert_threshold:
                    violation_detected = True
                    severity = 'warning'

            elif target.sla_type == SLAType.ERROR_RATE:
                if metric_value > target.critical_threshold:
                    violation_detected = True
                    severity = 'critical'
                elif metric_value > target.alert_threshold:
                    violation_detected = True
                    severity = 'warning'

            elif target.sla_type == SLAType.THROUGHPUT:
                if metric_value < target.critical_threshold:
                    violation_detected = True
                    severity = 'critical'
                elif metric_value < target.alert_threshold:
                    violation_detected = True
                    severity = 'warning'

            if violation_detected:
                violation = {
                    'sla_name': sla_name,
                    'target_value': target.target_value,
                    'actual_value': metric_value,
                    'severity': severity,
                    'timestamp': current_time,
                    'measurement_window': target.measurement_window
                }
                violations.append(violation)
                self.sla_violations.append(violation)

                # Record Prometheus metric
                SLA_VIOLATIONS.labels(sla_type=target.sla_type.value, severity=severity).inc()

        return violations

    def get_sla_dashboard(self) -> Dict[str, Any]:
        """Get SLA dashboard data"""
        dashboard = {
            'current_status': {},
            'violation_summary': {},
            'trends': {}
        }

        current_time = datetime.utcnow()

        for sla_name, target in self.sla_targets.items():
            # Get recent measurements
            recent_measurements = [
                m for m in self.measurement_history.get(sla_name, [])
                if (current_time - m['timestamp']).total_seconds() < target.measurement_window
            ]

            if recent_measurements:
                current_value = recent_measurements[-1]['value']
                avg_value = sum(m['value'] for m in recent_measurements) / len(recent_measurements)

                # Determine status
                if target.sla_type in [SLAType.LATENCY, SLAType.ERROR_RATE]:
                    status = 'healthy' if current_value <= target.target_value else 'degraded'
                else:  # AVAILABILITY, THROUGHPUT
                    status = 'healthy' if current_value >= target.target_value else 'degraded'

                dashboard['current_status'][sla_name] = {
                    'current_value': current_value,
                    'target_value': target.target_value,
                    'average_value': avg_value,
                    'status': status,
                    'measurements_count': len(recent_measurements)
                }

        # Violation summary
        recent_violations = [
            v for v in self.sla_violations
            if (current_time - v['timestamp']).total_seconds() < 86400  # Last 24 hours
        ]

        dashboard['violation_summary'] = {
            'total_violations_24h': len(recent_violations),
            'critical_violations_24h': len([v for v in recent_violations if v['severity'] == 'critical']),
            'warning_violations_24h': len([v for v in recent_violations if v['severity'] == 'warning'])
        }

        return dashboard


class MonitoringService:
    """
    Production-grade monitoring and observability service
    Implements FAANG+ standards with comprehensive metrics, tracing, and alerting
    """

    def __init__(self):
        self.settings = get_settings()
        self.profiler = PerformanceProfiler()
        self.alert_manager = AlertManager()
        self.sla_monitor = SLAMonitor()

        # System monitoring
        self.system_metrics_interval = 30  # seconds
        self.monitoring_active = False
        self.monitor_task: Optional[asyncio.Task] = None

        # Metrics collection
        self.metrics_buffer: List[Dict[str, Any]] = []
        self.max_buffer_size = 10000

    async def initialize(self):
        """Initialize monitoring service"""
        logger.info("Initializing Production Monitoring Service...")

        # Initialize distributed tracing
        await self._setup_distributed_tracing()

        # Initialize metrics collection
        await self._setup_metrics_collection()

        # Start system monitoring
        await self.start_monitoring()

        logger.info("Production Monitoring Service initialized successfully")

    async def _setup_distributed_tracing(self):
        """Setup distributed tracing with Jaeger"""
        try:
            # Configure Jaeger exporter
            jaeger_exporter = JaegerExporter(
                agent_host_name="localhost",
                agent_port=6831,
            )

            # Setup trace provider
            trace.set_tracer_provider(TracerProvider())
            span_processor = BatchSpanProcessor(jaeger_exporter)
            trace.get_tracer_provider().add_span_processor(span_processor)

            logger.info("Distributed tracing initialized with Jaeger")

        except Exception as e:
            logger.warning(f"Failed to initialize distributed tracing: {e}")

    async def _setup_metrics_collection(self):
        """Setup Prometheus metrics collection"""
        try:
            # Start Prometheus metrics server
            prometheus_client.start_http_server(9090)
            logger.info("Prometheus metrics server started on port 9090")

        except Exception as e:
            logger.warning(f"Failed to start Prometheus server: {e}")

    async def start_monitoring(self):
        """Start system monitoring loop"""
        self.monitoring_active = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("System monitoring started")

    async def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("System monitoring stopped")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                metrics = await self._collect_system_metrics()

                # Check SLA compliance
                sla_violations = await self.sla_monitor.check_sla_compliance(metrics)

                # Evaluate alert conditions
                alerts = await self.alert_manager.evaluate_alert_conditions(metrics)

                # Update Prometheus metrics
                await self._update_prometheus_metrics(metrics)

                # Buffer metrics for analysis
                self._buffer_metrics(metrics)

                await asyncio.sleep(self.system_metrics_interval)

            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(self.system_metrics_interval)

    async def _collect_system_metrics(self) -> Dict[str, float]:
        """Collect comprehensive system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)

            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free_gb = disk.free / (1024**3)

            # Network metrics
            network = psutil.net_io_counters()

            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024**2)  # MB
            process_cpu = process.cpu_percent()

            metrics = {
                'cpu_usage': cpu_percent,
                'cpu_count': cpu_count,
                'memory_usage': memory_percent,
                'memory_available_gb': memory_available_gb,
                'disk_usage': disk_percent,
                'disk_free_gb': disk_free_gb,
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'process_memory_mb': process_memory,
                'process_cpu_percent': process_cpu,
                'timestamp': time.time()
            }

            return metrics

        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            return {}

    async def _update_prometheus_metrics(self, metrics: Dict[str, float]):
        """Update Prometheus metrics"""
        try:
            for metric_name, value in metrics.items():
                if metric_name != 'timestamp':
                    SYSTEM_METRICS.labels(resource_type=metric_name).set(value)

        except Exception as e:
            logger.error(f"Failed to update Prometheus metrics: {e}")

    def _buffer_metrics(self, metrics: Dict[str, Any]):
        """Buffer metrics for analysis"""
        self.metrics_buffer.append(metrics)

        # Cleanup old metrics
        if len(self.metrics_buffer) > self.max_buffer_size:
            self.metrics_buffer = self.metrics_buffer[-self.max_buffer_size:]

    async def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard"""
        # Get latest metrics
        latest_metrics = self.metrics_buffer[-1] if self.metrics_buffer else {}

        # Get SLA dashboard
        sla_dashboard = self.sla_monitor.get_sla_dashboard()

        # Get alert stats
        alert_stats = self.alert_manager.get_alert_stats()

        # Calculate uptime
        uptime_seconds = time.time() - (self.metrics_buffer[0]['timestamp'] if self.metrics_buffer else time.time())

        return {
            'system_health': {
                'status': 'healthy' if latest_metrics.get('cpu_usage', 0) < 80 else 'degraded',
                'uptime_seconds': uptime_seconds,
                'last_updated': datetime.utcnow().isoformat()
            },
            'current_metrics': latest_metrics,
            'sla_status': sla_dashboard,
            'alerts': {
                'active_count': len(self.alert_manager.active_alerts),
                'stats': alert_stats
            },
            'performance': {
                'active_traces': len(self.profiler.active_traces),
                'baseline_operations': len(self.profiler.performance_baselines)
            }
        }

    async def create_custom_alert(
        self,
        metric_name: str,
        threshold: float,
        comparison: str = 'greater_than',
        severity: AlertSeverity = AlertSeverity.MEDIUM
    ):
        """Create custom alert rule"""
        self.alert_manager.alert_thresholds[metric_name] = threshold
        logger.info(f"Custom alert created: {metric_name} {comparison} {threshold}")

    async def cleanup(self):
        """Cleanup monitoring service"""
        await self.stop_monitoring()
        logger.info("Monitoring Service cleanup completed")
