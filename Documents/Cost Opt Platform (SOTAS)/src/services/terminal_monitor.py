"""
Terminal Monitoring System
FAANG+ implementation with real-time process monitoring, resource tracking, and intelligent alerting
Integrates with GitHub for repository monitoring and cost optimization insights
"""

import asyncio
import logging
import time
import psutil
import subprocess
import json
import os
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import aiohttp
import aiofiles

from opentelemetry import trace
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for terminal monitoring
TERMINAL_SESSIONS = Gauge(
    'terminal_sessions_active',
    'Active terminal sessions',
    ['session_type', 'user']
)

COMMAND_EXECUTIONS = Counter(
    'terminal_commands_total',
    'Total terminal commands executed',
    ['command', 'status', 'user']
)

RESOURCE_USAGE = Gauge(
    'terminal_resource_usage',
    'Resource usage by terminal processes',
    ['resource_type', 'process_name']
)

GITHUB_API_CALLS = Counter(
    'github_api_calls_total',
    'GitHub API calls made',
    ['endpoint', 'status']
)


class SessionType(Enum):
    """Types of terminal sessions"""
    INTERACTIVE = "interactive"
    BATCH = "batch"
    MONITORING = "monitoring"
    DEPLOYMENT = "deployment"
    TESTING = "testing"


class CommandStatus(Enum):
    """Command execution status"""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    KILLED = "killed"


@dataclass
class TerminalSession:
    """Terminal session metadata"""
    session_id: str
    session_type: SessionType
    user: str
    start_time: datetime
    last_activity: datetime
    commands_executed: int = 0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    is_active: bool = True
    process_id: Optional[int] = None
    working_directory: str = "/"
    environment_vars: Dict[str, str] = field(default_factory=dict)


@dataclass
class CommandExecution:
    """Command execution record"""
    command_id: str
    session_id: str
    command: str
    args: List[str]
    start_time: datetime
    end_time: Optional[datetime] = None
    exit_code: Optional[int] = None
    status: CommandStatus = CommandStatus.SUCCESS
    stdout: str = ""
    stderr: str = ""
    resource_usage: Dict[str, float] = field(default_factory=dict)


class GitHubIntegration:
    """GitHub integration for repository monitoring and cost insights"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or os.getenv('GITHUB_TOKEN')
        self.base_url = "https://api.github.com"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self):
        """Initialize GitHub integration"""
        headers = {}
        if self.token:
            headers['Authorization'] = f'token {self.token}'
            headers['Accept'] = 'application/vnd.github.v3+json'
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        
        logger.info("GitHub integration initialized")
    
    async def get_repository_info(self, owner: str, repo: str) -> Dict[str, Any]:
        """Get repository information"""
        if not self.session:
            await self.initialize()
        
        try:
            async with self.session.get(f"{self.base_url}/repos/{owner}/{repo}") as response:
                GITHUB_API_CALLS.labels(endpoint='repos', status=str(response.status)).inc()
                
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"GitHub API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"GitHub API request failed: {e}")
            GITHUB_API_CALLS.labels(endpoint='repos', status='error').inc()
            return {}
    
    async def get_commit_activity(self, owner: str, repo: str, since: datetime) -> List[Dict[str, Any]]:
        """Get recent commit activity"""
        if not self.session:
            await self.initialize()
        
        try:
            params = {'since': since.isoformat()}
            async with self.session.get(
                f"{self.base_url}/repos/{owner}/{repo}/commits",
                params=params
            ) as response:
                GITHUB_API_CALLS.labels(endpoint='commits', status=str(response.status)).inc()
                
                if response.status == 200:
                    return await response.json()
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"GitHub commits API failed: {e}")
            return []
    
    async def get_workflow_runs(self, owner: str, repo: str) -> List[Dict[str, Any]]:
        """Get GitHub Actions workflow runs"""
        if not self.session:
            await self.initialize()
        
        try:
            async with self.session.get(
                f"{self.base_url}/repos/{owner}/{repo}/actions/runs"
            ) as response:
                GITHUB_API_CALLS.labels(endpoint='actions', status=str(response.status)).inc()
                
                if response.status == 200:
                    data = await response.json()
                    return data.get('workflow_runs', [])
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"GitHub Actions API failed: {e}")
            return []
    
    async def cleanup(self):
        """Cleanup GitHub integration"""
        if self.session:
            await self.session.close()


class ProcessMonitor:
    """Real-time process monitoring with resource tracking"""
    
    def __init__(self):
        self.monitored_processes: Dict[int, Dict[str, Any]] = {}
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
    async def start_monitoring(self, interval: float = 1.0):
        """Start process monitoring"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("Process monitoring started")
    
    async def stop_monitoring(self):
        """Stop process monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Process monitoring stopped")
    
    async def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                await self._update_process_metrics()
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Process monitoring error: {e}")
                await asyncio.sleep(interval)
    
    async def _update_process_metrics(self):
        """Update process metrics"""
        try:
            # Monitor all processes
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'cmdline']):
                try:
                    proc_info = proc.info
                    pid = proc_info['pid']
                    name = proc_info['name']
                    
                    # Filter for relevant processes
                    if self._is_relevant_process(name, proc_info.get('cmdline', [])):
                        cpu_percent = proc_info['cpu_percent'] or 0.0
                        memory_mb = (proc_info['memory_info'].rss / 1024 / 1024) if proc_info['memory_info'] else 0.0
                        
                        # Update metrics
                        RESOURCE_USAGE.labels(resource_type='cpu_percent', process_name=name).set(cpu_percent)
                        RESOURCE_USAGE.labels(resource_type='memory_mb', process_name=name).set(memory_mb)
                        
                        # Store process info
                        self.monitored_processes[pid] = {
                            'name': name,
                            'cpu_percent': cpu_percent,
                            'memory_mb': memory_mb,
                            'last_updated': time.time()
                        }
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"Process metrics update failed: {e}")
    
    def _is_relevant_process(self, name: str, cmdline: List[str]) -> bool:
        """Check if process is relevant for monitoring"""
        relevant_names = {
            'python', 'uvicorn', 'gunicorn', 'redis-server', 'postgres',
            'docker', 'node', 'npm', 'git', 'bash', 'zsh'
        }
        
        relevant_keywords = {
            'cost-optimizer', 'claude', 'optimization', 'api', 'server'
        }
        
        # Check process name
        if any(rel_name in name.lower() for rel_name in relevant_names):
            return True
        
        # Check command line
        cmdline_str = ' '.join(cmdline).lower()
        if any(keyword in cmdline_str for keyword in relevant_keywords):
            return True
        
        return False
    
    def get_process_stats(self) -> Dict[str, Any]:
        """Get current process statistics"""
        current_time = time.time()
        active_processes = {
            pid: info for pid, info in self.monitored_processes.items()
            if current_time - info['last_updated'] < 10  # Active within last 10 seconds
        }
        
        total_cpu = sum(info['cpu_percent'] for info in active_processes.values())
        total_memory = sum(info['memory_mb'] for info in active_processes.values())
        
        return {
            'active_processes': len(active_processes),
            'total_cpu_percent': total_cpu,
            'total_memory_mb': total_memory,
            'processes': active_processes
        }


class TerminalMonitor:
    """
    Production-grade terminal monitoring system with GitHub integration
    Implements FAANG+ standards for process monitoring and cost optimization insights
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.sessions: Dict[str, TerminalSession] = {}
        self.command_history: List[CommandExecution] = []
        self.github_integration = GitHubIntegration()
        self.process_monitor = ProcessMonitor()
        
        # Configuration
        self.max_history_size = 10000
        self.session_timeout = 3600  # 1 hour
        self.monitoring_interval = 5.0  # 5 seconds
        
        # Monitoring state
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """Initialize terminal monitoring system"""
        logger.info("Initializing Terminal Monitor...")
        
        await self.github_integration.initialize()
        await self.process_monitor.start_monitoring()
        
        # Start monitoring loop
        await self.start_monitoring()
        
        logger.info("Terminal Monitor initialized successfully")
    
    async def start_monitoring(self):
        """Start terminal monitoring"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Terminal monitoring started")
    
    async def stop_monitoring(self):
        """Stop terminal monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        await self.process_monitor.stop_monitoring()
        logger.info("Terminal monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                await self._update_session_metrics()
                await self._cleanup_inactive_sessions()
                await self._collect_system_metrics()
                await asyncio.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"Terminal monitoring loop error: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _update_session_metrics(self):
        """Update session metrics"""
        active_sessions = 0
        for session in self.sessions.values():
            if session.is_active:
                active_sessions += 1
                TERMINAL_SESSIONS.labels(
                    session_type=session.session_type.value,
                    user=session.user
                ).set(1)
        
        logger.debug(f"Active terminal sessions: {active_sessions}")
    
    async def _cleanup_inactive_sessions(self):
        """Cleanup inactive sessions"""
        current_time = datetime.utcnow()
        inactive_sessions = []
        
        for session_id, session in self.sessions.items():
            if session.is_active:
                time_since_activity = (current_time - session.last_activity).total_seconds()
                if time_since_activity > self.session_timeout:
                    session.is_active = False
                    inactive_sessions.append(session_id)
        
        for session_id in inactive_sessions:
            logger.info(f"Marking session {session_id} as inactive")
    
    async def _collect_system_metrics(self):
        """Collect system-wide metrics"""
        try:
            # CPU and memory usage
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            RESOURCE_USAGE.labels(resource_type='system_cpu', process_name='system').set(cpu_percent)
            RESOURCE_USAGE.labels(resource_type='system_memory_percent', process_name='system').set(memory.percent)
            RESOURCE_USAGE.labels(resource_type='system_disk_percent', process_name='system').set(disk.percent)
            
        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")
    
    async def create_session(
        self,
        session_type: SessionType,
        user: str,
        working_directory: str = "/",
        environment_vars: Optional[Dict[str, str]] = None
    ) -> str:
        """Create a new terminal session"""
        session_id = f"session_{int(time.time())}_{user}"
        
        session = TerminalSession(
            session_id=session_id,
            session_type=session_type,
            user=user,
            start_time=datetime.utcnow(),
            last_activity=datetime.utcnow(),
            working_directory=working_directory,
            environment_vars=environment_vars or {}
        )
        
        self.sessions[session_id] = session
        
        logger.info(f"Created terminal session {session_id} for user {user}")
        return session_id
    
    async def execute_command(
        self,
        session_id: str,
        command: str,
        args: List[str] = None,
        timeout: int = 300
    ) -> CommandExecution:
        """Execute command in terminal session"""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        session.last_activity = datetime.utcnow()
        session.commands_executed += 1
        
        command_id = f"cmd_{int(time.time())}_{session_id}"
        args = args or []
        
        execution = CommandExecution(
            command_id=command_id,
            session_id=session_id,
            command=command,
            args=args,
            start_time=datetime.utcnow()
        )
        
        try:
            # Execute command
            full_command = [command] + args
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=session.working_directory,
                env={**os.environ, **session.environment_vars}
            )
            
            # Wait for completion with timeout
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )
            
            execution.end_time = datetime.utcnow()
            execution.exit_code = process.returncode
            execution.stdout = stdout.decode('utf-8', errors='replace')
            execution.stderr = stderr.decode('utf-8', errors='replace')
            execution.status = CommandStatus.SUCCESS if process.returncode == 0 else CommandStatus.FAILED
            
        except asyncio.TimeoutError:
            execution.end_time = datetime.utcnow()
            execution.status = CommandStatus.TIMEOUT
            execution.stderr = f"Command timed out after {timeout} seconds"
            
        except Exception as e:
            execution.end_time = datetime.utcnow()
            execution.status = CommandStatus.FAILED
            execution.stderr = str(e)
        
        # Record metrics
        COMMAND_EXECUTIONS.labels(
            command=command,
            status=execution.status.value,
            user=session.user
        ).inc()
        
        # Store execution
        self.command_history.append(execution)
        
        # Cleanup old history
        if len(self.command_history) > self.max_history_size:
            self.command_history = self.command_history[-self.max_history_size:]
        
        logger.info(f"Executed command {command} in session {session_id}: {execution.status.value}")
        return execution
    
    async def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get comprehensive monitoring statistics"""
        process_stats = self.process_monitor.get_process_stats()
        
        # Session statistics
        active_sessions = sum(1 for s in self.sessions.values() if s.is_active)
        total_commands = sum(s.commands_executed for s in self.sessions.values())
        
        # Command statistics
        recent_commands = [
            cmd for cmd in self.command_history
            if (datetime.utcnow() - cmd.start_time).total_seconds() < 3600
        ]
        
        command_success_rate = (
            len([cmd for cmd in recent_commands if cmd.status == CommandStatus.SUCCESS]) /
            len(recent_commands) if recent_commands else 0
        )
        
        return {
            'sessions': {
                'active': active_sessions,
                'total': len(self.sessions),
                'total_commands': total_commands
            },
            'commands': {
                'recent_1h': len(recent_commands),
                'success_rate': command_success_rate,
                'total_history': len(self.command_history)
            },
            'processes': process_stats,
            'system': {
                'monitoring_active': self.monitoring,
                'monitoring_interval': self.monitoring_interval
            }
        }
    
    async def cleanup(self):
        """Cleanup terminal monitor"""
        await self.stop_monitoring()
        await self.github_integration.cleanup()
        
        logger.info("Terminal Monitor cleanup completed")
