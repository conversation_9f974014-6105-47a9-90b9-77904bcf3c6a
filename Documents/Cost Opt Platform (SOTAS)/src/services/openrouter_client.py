"""
OpenRouter.ai Client Integration
FAANG+ implementation for unified LLM provider access with intelligent model selection
Routes all LLM requests through OpenRouter.ai with cost optimization and quality tracking
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import os

from opentelemetry import trace
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for OpenRouter monitoring
OPENROUTER_REQUESTS = Counter(
    'openrouter_requests_total',
    'Total OpenRouter API requests',
    ['model', 'status', 'provider']
)

OPENROUTER_LATENCY = Histogram(
    'openrouter_request_latency_seconds',
    'OpenRouter request latency',
    ['model', 'provider']
)

OPENROUTER_COSTS = Counter(
    'openrouter_costs_total',
    'Total OpenRouter costs',
    ['model', 'provider']
)

MODEL_QUALITY_SCORES = Gauge(
    'openrouter_model_quality_score',
    'Model quality scores',
    ['model', 'task_type']
)


class TaskType(Enum):
    """Types of tasks for model selection"""
    TEXT_OPTIMIZATION = "text_optimization"
    CODE_GENERATION = "code_generation"
    REASONING = "reasoning"
    CREATIVE_WRITING = "creative_writing"
    ANALYSIS = "analysis"
    SUMMARIZATION = "summarization"
    TRANSLATION = "translation"
    CONVERSATION = "conversation"


class ModelTier(Enum):
    """Model performance tiers"""
    ULTRA_PREMIUM = "ultra_premium"  # GPT-4, Claude-3.5-Sonnet
    PREMIUM = "premium"              # GPT-3.5-Turbo, Claude-3-Haiku
    STANDARD = "standard"            # Llama-3.1, Mistral-7B
    BUDGET = "budget"               # Smaller models for simple tasks


@dataclass
class ModelInfo:
    """OpenRouter model information"""
    id: str
    name: str
    provider: str
    tier: ModelTier
    context_length: int
    input_cost_per_token: float
    output_cost_per_token: float
    capabilities: List[TaskType]
    quality_scores: Dict[TaskType, float] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    is_available: bool = True


@dataclass
class OpenRouterRequest:
    """OpenRouter API request"""
    model: str
    messages: List[Dict[str, str]]
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop: Optional[List[str]] = None
    stream: bool = False
    user_id: Optional[str] = None
    task_type: Optional[TaskType] = None


@dataclass
class OpenRouterResponse:
    """OpenRouter API response"""
    id: str
    model: str
    content: str
    usage: Dict[str, int]
    cost: float
    latency_ms: float
    provider: str
    quality_score: Optional[float] = None
    finish_reason: Optional[str] = None
    error: Optional[str] = None


class ModelSelector:
    """Intelligent model selection based on task requirements and cost optimization"""
    
    def __init__(self):
        # Model database as of June 16, 2025
        self.models = {
            # Ultra Premium Models
            "anthropic/claude-3.5-sonnet": ModelInfo(
                id="anthropic/claude-3.5-sonnet",
                name="Claude 3.5 Sonnet",
                provider="anthropic",
                tier=ModelTier.ULTRA_PREMIUM,
                context_length=200000,
                input_cost_per_token=0.000003,
                output_cost_per_token=0.000015,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.REASONING, TaskType.CODE_GENERATION, TaskType.ANALYSIS],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.98,
                    TaskType.REASONING: 0.96,
                    TaskType.CODE_GENERATION: 0.94,
                    TaskType.ANALYSIS: 0.97
                }
            ),
            "openai/gpt-4-turbo": ModelInfo(
                id="openai/gpt-4-turbo",
                name="GPT-4 Turbo",
                provider="openai",
                tier=ModelTier.ULTRA_PREMIUM,
                context_length=128000,
                input_cost_per_token=0.00001,
                output_cost_per_token=0.00003,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.REASONING, TaskType.CODE_GENERATION, TaskType.CREATIVE_WRITING],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.95,
                    TaskType.REASONING: 0.98,
                    TaskType.CODE_GENERATION: 0.96,
                    TaskType.CREATIVE_WRITING: 0.97
                }
            ),
            
            # Premium Models
            "anthropic/claude-3-haiku": ModelInfo(
                id="anthropic/claude-3-haiku",
                name="Claude 3 Haiku",
                provider="anthropic",
                tier=ModelTier.PREMIUM,
                context_length=200000,
                input_cost_per_token=0.00000025,
                output_cost_per_token=0.00000125,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.SUMMARIZATION, TaskType.ANALYSIS],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.88,
                    TaskType.SUMMARIZATION: 0.92,
                    TaskType.ANALYSIS: 0.89
                }
            ),
            "openai/gpt-3.5-turbo": ModelInfo(
                id="openai/gpt-3.5-turbo",
                name="GPT-3.5 Turbo",
                provider="openai",
                tier=ModelTier.PREMIUM,
                context_length=16385,
                input_cost_per_token=0.0000005,
                output_cost_per_token=0.0000015,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.CONVERSATION, TaskType.SUMMARIZATION],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.85,
                    TaskType.CONVERSATION: 0.90,
                    TaskType.SUMMARIZATION: 0.87
                }
            ),
            
            # Standard Models
            "meta-llama/llama-3.1-70b-instruct": ModelInfo(
                id="meta-llama/llama-3.1-70b-instruct",
                name="Llama 3.1 70B Instruct",
                provider="meta",
                tier=ModelTier.STANDARD,
                context_length=131072,
                input_cost_per_token=0.00000088,
                output_cost_per_token=0.00000088,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.CODE_GENERATION, TaskType.REASONING],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.82,
                    TaskType.CODE_GENERATION: 0.85,
                    TaskType.REASONING: 0.83
                }
            ),
            "mistralai/mistral-7b-instruct": ModelInfo(
                id="mistralai/mistral-7b-instruct",
                name="Mistral 7B Instruct",
                provider="mistral",
                tier=ModelTier.STANDARD,
                context_length=32768,
                input_cost_per_token=0.00000025,
                output_cost_per_token=0.00000025,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.CONVERSATION, TaskType.SUMMARIZATION],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.78,
                    TaskType.CONVERSATION: 0.80,
                    TaskType.SUMMARIZATION: 0.79
                }
            ),
            
            # Budget Models
            "deepseek-ai/deepseek-coder-6.7b-instruct": ModelInfo(
                id="deepseek-ai/deepseek-coder-6.7b-instruct",
                name="DeepSeek Coder 6.7B",
                provider="deepseek",
                tier=ModelTier.BUDGET,
                context_length=16384,
                input_cost_per_token=0.00000014,
                output_cost_per_token=0.00000028,
                capabilities=[TaskType.CODE_GENERATION, TaskType.TEXT_OPTIMIZATION],
                quality_scores={
                    TaskType.CODE_GENERATION: 0.82,
                    TaskType.TEXT_OPTIMIZATION: 0.75
                }
            ),
            "qwen/qwen-2.5-7b-instruct": ModelInfo(
                id="qwen/qwen-2.5-7b-instruct",
                name="Qwen 2.5 7B Instruct",
                provider="alibaba",
                tier=ModelTier.BUDGET,
                context_length=32768,
                input_cost_per_token=0.00000018,
                output_cost_per_token=0.00000018,
                capabilities=[TaskType.TEXT_OPTIMIZATION, TaskType.TRANSLATION, TaskType.SUMMARIZATION],
                quality_scores={
                    TaskType.TEXT_OPTIMIZATION: 0.76,
                    TaskType.TRANSLATION: 0.84,
                    TaskType.SUMMARIZATION: 0.78
                }
            )
        }
    
    def select_model(
        self,
        task_type: TaskType,
        quality_threshold: float = 0.8,
        max_cost_per_token: Optional[float] = None,
        context_length_required: int = 4096,
        prefer_speed: bool = False
    ) -> ModelInfo:
        """Select optimal model based on requirements"""
        
        # Filter models by capabilities
        capable_models = [
            model for model in self.models.values()
            if task_type in model.capabilities and model.is_available
        ]
        
        if not capable_models:
            # Fallback to Claude 3.5 Sonnet for any task
            return self.models["anthropic/claude-3.5-sonnet"]
        
        # Filter by quality threshold
        quality_models = [
            model for model in capable_models
            if model.quality_scores.get(task_type, 0.0) >= quality_threshold
        ]
        
        if not quality_models:
            # Relax quality threshold and pick best available
            quality_models = sorted(
                capable_models,
                key=lambda m: m.quality_scores.get(task_type, 0.0),
                reverse=True
            )[:3]  # Top 3 models
        
        # Filter by cost if specified
        if max_cost_per_token:
            cost_filtered = [
                model for model in quality_models
                if max(model.input_cost_per_token, model.output_cost_per_token) <= max_cost_per_token
            ]
            if cost_filtered:
                quality_models = cost_filtered
        
        # Filter by context length
        context_filtered = [
            model for model in quality_models
            if model.context_length >= context_length_required
        ]
        if context_filtered:
            quality_models = context_filtered
        
        # Select based on optimization strategy
        if prefer_speed:
            # Prefer smaller, faster models
            return min(quality_models, key=lambda m: m.tier.value)
        else:
            # Optimize for cost-effectiveness (quality per dollar)
            def cost_effectiveness(model: ModelInfo) -> float:
                quality = model.quality_scores.get(task_type, 0.0)
                avg_cost = (model.input_cost_per_token + model.output_cost_per_token) / 2
                return quality / (avg_cost * 1000000) if avg_cost > 0 else 0
            
            return max(quality_models, key=cost_effectiveness)
    
    def get_fallback_chain(self, primary_model: str, task_type: TaskType) -> List[str]:
        """Get fallback model chain for reliability"""
        fallbacks = []
        
        # Add models from same tier
        primary = self.models.get(primary_model)
        if primary:
            same_tier_models = [
                model.id for model in self.models.values()
                if (model.tier == primary.tier and 
                    model.id != primary_model and
                    task_type in model.capabilities and
                    model.is_available)
            ]
            fallbacks.extend(same_tier_models[:2])
        
        # Add lower tier models as final fallbacks
        budget_models = [
            model.id for model in self.models.values()
            if (model.tier == ModelTier.BUDGET and
                task_type in model.capabilities and
                model.is_available)
        ]
        fallbacks.extend(budget_models[:1])
        
        return fallbacks


class OpenRouterClient:
    """
    Production-grade OpenRouter.ai client with intelligent model selection
    Implements FAANG+ standards for reliability, cost optimization, and quality tracking
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.settings = get_settings()
        self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        self.base_url = "https://openrouter.ai/api/v1"
        self.session: Optional[aiohttp.ClientSession] = None
        self.model_selector = ModelSelector()
        
        # Configuration
        self.timeout = 60.0
        self.max_retries = 3
        self.retry_delay = 1.0
        
        if not self.api_key:
            logger.warning("OpenRouter API key not provided - some features may be limited")
    
    async def initialize(self):
        """Initialize OpenRouter client"""
        headers = {
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://cost-optimizer.ai',
            'X-Title': 'Claude Cost Optimizer'
        }
        
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        
        logger.info("OpenRouter client initialized")
    
    async def generate(
        self,
        prompt: str,
        task_type: TaskType = TaskType.TEXT_OPTIMIZATION,
        quality_threshold: float = 0.8,
        max_cost_per_token: Optional[float] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        user_id: Optional[str] = None,
        model_override: Optional[str] = None
    ) -> OpenRouterResponse:
        """Generate text using optimal model selection"""
        
        if not self.session:
            await self.initialize()
        
        with tracer.start_as_current_span("openrouter_generate") as span:
            span.set_attribute("task_type", task_type.value)
            span.set_attribute("quality_threshold", quality_threshold)
            
            # Select optimal model
            if model_override:
                selected_model = self.model_selector.models.get(
                    model_override,
                    self.model_selector.models["anthropic/claude-3.5-sonnet"]
                )
            else:
                selected_model = self.model_selector.select_model(
                    task_type=task_type,
                    quality_threshold=quality_threshold,
                    max_cost_per_token=max_cost_per_token,
                    context_length_required=len(prompt) * 2  # Rough estimate
                )
            
            span.set_attribute("selected_model", selected_model.id)
            
            # Prepare request
            request = OpenRouterRequest(
                model=selected_model.id,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                user_id=user_id,
                task_type=task_type
            )
            
            # Execute with fallback chain
            fallback_models = [selected_model.id] + self.model_selector.get_fallback_chain(
                selected_model.id, task_type
            )
            
            for attempt, model_id in enumerate(fallback_models):
                try:
                    response = await self._execute_request(request, model_id)
                    if response.error is None:
                        return response
                    
                    logger.warning(f"Model {model_id} failed: {response.error}")
                    
                except Exception as e:
                    logger.error(f"Request to {model_id} failed: {e}")
                    if attempt == len(fallback_models) - 1:
                        raise
            
            # If all models fail, raise exception
            raise RuntimeError("All model fallbacks exhausted")
    
    async def _execute_request(self, request: OpenRouterRequest, model_id: str) -> OpenRouterResponse:
        """Execute single API request"""
        start_time = time.time()
        
        # Update request with specific model
        request.model = model_id
        model_info = self.model_selector.models.get(model_id)
        
        payload = {
            "model": request.model,
            "messages": request.messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "frequency_penalty": request.frequency_penalty,
            "presence_penalty": request.presence_penalty,
            "stream": request.stream
        }
        
        # Remove None values
        payload = {k: v for k, v in payload.items() if v is not None}
        
        try:
            async with self.session.post(
                f"{self.base_url}/chat/completions",
                json=payload
            ) as response:
                latency_ms = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    
                    # Extract response data
                    choice = data['choices'][0]
                    content = choice['message']['content']
                    usage = data.get('usage', {})
                    
                    # Calculate cost
                    input_tokens = usage.get('prompt_tokens', 0)
                    output_tokens = usage.get('completion_tokens', 0)
                    
                    cost = 0.0
                    if model_info:
                        cost = (
                            input_tokens * model_info.input_cost_per_token +
                            output_tokens * model_info.output_cost_per_token
                        )
                    
                    # Record metrics
                    provider = model_info.provider if model_info else "unknown"
                    OPENROUTER_REQUESTS.labels(model=model_id, status='success', provider=provider).inc()
                    OPENROUTER_LATENCY.labels(model=model_id, provider=provider).observe(latency_ms / 1000)
                    OPENROUTER_COSTS.labels(model=model_id, provider=provider).inc(cost)
                    
                    # Calculate quality score
                    quality_score = None
                    if model_info and request.task_type:
                        quality_score = model_info.quality_scores.get(request.task_type)
                    
                    return OpenRouterResponse(
                        id=data.get('id', ''),
                        model=model_id,
                        content=content,
                        usage=usage,
                        cost=cost,
                        latency_ms=latency_ms,
                        provider=provider,
                        quality_score=quality_score,
                        finish_reason=choice.get('finish_reason')
                    )
                
                else:
                    error_data = await response.text()
                    provider = model_info.provider if model_info else "unknown"
                    OPENROUTER_REQUESTS.labels(model=model_id, status='error', provider=provider).inc()
                    
                    return OpenRouterResponse(
                        id='',
                        model=model_id,
                        content='',
                        usage={},
                        cost=0.0,
                        latency_ms=latency_ms,
                        provider=provider,
                        error=f"HTTP {response.status}: {error_data}"
                    )
                    
        except Exception as e:
            latency_ms = (time.time() - start_time) * 1000
            provider = model_info.provider if model_info else "unknown"
            OPENROUTER_REQUESTS.labels(model=model_id, status='error', provider=provider).inc()
            
            return OpenRouterResponse(
                id='',
                model=model_id,
                content='',
                usage={},
                cost=0.0,
                latency_ms=latency_ms,
                provider=provider,
                error=str(e)
            )
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get available models from OpenRouter"""
        if not self.session:
            await self.initialize()
        
        try:
            async with self.session.get(f"{self.base_url}/models") as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('data', [])
                else:
                    logger.error(f"Failed to fetch models: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error fetching models: {e}")
            return []
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        # Return local statistics since OpenRouter doesn't provide detailed usage API
        return {
            'models_available': len(self.model_selector.models),
            'total_requests': sum(
                metric.samples[0].value for metric in OPENROUTER_REQUESTS.collect()
                for sample in metric.samples if sample.name.endswith('_total')
            ),
            'total_cost': sum(
                metric.samples[0].value for metric in OPENROUTER_COSTS.collect()
                for sample in metric.samples if sample.name.endswith('_total')
            ),
            'average_latency': sum(
                metric.samples[0].value for metric in OPENROUTER_LATENCY.collect()
                for sample in metric.samples if sample.name.endswith('_sum')
            ) / max(1, sum(
                metric.samples[0].value for metric in OPENROUTER_LATENCY.collect()
                for sample in metric.samples if sample.name.endswith('_count')
            ))
        }
    
    async def cleanup(self):
        """Cleanup OpenRouter client"""
        if self.session:
            await self.session.close()
        
        logger.info("OpenRouter client cleanup completed")
