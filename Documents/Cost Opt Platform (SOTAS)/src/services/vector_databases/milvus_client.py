"""
Milvus Vector Database Client
Production-grade implementation for semantic caching layer 5
Designed for 100M+ vectors with linear scaling and advanced sharding
"""

import asyncio
import logging
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from uuid import uuid4

from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, Index, SearchResult
)
from opentelemetry import trace
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for Milvus monitoring
MILVUS_OPERATIONS = Counter(
    'milvus_operations_total',
    'Total Milvus operations',
    ['operation', 'collection', 'status']
)

MILVUS_SEARCH_LATENCY = Histogram(
    'milvus_search_latency_seconds',
    'Milvus search operation latency',
    ['collection', 'index_type']
)

MILVUS_COLLECTION_SIZE = Gauge(
    'milvus_collection_size',
    'Number of entities in Milvus collection',
    ['collection']
)

MILVUS_MEMORY_USAGE = Gauge(
    'milvus_memory_usage_bytes',
    'Milvus memory usage in bytes',
    ['collection']
)


@dataclass
class MilvusSearchResult:
    """Milvus search result with comprehensive metadata"""
    id: str
    score: float
    entity: Dict[str, Any]
    distance: float


@dataclass
class MilvusConfig:
    """Milvus configuration optimized for 100M+ vectors"""
    host: str = "localhost"
    port: int = 19530
    collection_name: str = "optimization_vectors"
    
    # Vector configuration
    vector_dim: int = 384  # Sentence transformer default
    metric_type: str = "COSINE"  # COSINE, L2, IP
    
    # Index configuration for massive scale
    index_type: str = "IVF_FLAT"  # IVF_FLAT, IVF_SQ8, HNSW, ANNOY
    index_params: Dict[str, Any] = None
    
    # Search configuration
    search_params: Dict[str, Any] = None
    
    # Sharding configuration for horizontal scaling
    shards_num: int = 4  # Number of shards for horizontal scaling
    
    # Performance optimization
    consistency_level: str = "Eventually"  # Strong, Bounded, Eventually
    
    def __post_init__(self):
        if self.index_params is None:
            # Optimized index parameters for 100M+ vectors
            if self.index_type == "IVF_FLAT":
                self.index_params = {
                    "nlist": 16384,  # Number of cluster units
                    "metric_type": self.metric_type
                }
            elif self.index_type == "HNSW":
                self.index_params = {
                    "M": 16,  # Number of bi-directional links
                    "efConstruction": 200,  # Size of dynamic candidate list
                    "metric_type": self.metric_type
                }
            elif self.index_type == "IVF_SQ8":
                self.index_params = {
                    "nlist": 16384,
                    "metric_type": self.metric_type
                }
        
        if self.search_params is None:
            # Optimized search parameters
            if self.index_type == "IVF_FLAT":
                self.search_params = {
                    "nprobe": 128,  # Number of clusters to search
                    "metric_type": self.metric_type
                }
            elif self.index_type == "HNSW":
                self.search_params = {
                    "ef": 200,  # Size of dynamic candidate list
                    "metric_type": self.metric_type
                }
            elif self.index_type == "IVF_SQ8":
                self.search_params = {
                    "nprobe": 128,
                    "metric_type": self.metric_type
                }


class MilvusVectorDB:
    """
    Production-grade Milvus vector database client
    
    Features:
    - Horizontal scaling for 100M+ vectors with linear performance
    - Advanced sharding strategy for optimal distribution
    - Multiple index types optimized for different use cases
    - Circuit breaker pattern for fault tolerance
    - Comprehensive monitoring and performance tracking
    - Memory-efficient operations with batch processing
    """
    
    def __init__(self, config: Optional[MilvusConfig] = None):
        self.settings = get_settings()
        self.config = config or MilvusConfig(
            host=self.settings.milvus_host,
            port=self.settings.milvus_port,
            collection_name=self.settings.milvus_collection_name
        )
        
        self.collection: Optional[Collection] = None
        self.is_connected = False
        
        # Circuit breaker state
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_open = False
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_timeout = 60
        
        # Performance tracking
        self.stats = {
            'total_searches': 0,
            'total_inserts': 0,
            'total_deletes': 0,
            'total_entities': 0,
            'average_search_latency_ms': 0.0,
            'memory_usage_bytes': 0,
            'index_type': self.config.index_type,
            'shards_num': self.config.shards_num
        }
        
        # Local cache for frequently accessed results
        self.local_cache = {}
        self.local_cache_max_size = 1000
    
    async def connect(self) -> bool:
        """
        Connect to Milvus and initialize collection with sharding
        
        Returns:
            bool: True if connection successful
        """
        try:
            # Connect to Milvus
            connections.connect(
                alias="default",
                host=self.config.host,
                port=self.config.port
            )
            
            logger.info(f"Connected to Milvus at {self.config.host}:{self.config.port}")
            
            # Initialize collection with sharding
            await self._ensure_collection_exists()
            
            # Load collection into memory for search
            if self.collection:
                await asyncio.to_thread(self.collection.load)
                logger.info(f"Loaded collection {self.config.collection_name} into memory")
            
            self.is_connected = True
            self.circuit_open = False
            self.failure_count = 0
            
            # Update collection metrics
            await self._update_collection_metrics()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Milvus"""
        try:
            if self.collection:
                await asyncio.to_thread(self.collection.release)
            
            connections.disconnect("default")
            logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")
        
        self.is_connected = False
        self.collection = None
    
    async def search_vectors(
        self,
        query_vectors: List[List[float]],
        limit: int = 10,
        expr: Optional[str] = None,
        output_fields: Optional[List[str]] = None
    ) -> List[List[MilvusSearchResult]]:
        """
        Search for similar vectors with optimized performance for large scale
        
        Args:
            query_vectors: List of query vectors
            limit: Maximum number of results per query
            expr: Optional filter expression
            output_fields: Fields to return in results
            
        Returns:
            List of search results for each query vector
        """
        if not self._can_execute():
            logger.warning("Milvus circuit breaker is open, skipping search")
            return []
        
        with tracer.start_as_current_span("milvus_search") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("query_count", len(query_vectors))
            span.set_attribute("limit", limit)
            
            start_time = time.time()
            
            try:
                # Check local cache first
                cache_key = self._generate_cache_key(query_vectors, limit, expr)
                if cache_key in self.local_cache:
                    span.set_attribute("cache_hit", True)
                    return self.local_cache[cache_key]
                
                # Perform vector search with optimized parameters
                search_results = await asyncio.to_thread(
                    self.collection.search,
                    data=query_vectors,
                    anns_field="vector",
                    param=self.config.search_params,
                    limit=limit,
                    expr=expr,
                    output_fields=output_fields or ["id", "content", "metadata", "timestamp"],
                    consistency_level=self.config.consistency_level
                )
                
                # Convert to our result format
                results = []
                for search_result in search_results:
                    query_results = []
                    for hit in search_result:
                        entity_data = {field: hit.entity.get(field) for field in (output_fields or [])}
                        query_results.append(
                            MilvusSearchResult(
                                id=str(hit.id),
                                score=1.0 - hit.distance if self.config.metric_type == "COSINE" else hit.distance,
                                entity=entity_data,
                                distance=hit.distance
                            )
                        )
                    results.append(query_results)
                
                # Cache results
                self._cache_result(cache_key, results)
                
                # Record metrics
                search_latency = (time.time() - start_time) * 1000
                MILVUS_SEARCH_LATENCY.labels(
                    collection=self.config.collection_name,
                    index_type=self.config.index_type
                ).observe(search_latency / 1000)
                
                MILVUS_OPERATIONS.labels(
                    operation='search',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()
                
                # Update statistics
                self.stats['total_searches'] += 1
                self.stats['average_search_latency_ms'] = (
                    (self.stats['average_search_latency_ms'] * (self.stats['total_searches'] - 1) + search_latency) /
                    self.stats['total_searches']
                )
                
                span.set_attribute("total_results", sum(len(r) for r in results))
                span.set_attribute("search_latency_ms", search_latency)
                
                logger.debug(f"Milvus search completed: {len(results)} queries in {search_latency:.2f}ms")
                
                self._record_success()
                return results
                
            except Exception as e:
                logger.error(f"Milvus search failed: {e}")
                span.record_exception(e)
                
                MILVUS_OPERATIONS.labels(
                    operation='search',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()
                
                self._record_failure()
                return []

    async def insert_vectors(
        self,
        entities: List[Dict[str, Any]],
        batch_size: int = 1000
    ) -> bool:
        """
        Insert vectors with optimized batching for large scale operations

        Args:
            entities: List of entities to insert
            batch_size: Batch size for insertion (optimized for memory usage)

        Returns:
            bool: True if insertion successful
        """
        if not self._can_execute():
            logger.warning("Milvus circuit breaker is open, skipping insert")
            return False

        with tracer.start_as_current_span("milvus_insert") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("entity_count", len(entities))
            span.set_attribute("batch_size", batch_size)

            try:
                # Process in batches for memory efficiency
                total_inserted = 0
                for i in range(0, len(entities), batch_size):
                    batch = entities[i:i + batch_size]

                    # Prepare data for insertion
                    insert_data = self._prepare_insert_data(batch)

                    # Insert batch
                    await asyncio.to_thread(
                        self.collection.insert,
                        insert_data
                    )

                    total_inserted += len(batch)

                    # Log progress for large insertions
                    if len(entities) > 10000 and i % (batch_size * 10) == 0:
                        logger.info(f"Inserted {total_inserted}/{len(entities)} entities")

                # Flush to ensure data is persisted
                await asyncio.to_thread(self.collection.flush)

                # Record metrics
                MILVUS_OPERATIONS.labels(
                    operation='insert',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()

                self.stats['total_inserts'] += len(entities)

                # Update collection metrics
                await self._update_collection_metrics()

                logger.debug(f"Inserted {len(entities)} entities to Milvus")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Milvus insert failed: {e}")
                span.record_exception(e)

                MILVUS_OPERATIONS.labels(
                    operation='insert',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def delete_vectors(self, expr: str) -> bool:
        """
        Delete vectors using expression

        Args:
            expr: Delete expression (e.g., "id in [1, 2, 3]")

        Returns:
            bool: True if deletion successful
        """
        if not self._can_execute():
            logger.warning("Milvus circuit breaker is open, skipping delete")
            return False

        with tracer.start_as_current_span("milvus_delete") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("expression", expr)

            try:
                # Delete entities
                await asyncio.to_thread(
                    self.collection.delete,
                    expr
                )

                # Flush to ensure deletion is persisted
                await asyncio.to_thread(self.collection.flush)

                # Clear relevant cache entries
                self._clear_cache()

                # Record metrics
                MILVUS_OPERATIONS.labels(
                    operation='delete',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()

                self.stats['total_deletes'] += 1

                # Update collection metrics
                await self._update_collection_metrics()

                logger.debug(f"Deleted entities from Milvus with expression: {expr}")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Milvus delete failed: {e}")
                span.record_exception(e)

                MILVUS_OPERATIONS.labels(
                    operation='delete',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def _ensure_collection_exists(self):
        """Ensure collection exists with optimized schema for 100M+ vectors"""
        try:
            # Check if collection exists
            has_collection = await asyncio.to_thread(
                utility.has_collection,
                self.config.collection_name
            )

            if not has_collection:
                logger.info(f"Creating Milvus collection: {self.config.collection_name}")

                # Define schema optimized for large scale
                fields = [
                    FieldSchema(
                        name="id",
                        dtype=DataType.INT64,
                        is_primary=True,
                        auto_id=True
                    ),
                    FieldSchema(
                        name="vector",
                        dtype=DataType.FLOAT_VECTOR,
                        dim=self.config.vector_dim
                    ),
                    FieldSchema(
                        name="content",
                        dtype=DataType.VARCHAR,
                        max_length=65535
                    ),
                    FieldSchema(
                        name="metadata",
                        dtype=DataType.JSON
                    ),
                    FieldSchema(
                        name="timestamp",
                        dtype=DataType.INT64
                    ),
                    FieldSchema(
                        name="quality_score",
                        dtype=DataType.FLOAT
                    )
                ]

                schema = CollectionSchema(
                    fields=fields,
                    description="Semantic cache for cost optimization with horizontal scaling",
                    enable_dynamic_field=True
                )

                # Create collection with sharding for horizontal scaling
                collection = Collection(
                    name=self.config.collection_name,
                    schema=schema,
                    shards_num=self.config.shards_num,
                    consistency_level=self.config.consistency_level
                )

                # Create index for optimal search performance
                await self._create_optimized_index(collection)

                self.collection = collection
                logger.info(f"Created Milvus collection with {self.config.shards_num} shards")
            else:
                # Load existing collection
                self.collection = Collection(self.config.collection_name)
                logger.info(f"Loaded existing Milvus collection: {self.config.collection_name}")

        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {e}")
            raise

    async def _create_optimized_index(self, collection: Collection):
        """Create optimized index for 100M+ vectors"""
        try:
            # Create vector index
            index_params = {
                "index_type": self.config.index_type,
                "params": self.config.index_params
            }

            await asyncio.to_thread(
                collection.create_index,
                field_name="vector",
                index_params=index_params
            )

            # Create scalar indexes for filtering
            scalar_indexes = [
                ("timestamp", {}),
                ("quality_score", {}),
            ]

            for field_name, params in scalar_indexes:
                try:
                    await asyncio.to_thread(
                        collection.create_index,
                        field_name=field_name,
                        index_params=params
                    )
                except Exception as e:
                    logger.warning(f"Failed to create index on {field_name}: {e}")

            logger.info(f"Created {self.config.index_type} index on vector field")

        except Exception as e:
            logger.error(f"Failed to create index: {e}")
            raise

    def _prepare_insert_data(self, entities: List[Dict[str, Any]]) -> List[List[Any]]:
        """Prepare data for Milvus insertion"""
        # Extract fields in the correct order
        vectors = [entity["vector"] for entity in entities]
        contents = [entity.get("content", "") for entity in entities]
        metadata = [entity.get("metadata", {}) for entity in entities]
        timestamps = [entity.get("timestamp", int(time.time() * 1000)) for entity in entities]
        quality_scores = [entity.get("quality_score", 0.0) for entity in entities]

        return [vectors, contents, metadata, timestamps, quality_scores]

    def _can_execute(self) -> bool:
        """Check if circuit breaker allows execution"""
        if not self.is_connected:
            return False

        if not self.circuit_open:
            return True

        # Check if we should try to recover
        if time.time() - self.last_failure_time > self.circuit_breaker_timeout:
            self.circuit_open = False
            logger.info("Milvus circuit breaker reset")
            return True

        return False

    def _record_success(self):
        """Record successful operation"""
        self.failure_count = 0
        self.circuit_open = False

    def _record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.circuit_breaker_threshold:
            self.circuit_open = True
            logger.warning(f"Milvus circuit breaker opened after {self.failure_count} failures")

    def _generate_cache_key(
        self,
        query_vectors: List[List[float]],
        limit: int,
        expr: Optional[str]
    ) -> str:
        """Generate cache key for search results"""
        vectors_hash = hashlib.md5(str(query_vectors).encode()).hexdigest()[:8]
        expr_hash = hashlib.md5(str(expr).encode()).hexdigest()[:8] if expr else "none"
        return f"milvus_search_{vectors_hash}_{limit}_{expr_hash}"

    def _cache_result(self, key: str, result: List[List[MilvusSearchResult]]):
        """Cache search result with size limit"""
        if len(self.local_cache) >= self.local_cache_max_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.local_cache))
            del self.local_cache[oldest_key]

        self.local_cache[key] = result

    def _clear_cache(self):
        """Clear local cache"""
        self.local_cache.clear()

    async def _update_collection_metrics(self):
        """Update Prometheus metrics for collection"""
        try:
            if not self.collection:
                return

            # Get collection statistics
            stats = await asyncio.to_thread(self.collection.get_stats)

            # Extract entity count
            entity_count = 0
            memory_usage = 0

            for stat in stats:
                if stat.get("name") == "row_count":
                    entity_count = int(stat.get("value", 0))
                elif stat.get("name") == "memory_size":
                    memory_usage = int(stat.get("value", 0))

            MILVUS_COLLECTION_SIZE.labels(collection=self.config.collection_name).set(entity_count)
            MILVUS_MEMORY_USAGE.labels(collection=self.config.collection_name).set(memory_usage)

            self.stats['total_entities'] = entity_count
            self.stats['memory_usage_bytes'] = memory_usage

        except Exception as e:
            logger.warning(f"Failed to update collection metrics: {e}")

    async def get_stats(self) -> Dict[str, Any]:
        """Get Milvus statistics"""
        await self._update_collection_metrics()

        return {
            'is_connected': self.is_connected,
            'circuit_open': self.circuit_open,
            'failure_count': self.failure_count,
            'collection_name': self.config.collection_name,
            'host': f"{self.config.host}:{self.config.port}",
            **self.stats
        }

    async def health_check(self) -> bool:
        """Perform health check"""
        try:
            if not self.is_connected or not self.collection:
                return False

            # Simple health check - get collection stats
            await asyncio.to_thread(self.collection.get_stats)
            return True

        except Exception as e:
            logger.error(f"Milvus health check failed: {e}")
            return False
