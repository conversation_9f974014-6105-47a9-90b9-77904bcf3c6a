"""
Qdrant Vector Database Client
Production-grade implementation for semantic caching layer 3
Optimized for Apple Silicon with <20ms P99 search latency
"""

import asyncio
import logging
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from uuid import uuid4

import numpy as np
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import models
from qdrant_client.http.models import (
    Distance, VectorParams, CreateCollection, PointStruct,
    Filter, FieldCondition, Range, SearchRequest
)
from opentelemetry import trace
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for Qdrant monitoring
QDRANT_OPERATIONS = Counter(
    'qdrant_operations_total',
    'Total Qdrant operations',
    ['operation', 'collection', 'status']
)

QDRANT_SEARCH_LATENCY = Histogram(
    'qdrant_search_latency_seconds',
    'Qdrant search operation latency',
    ['collection']
)

QDRANT_COLLECTION_SIZE = Gauge(
    'qdrant_collection_size',
    'Number of vectors in Qdrant collection',
    ['collection']
)

QDRANT_CACHE_HITS = Counter(
    'qdrant_cache_hits_total',
    'Qdrant cache hits',
    ['collection']
)


@dataclass
class VectorSearchResult:
    """Vector search result with metadata"""
    id: str
    score: float
    payload: Dict[str, Any]
    vector: Optional[List[float]] = None


@dataclass
class QdrantConfig:
    """Qdrant configuration"""
    host: str = "localhost"
    port: int = 6333
    api_key: Optional[str] = None
    timeout: int = 30
    collection_name: str = "semantic_cache"
    vector_size: int = 384  # Sentence transformer default
    distance_metric: Distance = Distance.COSINE
    
    # Performance optimization settings
    hnsw_config: Dict[str, Any] = None
    quantization_config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.hnsw_config is None:
            # Optimized HNSW parameters for Apple Silicon
            self.hnsw_config = {
                "m": 16,  # Number of bi-directional links for each node
                "ef_construct": 100,  # Size of dynamic candidate list
                "full_scan_threshold": 10000,  # Use full scan for small collections
                "max_indexing_threads": 8,  # Optimize for Apple Silicon cores
            }
        
        if self.quantization_config is None:
            # Scalar quantization for memory efficiency
            self.quantization_config = {
                "scalar": {
                    "type": "int8",
                    "quantile": 0.99,
                    "always_ram": True
                }
            }


class QdrantVectorDB:
    """
    Production-grade Qdrant vector database client
    
    Features:
    - Apple Silicon optimization for <20ms P99 latency
    - Automatic collection management
    - Circuit breaker pattern for fault tolerance
    - Comprehensive monitoring and metrics
    - Batch operations for high throughput
    """
    
    def __init__(self, config: Optional[QdrantConfig] = None):
        self.settings = get_settings()
        self.config = config or QdrantConfig(
            host=self.settings.qdrant_host,
            port=self.settings.qdrant_port,
            api_key=self.settings.qdrant_api_key,
            collection_name=self.settings.qdrant_collection_name
        )
        
        self.client: Optional[QdrantClient] = None
        self.is_connected = False
        
        # Circuit breaker state
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_open = False
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_timeout = 60
        
        # Performance tracking
        self.stats = {
            'total_searches': 0,
            'total_inserts': 0,
            'total_updates': 0,
            'total_deletes': 0,
            'cache_hits': 0,
            'average_search_latency_ms': 0.0,
            'collection_size': 0
        }
        
        # Local cache for frequently accessed vectors
        self.local_cache = {}
        self.local_cache_max_size = 1000
    
    async def connect(self) -> bool:
        """
        Connect to Qdrant and initialize collections
        
        Returns:
            bool: True if connection successful
        """
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(
                host=self.config.host,
                port=self.config.port,
                api_key=self.config.api_key,
                timeout=self.config.timeout,
                prefer_grpc=True  # Use gRPC for better performance
            )
            
            # Test connection
            collections = await asyncio.to_thread(self.client.get_collections)
            logger.info(f"Connected to Qdrant at {self.config.host}:{self.config.port}")
            
            # Initialize collection if it doesn't exist
            await self._ensure_collection_exists()
            
            self.is_connected = True
            self.circuit_open = False
            self.failure_count = 0
            
            # Update collection size metric
            await self._update_collection_metrics()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Qdrant"""
        if self.client:
            try:
                self.client.close()
                logger.info("Disconnected from Qdrant")
            except Exception as e:
                logger.error(f"Error disconnecting from Qdrant: {e}")
        
        self.is_connected = False
        self.client = None
    
    async def search_similar(
        self,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[VectorSearchResult]:
        """
        Search for similar vectors with comprehensive error handling
        
        Args:
            query_vector: Query vector for similarity search
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold
            filter_conditions: Optional metadata filters
            
        Returns:
            List of VectorSearchResult objects
        """
        if not self._can_execute():
            logger.warning("Qdrant circuit breaker is open, skipping search")
            return []
        
        with tracer.start_as_current_span("qdrant_search") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("limit", limit)
            span.set_attribute("score_threshold", score_threshold)
            
            start_time = time.time()
            
            try:
                # Check local cache first
                cache_key = self._generate_cache_key(query_vector, limit, score_threshold)
                if cache_key in self.local_cache:
                    QDRANT_CACHE_HITS.labels(collection=self.config.collection_name).inc()
                    span.set_attribute("cache_hit", True)
                    return self.local_cache[cache_key]
                
                # Build filter if provided
                search_filter = None
                if filter_conditions:
                    search_filter = self._build_filter(filter_conditions)
                
                # Perform vector search
                search_result = await asyncio.to_thread(
                    self.client.search,
                    collection_name=self.config.collection_name,
                    query_vector=query_vector,
                    limit=limit,
                    score_threshold=score_threshold,
                    query_filter=search_filter,
                    with_payload=True,
                    with_vectors=False  # Don't return vectors for performance
                )
                
                # Convert to our result format
                results = [
                    VectorSearchResult(
                        id=str(point.id),
                        score=point.score,
                        payload=point.payload or {}
                    )
                    for point in search_result
                ]
                
                # Cache results
                self._cache_result(cache_key, results)
                
                # Record metrics
                search_latency = (time.time() - start_time) * 1000
                QDRANT_SEARCH_LATENCY.labels(collection=self.config.collection_name).observe(search_latency / 1000)
                QDRANT_OPERATIONS.labels(
                    operation='search',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()
                
                # Update statistics
                self.stats['total_searches'] += 1
                self.stats['average_search_latency_ms'] = (
                    (self.stats['average_search_latency_ms'] * (self.stats['total_searches'] - 1) + search_latency) /
                    self.stats['total_searches']
                )
                
                span.set_attribute("results_count", len(results))
                span.set_attribute("search_latency_ms", search_latency)
                
                logger.debug(f"Qdrant search completed: {len(results)} results in {search_latency:.2f}ms")
                
                self._record_success()
                return results
                
            except Exception as e:
                logger.error(f"Qdrant search failed: {e}")
                span.record_exception(e)
                
                QDRANT_OPERATIONS.labels(
                    operation='search',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()
                
                self._record_failure()
                return []

    async def insert_vectors(
        self,
        vectors: List[Tuple[str, List[float], Dict[str, Any]]]
    ) -> bool:
        """
        Insert multiple vectors with metadata

        Args:
            vectors: List of (id, vector, payload) tuples

        Returns:
            bool: True if insertion successful
        """
        if not self._can_execute():
            logger.warning("Qdrant circuit breaker is open, skipping insert")
            return False

        with tracer.start_as_current_span("qdrant_insert") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("vector_count", len(vectors))

            try:
                # Convert to Qdrant points
                points = [
                    PointStruct(
                        id=vector_id,
                        vector=vector,
                        payload=payload
                    )
                    for vector_id, vector, payload in vectors
                ]

                # Batch insert for performance
                await asyncio.to_thread(
                    self.client.upsert,
                    collection_name=self.config.collection_name,
                    points=points
                )

                # Record metrics
                QDRANT_OPERATIONS.labels(
                    operation='insert',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()

                self.stats['total_inserts'] += len(vectors)

                # Update collection size
                await self._update_collection_metrics()

                logger.debug(f"Inserted {len(vectors)} vectors to Qdrant")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Qdrant insert failed: {e}")
                span.record_exception(e)

                QDRANT_OPERATIONS.labels(
                    operation='insert',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def delete_vectors(self, vector_ids: List[str]) -> bool:
        """
        Delete vectors by IDs

        Args:
            vector_ids: List of vector IDs to delete

        Returns:
            bool: True if deletion successful
        """
        if not self._can_execute():
            logger.warning("Qdrant circuit breaker is open, skipping delete")
            return False

        with tracer.start_as_current_span("qdrant_delete") as span:
            span.set_attribute("collection", self.config.collection_name)
            span.set_attribute("delete_count", len(vector_ids))

            try:
                await asyncio.to_thread(
                    self.client.delete,
                    collection_name=self.config.collection_name,
                    points_selector=models.PointIdsList(
                        points=vector_ids
                    )
                )

                # Clear from local cache
                for vector_id in vector_ids:
                    cache_keys_to_remove = [
                        key for key in self.local_cache.keys()
                        if vector_id in str(key)
                    ]
                    for key in cache_keys_to_remove:
                        del self.local_cache[key]

                # Record metrics
                QDRANT_OPERATIONS.labels(
                    operation='delete',
                    collection=self.config.collection_name,
                    status='success'
                ).inc()

                self.stats['total_deletes'] += len(vector_ids)

                # Update collection size
                await self._update_collection_metrics()

                logger.debug(f"Deleted {len(vector_ids)} vectors from Qdrant")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Qdrant delete failed: {e}")
                span.record_exception(e)

                QDRANT_OPERATIONS.labels(
                    operation='delete',
                    collection=self.config.collection_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def _ensure_collection_exists(self):
        """Ensure the collection exists with proper configuration"""
        try:
            # Check if collection exists
            collections = await asyncio.to_thread(self.client.get_collections)
            collection_names = [col.name for col in collections.collections]

            if self.config.collection_name not in collection_names:
                logger.info(f"Creating Qdrant collection: {self.config.collection_name}")

                # Create collection with optimized settings
                await asyncio.to_thread(
                    self.client.create_collection,
                    collection_name=self.config.collection_name,
                    vectors_config=VectorParams(
                        size=self.config.vector_size,
                        distance=self.config.distance_metric,
                        hnsw_config=models.HnswConfigDiff(**self.config.hnsw_config),
                        quantization_config=models.QuantizationConfig(**self.config.quantization_config)
                    ),
                    optimizers_config=models.OptimizersConfigDiff(
                        default_segment_number=2,  # Optimize for Apple Silicon
                        max_segment_size=200000,
                        memmap_threshold=50000,
                        indexing_threshold=20000,
                        flush_interval_sec=5,
                        max_optimization_threads=4
                    )
                )

                logger.info(f"Created Qdrant collection: {self.config.collection_name}")
            else:
                logger.info(f"Qdrant collection already exists: {self.config.collection_name}")

        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {e}")
            raise

    def _can_execute(self) -> bool:
        """Check if circuit breaker allows execution"""
        if not self.is_connected:
            return False

        if not self.circuit_open:
            return True

        # Check if we should try to recover
        if time.time() - self.last_failure_time > self.circuit_breaker_timeout:
            self.circuit_open = False
            logger.info("Qdrant circuit breaker reset")
            return True

        return False

    def _record_success(self):
        """Record successful operation"""
        self.failure_count = 0
        self.circuit_open = False

    def _record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.circuit_breaker_threshold:
            self.circuit_open = True
            logger.warning(f"Qdrant circuit breaker opened after {self.failure_count} failures")

    def _generate_cache_key(
        self,
        query_vector: List[float],
        limit: int,
        score_threshold: float
    ) -> str:
        """Generate cache key for search results"""
        vector_hash = hashlib.md5(str(query_vector).encode()).hexdigest()[:8]
        return f"qdrant_search_{vector_hash}_{limit}_{score_threshold}"

    def _cache_result(self, key: str, result: List[VectorSearchResult]):
        """Cache search result with size limit"""
        if len(self.local_cache) >= self.local_cache_max_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.local_cache))
            del self.local_cache[oldest_key]

        self.local_cache[key] = result

    def _build_filter(self, conditions: Dict[str, Any]) -> Filter:
        """Build Qdrant filter from conditions"""
        must_conditions = []

        for field, value in conditions.items():
            if isinstance(value, dict):
                # Range filter
                if 'gte' in value or 'lte' in value:
                    must_conditions.append(
                        FieldCondition(
                            key=field,
                            range=Range(
                                gte=value.get('gte'),
                                lte=value.get('lte')
                            )
                        )
                    )
            else:
                # Exact match filter
                must_conditions.append(
                    FieldCondition(
                        key=field,
                        match=models.MatchValue(value=value)
                    )
                )

        return Filter(must=must_conditions)

    async def _update_collection_metrics(self):
        """Update Prometheus metrics for collection"""
        try:
            collection_info = await asyncio.to_thread(
                self.client.get_collection,
                collection_name=self.config.collection_name
            )

            vector_count = collection_info.vectors_count or 0
            QDRANT_COLLECTION_SIZE.labels(collection=self.config.collection_name).set(vector_count)
            self.stats['collection_size'] = vector_count

        except Exception as e:
            logger.warning(f"Failed to update collection metrics: {e}")

    async def get_stats(self) -> Dict[str, Any]:
        """Get Qdrant statistics"""
        await self._update_collection_metrics()

        return {
            'is_connected': self.is_connected,
            'circuit_open': self.circuit_open,
            'failure_count': self.failure_count,
            'collection_name': self.config.collection_name,
            'host': f"{self.config.host}:{self.config.port}",
            **self.stats
        }

    async def health_check(self) -> bool:
        """Perform health check"""
        try:
            if not self.is_connected:
                return False

            # Simple health check - get collection info
            await asyncio.to_thread(
                self.client.get_collection,
                collection_name=self.config.collection_name
            )

            return True

        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return False
