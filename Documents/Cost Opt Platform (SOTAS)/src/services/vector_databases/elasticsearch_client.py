"""
Elasticsearch Client
Production-grade implementation for semantic caching layer 6
Features full-text search, aggregations with <30ms search latency
"""

import asyncio
import logging
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from uuid import uuid4

from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError
from opentelemetry import trace
from prometheus_client import Counter, Histogram, Gauge

from src.core.config import get_settings

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for Elasticsearch monitoring
ELASTICSEARCH_OPERATIONS = Counter(
    'elasticsearch_operations_total',
    'Total Elasticsearch operations',
    ['operation', 'index', 'status']
)

ELASTICSEARCH_SEARCH_LATENCY = Histogram(
    'elasticsearch_search_latency_seconds',
    'Elasticsearch search operation latency',
    ['index', 'query_type']
)

ELASTICSEARCH_INDEX_SIZE = Gauge(
    'elasticsearch_index_size',
    'Number of documents in Elasticsearch index',
    ['index']
)

ELASTICSEARCH_QUERY_CACHE_HITS = Counter(
    'elasticsearch_query_cache_hits_total',
    'Elasticsearch query cache hits',
    ['index']
)


@dataclass
class ElasticsearchSearchResult:
    """Elasticsearch search result with comprehensive metadata"""
    id: str
    score: float
    source: Dict[str, Any]
    highlight: Optional[Dict[str, List[str]]] = None
    explanation: Optional[Dict[str, Any]] = None


@dataclass
class ElasticsearchConfig:
    """Elasticsearch configuration optimized for <30ms search latency"""
    host: str = "localhost"
    port: int = 9200
    username: Optional[str] = None
    password: Optional[str] = None
    index_name: str = "cost_optimization"
    
    # Connection configuration
    timeout: int = 30
    max_retries: int = 3
    retry_on_timeout: bool = True
    
    # Index configuration for optimal performance
    number_of_shards: int = 3
    number_of_replicas: int = 1
    refresh_interval: str = "1s"
    
    # Search configuration
    default_size: int = 10
    max_result_window: int = 10000
    
    # Performance optimization settings
    index_settings: Dict[str, Any] = None
    mapping_settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.index_settings is None:
            # Optimized index settings for fast search
            self.index_settings = {
                "number_of_shards": self.number_of_shards,
                "number_of_replicas": self.number_of_replicas,
                "refresh_interval": self.refresh_interval,
                "max_result_window": self.max_result_window,
                "analysis": {
                    "analyzer": {
                        "optimization_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": [
                                "lowercase",
                                "stop",
                                "snowball",
                                "optimization_synonyms"
                            ]
                        }
                    },
                    "filter": {
                        "optimization_synonyms": {
                            "type": "synonym",
                            "synonyms": [
                                "optimize,optimization,optimise",
                                "cost,price,expense",
                                "model,llm,ai",
                                "prompt,query,request"
                            ]
                        }
                    }
                },
                "index": {
                    "queries": {
                        "cache": {
                            "enabled": True
                        }
                    }
                }
            }
        
        if self.mapping_settings is None:
            # Optimized mapping for semantic caching
            self.mapping_settings = {
                "properties": {
                    "content": {
                        "type": "text",
                        "analyzer": "optimization_analyzer",
                        "search_analyzer": "optimization_analyzer",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 256
                            },
                            "suggest": {
                                "type": "completion"
                            }
                        }
                    },
                    "metadata": {
                        "type": "object",
                        "dynamic": True
                    },
                    "timestamp": {
                        "type": "date",
                        "format": "epoch_millis"
                    },
                    "quality_score": {
                        "type": "float"
                    },
                    "model_used": {
                        "type": "keyword"
                    },
                    "cost_savings": {
                        "type": "float"
                    },
                    "optimization_level": {
                        "type": "keyword"
                    },
                    "user_id": {
                        "type": "keyword"
                    },
                    "tags": {
                        "type": "keyword"
                    },
                    "vector_embedding": {
                        "type": "dense_vector",
                        "dims": 384,
                        "index": True,
                        "similarity": "cosine"
                    }
                }
            }


class ElasticsearchVectorDB:
    """
    Production-grade Elasticsearch client for semantic caching
    
    Features:
    - Full-text search with <30ms latency optimization
    - Advanced aggregations and analytics
    - Query caching and performance monitoring
    - Circuit breaker pattern for fault tolerance
    - Comprehensive search capabilities (fuzzy, phrase, bool queries)
    """
    
    def __init__(self, config: Optional[ElasticsearchConfig] = None):
        self.settings = get_settings()
        self.config = config or ElasticsearchConfig(
            host=self.settings.elasticsearch_host,
            port=self.settings.elasticsearch_port,
            username=self.settings.elasticsearch_username,
            password=self.settings.elasticsearch_password,
            index_name=self.settings.elasticsearch_index_name
        )
        
        self.client: Optional[AsyncElasticsearch] = None
        self.is_connected = False
        
        # Circuit breaker state
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_open = False
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_timeout = 60
        
        # Performance tracking
        self.stats = {
            'total_searches': 0,
            'total_inserts': 0,
            'total_updates': 0,
            'total_deletes': 0,
            'cache_hits': 0,
            'average_search_latency_ms': 0.0,
            'document_count': 0,
            'index_size_bytes': 0
        }
        
        # Local cache for frequently accessed results
        self.local_cache = {}
        self.local_cache_max_size = 1000
    
    async def connect(self) -> bool:
        """
        Connect to Elasticsearch and initialize index
        
        Returns:
            bool: True if connection successful
        """
        try:
            # Configure authentication
            auth = None
            if self.config.username and self.config.password:
                auth = (self.config.username, self.config.password)
            
            # Initialize Elasticsearch client
            self.client = AsyncElasticsearch(
                hosts=[f"http://{self.config.host}:{self.config.port}"],
                http_auth=auth,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries,
                retry_on_timeout=self.config.retry_on_timeout,
                # Performance optimizations
                sniff_on_start=False,
                sniff_on_connection_fail=False,
                sniffer_timeout=None,
                http_compress=True
            )
            
            # Test connection
            cluster_health = await self.client.cluster.health()
            if cluster_health["status"] in ["red"]:
                raise ConnectionError(f"Elasticsearch cluster is in {cluster_health['status']} state")
            
            logger.info(f"Connected to Elasticsearch at {self.config.host}:{self.config.port}")
            
            # Initialize index if it doesn't exist
            await self._ensure_index_exists()
            
            self.is_connected = True
            self.circuit_open = False
            self.failure_count = 0
            
            # Update index metrics
            await self._update_index_metrics()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Elasticsearch: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Elasticsearch"""
        if self.client:
            try:
                await self.client.close()
                logger.info("Disconnected from Elasticsearch")
            except Exception as e:
                logger.error(f"Error disconnecting from Elasticsearch: {e}")
        
        self.is_connected = False
        self.client = None
    
    async def search(
        self,
        query: Dict[str, Any],
        size: Optional[int] = None,
        from_: int = 0,
        sort: Optional[List[Dict[str, Any]]] = None,
        highlight: Optional[Dict[str, Any]] = None,
        aggregations: Optional[Dict[str, Any]] = None,
        explain: bool = False
    ) -> Tuple[List[ElasticsearchSearchResult], Dict[str, Any]]:
        """
        Perform advanced search with comprehensive query support
        
        Args:
            query: Elasticsearch query DSL
            size: Number of results to return
            from_: Starting offset for pagination
            sort: Sort configuration
            highlight: Highlighting configuration
            aggregations: Aggregations to compute
            explain: Whether to include score explanation
            
        Returns:
            Tuple of (search results, aggregations)
        """
        if not self._can_execute():
            logger.warning("Elasticsearch circuit breaker is open, skipping search")
            return [], {}
        
        with tracer.start_as_current_span("elasticsearch_search") as span:
            span.set_attribute("index", self.config.index_name)
            span.set_attribute("size", size or self.config.default_size)
            span.set_attribute("has_aggregations", aggregations is not None)
            
            start_time = time.time()
            size = size or self.config.default_size
            
            try:
                # Check local cache first
                cache_key = self._generate_cache_key(query, size, from_, sort)
                if cache_key in self.local_cache:
                    ELASTICSEARCH_QUERY_CACHE_HITS.labels(index=self.config.index_name).inc()
                    span.set_attribute("cache_hit", True)
                    return self.local_cache[cache_key]
                
                # Build search request
                search_body = {
                    "query": query,
                    "size": size,
                    "from": from_,
                    "track_total_hits": True
                }
                
                if sort:
                    search_body["sort"] = sort
                
                if highlight:
                    search_body["highlight"] = highlight
                
                if aggregations:
                    search_body["aggs"] = aggregations
                
                if explain:
                    search_body["explain"] = True
                
                # Execute search with performance optimization
                response = await self.client.search(
                    index=self.config.index_name,
                    body=search_body,
                    request_cache=True,  # Enable request caching
                    preference="_local"  # Prefer local shard for consistency
                )
                
                # Process search results
                results = []
                hits = response.get("hits", {}).get("hits", [])
                
                for hit in hits:
                    result = ElasticsearchSearchResult(
                        id=hit["_id"],
                        score=hit["_score"],
                        source=hit["_source"],
                        highlight=hit.get("highlight"),
                        explanation=hit.get("_explanation") if explain else None
                    )
                    results.append(result)
                
                # Extract aggregations
                aggs_result = response.get("aggregations", {})
                
                # Cache results
                cache_result = (results, aggs_result)
                self._cache_result(cache_key, cache_result)
                
                # Record metrics
                search_latency = (time.time() - start_time) * 1000
                query_type = self._determine_query_type(query)
                
                ELASTICSEARCH_SEARCH_LATENCY.labels(
                    index=self.config.index_name,
                    query_type=query_type
                ).observe(search_latency / 1000)
                
                ELASTICSEARCH_OPERATIONS.labels(
                    operation='search',
                    index=self.config.index_name,
                    status='success'
                ).inc()
                
                # Update statistics
                self.stats['total_searches'] += 1
                self.stats['average_search_latency_ms'] = (
                    (self.stats['average_search_latency_ms'] * (self.stats['total_searches'] - 1) + search_latency) /
                    self.stats['total_searches']
                )
                
                span.set_attribute("results_count", len(results))
                span.set_attribute("search_latency_ms", search_latency)
                span.set_attribute("query_type", query_type)
                
                logger.debug(f"Elasticsearch search completed: {len(results)} results in {search_latency:.2f}ms")
                
                self._record_success()
                return results, aggs_result
                
            except Exception as e:
                logger.error(f"Elasticsearch search failed: {e}")
                span.record_exception(e)
                
                ELASTICSEARCH_OPERATIONS.labels(
                    operation='search',
                    index=self.config.index_name,
                    status='error'
                ).inc()
                
                self._record_failure()
                return [], {}

    async def bulk_index(
        self,
        documents: List[Dict[str, Any]],
        batch_size: int = 1000
    ) -> bool:
        """
        Bulk index documents with optimized batching

        Args:
            documents: List of documents to index
            batch_size: Batch size for bulk operations

        Returns:
            bool: True if indexing successful
        """
        if not self._can_execute():
            logger.warning("Elasticsearch circuit breaker is open, skipping bulk index")
            return False

        with tracer.start_as_current_span("elasticsearch_bulk_index") as span:
            span.set_attribute("index", self.config.index_name)
            span.set_attribute("document_count", len(documents))
            span.set_attribute("batch_size", batch_size)

            try:
                # Process in batches for memory efficiency
                total_indexed = 0
                for i in range(0, len(documents), batch_size):
                    batch = documents[i:i + batch_size]

                    # Prepare bulk request
                    bulk_body = []
                    for doc in batch:
                        doc_id = doc.pop("_id", str(uuid4()))
                        bulk_body.append({
                            "index": {
                                "_index": self.config.index_name,
                                "_id": doc_id
                            }
                        })
                        bulk_body.append(doc)

                    # Execute bulk request
                    response = await self.client.bulk(
                        body=bulk_body,
                        refresh="wait_for"  # Ensure documents are searchable
                    )

                    # Check for errors
                    if response.get("errors"):
                        error_count = sum(1 for item in response["items"] if "error" in item.get("index", {}))
                        logger.warning(f"Bulk index had {error_count} errors in batch")

                    total_indexed += len(batch)

                    # Log progress for large operations
                    if len(documents) > 10000 and i % (batch_size * 10) == 0:
                        logger.info(f"Indexed {total_indexed}/{len(documents)} documents")

                # Record metrics
                ELASTICSEARCH_OPERATIONS.labels(
                    operation='bulk_index',
                    index=self.config.index_name,
                    status='success'
                ).inc()

                self.stats['total_inserts'] += len(documents)

                # Update index metrics
                await self._update_index_metrics()

                logger.debug(f"Bulk indexed {len(documents)} documents to Elasticsearch")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Elasticsearch bulk index failed: {e}")
                span.record_exception(e)

                ELASTICSEARCH_OPERATIONS.labels(
                    operation='bulk_index',
                    index=self.config.index_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def delete_by_query(self, query: Dict[str, Any]) -> bool:
        """
        Delete documents matching query

        Args:
            query: Elasticsearch query DSL

        Returns:
            bool: True if deletion successful
        """
        if not self._can_execute():
            logger.warning("Elasticsearch circuit breaker is open, skipping delete")
            return False

        with tracer.start_as_current_span("elasticsearch_delete_by_query") as span:
            span.set_attribute("index", self.config.index_name)

            try:
                response = await self.client.delete_by_query(
                    index=self.config.index_name,
                    body={"query": query},
                    refresh=True
                )

                deleted_count = response.get("deleted", 0)

                # Clear relevant cache entries
                self._clear_cache()

                # Record metrics
                ELASTICSEARCH_OPERATIONS.labels(
                    operation='delete_by_query',
                    index=self.config.index_name,
                    status='success'
                ).inc()

                self.stats['total_deletes'] += deleted_count

                # Update index metrics
                await self._update_index_metrics()

                logger.debug(f"Deleted {deleted_count} documents from Elasticsearch")
                self._record_success()
                return True

            except Exception as e:
                logger.error(f"Elasticsearch delete by query failed: {e}")
                span.record_exception(e)

                ELASTICSEARCH_OPERATIONS.labels(
                    operation='delete_by_query',
                    index=self.config.index_name,
                    status='error'
                ).inc()

                self._record_failure()
                return False

    async def suggest(
        self,
        text: str,
        field: str = "content.suggest",
        size: int = 5
    ) -> List[str]:
        """
        Get search suggestions using completion suggester

        Args:
            text: Text to get suggestions for
            field: Field to search suggestions in
            size: Number of suggestions to return

        Returns:
            List of suggestion strings
        """
        if not self._can_execute():
            return []

        try:
            response = await self.client.search(
                index=self.config.index_name,
                body={
                    "suggest": {
                        "optimization_suggest": {
                            "prefix": text,
                            "completion": {
                                "field": field,
                                "size": size
                            }
                        }
                    }
                }
            )

            suggestions = []
            suggest_results = response.get("suggest", {}).get("optimization_suggest", [])

            for suggest_result in suggest_results:
                for option in suggest_result.get("options", []):
                    suggestions.append(option["text"])

            return suggestions

        except Exception as e:
            logger.error(f"Elasticsearch suggest failed: {e}")
            return []

    async def _ensure_index_exists(self):
        """Ensure index exists with optimized settings"""
        try:
            # Check if index exists
            exists = await self.client.indices.exists(index=self.config.index_name)

            if not exists:
                logger.info(f"Creating Elasticsearch index: {self.config.index_name}")

                # Create index with optimized settings
                await self.client.indices.create(
                    index=self.config.index_name,
                    body={
                        "settings": self.config.index_settings,
                        "mappings": self.config.mapping_settings
                    }
                )

                logger.info(f"Created Elasticsearch index: {self.config.index_name}")
            else:
                logger.info(f"Elasticsearch index already exists: {self.config.index_name}")

        except Exception as e:
            logger.error(f"Failed to ensure index exists: {e}")
            raise

    def _determine_query_type(self, query: Dict[str, Any]) -> str:
        """Determine query type for metrics"""
        if "match" in query:
            return "match"
        elif "term" in query:
            return "term"
        elif "bool" in query:
            return "bool"
        elif "multi_match" in query:
            return "multi_match"
        elif "fuzzy" in query:
            return "fuzzy"
        elif "range" in query:
            return "range"
        else:
            return "other"

    def _can_execute(self) -> bool:
        """Check if circuit breaker allows execution"""
        if not self.is_connected:
            return False

        if not self.circuit_open:
            return True

        # Check if we should try to recover
        if time.time() - self.last_failure_time > self.circuit_breaker_timeout:
            self.circuit_open = False
            logger.info("Elasticsearch circuit breaker reset")
            return True

        return False

    def _record_success(self):
        """Record successful operation"""
        self.failure_count = 0
        self.circuit_open = False

    def _record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.circuit_breaker_threshold:
            self.circuit_open = True
            logger.warning(f"Elasticsearch circuit breaker opened after {self.failure_count} failures")

    def _generate_cache_key(
        self,
        query: Dict[str, Any],
        size: int,
        from_: int,
        sort: Optional[List[Dict[str, Any]]]
    ) -> str:
        """Generate cache key for search results"""
        query_hash = hashlib.md5(str(query).encode()).hexdigest()[:8]
        sort_hash = hashlib.md5(str(sort).encode()).hexdigest()[:8] if sort else "none"
        return f"es_search_{query_hash}_{size}_{from_}_{sort_hash}"

    def _cache_result(self, key: str, result: Tuple[List[ElasticsearchSearchResult], Dict[str, Any]]):
        """Cache search result with size limit"""
        if len(self.local_cache) >= self.local_cache_max_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.local_cache))
            del self.local_cache[oldest_key]

        self.local_cache[key] = result

    def _clear_cache(self):
        """Clear local cache"""
        self.local_cache.clear()

    async def _update_index_metrics(self):
        """Update Prometheus metrics for index"""
        try:
            # Get index statistics
            stats = await self.client.indices.stats(index=self.config.index_name)

            index_stats = stats.get("indices", {}).get(self.config.index_name, {})
            total_stats = index_stats.get("total", {})

            # Extract document count and size
            doc_count = total_stats.get("docs", {}).get("count", 0)
            size_bytes = total_stats.get("store", {}).get("size_in_bytes", 0)

            ELASTICSEARCH_INDEX_SIZE.labels(index=self.config.index_name).set(doc_count)

            self.stats['document_count'] = doc_count
            self.stats['index_size_bytes'] = size_bytes

        except Exception as e:
            logger.warning(f"Failed to update index metrics: {e}")

    async def get_stats(self) -> Dict[str, Any]:
        """Get Elasticsearch statistics"""
        await self._update_index_metrics()

        return {
            'is_connected': self.is_connected,
            'circuit_open': self.circuit_open,
            'failure_count': self.failure_count,
            'index_name': self.config.index_name,
            'host': f"{self.config.host}:{self.config.port}",
            **self.stats
        }

    async def health_check(self) -> bool:
        """Perform health check"""
        try:
            if not self.is_connected:
                return False

            # Simple health check - cluster health
            health = await self.client.cluster.health()
            return health["status"] in ["green", "yellow"]

        except Exception as e:
            logger.error(f"Elasticsearch health check failed: {e}")
            return False
