"""
Performance Benchmarking Service
FAANG+ implementation with comprehensive performance monitoring and SLA validation
Implements TikTok-level performance optimization with Google reliability standards
"""

import asyncio
import logging
import time
import statistics
import psutil
import os
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json
from datetime import datetime, timed<PERSON>ta
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from opentelemetry import trace
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, Summary

from src.core.config import get_settings
from src.core.optimizer import CostOptimizer
from src.core.models import OptimizationRequest, OptimizationLevel

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Prometheus metrics for performance monitoring
BENCHMARK_DURATION = Histogram(
    'benchmark_duration_seconds',
    'Benchmark execution duration',
    ['benchmark_type', 'load_level']
)

BENCHMARK_THROUGHPUT = Gauge(
    'benchmark_throughput_rps',
    'Benchmark throughput in requests per second',
    ['benchmark_type']
)

BENCHMARK_LATENCY = Histogram(
    'benchmark_latency_seconds',
    'Benchmark request latency',
    ['benchmark_type', 'percentile']
)

BENCHMARK_ERRORS = Counter(
    'benchmark_errors_total',
    'Benchmark errors',
    ['benchmark_type', 'error_type']
)

SYSTEM_RESOURCES = Gauge(
    'benchmark_system_resources',
    'System resource utilization during benchmarks',
    ['resource_type']
)


class BenchmarkType(Enum):
    """Types of performance benchmarks"""
    LATENCY = "latency"
    THROUGHPUT = "throughput"
    LOAD = "load"
    STRESS = "stress"
    ENDURANCE = "endurance"
    SCALABILITY = "scalability"
    MEMORY = "memory"
    CPU = "cpu"


class LoadLevel(Enum):
    """Load levels for benchmarking"""
    LIGHT = "light"
    MEDIUM = "medium"
    HEAVY = "heavy"
    EXTREME = "extreme"


@dataclass
class BenchmarkConfig:
    """Benchmark configuration"""
    name: str
    benchmark_type: BenchmarkType
    duration_seconds: int
    concurrent_users: int
    requests_per_second: Optional[int] = None
    ramp_up_time: int = 10
    ramp_down_time: int = 5
    target_latency_p99: float = 50.0  # milliseconds
    target_throughput: float = 1000.0  # RPS
    target_error_rate: float = 0.01  # 1%
    memory_limit_mb: int = 1000
    cpu_limit_percent: float = 80.0


@dataclass
class BenchmarkResult:
    """Benchmark execution result"""
    config: BenchmarkConfig
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    
    # Request metrics
    total_requests: int
    successful_requests: int
    failed_requests: int
    error_rate: float
    
    # Latency metrics (in milliseconds)
    latency_min: float
    latency_max: float
    latency_mean: float
    latency_median: float
    latency_p95: float
    latency_p99: float
    latency_std: float
    
    # Throughput metrics
    throughput_rps: float
    peak_throughput_rps: float
    
    # Resource metrics
    cpu_usage_percent: float
    memory_usage_mb: float
    peak_memory_mb: float
    
    # Quality metrics
    average_quality_score: float
    average_savings_percentage: float
    cache_hit_rate: float
    
    # SLA compliance
    sla_compliance: Dict[str, bool] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary"""
        return {
            'config': {
                'name': self.config.name,
                'type': self.config.benchmark_type.value,
                'duration': self.config.duration_seconds,
                'concurrent_users': self.config.concurrent_users
            },
            'timing': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat(),
                'duration_seconds': self.duration_seconds
            },
            'requests': {
                'total': self.total_requests,
                'successful': self.successful_requests,
                'failed': self.failed_requests,
                'error_rate': self.error_rate
            },
            'latency': {
                'min_ms': self.latency_min,
                'max_ms': self.latency_max,
                'mean_ms': self.latency_mean,
                'median_ms': self.latency_median,
                'p95_ms': self.latency_p95,
                'p99_ms': self.latency_p99,
                'std_ms': self.latency_std
            },
            'throughput': {
                'average_rps': self.throughput_rps,
                'peak_rps': self.peak_throughput_rps
            },
            'resources': {
                'cpu_percent': self.cpu_usage_percent,
                'memory_mb': self.memory_usage_mb,
                'peak_memory_mb': self.peak_memory_mb
            },
            'quality': {
                'average_quality_score': self.average_quality_score,
                'average_savings_percentage': self.average_savings_percentage,
                'cache_hit_rate': self.cache_hit_rate
            },
            'sla_compliance': self.sla_compliance
        }


class ResourceMonitor:
    """System resource monitoring during benchmarks"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.monitoring = False
        self.cpu_samples = []
        self.memory_samples = []
        self.monitor_task = None
    
    async def start_monitoring(self, interval: float = 1.0):
        """Start resource monitoring"""
        self.monitoring = True
        self.cpu_samples.clear()
        self.memory_samples.clear()
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
    
    async def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_loop(self, interval: float):
        """Resource monitoring loop"""
        while self.monitoring:
            try:
                # CPU usage
                cpu_percent = self.process.cpu_percent()
                self.cpu_samples.append(cpu_percent)
                
                # Memory usage
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                self.memory_samples.append(memory_mb)
                
                # Update Prometheus metrics
                SYSTEM_RESOURCES.labels(resource_type='cpu_percent').set(cpu_percent)
                SYSTEM_RESOURCES.labels(resource_type='memory_mb').set(memory_mb)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(interval)
    
    def get_stats(self) -> Dict[str, float]:
        """Get resource usage statistics"""
        if not self.cpu_samples or not self.memory_samples:
            return {
                'cpu_avg': 0.0,
                'cpu_max': 0.0,
                'memory_avg': 0.0,
                'memory_max': 0.0
            }
        
        return {
            'cpu_avg': statistics.mean(self.cpu_samples),
            'cpu_max': max(self.cpu_samples),
            'memory_avg': statistics.mean(self.memory_samples),
            'memory_max': max(self.memory_samples)
        }


class LoadGenerator:
    """Load generator for performance testing"""
    
    def __init__(self, optimizer: CostOptimizer):
        self.optimizer = optimizer
        self.active_requests = 0
        self.request_counter = 0
        
    async def generate_load(
        self,
        config: BenchmarkConfig,
        result_collector: Callable[[float, bool, Dict[str, Any]], None]
    ):
        """Generate load according to benchmark configuration"""
        
        with tracer.start_as_current_span("load_generation") as span:
            span.set_attribute("benchmark_type", config.benchmark_type.value)
            span.set_attribute("concurrent_users", config.concurrent_users)
            span.set_attribute("duration", config.duration_seconds)
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(config.concurrent_users)
            
            # Generate requests for the specified duration
            start_time = time.time()
            tasks = []
            
            while time.time() - start_time < config.duration_seconds:
                # Create request task
                task = asyncio.create_task(
                    self._make_request(semaphore, result_collector)
                )
                tasks.append(task)
                
                # Control request rate if specified
                if config.requests_per_second:
                    await asyncio.sleep(1.0 / config.requests_per_second)
                else:
                    await asyncio.sleep(0.001)  # Small delay to prevent overwhelming
            
            # Wait for all requests to complete
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _make_request(
        self,
        semaphore: asyncio.Semaphore,
        result_collector: Callable[[float, bool, Dict[str, Any]], None]
    ):
        """Make a single optimization request"""
        async with semaphore:
            self.active_requests += 1
            self.request_counter += 1
            
            try:
                # Create optimization request
                request = OptimizationRequest(
                    prompt=f"Performance test request {self.request_counter} with content to optimize",
                    optimization_level=OptimizationLevel.MODERATE,
                    quality_threshold=0.8,
                    user_id=f"benchmark_user_{self.request_counter % 100}"
                )
                
                # Measure request latency
                start_time = time.time()
                response = await self.optimizer.optimize(request)
                latency = (time.time() - start_time) * 1000  # Convert to milliseconds
                
                # Collect result
                result_collector(latency, True, {
                    'quality_score': response.quality_score,
                    'savings_percentage': response.savings_percentage,
                    'cache_hit': response.cache_hit,
                    'selected_model': response.selected_model
                })
                
            except Exception as e:
                latency = 0.0
                result_collector(latency, False, {'error': str(e)})
                logger.error(f"Request failed: {e}")
            
            finally:
                self.active_requests -= 1


class PerformanceBenchmark:
    """
    Production-grade performance benchmarking service
    Implements FAANG+ standards with comprehensive SLA validation
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.optimizer: Optional[CostOptimizer] = None
        self.resource_monitor = ResourceMonitor()
        self.benchmark_results: List[BenchmarkResult] = []
        
        # Predefined benchmark configurations
        self.benchmark_configs = {
            'latency_test': BenchmarkConfig(
                name='Latency Benchmark',
                benchmark_type=BenchmarkType.LATENCY,
                duration_seconds=60,
                concurrent_users=10,
                target_latency_p99=50.0
            ),
            'throughput_test': BenchmarkConfig(
                name='Throughput Benchmark',
                benchmark_type=BenchmarkType.THROUGHPUT,
                duration_seconds=120,
                concurrent_users=100,
                target_throughput=1000.0
            ),
            'load_test': BenchmarkConfig(
                name='Load Test',
                benchmark_type=BenchmarkType.LOAD,
                duration_seconds=300,
                concurrent_users=500,
                target_latency_p99=100.0,
                target_throughput=800.0
            ),
            'stress_test': BenchmarkConfig(
                name='Stress Test',
                benchmark_type=BenchmarkType.STRESS,
                duration_seconds=600,
                concurrent_users=1000,
                target_latency_p99=200.0,
                target_error_rate=0.05
            ),
            'endurance_test': BenchmarkConfig(
                name='Endurance Test',
                benchmark_type=BenchmarkType.ENDURANCE,
                duration_seconds=3600,  # 1 hour
                concurrent_users=200,
                target_latency_p99=75.0,
                memory_limit_mb=2000
            )
        }
    
    async def initialize(self, optimizer: CostOptimizer):
        """Initialize benchmark service"""
        self.optimizer = optimizer
        logger.info("Performance Benchmark service initialized")
    
    async def run_benchmark(self, config_name: str) -> BenchmarkResult:
        """Run a specific benchmark"""
        if config_name not in self.benchmark_configs:
            raise ValueError(f"Unknown benchmark configuration: {config_name}")
        
        config = self.benchmark_configs[config_name]
        return await self._execute_benchmark(config)
    
    async def run_custom_benchmark(self, config: BenchmarkConfig) -> BenchmarkResult:
        """Run a custom benchmark"""
        return await self._execute_benchmark(config)

    async def _execute_benchmark(self, config: BenchmarkConfig) -> BenchmarkResult:
        """Execute benchmark with comprehensive monitoring"""
        if not self.optimizer:
            raise RuntimeError("Benchmark service not initialized")

        logger.info(f"Starting benchmark: {config.name}")

        with tracer.start_as_current_span("benchmark_execution") as span:
            span.set_attribute("benchmark_name", config.name)
            span.set_attribute("benchmark_type", config.benchmark_type.value)

            # Initialize result collectors
            latencies = []
            successful_requests = 0
            failed_requests = 0
            quality_scores = []
            savings_percentages = []
            cache_hits = 0

            def collect_result(latency: float, success: bool, metadata: Dict[str, Any]):
                nonlocal successful_requests, failed_requests, cache_hits

                latencies.append(latency)
                if success:
                    successful_requests += 1
                    quality_scores.append(metadata.get('quality_score', 0.0))
                    savings_percentages.append(metadata.get('savings_percentage', 0.0))
                    if metadata.get('cache_hit', False):
                        cache_hits += 1
                else:
                    failed_requests += 1
                    BENCHMARK_ERRORS.labels(
                        benchmark_type=config.benchmark_type.value,
                        error_type='request_failure'
                    ).inc()

            # Start resource monitoring
            await self.resource_monitor.start_monitoring()

            # Record start time
            start_time = datetime.utcnow()
            benchmark_start = time.time()

            try:
                # Execute load generation
                load_generator = LoadGenerator(self.optimizer)

                with BENCHMARK_DURATION.labels(
                    benchmark_type=config.benchmark_type.value,
                    load_level='medium'
                ).time():
                    await load_generator.generate_load(config, collect_result)

                # Record end time
                end_time = datetime.utcnow()
                duration = time.time() - benchmark_start

                # Stop resource monitoring
                await self.resource_monitor.stop_monitoring()
                resource_stats = self.resource_monitor.get_stats()

                # Calculate metrics
                total_requests = successful_requests + failed_requests
                error_rate = failed_requests / total_requests if total_requests > 0 else 0.0
                throughput = successful_requests / duration if duration > 0 else 0.0

                # Calculate latency percentiles
                if latencies:
                    latencies.sort()
                    latency_min = min(latencies)
                    latency_max = max(latencies)
                    latency_mean = statistics.mean(latencies)
                    latency_median = statistics.median(latencies)
                    latency_p95 = latencies[int(len(latencies) * 0.95)] if latencies else 0.0
                    latency_p99 = latencies[int(len(latencies) * 0.99)] if latencies else 0.0
                    latency_std = statistics.stdev(latencies) if len(latencies) > 1 else 0.0
                else:
                    latency_min = latency_max = latency_mean = latency_median = 0.0
                    latency_p95 = latency_p99 = latency_std = 0.0

                # Calculate quality metrics
                avg_quality = statistics.mean(quality_scores) if quality_scores else 0.0
                avg_savings = statistics.mean(savings_percentages) if savings_percentages else 0.0
                cache_hit_rate = cache_hits / successful_requests if successful_requests > 0 else 0.0

                # Create result
                result = BenchmarkResult(
                    config=config,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=duration,
                    total_requests=total_requests,
                    successful_requests=successful_requests,
                    failed_requests=failed_requests,
                    error_rate=error_rate,
                    latency_min=latency_min,
                    latency_max=latency_max,
                    latency_mean=latency_mean,
                    latency_median=latency_median,
                    latency_p95=latency_p95,
                    latency_p99=latency_p99,
                    latency_std=latency_std,
                    throughput_rps=throughput,
                    peak_throughput_rps=throughput,  # Simplified for now
                    cpu_usage_percent=resource_stats['cpu_avg'],
                    memory_usage_mb=resource_stats['memory_avg'],
                    peak_memory_mb=resource_stats['memory_max'],
                    average_quality_score=avg_quality,
                    average_savings_percentage=avg_savings,
                    cache_hit_rate=cache_hit_rate
                )

                # Validate SLA compliance
                result.sla_compliance = self._validate_sla_compliance(config, result)

                # Record metrics
                BENCHMARK_THROUGHPUT.labels(benchmark_type=config.benchmark_type.value).set(throughput)
                BENCHMARK_LATENCY.labels(
                    benchmark_type=config.benchmark_type.value,
                    percentile='p99'
                ).observe(latency_p99 / 1000)  # Convert to seconds

                # Store result
                self.benchmark_results.append(result)

                # Log summary
                self._log_benchmark_summary(result)

                return result

            except Exception as e:
                await self.resource_monitor.stop_monitoring()
                logger.error(f"Benchmark execution failed: {e}")
                raise

    def _validate_sla_compliance(self, config: BenchmarkConfig, result: BenchmarkResult) -> Dict[str, bool]:
        """Validate SLA compliance"""
        compliance = {}

        # Latency SLA
        compliance['latency_p99'] = result.latency_p99 <= config.target_latency_p99

        # Throughput SLA
        compliance['throughput'] = result.throughput_rps >= config.target_throughput

        # Error rate SLA
        compliance['error_rate'] = result.error_rate <= config.target_error_rate

        # Memory usage SLA
        compliance['memory_usage'] = result.peak_memory_mb <= config.memory_limit_mb

        # CPU usage SLA
        compliance['cpu_usage'] = result.cpu_usage_percent <= config.cpu_limit_percent

        return compliance

    def _log_benchmark_summary(self, result: BenchmarkResult):
        """Log benchmark summary"""
        logger.info(f"Benchmark '{result.config.name}' completed:")
        logger.info(f"  Duration: {result.duration_seconds:.2f}s")
        logger.info(f"  Total Requests: {result.total_requests}")
        logger.info(f"  Success Rate: {(result.successful_requests/result.total_requests)*100:.1f}%")
        logger.info(f"  Throughput: {result.throughput_rps:.1f} RPS")
        logger.info(f"  Latency P99: {result.latency_p99:.2f}ms")
        logger.info(f"  Cache Hit Rate: {result.cache_hit_rate*100:.1f}%")
        logger.info(f"  Avg Quality Score: {result.average_quality_score:.3f}")
        logger.info(f"  Avg Savings: {result.average_savings_percentage:.1f}%")

        # SLA compliance
        all_compliant = all(result.sla_compliance.values())
        compliance_status = "✅ PASS" if all_compliant else "❌ FAIL"
        logger.info(f"  SLA Compliance: {compliance_status}")

        for sla, compliant in result.sla_compliance.items():
            status = "✅" if compliant else "❌"
            logger.info(f"    {sla}: {status}")

    async def run_comprehensive_benchmark_suite(self) -> Dict[str, BenchmarkResult]:
        """Run comprehensive benchmark suite"""
        logger.info("Starting comprehensive benchmark suite...")

        results = {}
        suite_order = ['latency_test', 'throughput_test', 'load_test', 'stress_test']

        for config_name in suite_order:
            try:
                logger.info(f"Running {config_name}...")
                result = await self.run_benchmark(config_name)
                results[config_name] = result

                # Check if critical SLAs are met before continuing
                if not result.sla_compliance.get('latency_p99', False):
                    logger.warning(f"Latency SLA failed in {config_name}, continuing with caution")

                # Brief pause between benchmarks
                await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Benchmark {config_name} failed: {e}")
                continue

        # Generate comprehensive report
        self._generate_comprehensive_report(results)

        return results

    def _generate_comprehensive_report(self, results: Dict[str, BenchmarkResult]):
        """Generate comprehensive benchmark report"""
        logger.info("\n" + "="*80)
        logger.info("COMPREHENSIVE BENCHMARK REPORT")
        logger.info("="*80)

        for name, result in results.items():
            logger.info(f"\n{result.config.name}:")
            logger.info(f"  Throughput: {result.throughput_rps:.1f} RPS")
            logger.info(f"  Latency P99: {result.latency_p99:.2f}ms")
            logger.info(f"  Error Rate: {result.error_rate*100:.2f}%")
            logger.info(f"  Memory Usage: {result.memory_usage_mb:.1f}MB")
            logger.info(f"  Quality Score: {result.average_quality_score:.3f}")

            # Overall compliance
            all_compliant = all(result.sla_compliance.values())
            logger.info(f"  SLA Compliance: {'✅ PASS' if all_compliant else '❌ FAIL'}")

        logger.info("="*80)

    async def get_benchmark_history(self) -> List[Dict[str, Any]]:
        """Get benchmark execution history"""
        return [result.to_dict() for result in self.benchmark_results]

    async def export_results(self, filepath: str):
        """Export benchmark results to JSON file"""
        results_data = {
            'export_time': datetime.utcnow().isoformat(),
            'total_benchmarks': len(self.benchmark_results),
            'results': [result.to_dict() for result in self.benchmark_results]
        }

        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)

        logger.info(f"Benchmark results exported to {filepath}")

    async def cleanup(self):
        """Cleanup benchmark service"""
        await self.resource_monitor.stop_monitoring()
        logger.info("Performance Benchmark service cleanup completed")
