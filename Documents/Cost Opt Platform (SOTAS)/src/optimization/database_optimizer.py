"""
Database Performance Optimization
Production-grade database optimization for 100M+ user scale
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.pool import QueuePool
from sqlalchemy.engine import Engine

from prometheus_client import Counter, Histogram, Gauge

logger = logging.getLogger(__name__)

# Database metrics
DB_QUERY_COUNT = Counter(
    'database_queries_total',
    'Total database queries',
    ['query_type', 'table', 'status']
)

DB_QUERY_DURATION = Histogram(
    'database_query_duration_seconds',
    'Database query execution time',
    ['query_type', 'table'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
)

DB_CONNECTION_POOL_SIZE = Gauge(
    'database_connection_pool_size',
    'Database connection pool size'
)

DB_CONNECTION_POOL_CHECKED_OUT = Gauge(
    'database_connection_pool_checked_out',
    'Database connections checked out'
)

DB_SLOW_QUERIES = Counter(
    'database_slow_queries_total',
    'Total slow database queries',
    ['query_type', 'table']
)


@dataclass
class QueryProfile:
    """Database query performance profile"""
    query_hash: str
    query_type: str
    table: str
    execution_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    slow_query_threshold: float = 1.0  # 1 second
    
    def add_execution(self, duration: float):
        """Add query execution timing"""
        self.execution_count += 1
        self.total_time += duration
        self.min_time = min(self.min_time, duration)
        self.max_time = max(self.max_time, duration)
        self.avg_time = self.total_time / self.execution_count
        
        # Check if it's a slow query
        if duration > self.slow_query_threshold:
            DB_SLOW_QUERIES.labels(
                query_type=self.query_type,
                table=self.table
            ).inc()


class DatabaseOptimizer:
    """
    Comprehensive database performance optimizer
    """
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.query_profiles: Dict[str, QueryProfile] = {}
        self.optimization_enabled = True
        
        # Connection pool optimization
        self.pool_config = {
            'poolclass': QueuePool,
            'pool_size': 20,
            'max_overflow': 30,
            'pool_pre_ping': True,
            'pool_recycle': 3600,  # 1 hour
            'pool_timeout': 30,
            'echo': False
        }
        
        # Query optimization settings
        self.slow_query_threshold = 1.0  # 1 second
        self.query_cache_size = 1000
        self.enable_query_logging = True
        
        # Index recommendations
        self.missing_indexes: List[Dict[str, Any]] = []
        self.unused_indexes: List[Dict[str, Any]] = []
        
        # Statistics
        self.stats = {
            'total_queries': 0,
            'slow_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    def create_optimized_engine(self) -> Engine:
        """Create optimized database engine"""
        engine = sa.create_engine(
            self.database_url,
            **self.pool_config
        )
        
        # Add event listeners for monitoring
        self._setup_event_listeners(engine)
        
        return engine
    
    def _setup_event_listeners(self, engine: Engine):
        """Setup SQLAlchemy event listeners for monitoring"""
        
        @sa.event.listens_for(engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @sa.event.listens_for(engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total_time = time.time() - context._query_start_time
            
            # Parse query information
            query_info = self._parse_query(statement)
            
            # Record metrics
            DB_QUERY_COUNT.labels(
                query_type=query_info['type'],
                table=query_info['table'],
                status='success'
            ).inc()
            
            DB_QUERY_DURATION.labels(
                query_type=query_info['type'],
                table=query_info['table']
            ).observe(total_time)
            
            # Profile query
            self._profile_query(statement, query_info, total_time)
            
            # Update statistics
            self.stats['total_queries'] += 1
            if total_time > self.slow_query_threshold:
                self.stats['slow_queries'] += 1
        
        @sa.event.listens_for(engine, "handle_error")
        def handle_error(exception_context):
            statement = exception_context.statement
            if statement:
                query_info = self._parse_query(statement)
                
                DB_QUERY_COUNT.labels(
                    query_type=query_info['type'],
                    table=query_info['table'],
                    status='error'
                ).inc()
    
    def _parse_query(self, statement: str) -> Dict[str, str]:
        """Parse query to extract type and table information"""
        statement_upper = statement.upper().strip()
        
        # Determine query type
        if statement_upper.startswith('SELECT'):
            query_type = 'SELECT'
        elif statement_upper.startswith('INSERT'):
            query_type = 'INSERT'
        elif statement_upper.startswith('UPDATE'):
            query_type = 'UPDATE'
        elif statement_upper.startswith('DELETE'):
            query_type = 'DELETE'
        else:
            query_type = 'OTHER'
        
        # Extract table name (simplified)
        table = 'unknown'
        try:
            if 'FROM' in statement_upper:
                parts = statement_upper.split('FROM')[1].split()
                if parts:
                    table = parts[0].strip('`"[]')
            elif 'INTO' in statement_upper:
                parts = statement_upper.split('INTO')[1].split()
                if parts:
                    table = parts[0].strip('`"[]')
            elif 'UPDATE' in statement_upper:
                parts = statement_upper.split('UPDATE')[1].split()
                if parts:
                    table = parts[0].strip('`"[]')
        except:
            pass
        
        return {
            'type': query_type,
            'table': table
        }
    
    def _profile_query(self, statement: str, query_info: Dict[str, str], duration: float):
        """Profile query performance"""
        if not self.optimization_enabled:
            return
        
        # Create query hash for grouping similar queries
        query_hash = str(hash(statement.strip()))
        
        if query_hash not in self.query_profiles:
            if len(self.query_profiles) >= self.query_cache_size:
                # Remove least executed query
                min_count = min(p.execution_count for p in self.query_profiles.values())
                for qh, profile in list(self.query_profiles.items()):
                    if profile.execution_count == min_count:
                        del self.query_profiles[qh]
                        break
            
            self.query_profiles[query_hash] = QueryProfile(
                query_hash=query_hash,
                query_type=query_info['type'],
                table=query_info['table'],
                slow_query_threshold=self.slow_query_threshold
            )
        
        self.query_profiles[query_hash].add_execution(duration)
    
    async def analyze_performance(self) -> Dict[str, Any]:
        """Analyze database performance and provide recommendations"""
        
        # Get slow queries
        slow_queries = [
            {
                'query_hash': profile.query_hash,
                'query_type': profile.query_type,
                'table': profile.table,
                'execution_count': profile.execution_count,
                'avg_time': profile.avg_time,
                'max_time': profile.max_time,
                'total_time': profile.total_time
            }
            for profile in self.query_profiles.values()
            if profile.avg_time > self.slow_query_threshold
        ]
        
        # Sort by total time impact
        slow_queries.sort(key=lambda q: q['total_time'], reverse=True)
        
        # Get most frequent queries
        frequent_queries = sorted(
            self.query_profiles.values(),
            key=lambda p: p.execution_count,
            reverse=True
        )[:10]
        
        # Generate recommendations
        recommendations = await self._generate_recommendations()
        
        return {
            'summary': {
                'total_queries': self.stats['total_queries'],
                'slow_queries': self.stats['slow_queries'],
                'slow_query_percentage': (self.stats['slow_queries'] / max(self.stats['total_queries'], 1)) * 100,
                'profiled_queries': len(self.query_profiles)
            },
            'slow_queries': slow_queries[:10],
            'frequent_queries': [
                {
                    'query_type': p.query_type,
                    'table': p.table,
                    'execution_count': p.execution_count,
                    'avg_time': p.avg_time
                }
                for p in frequent_queries
            ],
            'recommendations': recommendations,
            'connection_pool_stats': await self._get_pool_stats()
        }
    
    async def _generate_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Check for slow queries
        slow_query_count = sum(1 for p in self.query_profiles.values() if p.avg_time > self.slow_query_threshold)
        if slow_query_count > 0:
            recommendations.append({
                'type': 'slow_queries',
                'priority': 'high',
                'title': f'{slow_query_count} slow queries detected',
                'description': 'Consider adding indexes, optimizing WHERE clauses, or query restructuring',
                'impact': 'high'
            })
        
        # Check for frequent queries
        frequent_queries = [p for p in self.query_profiles.values() if p.execution_count > 100]
        if frequent_queries:
            recommendations.append({
                'type': 'frequent_queries',
                'priority': 'medium',
                'title': f'{len(frequent_queries)} frequently executed queries',
                'description': 'Consider caching results or optimizing these high-frequency queries',
                'impact': 'medium'
            })
        
        # Check query patterns
        select_queries = sum(1 for p in self.query_profiles.values() if p.query_type == 'SELECT')
        total_queries = len(self.query_profiles)
        
        if total_queries > 0 and (select_queries / total_queries) < 0.7:
            recommendations.append({
                'type': 'query_pattern',
                'priority': 'medium',
                'title': 'High write-to-read ratio detected',
                'description': 'Consider read replicas or write optimization strategies',
                'impact': 'medium'
            })
        
        return recommendations
    
    async def _get_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        # This would integrate with the actual connection pool
        # For now, return mock data
        return {
            'pool_size': self.pool_config['pool_size'],
            'max_overflow': self.pool_config['max_overflow'],
            'checked_out': 5,  # Would be actual value
            'overflow': 2,     # Would be actual value
            'invalid': 0       # Would be actual value
        }
    
    async def optimize_indexes(self, engine: Engine) -> List[Dict[str, Any]]:
        """Analyze and recommend index optimizations"""
        recommendations = []
        
        try:
            async with engine.begin() as conn:
                # Check for missing indexes (PostgreSQL specific)
                if 'postgresql' in self.database_url:
                    missing_indexes_query = text("""
                        SELECT 
                            schemaname,
                            tablename,
                            attname,
                            n_distinct,
                            correlation
                        FROM pg_stats 
                        WHERE schemaname = 'public'
                        AND n_distinct > 100
                        ORDER BY n_distinct DESC
                        LIMIT 10
                    """)
                    
                    result = await conn.execute(missing_indexes_query)
                    for row in result:
                        recommendations.append({
                            'type': 'missing_index',
                            'table': row.tablename,
                            'column': row.attname,
                            'priority': 'high' if row.n_distinct > 1000 else 'medium',
                            'reason': f'High cardinality column ({row.n_distinct} distinct values)'
                        })
                
                # Check for unused indexes
                unused_indexes_query = text("""
                    SELECT 
                        schemaname,
                        tablename,
                        indexname,
                        idx_scan
                    FROM pg_stat_user_indexes 
                    WHERE idx_scan < 10
                    AND schemaname = 'public'
                    ORDER BY idx_scan
                """)
                
                result = await conn.execute(unused_indexes_query)
                for row in result:
                    recommendations.append({
                        'type': 'unused_index',
                        'table': row.tablename,
                        'index': row.indexname,
                        'priority': 'low',
                        'reason': f'Index scanned only {row.idx_scan} times'
                    })
        
        except Exception as e:
            logger.error(f"Failed to analyze indexes: {e}")
        
        return recommendations
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get comprehensive optimization report"""
        
        # Calculate performance metrics
        total_time = sum(p.total_time for p in self.query_profiles.values())
        avg_query_time = total_time / max(self.stats['total_queries'], 1)
        
        # Find bottlenecks
        bottlenecks = []
        for profile in self.query_profiles.values():
            if profile.avg_time > self.slow_query_threshold:
                bottlenecks.append({
                    'type': 'slow_query',
                    'table': profile.table,
                    'query_type': profile.query_type,
                    'avg_time': profile.avg_time,
                    'impact_score': profile.total_time
                })
        
        # Sort by impact
        bottlenecks.sort(key=lambda b: b['impact_score'], reverse=True)
        
        return {
            'performance_summary': {
                'total_queries': self.stats['total_queries'],
                'total_time': total_time,
                'avg_query_time': avg_query_time,
                'slow_query_count': self.stats['slow_queries'],
                'slow_query_percentage': (self.stats['slow_queries'] / max(self.stats['total_queries'], 1)) * 100
            },
            'top_bottlenecks': bottlenecks[:5],
            'query_distribution': self._get_query_distribution(),
            'optimization_status': {
                'enabled': self.optimization_enabled,
                'slow_query_threshold': self.slow_query_threshold,
                'profiled_queries': len(self.query_profiles)
            }
        }
    
    def _get_query_distribution(self) -> Dict[str, int]:
        """Get distribution of query types"""
        distribution = defaultdict(int)
        for profile in self.query_profiles.values():
            distribution[profile.query_type] += profile.execution_count
        return dict(distribution)
    
    def reset_statistics(self):
        """Reset all performance statistics"""
        self.query_profiles.clear()
        self.stats = {
            'total_queries': 0,
            'slow_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        logger.info("Database performance statistics reset")


# Global database optimizer instance
_db_optimizer: Optional[DatabaseOptimizer] = None


def get_database_optimizer(database_url: str = None) -> DatabaseOptimizer:
    """Get global database optimizer instance"""
    global _db_optimizer
    
    if _db_optimizer is None and database_url:
        _db_optimizer = DatabaseOptimizer(database_url)
    
    return _db_optimizer
