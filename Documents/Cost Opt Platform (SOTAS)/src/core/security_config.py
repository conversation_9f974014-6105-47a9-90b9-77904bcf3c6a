"""
Security Configuration
Centralized security settings and validation for production deployment
"""

import os
import secrets
import logging
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security levels for different environments"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class SecurityHeaders:
    """Security headers configuration"""
    
    # Core security headers
    x_frame_options: str = "DENY"
    x_content_type_options: str = "nosniff"
    x_xss_protection: str = "1; mode=block"
    referrer_policy: str = "strict-origin-when-cross-origin"
    
    # Content Security Policy
    csp_default_src: str = "'self'"
    csp_script_src: str = "'self' 'unsafe-inline' 'unsafe-eval'"
    csp_style_src: str = "'self' 'unsafe-inline'"
    csp_img_src: str = "'self' data: https:"
    csp_font_src: str = "'self' data:"
    csp_connect_src: str = "'self' https://openrouter.ai"
    csp_frame_ancestors: str = "'none'"
    csp_base_uri: str = "'self'"
    csp_form_action: str = "'self'"
    csp_upgrade_insecure_requests: bool = True
    
    # HSTS configuration
    hsts_max_age: int = 63072000  # 2 years
    hsts_include_subdomains: bool = True
    hsts_preload: bool = True
    
    # Permissions Policy
    permissions_policy: Dict[str, str] = None
    
    # Additional security headers
    x_permitted_cross_domain_policies: str = "none"
    cross_origin_embedder_policy: str = "require-corp"
    cross_origin_opener_policy: str = "same-origin"
    cross_origin_resource_policy: str = "same-origin"
    
    def __post_init__(self):
        if self.permissions_policy is None:
            self.permissions_policy = {
                "accelerometer": "()",
                "ambient-light-sensor": "()",
                "autoplay": "()",
                "battery": "()",
                "camera": "()",
                "cross-origin-isolated": "()",
                "display-capture": "()",
                "document-domain": "()",
                "encrypted-media": "()",
                "execution-while-not-rendered": "()",
                "execution-while-out-of-viewport": "()",
                "fullscreen": "()",
                "geolocation": "()",
                "gyroscope": "()",
                "keyboard-map": "()",
                "magnetometer": "()",
                "microphone": "()",
                "midi": "()",
                "navigation-override": "()",
                "payment": "()",
                "picture-in-picture": "()",
                "publickey-credentials-get": "()",
                "screen-wake-lock": "()",
                "sync-xhr": "()",
                "usb": "()",
                "web-share": "()",
                "xr-spatial-tracking": "()"
            }
    
    def get_csp_header(self) -> str:
        """Generate Content Security Policy header"""
        policies = [
            f"default-src {self.csp_default_src}",
            f"script-src {self.csp_script_src}",
            f"style-src {self.csp_style_src}",
            f"img-src {self.csp_img_src}",
            f"font-src {self.csp_font_src}",
            f"connect-src {self.csp_connect_src}",
            f"frame-ancestors {self.csp_frame_ancestors}",
            f"base-uri {self.csp_base_uri}",
            f"form-action {self.csp_form_action}"
        ]
        
        if self.csp_upgrade_insecure_requests:
            policies.append("upgrade-insecure-requests")
        
        return "; ".join(policies)
    
    def get_hsts_header(self) -> str:
        """Generate HSTS header"""
        hsts = f"max-age={self.hsts_max_age}"
        
        if self.hsts_include_subdomains:
            hsts += "; includeSubDomains"
        
        if self.hsts_preload:
            hsts += "; preload"
        
        return hsts
    
    def get_permissions_policy_header(self) -> str:
        """Generate Permissions Policy header"""
        policies = [f"{feature}={value}" for feature, value in self.permissions_policy.items()]
        return ", ".join(policies)
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary for middleware"""
        return {
            "X-Frame-Options": self.x_frame_options,
            "X-Content-Type-Options": self.x_content_type_options,
            "X-XSS-Protection": self.x_xss_protection,
            "Referrer-Policy": self.referrer_policy,
            "Content-Security-Policy": self.get_csp_header(),
            "Strict-Transport-Security": self.get_hsts_header(),
            "Permissions-Policy": self.get_permissions_policy_header(),
            "X-Permitted-Cross-Domain-Policies": self.x_permitted_cross_domain_policies,
            "Cross-Origin-Embedder-Policy": self.cross_origin_embedder_policy,
            "Cross-Origin-Opener-Policy": self.cross_origin_opener_policy,
            "Cross-Origin-Resource-Policy": self.cross_origin_resource_policy
        }


@dataclass
class RateLimitConfig:
    """Rate limiting configuration"""
    
    # Basic rate limits
    requests_per_minute: int = 100
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    
    # Burst limits
    burst_size: int = 20
    burst_window_seconds: int = 10
    
    # API-specific limits
    optimization_requests_per_minute: int = 50
    batch_requests_per_minute: int = 10
    
    # Abuse detection
    suspicious_requests_per_minute: int = 500
    error_rate_threshold: float = 0.5
    ban_duration_minutes: int = 60
    
    # Whitelist
    whitelisted_ips: Set[str] = None
    
    def __post_init__(self):
        if self.whitelisted_ips is None:
            self.whitelisted_ips = {
                "127.0.0.1", "::1", "localhost"
            }


@dataclass
class AuthenticationConfig:
    """Authentication configuration"""
    
    # JWT settings
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    jwt_refresh_expiration_days: int = 30
    
    # API key settings
    api_key_length: int = 64
    api_key_prefix: str = "copt_"
    
    # Password requirements
    min_password_length: int = 12
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_numbers: bool = True
    require_special_chars: bool = True
    
    # Session settings
    session_timeout_minutes: int = 60
    max_concurrent_sessions: int = 5
    
    # Brute force protection
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15


@dataclass
class InputValidationConfig:
    """Input validation configuration"""
    
    # Size limits
    max_request_size_mb: int = 10
    max_header_size_kb: int = 8
    max_prompt_length: int = 100000
    max_string_length: int = 1000
    max_array_size: int = 1000
    
    # Content validation
    enable_sql_injection_detection: bool = True
    enable_xss_detection: bool = True
    enable_path_traversal_detection: bool = True
    enable_command_injection_detection: bool = True
    
    # Sanitization
    enable_html_sanitization: bool = True
    enable_url_encoding: bool = True


class SecurityConfigManager:
    """Centralized security configuration management"""
    
    def __init__(self, security_level: SecurityLevel = SecurityLevel.PRODUCTION):
        self.security_level = security_level
        self._load_configuration()
    
    def _load_configuration(self):
        """Load security configuration based on environment"""
        
        if self.security_level == SecurityLevel.DEVELOPMENT:
            self._load_development_config()
        elif self.security_level == SecurityLevel.STAGING:
            self._load_staging_config()
        else:
            self._load_production_config()
    
    def _load_development_config(self):
        """Load relaxed security for development"""
        
        self.headers = SecurityHeaders(
            csp_script_src="'self' 'unsafe-inline' 'unsafe-eval'",
            hsts_max_age=0,  # Disable HSTS in development
            hsts_preload=False
        )
        
        self.rate_limits = RateLimitConfig(
            requests_per_minute=1000,
            burst_size=100
        )
        
        self.auth = AuthenticationConfig(
            jwt_expiration_hours=168,  # 1 week
            min_password_length=8,
            require_special_chars=False
        )
        
        self.input_validation = InputValidationConfig(
            max_request_size_mb=50  # Larger for development
        )
    
    def _load_staging_config(self):
        """Load moderate security for staging"""
        
        self.headers = SecurityHeaders(
            hsts_max_age=86400,  # 1 day
            hsts_preload=False
        )
        
        self.rate_limits = RateLimitConfig(
            requests_per_minute=200,
            burst_size=50
        )
        
        self.auth = AuthenticationConfig(
            jwt_expiration_hours=48,  # 2 days
            min_password_length=10
        )
        
        self.input_validation = InputValidationConfig(
            max_request_size_mb=20
        )
    
    def _load_production_config(self):
        """Load maximum security for production"""
        
        self.headers = SecurityHeaders()  # Use defaults (most secure)
        
        self.rate_limits = RateLimitConfig()  # Use defaults
        
        self.auth = AuthenticationConfig()  # Use defaults
        
        self.input_validation = InputValidationConfig()  # Use defaults
    
    def validate_configuration(self) -> List[str]:
        """Validate security configuration and return warnings"""
        
        warnings = []
        
        # Check secret key
        secret_key = os.getenv("SECRET_KEY", "")
        if len(secret_key) < 32:
            warnings.append("SECRET_KEY is too short (minimum 32 characters)")
        
        if secret_key == "your-secret-key-change-in-production":
            warnings.append("Using default SECRET_KEY in production")
        
        # Check HTTPS in production
        if self.security_level == SecurityLevel.PRODUCTION:
            if not os.getenv("FORCE_HTTPS", "").lower() == "true":
                warnings.append("HTTPS not enforced in production")
        
        # Check CORS configuration
        allowed_origins = os.getenv("ALLOWED_ORIGINS", "*")
        if "*" in allowed_origins and self.security_level == SecurityLevel.PRODUCTION:
            warnings.append("Wildcard CORS origins in production")
        
        return warnings
    
    def generate_secure_key(self, length: int = 64) -> str:
        """Generate cryptographically secure key"""
        return secrets.token_urlsafe(length)
    
    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for middleware"""
        return self.headers.to_dict()


# Global security configuration instance
_security_config: Optional[SecurityConfigManager] = None


def get_security_config() -> SecurityConfigManager:
    """Get global security configuration"""
    global _security_config
    
    if _security_config is None:
        # Determine security level from environment
        env = os.getenv("ENVIRONMENT", "production").lower()
        
        if env == "development":
            level = SecurityLevel.DEVELOPMENT
        elif env == "staging":
            level = SecurityLevel.STAGING
        else:
            level = SecurityLevel.PRODUCTION
        
        _security_config = SecurityConfigManager(level)
        
        # Validate configuration and log warnings
        warnings = _security_config.validate_configuration()
        for warning in warnings:
            logger.warning(f"Security configuration warning: {warning}")
    
    return _security_config


def reset_security_config():
    """Reset security configuration (for testing)"""
    global _security_config
    _security_config = None
