"""
Database Management
SQLAlchemy models and connection management with production-grade patterns
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    Index, ForeignKey, MetaData
)
from sqlalchemy.ext.asyncio import (
    AsyncSession, create_async_engine, async_sessionmaker
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.pool import QueuePool

from src.core.config import get_settings


def generate_uuid():
    """Generate UUID string for SQLite compatibility"""
    return str(uuid4())


logger = logging.getLogger(__name__)
settings = get_settings()

# Database metadata and base
metadata = MetaData()
Base = declarative_base(metadata=metadata)


class OptimizationRequest(Base):
    """Optimization request records"""
    __tablename__ = "optimization_requests"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    original_prompt = Column(Text, nullable=False)
    optimized_prompt = Column(Text)
    selected_model = Column(String(100), nullable=False)
    original_cost = Column(Float)
    optimized_cost = Column(Float)
    savings_percentage = Column(Float)
    quality_score = Column(Float)
    processing_time_ms = Column(Integer)
    cache_hit = Column(Boolean, default=False)
    cache_hit_type = Column(String(50))
    task_complexity = Column(String(50))
    routing_reason = Column(String(200))
    compression_ratio = Column(Float)
    user_id = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_optimization_requests_model', 'selected_model'),
        Index('idx_optimization_requests_created_at', 'created_at'),
        Index('idx_optimization_requests_user_id', 'user_id'),
        Index('idx_optimization_requests_cache_hit', 'cache_hit'),
    )

# Aliases for backward compatibility
OptimizationRecord = OptimizationRequest


class ModelPerformance(Base):
    """Model performance tracking"""
    __tablename__ = "model_performance"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    model_name = Column(String(100), nullable=False)
    task_type = Column(String(50))
    quality_score = Column(Float)
    cost_per_token = Column(Float)
    avg_response_time_ms = Column(Integer)
    success_rate = Column(Float)
    total_requests = Column(Integer, default=0)
    total_failures = Column(Integer, default=0)
    last_updated = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_model_performance_model_task', 'model_name', 'task_type'),
        Index('idx_model_performance_last_updated', 'last_updated'),
    )


class CacheEntry(Base):
    """Semantic cache entries"""
    __tablename__ = "cache_entries"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    prompt_hash = Column(String(64), unique=True, nullable=False)
    prompt_embedding = Column(Text)  # JSON-encoded vector for SQLite
    response_data = Column(JSON)
    model_used = Column(String(100))
    quality_score = Column(Float)
    hit_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    
    __table_args__ = (
        Index('idx_cache_entries_hash', 'prompt_hash'),
        Index('idx_cache_entries_expires_at', 'expires_at'),
        Index('idx_cache_entries_hit_count', 'hit_count'),
    )


class OptimizationStep(Base):
    """Individual optimization steps"""
    __tablename__ = "optimization_steps"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    request_id = Column(String(36), ForeignKey('optimization_requests.id'))
    agent = Column(String(100), nullable=False)
    action = Column(String(200), nullable=False)
    before_state = Column(Text)
    after_state = Column(Text)
    savings = Column(Float)
    processing_time_ms = Column(Integer)
    step_order = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship
    request = relationship("OptimizationRequest", backref="steps")
    
    __table_args__ = (
        Index('idx_optimization_steps_request_id', 'request_id'),
        Index('idx_optimization_steps_agent', 'agent'),
    )


class AdaptiveLearningMetrics(Base):
    """Adaptive learning metrics and history"""
    __tablename__ = "adaptive_learning_metrics"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    current_cost_reduction_target = Column(Float, nullable=False)
    success_rate = Column(Float)
    optimization_accuracy = Column(Float)
    model_selection_accuracy = Column(Float)
    learning_iterations = Column(Integer, default=0)
    model_preferences = Column(JSON)  # Learned model preferences
    task_complexity_patterns = Column(JSON)  # Learned patterns
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_adaptive_learning_created_at', 'created_at'),
    )


class SystemMetrics(Base):
    """System-wide metrics and health data"""
    __tablename__ = "system_metrics"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float)
    metric_data = Column(JSON)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_system_metrics_name_timestamp', 'metric_name', 'timestamp'),
    )


class DatabaseManager:
    """Production-grade database connection manager"""
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = None
        self.async_session_factory = None
        self._connection_pool = None
    
    async def initialize(self):
        """Initialize database connection - alias for connect()"""
        await self.connect()

    async def connect(self):
        """Initialize database connection with production settings"""
        try:
            # Create async engine with connection pooling
            self.engine = create_async_engine(
                self.settings.database_url,
                poolclass=QueuePool,
                pool_size=self.settings.database_pool_size,
                max_overflow=self.settings.database_max_overflow,
                pool_timeout=self.settings.database_pool_timeout,
                pool_recycle=self.settings.database_pool_recycle,
                echo=self.settings.debug,
                future=True
            )

            # Create session factory
            self.async_session_factory = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # Test connection
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            logger.info("Database connected successfully")

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup database connections - alias for disconnect()"""
        await self.disconnect()

    async def disconnect(self):
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database disconnected")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with automatic cleanup"""
        if not self.async_session_factory:
            raise RuntimeError("Database not connected")
        
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def health_check(self) -> bool:
        """Check database health"""
        try:
            async with self.get_session() as session:
                result = await session.execute("SELECT 1")
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def get_connection_stats(self) -> dict:
        """Get connection pool statistics"""
        if not self.engine:
            return {}
        
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }


# Global database manager instance
_database_manager: Optional[DatabaseManager] = None


def get_database() -> DatabaseManager:
    """Get global database manager instance"""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for getting database session"""
    db = get_database()
    async with db.get_session() as session:
        yield session


# Import conversation models to register them with Base
try:
    from src.models.conversation import Conversation, Message
    logger.info("Conversation models imported successfully")
except ImportError as e:
    logger.warning(f"Could not import conversation models: {e}")

# Global database instance for easy access
database = get_database()
