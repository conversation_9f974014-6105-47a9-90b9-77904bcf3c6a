"""
Core Data Models
Pydantic models for request/response validation and data structures
"""

import time
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator


class ModelType(str, Enum):
    """Available model types"""
    CLAUDE_SONNET = "claude-sonnet"
    DEEPSEEK_V3 = "deepseek-v3"
    LLAMA_FREE = "llama-free"
    MISTRAL_FREE = "mistral-free"
    DEEPSEEK_CODER = "deepseek-coder"


class TaskComplexity(str, Enum):
    """Task complexity levels"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"


class OptimizationLevel(int, Enum):
    """Optimization aggressiveness levels"""
    CONSERVATIVE = 1
    BALANCED = 2
    MODERATE = 3
    AGGRESSIVE = 4
    MAXIMUM = 5


class CacheHitType(str, Enum):
    """Cache hit types"""
    EXACT_MATCH = "exact_match"
    HIGH_SIMILARITY = "high_similarity"
    MEDIUM_SIMILARITY = "medium_similarity"
    MISS = "miss"


# Request Models
class OptimizationRequest(BaseModel):
    """Request for cost optimization"""
    prompt: str = Field(..., min_length=1, max_length=100000, description="Input prompt to optimize")
    quality_threshold: float = Field(0.85, ge=0.6, le=1.0, description="Minimum quality score required")
    max_cost: Optional[float] = Field(None, gt=0, description="Maximum cost allowed")
    optimization_level: OptimizationLevel = Field(OptimizationLevel.MODERATE, description="Optimization aggressiveness")
    model_preference: Optional[ModelType] = Field(None, description="Preferred model if available")
    use_case: Optional[str] = Field(None, description="Use case for better optimization")
    user_id: Optional[str] = Field(None, description="User ID for personalization")
    
    @validator("prompt")
    def validate_prompt(cls, v):
        if not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v.strip()


class BatchOptimizationRequest(BaseModel):
    """Batch optimization request"""
    requests: List[OptimizationRequest] = Field(..., min_items=1, max_items=50)
    parallel_processing: bool = Field(True, description="Process requests in parallel")


# Response Models
class OptimizationStep(BaseModel):
    """Individual optimization step"""
    agent: str = Field(..., description="Agent that performed the step")
    action: str = Field(..., description="Action taken")
    before: str = Field(..., description="State before optimization")
    after: str = Field(..., description="State after optimization")
    savings: float = Field(..., description="Cost savings from this step")
    processing_time_ms: int = Field(..., description="Time taken for this step")


class ModelPerformance(BaseModel):
    """Model performance metrics"""
    model_name: str
    quality_score: float = Field(..., ge=0.0, le=1.0)
    cost_per_token: float = Field(..., ge=0.0)
    avg_response_time_ms: int = Field(..., ge=0)
    success_rate: float = Field(..., ge=0.0, le=1.0)
    last_updated: datetime


class OptimizationResponse(BaseModel):
    """Response from optimization"""
    request_id: UUID = Field(default_factory=uuid4)
    optimized_prompt: str = Field(..., description="Optimized prompt")
    selected_model: str = Field(..., description="Model selected for execution")
    original_cost: float = Field(..., ge=0, description="Original estimated cost")
    optimized_cost: float = Field(..., ge=0, description="Optimized actual cost")
    savings_percentage: float = Field(..., description="Percentage cost savings")
    quality_score: float = Field(..., ge=0.0, le=1.0, description="Quality score of optimization")
    processing_time_ms: int = Field(..., ge=0, description="Total processing time")
    cache_hit: bool = Field(..., description="Whether result came from cache")
    cache_hit_type: CacheHitType = Field(..., description="Type of cache hit")
    optimization_steps: List[OptimizationStep] = Field(default_factory=list)
    task_complexity: TaskComplexity = Field(..., description="Detected task complexity")
    routing_reason: str = Field(..., description="Reason for model selection")
    compression_ratio: float = Field(..., ge=0.0, le=1.0, description="Token compression achieved")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class BatchOptimizationResponse(BaseModel):
    """Batch optimization response"""
    batch_id: UUID = Field(default_factory=uuid4)
    results: List[OptimizationResponse]
    total_savings: float = Field(..., description="Total cost savings")
    batch_processing_time_ms: int = Field(..., description="Total batch processing time")
    success_count: int = Field(..., description="Number of successful optimizations")
    failure_count: int = Field(..., description="Number of failed optimizations")


# Cache Models
class CacheEntry(BaseModel):
    """Cache entry model"""
    id: UUID = Field(default_factory=uuid4)
    prompt_hash: str = Field(..., description="Hash of the original prompt")
    prompt_embedding: List[float] = Field(..., description="Vector embedding of prompt")
    response_data: Dict[str, Any] = Field(..., description="Cached response data")
    model_used: str = Field(..., description="Model that generated the response")
    quality_score: float = Field(..., ge=0.0, le=1.0)
    hit_count: int = Field(0, ge=0, description="Number of times this entry was used")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field(..., description="Cache expiration time")


# Analytics Models
class UsageMetrics(BaseModel):
    """Usage metrics model"""
    timeframe: str = Field(..., description="Time period for metrics")
    total_requests: int = Field(..., ge=0)
    total_savings: float = Field(..., ge=0)
    average_savings_percentage: float = Field(..., ge=0)
    cache_hit_rate: float = Field(..., ge=0.0, le=1.0)
    model_distribution: Dict[str, int] = Field(default_factory=dict)
    quality_scores: Dict[str, float] = Field(default_factory=dict)
    avg_processing_time_ms: float = Field(..., ge=0)


class CacheStats(BaseModel):
    """Cache performance statistics"""
    hit_rate: float = Field(..., ge=0.0, le=1.0)
    miss_rate: float = Field(..., ge=0.0, le=1.0)
    total_entries: int = Field(..., ge=0)
    memory_usage_mb: float = Field(..., ge=0)
    average_retrieval_time_ms: float = Field(..., ge=0)


# Health Check Models
class ServiceStatus(str, Enum):
    """Service status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class HealthResponse(BaseModel):
    """Health check response"""
    status: ServiceStatus
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    uptime_seconds: int = Field(..., ge=0)
    services: Dict[str, str] = Field(default_factory=dict)
    version: str = Field(..., description="Application version")


# Error Models
class ErrorResponse(BaseModel):
    """Standardized error response"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
    timestamp: float = Field(default_factory=time.time)


# Configuration Models
class ModelConfig(BaseModel):
    """Model configuration"""
    name: str
    api_endpoint: str
    cost_per_input_token: float = Field(..., ge=0)
    cost_per_output_token: float = Field(..., ge=0)
    max_tokens: int = Field(..., gt=0)
    quality_score: float = Field(..., ge=0.0, le=1.0)
    use_cases: List[str] = Field(default_factory=list)
    enabled: bool = True


class OptimizationConfig(BaseModel):
    """Optimization configuration"""
    compression_enabled: bool = True
    max_compression_ratio: float = Field(0.7, ge=0.0, le=1.0)
    cache_enabled: bool = True
    cache_similarity_threshold: float = Field(0.75, ge=0.0, le=1.0)
    quality_threshold: float = Field(0.85, ge=0.0, le=1.0)
    adaptive_learning_enabled: bool = True
    cost_reduction_target: float = Field(200.0, ge=100.0)


# Learning Models
class LearningMetrics(BaseModel):
    """Adaptive learning metrics"""
    current_cost_reduction_target: float = Field(..., ge=100.0)
    success_rate: float = Field(..., ge=0.0, le=1.0)
    optimization_accuracy: float = Field(..., ge=0.0, le=1.0)
    model_selection_accuracy: float = Field(..., ge=0.0, le=1.0)
    learning_iterations: int = Field(..., ge=0)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
