"""
Environment Configuration Validator
Production-grade environment validation with FAANG+ standards
"""

import os
import logging
import re
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationResult:
    """Environment validation result"""
    level: ValidationLevel
    variable: str
    message: str
    current_value: Optional[str] = None
    expected_format: Optional[str] = None


class EnvironmentValidator:
    """
    Production-grade environment configuration validator
    Ensures all required environment variables are properly configured
    """
    
    def __init__(self):
        # Required environment variables with validation rules
        self.required_vars = {
            # Core Application
            'ENVIRONMENT': {
                'required': True,
                'allowed_values': ['development', 'staging', 'production'],
                'description': 'Application environment'
            },
            'HOST': {
                'required': True,
                'pattern': r'^[\w\.-]+$',
                'description': 'Application host'
            },
            'PORT': {
                'required': True,
                'type': 'int',
                'min_value': 1,
                'max_value': 65535,
                'description': 'Application port'
            },
            
            # Database Configuration
            'DATABASE_URL': {
                'required': True,
                'pattern': r'^postgresql(\+asyncpg)?://[\w\.-]+:\w+@[\w\.-]+:\d+/\w+$',
                'description': 'PostgreSQL database connection string'
            },
            'DATABASE_POOL_SIZE': {
                'required': False,
                'type': 'int',
                'min_value': 1,
                'max_value': 100,
                'default': 20,
                'description': 'Database connection pool size'
            },
            
            # Redis Configuration
            'REDIS_URL': {
                'required': True,
                'pattern': r'^redis://[\w\.-]+(:\d+)?(/\d+)?$',
                'description': 'Redis connection string'
            },
            
            # OpenRouter API
            'OPENROUTER_API_KEY': {
                'required': True,
                'min_length': 10,
                'description': 'OpenRouter API key for model routing'
            },
            'OPENROUTER_BASE_URL': {
                'required': False,
                'pattern': r'^https?://[\w\.-]+',
                'default': 'https://openrouter.ai/api/v1',
                'description': 'OpenRouter API base URL'
            },
            
            # Security
            'JWT_SECRET_KEY': {
                'required': True,
                'min_length': 32,
                'description': 'JWT secret key for token signing'
            },
            'API_KEY': {
                'required': True,
                'min_length': 16,
                'description': 'API key for service authentication'
            },
            
            # Vector Databases (Optional but recommended)
            'CHROMADB_HOST': {
                'required': False,
                'pattern': r'^[\w\.-]+$',
                'default': 'localhost',
                'description': 'ChromaDB host'
            },
            'QDRANT_HOST': {
                'required': False,
                'pattern': r'^[\w\.-]+$',
                'default': 'localhost',
                'description': 'Qdrant host'
            },
            'WEAVIATE_HOST': {
                'required': False,
                'pattern': r'^[\w\.-]+$',
                'default': 'localhost',
                'description': 'Weaviate host'
            },
            'MILVUS_HOST': {
                'required': False,
                'pattern': r'^[\w\.-]+$',
                'default': 'localhost',
                'description': 'Milvus host'
            },
            'ELASTICSEARCH_HOST': {
                'required': False,
                'pattern': r'^[\w\.-]+$',
                'default': 'localhost',
                'description': 'Elasticsearch host'
            },
            
            # Performance Settings
            'MAX_CONCURRENT_REQUESTS': {
                'required': False,
                'type': 'int',
                'min_value': 1,
                'max_value': 1000,
                'default': 100,
                'description': 'Maximum concurrent requests'
            },
            'REQUEST_TIMEOUT_SECONDS': {
                'required': False,
                'type': 'int',
                'min_value': 1,
                'max_value': 300,
                'default': 30,
                'description': 'Request timeout in seconds'
            },
            
            # Quality Thresholds
            'DEFAULT_QUALITY_THRESHOLD': {
                'required': False,
                'type': 'float',
                'min_value': 0.0,
                'max_value': 1.0,
                'default': 0.85,
                'description': 'Default quality threshold'
            },
            'MAX_COMPRESSION_RATIO': {
                'required': False,
                'type': 'float',
                'min_value': 0.0,
                'max_value': 1.0,
                'default': 0.7,
                'description': 'Maximum compression ratio'
            }
        }
        
        # Environment-specific requirements
        self.environment_requirements = {
            'production': {
                'required_vars': [
                    'DATABASE_SSL_MODE',
                    'SENTRY_DSN',
                    'SSL_ENABLED'
                ],
                'forbidden_values': {
                    'DEBUG': ['true', 'True', '1'],
                    'MOCK_OPENROUTER': ['true', 'True', '1'],
                    'MOCK_VECTOR_DATABASES': ['true', 'True', '1']
                }
            },
            'staging': {
                'required_vars': [
                    'DATABASE_SSL_MODE'
                ],
                'forbidden_values': {
                    'DEBUG': ['true', 'True', '1']
                }
            },
            'development': {
                'allowed_values': {
                    'DEBUG': ['true', 'True', '1', 'false', 'False', '0']
                }
            }
        }
    
    def validate_environment(self) -> List[ValidationResult]:
        """
        Validate all environment variables
        
        Returns:
            List of validation results
        """
        results = []
        
        # Get current environment
        environment = os.getenv('ENVIRONMENT', 'development')
        
        # Validate core variables
        for var_name, config in self.required_vars.items():
            result = self._validate_variable(var_name, config)
            if result:
                results.append(result)
        
        # Validate environment-specific requirements
        env_results = self._validate_environment_specific(environment)
        results.extend(env_results)
        
        # Validate security settings
        security_results = self._validate_security_settings(environment)
        results.extend(security_results)
        
        return results
    
    def _validate_variable(self, var_name: str, config: Dict[str, Any]) -> Optional[ValidationResult]:
        """Validate a single environment variable"""
        value = os.getenv(var_name)
        
        # Check if required variable is missing
        if config.get('required', False) and not value:
            return ValidationResult(
                level=ValidationLevel.ERROR,
                variable=var_name,
                message=f"Required environment variable '{var_name}' is not set",
                expected_format=config.get('description', 'No description available')
            )
        
        # If variable is not set and not required, check for default
        if not value:
            if 'default' in config:
                return ValidationResult(
                    level=ValidationLevel.INFO,
                    variable=var_name,
                    message=f"Using default value for '{var_name}'",
                    current_value=str(config['default'])
                )
            return None
        
        # Validate type
        if 'type' in config:
            type_result = self._validate_type(var_name, value, config)
            if type_result:
                return type_result
        
        # Validate pattern
        if 'pattern' in config:
            if not re.match(config['pattern'], value):
                return ValidationResult(
                    level=ValidationLevel.ERROR,
                    variable=var_name,
                    message=f"Value for '{var_name}' does not match expected pattern",
                    current_value=value,
                    expected_format=config['pattern']
                )
        
        # Validate allowed values
        if 'allowed_values' in config:
            if value not in config['allowed_values']:
                return ValidationResult(
                    level=ValidationLevel.ERROR,
                    variable=var_name,
                    message=f"Value for '{var_name}' is not in allowed values",
                    current_value=value,
                    expected_format=f"One of: {', '.join(config['allowed_values'])}"
                )
        
        # Validate string length
        if 'min_length' in config:
            if len(value) < config['min_length']:
                return ValidationResult(
                    level=ValidationLevel.ERROR,
                    variable=var_name,
                    message=f"Value for '{var_name}' is too short (minimum {config['min_length']} characters)",
                    current_value=f"Length: {len(value)}"
                )
        
        return None
    
    def _validate_type(self, var_name: str, value: str, config: Dict[str, Any]) -> Optional[ValidationResult]:
        """Validate variable type and range"""
        var_type = config['type']
        
        try:
            if var_type == 'int':
                int_value = int(value)
                
                if 'min_value' in config and int_value < config['min_value']:
                    return ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Value for '{var_name}' is below minimum ({config['min_value']})",
                        current_value=value
                    )
                
                if 'max_value' in config and int_value > config['max_value']:
                    return ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Value for '{var_name}' is above maximum ({config['max_value']})",
                        current_value=value
                    )
            
            elif var_type == 'float':
                float_value = float(value)
                
                if 'min_value' in config and float_value < config['min_value']:
                    return ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Value for '{var_name}' is below minimum ({config['min_value']})",
                        current_value=value
                    )
                
                if 'max_value' in config and float_value > config['max_value']:
                    return ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Value for '{var_name}' is above maximum ({config['max_value']})",
                        current_value=value
                    )
            
            elif var_type == 'bool':
                if value.lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                    return ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Value for '{var_name}' is not a valid boolean",
                        current_value=value,
                        expected_format="true, false, 1, 0, yes, or no"
                    )
        
        except ValueError:
            return ValidationResult(
                level=ValidationLevel.ERROR,
                variable=var_name,
                message=f"Value for '{var_name}' cannot be converted to {var_type}",
                current_value=value,
                expected_format=f"Valid {var_type} value"
            )
        
        return None
    
    def _validate_environment_specific(self, environment: str) -> List[ValidationResult]:
        """Validate environment-specific requirements"""
        results = []
        
        if environment not in self.environment_requirements:
            return results
        
        env_config = self.environment_requirements[environment]
        
        # Check required variables for this environment
        for var_name in env_config.get('required_vars', []):
            if not os.getenv(var_name):
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    variable=var_name,
                    message=f"Variable '{var_name}' is required for {environment} environment"
                ))
        
        # Check forbidden values
        for var_name, forbidden_values in env_config.get('forbidden_values', {}).items():
            value = os.getenv(var_name)
            if value in forbidden_values:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    variable=var_name,
                    message=f"Value '{value}' for '{var_name}' is not allowed in {environment} environment",
                    current_value=value
                ))
        
        return results
    
    def _validate_security_settings(self, environment: str) -> List[ValidationResult]:
        """Validate security-related settings"""
        results = []
        
        # Check for weak secrets in production
        if environment == 'production':
            weak_secrets = [
                'your_jwt_secret_key_here_change_in_production',
                'your_api_key_here_change_in_production',
                'change_this_in_production',
                'secret',
                'password',
                '123456'
            ]
            
            for var_name in ['JWT_SECRET_KEY', 'API_KEY']:
                value = os.getenv(var_name, '')
                if value.lower() in [s.lower() for s in weak_secrets]:
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        variable=var_name,
                        message=f"Weak or default secret detected for '{var_name}' in production"
                    ))
        
        # Check SSL settings for production
        if environment == 'production':
            ssl_enabled = os.getenv('SSL_ENABLED', 'false').lower()
            if ssl_enabled not in ['true', '1', 'yes']:
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    variable='SSL_ENABLED',
                    message="SSL is not enabled in production environment"
                ))
        
        return results
    
    def print_validation_results(self, results: List[ValidationResult]):
        """Print validation results in a formatted way"""
        if not results:
            logger.info("✅ All environment variables are properly configured")
            return
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        info = [r for r in results if r.level == ValidationLevel.INFO]
        
        if errors:
            logger.error(f"❌ Found {len(errors)} configuration errors:")
            for result in errors:
                logger.error(f"  • {result.variable}: {result.message}")
                if result.current_value:
                    logger.error(f"    Current: {result.current_value}")
                if result.expected_format:
                    logger.error(f"    Expected: {result.expected_format}")
        
        if warnings:
            logger.warning(f"⚠️  Found {len(warnings)} configuration warnings:")
            for result in warnings:
                logger.warning(f"  • {result.variable}: {result.message}")
        
        if info:
            logger.info(f"ℹ️  Configuration info ({len(info)} items):")
            for result in info:
                logger.info(f"  • {result.variable}: {result.message}")
    
    def validate_and_exit_on_error(self):
        """Validate environment and exit if critical errors found"""
        results = self.validate_environment()
        self.print_validation_results(results)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        if errors:
            logger.error("❌ Environment validation failed. Please fix the errors above.")
            exit(1)
        
        logger.info("✅ Environment validation passed")


def validate_environment():
    """Convenience function to validate environment"""
    validator = EnvironmentValidator()
    validator.validate_and_exit_on_error()


if __name__ == "__main__":
    validate_environment()
