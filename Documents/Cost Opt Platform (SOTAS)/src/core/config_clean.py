"""
Configuration Management
Clean, battle-tested configuration based on proven FastAPI patterns
"""

import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # App settings
    app_name: str = Field(default="Claude Sonnet Cost Optimizer")
    app_description: str = Field(default="Production-grade cost optimization platform")
    app_version: str = Field(default="1.0.0")
    environment: str = Field(default="local")  # local, staging, production
    
    # Database settings (SQLite for simplicity)
    database_url: str = Field(default="sqlite+aiosqlite:///./data/costopt.db")
    
    # Redis settings
    redis_url: str = Field(default="redis://localhost:6379")
    
    # Security settings
    secret_key: str = Field(default="your-secret-key-change-in-production")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    
    # OpenRouter API settings
    openrouter_api_key: str = Field(default="placeholder-key")
    openrouter_base_url: str = Field(default="https://openrouter.ai/api/v1")
    
    # CORS settings
    allowed_origins: List[str] = Field(default=["*"])
    
    # Cost optimization settings
    default_quality_threshold: float = Field(default=0.85)
    cache_ttl_seconds: int = Field(default=3600)
    max_retries: int = Field(default=3)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


def get_settings() -> Settings:
    """Get application settings"""
    return Settings()
