"""
OpenRouter.ai Integration Client
Production-grade client for OpenRouter API with cost tracking and error handling
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, asdict

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

from .config_clean import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class Usage:
    """Token usage tracking"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: float


@dataclass
class ChatMessage:
    """Chat message structure"""
    role: str  # "user", "assistant", "system"
    content: str


@dataclass
class ChatRequest:
    """Chat completion request"""
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stream: bool = False


@dataclass
class ChatResponse:
    """Chat completion response"""
    id: str
    model: str
    content: str
    usage: Usage
    created_at: datetime
    processing_time_ms: int


class OpenRouterError(Exception):
    """OpenRouter API error"""
    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(message)


class RateLimitError(OpenRouterError):
    """Rate limit exceeded error"""
    pass


class OpenRouterClient:
    """
    Production-grade OpenRouter API client
    Features: retry logic, rate limiting, cost tracking, error handling
    """
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or settings.openrouter_api_key
        self.base_url = base_url or settings.openrouter_base_url
        
        if not self.api_key or self.api_key == "placeholder-key":
            logger.warning("OpenRouter API key not configured - using placeholder")
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://costopt.local",  # Required by OpenRouter
                "X-Title": "Claude Cost Optimizer"
            },
            timeout=60.0
        )
        
        # Cost tracking
        self.total_cost = 0.0
        self.request_count = 0
        
        # Model pricing (per 1M tokens)
        self.model_pricing = {
            "anthropic/claude-3.5-sonnet": {"input": 3.0, "output": 15.0},
            "anthropic/claude-3-haiku": {"input": 0.25, "output": 1.25},
            "deepseek/deepseek-chat": {"input": 0.14, "output": 0.28},
            "meta-llama/llama-3.1-8b-instruct:free": {"input": 0.0, "output": 0.0},
            "meta-llama/llama-3.1-70b-instruct": {"input": 0.52, "output": 0.75},
        }
    
    def _calculate_cost(self, model: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Calculate cost based on token usage"""
        pricing = self.model_pricing.get(model, {"input": 1.0, "output": 2.0})
        
        input_cost = (prompt_tokens / 1_000_000) * pricing["input"]
        output_cost = (completion_tokens / 1_000_000) * pricing["output"]
        
        return input_cost + output_cost
    
    def _parse_usage(self, usage_data: Dict[str, Any], model: str) -> Usage:
        """Parse usage data from API response"""
        prompt_tokens = usage_data.get("prompt_tokens", 0)
        completion_tokens = usage_data.get("completion_tokens", 0)
        total_tokens = usage_data.get("total_tokens", prompt_tokens + completion_tokens)
        
        cost = self._calculate_cost(model, prompt_tokens, completion_tokens)
        
        return Usage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            cost_usd=cost
        )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    async def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        try:
            response = await self.client.post(endpoint, json=payload)
            
            if response.status_code == 429:
                raise RateLimitError("Rate limit exceeded", response.status_code)
            elif response.status_code == 401:
                raise OpenRouterError("Invalid API key", response.status_code)
            elif response.status_code >= 400:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                raise OpenRouterError(error_msg, response.status_code)
            
            response.raise_for_status()
            return response.json()
            
        except httpx.RequestError as e:
            raise OpenRouterError(f"Request failed: {e}")
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Create chat completion with cost optimization
        """
        start_time = time.time()
        
        # Convert messages to API format
        messages = [asdict(msg) for msg in request.messages]
        
        payload = {
            "model": request.model,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "frequency_penalty": request.frequency_penalty,
            "presence_penalty": request.presence_penalty,
            "stream": request.stream
        }
        
        # Remove None values
        payload = {k: v for k, v in payload.items() if v is not None}
        
        try:
            response_data = await self._make_request("/chat/completions", payload)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # Parse response
            choice = response_data["choices"][0]
            content = choice["message"]["content"]
            
            # Parse usage and calculate cost
            usage = self._parse_usage(response_data.get("usage", {}), request.model)
            
            # Update tracking
            self.total_cost += usage.cost_usd
            self.request_count += 1
            
            logger.info(
                f"OpenRouter request completed: {request.model}, "
                f"tokens: {usage.total_tokens}, cost: ${usage.cost_usd:.6f}, "
                f"time: {processing_time}ms"
            )
            
            return ChatResponse(
                id=response_data.get("id", "unknown"),
                model=response_data.get("model", request.model),
                content=content,
                usage=usage,
                created_at=datetime.utcnow(),
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Stream chat completion (for real-time responses)
        """
        request.stream = True
        
        # Convert messages to API format
        messages = [asdict(msg) for msg in request.messages]
        
        payload = {
            "model": request.model,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": True
        }
        
        # Remove None values
        payload = {k: v for k, v in payload.items() if v is not None}
        
        try:
            async with self.client.stream("POST", "/chat/completions", json=payload) as response:
                if response.status_code >= 400:
                    error_data = await response.aread()
                    raise OpenRouterError(f"Stream failed: {error_data}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix
                        if data == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            delta = chunk["choices"][0]["delta"]
                            if "content" in delta:
                                yield delta["content"]
                        except (json.JSONDecodeError, KeyError):
                            continue
                            
        except Exception as e:
            logger.error(f"Stream completion failed: {e}")
            raise
    
    async def get_models(self) -> List[Dict[str, Any]]:
        """Get available models from OpenRouter"""
        try:
            response_data = await self._make_request("/models", {})
            return response_data.get("data", [])
        except Exception as e:
            logger.error(f"Failed to get models: {e}")
            return []
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return {
            "total_requests": self.request_count,
            "total_cost_usd": self.total_cost,
            "average_cost_per_request": self.total_cost / max(1, self.request_count),
            "models_used": list(self.model_pricing.keys())
        }
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global client instance
_client: Optional[OpenRouterClient] = None


async def get_openrouter_client() -> OpenRouterClient:
    """Get or create global OpenRouter client"""
    global _client
    if _client is None:
        _client = OpenRouterClient()
    return _client


async def close_openrouter_client():
    """Close global OpenRouter client"""
    global _client
    if _client is not None:
        await _client.close()
        _client = None
