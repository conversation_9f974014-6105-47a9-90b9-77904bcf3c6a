"""
Database Management
Clean, battle-tested database setup with SQLite for simplicity
"""

import logging
from datetime import datetime
from typing import AsyncGenerator
from uuid import uuid4

from sqlalchemy import Column, String, Float, Boolean, DateTime, Text, Integer
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base

from .config_clean import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Database setup
engine = create_async_engine(
    settings.database_url,
    echo=settings.environment == "local",
    future=True
)

async_session_maker = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

Base = declarative_base()


def generate_uuid() -> str:
    """Generate UUID string"""
    return str(uuid4())


class OptimizationRequest(Base):
    """Optimization request records"""
    __tablename__ = "optimization_requests"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    original_prompt = Column(Text, nullable=False)
    optimized_prompt = Column(Text)
    selected_model = Column(String(100), nullable=False)
    original_cost = Column(Float)
    optimized_cost = Column(Float)
    savings_percentage = Column(Float)
    quality_score = Column(Float)
    processing_time_ms = Column(Integer)
    cache_hit = Column(Boolean, default=False)
    user_id = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class CacheEntry(Base):
    """Cache entries for optimization results"""
    __tablename__ = "cache_entries"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    prompt_hash = Column(String(64), unique=True, nullable=False)
    response_data = Column(Text)  # JSON string
    model_used = Column(String(100))
    quality_score = Column(Float)
    hit_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)


async def init_database():
    """Initialize database tables"""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


async def get_database() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()
