"""
Service Discovery for OrbStack Internal Networking
Production-grade service discovery with health checking and failover
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import socket
from urllib.parse import urlparse

from prometheus_client import Counter, Histogram, Gauge

logger = logging.getLogger(__name__)

# Service discovery metrics
SERVICE_DISCOVERY_REQUESTS = Counter(
    'service_discovery_requests_total',
    'Total service discovery requests',
    ['service_name', 'result']
)

SERVICE_HEALTH_CHECKS = Counter(
    'service_health_checks_total',
    'Total service health checks',
    ['service_name', 'status']
)

SERVICE_RESPONSE_TIME = Histogram(
    'service_response_time_seconds',
    'Service response time',
    ['service_name']
)

ACTIVE_SERVICES = Gauge(
    'active_services_total',
    'Number of active services',
    ['service_type']
)


class ServiceStatus(Enum):
    """Service status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    STARTING = "starting"
    STOPPING = "stopping"


@dataclass
class ServiceEndpoint:
    """Service endpoint configuration"""
    name: str
    host: str
    port: int
    protocol: str = "http"
    health_check_path: str = "/health"
    timeout_seconds: int = 5
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    
    # Service metadata
    service_type: str = "api"
    version: str = "1.0.0"
    tags: List[str] = field(default_factory=list)
    
    # Health status
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_health_check: float = 0.0
    consecutive_failures: int = 0
    
    @property
    def url(self) -> str:
        """Get full service URL"""
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def health_url(self) -> str:
        """Get health check URL"""
        return f"{self.url}{self.health_check_path}"
    
    def is_healthy(self) -> bool:
        """Check if service is healthy"""
        return self.status == ServiceStatus.HEALTHY
    
    def mark_healthy(self):
        """Mark service as healthy"""
        self.status = ServiceStatus.HEALTHY
        self.consecutive_failures = 0
        self.last_health_check = time.time()
    
    def mark_unhealthy(self):
        """Mark service as unhealthy"""
        self.status = ServiceStatus.UNHEALTHY
        self.consecutive_failures += 1
        self.last_health_check = time.time()


class ServiceRegistry:
    """
    Service registry for internal service discovery
    Optimized for OrbStack container networking
    """
    
    def __init__(self):
        self.services: Dict[str, ServiceEndpoint] = {}
        self.service_groups: Dict[str, List[str]] = {}
        self._health_check_interval = 30  # seconds
        self._health_check_task: Optional[asyncio.Task] = None
        self._session: Optional[aiohttp.ClientSession] = None
        
        # Initialize default services for OrbStack deployment
        self._register_default_services()
    
    def _register_default_services(self):
        """Register default services for OrbStack deployment"""
        
        # Database service
        self.register_service(ServiceEndpoint(
            name="database",
            host="db",
            port=5432,
            protocol="postgresql",
            health_check_path="/",
            service_type="database",
            tags=["postgres", "primary"]
        ))
        
        # Redis cache service
        self.register_service(ServiceEndpoint(
            name="redis",
            host="redis",
            port=6379,
            protocol="redis",
            health_check_path="/",
            service_type="cache",
            tags=["redis", "cache"]
        ))
        
        # ChromaDB vector database
        self.register_service(ServiceEndpoint(
            name="chromadb",
            host="chromadb",
            port=8000,
            protocol="http",
            health_check_path="/api/v1/heartbeat",
            service_type="vector_db",
            tags=["chromadb", "vector", "similarity"]
        ))
        
        # Jaeger tracing
        self.register_service(ServiceEndpoint(
            name="jaeger",
            host="jaeger",
            port=14268,
            protocol="http",
            health_check_path="/",
            service_type="tracing",
            tags=["jaeger", "tracing", "observability"]
        ))
        
        # Prometheus metrics
        self.register_service(ServiceEndpoint(
            name="prometheus",
            host="prometheus",
            port=9090,
            protocol="http",
            health_check_path="/-/healthy",
            service_type="metrics",
            tags=["prometheus", "metrics", "monitoring"]
        ))
        
        # Grafana dashboards
        self.register_service(ServiceEndpoint(
            name="grafana",
            host="grafana",
            port=3000,
            protocol="http",
            health_check_path="/api/health",
            service_type="dashboard",
            tags=["grafana", "dashboard", "visualization"]
        ))
        
        # N8N workflow automation
        self.register_service(ServiceEndpoint(
            name="n8n",
            host="n8n",
            port=5678,
            protocol="http",
            health_check_path="/healthz",
            service_type="automation",
            tags=["n8n", "workflow", "automation"]
        ))
        
        # Group services by type
        self.service_groups = {
            "core": ["database", "redis"],
            "ai": ["chromadb"],
            "monitoring": ["prometheus", "grafana", "jaeger"],
            "automation": ["n8n"]
        }
    
    def register_service(self, service: ServiceEndpoint):
        """Register a service in the registry"""
        self.services[service.name] = service
        logger.info(f"Registered service: {service.name} at {service.url}")
        
        SERVICE_DISCOVERY_REQUESTS.labels(
            service_name=service.name,
            result="registered"
        ).inc()
    
    def unregister_service(self, service_name: str):
        """Unregister a service from the registry"""
        if service_name in self.services:
            del self.services[service_name]
            logger.info(f"Unregistered service: {service_name}")
            
            SERVICE_DISCOVERY_REQUESTS.labels(
                service_name=service_name,
                result="unregistered"
            ).inc()
    
    def get_service(self, service_name: str) -> Optional[ServiceEndpoint]:
        """Get service by name"""
        service = self.services.get(service_name)
        
        if service:
            SERVICE_DISCOVERY_REQUESTS.labels(
                service_name=service_name,
                result="found"
            ).inc()
        else:
            SERVICE_DISCOVERY_REQUESTS.labels(
                service_name=service_name,
                result="not_found"
            ).inc()
        
        return service
    
    def get_healthy_service(self, service_name: str) -> Optional[ServiceEndpoint]:
        """Get service only if it's healthy"""
        service = self.get_service(service_name)
        
        if service and service.is_healthy():
            return service
        
        return None
    
    def get_services_by_type(self, service_type: str) -> List[ServiceEndpoint]:
        """Get all services of a specific type"""
        return [
            service for service in self.services.values()
            if service.service_type == service_type
        ]
    
    def get_healthy_services_by_type(self, service_type: str) -> List[ServiceEndpoint]:
        """Get all healthy services of a specific type"""
        return [
            service for service in self.get_services_by_type(service_type)
            if service.is_healthy()
        ]
    
    def get_service_group(self, group_name: str) -> List[ServiceEndpoint]:
        """Get all services in a group"""
        service_names = self.service_groups.get(group_name, [])
        return [
            self.services[name] for name in service_names
            if name in self.services
        ]
    
    async def start_health_monitoring(self):
        """Start background health monitoring"""
        if self._health_check_task is None:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10)
            )
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            logger.info("Started service health monitoring")
    
    async def stop_health_monitoring(self):
        """Stop background health monitoring"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
        
        if self._session:
            await self._session.close()
            self._session = None
        
        logger.info("Stopped service health monitoring")
    
    async def _health_check_loop(self):
        """Background health check loop"""
        while True:
            try:
                await self._check_all_services_health()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check loop error: {e}")
                await asyncio.sleep(5)  # Short delay on error
    
    async def _check_all_services_health(self):
        """Check health of all registered services"""
        tasks = []
        
        for service in self.services.values():
            task = asyncio.create_task(self._check_service_health(service))
            tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # Update metrics
        for service_type in set(s.service_type for s in self.services.values()):
            healthy_count = len(self.get_healthy_services_by_type(service_type))
            ACTIVE_SERVICES.labels(service_type=service_type).set(healthy_count)
    
    async def _check_service_health(self, service: ServiceEndpoint):
        """Check health of a specific service"""
        start_time = time.time()
        
        try:
            if service.protocol in ["http", "https"]:
                await self._check_http_health(service)
            elif service.protocol == "postgresql":
                await self._check_postgres_health(service)
            elif service.protocol == "redis":
                await self._check_redis_health(service)
            else:
                await self._check_tcp_health(service)
            
            # Record response time
            response_time = time.time() - start_time
            SERVICE_RESPONSE_TIME.labels(service_name=service.name).observe(response_time)
            
        except Exception as e:
            logger.debug(f"Health check failed for {service.name}: {e}")
            service.mark_unhealthy()
            
            SERVICE_HEALTH_CHECKS.labels(
                service_name=service.name,
                status="unhealthy"
            ).inc()
    
    async def _check_http_health(self, service: ServiceEndpoint):
        """Check HTTP service health"""
        if not self._session:
            raise Exception("HTTP session not initialized")
        
        async with self._session.get(service.health_url) as response:
            if response.status == 200:
                service.mark_healthy()
                SERVICE_HEALTH_CHECKS.labels(
                    service_name=service.name,
                    status="healthy"
                ).inc()
            else:
                service.mark_unhealthy()
                SERVICE_HEALTH_CHECKS.labels(
                    service_name=service.name,
                    status="unhealthy"
                ).inc()
    
    async def _check_postgres_health(self, service: ServiceEndpoint):
        """Check PostgreSQL service health"""
        # Simple TCP connection check for PostgreSQL
        await self._check_tcp_health(service)
    
    async def _check_redis_health(self, service: ServiceEndpoint):
        """Check Redis service health"""
        # Simple TCP connection check for Redis
        await self._check_tcp_health(service)
    
    async def _check_tcp_health(self, service: ServiceEndpoint):
        """Check TCP service health"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(service.host, service.port),
                timeout=service.timeout_seconds
            )
            writer.close()
            await writer.wait_closed()
            
            service.mark_healthy()
            SERVICE_HEALTH_CHECKS.labels(
                service_name=service.name,
                status="healthy"
            ).inc()
            
        except Exception:
            service.mark_unhealthy()
            SERVICE_HEALTH_CHECKS.labels(
                service_name=service.name,
                status="unhealthy"
            ).inc()
    
    def get_service_status(self) -> Dict[str, Dict]:
        """Get status of all services"""
        return {
            name: {
                "status": service.status.value,
                "url": service.url,
                "service_type": service.service_type,
                "last_health_check": service.last_health_check,
                "consecutive_failures": service.consecutive_failures,
                "tags": service.tags
            }
            for name, service in self.services.items()
        }


# Global service registry instance
_service_registry: Optional[ServiceRegistry] = None


def get_service_registry() -> ServiceRegistry:
    """Get global service registry instance"""
    global _service_registry
    
    if _service_registry is None:
        _service_registry = ServiceRegistry()
    
    return _service_registry


async def initialize_service_discovery():
    """Initialize service discovery system"""
    registry = get_service_registry()
    await registry.start_health_monitoring()
    logger.info("Service discovery system initialized")


async def shutdown_service_discovery():
    """Shutdown service discovery system"""
    registry = get_service_registry()
    await registry.stop_health_monitoring()
    logger.info("Service discovery system shutdown")


# Convenience functions for common service access
def get_database_url() -> Optional[str]:
    """Get database connection URL"""
    service = get_service_registry().get_healthy_service("database")
    if service:
        return f"postgresql+asyncpg://costopt:costopt123@{service.host}:{service.port}/costopt"
    return None


def get_redis_url() -> Optional[str]:
    """Get Redis connection URL"""
    service = get_service_registry().get_healthy_service("redis")
    if service:
        return f"redis://{service.host}:{service.port}"
    return None


def get_chromadb_url() -> Optional[str]:
    """Get ChromaDB connection URL"""
    service = get_service_registry().get_healthy_service("chromadb")
    if service:
        return f"{service.protocol}://{service.host}:{service.port}"
    return None
