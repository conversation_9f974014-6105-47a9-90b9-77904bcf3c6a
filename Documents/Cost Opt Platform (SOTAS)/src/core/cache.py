"""
Cache Management System
Redis + ChromaDB integration for ultra-semantic caching with 80% hit rate target
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

import redis.asyncio as aioredis
import chromadb
import numpy as np
from opentelemetry import trace
from sentence_transformers import SentenceTransformer
from prometheus_client import Counter, Histogram, Gauge
from collections import defaultdict

from src.core.config import get_settings

# Cache performance metrics
CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'cache_type', 'result']
)

CACHE_HIT_RATIO = Gauge(
    'cache_hit_ratio',
    'Cache hit ratio',
    ['cache_type']
)

CACHE_RESPONSE_TIME = Histogram(
    'cache_response_time_seconds',
    'Cache operation response time',
    ['operation', 'cache_type'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
)

CACHE_SIZE = Gauge(
    'cache_size_bytes',
    'Cache size in bytes',
    ['cache_type']
)

CACHE_EVICTIONS = Counter(
    'cache_evictions_total',
    'Total cache evictions',
    ['cache_type', 'reason']
)
from src.core.models import CacheHitType

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)


class CacheManager:
    """
    Production-grade cache manager with Redis and ChromaDB
    Implements ultra-semantic caching for maximum hit rates
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.redis_client: Optional[aioredis.Redis] = None
        self.chroma_client: Optional[chromadb.Client] = None
        self.chroma_collection = None
        self.embedding_model: Optional[SentenceTransformer] = None
        
        # Enhanced cache statistics for production monitoring
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'exact_matches': 0,
            'high_similarity_matches': 0,
            'medium_similarity_matches': 0,
            'total_entries': 0,
            'redis_hits': 0,
            'chroma_hits': 0,
            'evictions': 0,
            'total_size_bytes': 0,
            'avg_response_time': 0.0,
            'batch_operations': 0,
            'pipeline_operations': 0
        }

        # Performance optimization settings
        self.batch_size = 100
        self.pipeline_threshold = 10
        self.compression_enabled = True
        self.async_eviction = True
        self.preload_enabled = True

        # Multi-layer cache for maximum performance
        self.memory_cache = {}  # L1 cache - fastest
        self.memory_cache_max_size = 1000
        self.memory_cache_ttl = 300  # 5 minutes
        
        # Cache configuration
        self.similarity_thresholds = {
            'exact_match': 1.0,
            'high_similarity': 0.90,
            'medium_similarity': self.settings.cache_similarity_threshold
        }
    
    async def initialize(self):
        """Initialize cache connections and embedding model"""
        with tracer.start_as_current_span("cache_initialization"):
            logger.info("Initializing Cache Manager...")
            
            # Initialize Redis connection
            await self._initialize_redis()
            
            # Initialize ChromaDB connection
            await self._initialize_chromadb()
            
            # Initialize embedding model
            await self._initialize_embedding_model()
            
            logger.info("Cache Manager initialized successfully")
    
    async def _initialize_redis(self):
        """Initialize Redis connection with production settings"""
        try:
            self.redis_client = aioredis.from_url(
                self.settings.redis_url,
                max_connections=self.settings.redis_max_connections,
                socket_timeout=self.settings.redis_socket_timeout,
                socket_connect_timeout=self.settings.redis_socket_connect_timeout,
                decode_responses=True,
                retry_on_timeout=True
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            raise
    
    async def _initialize_chromadb(self):
        """Initialize ChromaDB for vector similarity search"""
        try:
            # Connect to ChromaDB
            self.chroma_client = chromadb.HttpClient(
                host=self.settings.chromadb_host,
                port=self.settings.chromadb_port
            )
            
            # Get or create collection
            try:
                self.chroma_collection = self.chroma_client.get_collection(
                    name=self.settings.chromadb_collection_name
                )
            except Exception:
                # Create collection if it doesn't exist
                self.chroma_collection = self.chroma_client.create_collection(
                    name=self.settings.chromadb_collection_name,
                    metadata={"description": "Optimization cache embeddings"}
                )
            
            logger.info("ChromaDB connection established")
            
        except Exception as e:
            logger.error(f"ChromaDB connection failed: {e}")
            raise
    
    async def _initialize_embedding_model(self):
        """Initialize sentence transformer for embeddings"""
        try:
            # Use a lightweight but effective model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Embedding model loaded")
            
        except Exception as e:
            logger.error(f"Embedding model initialization failed: {e}")
            raise
    
    async def get_cached_result(self, prompt: str) -> Optional[Tuple[CacheHitType, Dict[str, Any]]]:
        """
        Get cached result with multi-layer similarity matching
        Returns (cache_hit_type, cached_data) or None
        """
        with tracer.start_as_current_span("cache_lookup") as span:
            self.stats['total_requests'] += 1
            span.set_attribute("prompt_length", len(prompt))
            
            try:
                # Layer 1: Exact match in Redis
                exact_result = await self._check_exact_match(prompt)
                if exact_result:
                    self.stats['cache_hits'] += 1
                    self.stats['exact_matches'] += 1
                    span.set_attribute("cache_hit_type", "exact_match")
                    return CacheHitType.EXACT_MATCH, exact_result
                
                # Layer 2: Semantic similarity in ChromaDB
                similarity_result = await self._check_semantic_similarity(prompt)
                if similarity_result:
                    hit_type, cached_data = similarity_result
                    self.stats['cache_hits'] += 1
                    
                    if hit_type == CacheHitType.HIGH_SIMILARITY:
                        self.stats['high_similarity_matches'] += 1
                    else:
                        self.stats['medium_similarity_matches'] += 1
                    
                    span.set_attribute("cache_hit_type", hit_type.value)
                    return hit_type, cached_data
                
                # Cache miss
                self.stats['cache_misses'] += 1
                span.set_attribute("cache_hit_type", "miss")
                return None
                
            except Exception as e:
                logger.error(f"Cache lookup failed: {e}")
                span.record_exception(e)
                return None
    
    async def _check_exact_match(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Check for exact match in Redis"""
        prompt_hash = self._hash_prompt(prompt)
        
        try:
            cached_data = await self.redis_client.get(f"exact:{prompt_hash}")
            if cached_data:
                return json.loads(cached_data)
            return None
            
        except Exception as e:
            logger.error(f"Redis exact match check failed: {e}")
            return None
    
    async def _check_semantic_similarity(self, prompt: str) -> Optional[Tuple[CacheHitType, Dict[str, Any]]]:
        """Check for semantic similarity in ChromaDB"""
        try:
            # Generate embedding for the prompt
            embedding = self.embedding_model.encode(prompt).tolist()
            
            # Query ChromaDB for similar embeddings
            results = self.chroma_collection.query(
                query_embeddings=[embedding],
                n_results=5,  # Get top 5 similar results
                include=['metadatas', 'distances']
            )
            
            if not results['ids'][0]:
                return None
            
            # Check similarity thresholds
            for i, distance in enumerate(results['distances'][0]):
                similarity = 1 - distance  # Convert distance to similarity
                
                if similarity >= self.similarity_thresholds['high_similarity']:
                    # High similarity match
                    cached_data = results['metadatas'][0][i]['cached_data']
                    return CacheHitType.HIGH_SIMILARITY, json.loads(cached_data)
                
                elif similarity >= self.similarity_thresholds['medium_similarity']:
                    # Medium similarity match - adapt the response
                    cached_data = results['metadatas'][0][i]['cached_data']
                    adapted_data = await self._adapt_cached_response(
                        json.loads(cached_data), prompt, similarity
                    )
                    return CacheHitType.MEDIUM_SIMILARITY, adapted_data
            
            return None
            
        except Exception as e:
            logger.error(f"Semantic similarity check failed: {e}")
            return None
    
    async def _adapt_cached_response(
        self, cached_data: Dict[str, Any], new_prompt: str, similarity: float
    ) -> Dict[str, Any]:
        """Adapt cached response for medium similarity matches"""
        # For medium similarity, we can adjust the response slightly
        # This is a simplified adaptation - in production, you might use an LLM
        
        adapted_data = cached_data.copy()
        
        # Adjust quality score based on similarity
        if 'quality_score' in adapted_data:
            adapted_data['quality_score'] *= similarity
        
        # Mark as adapted
        adapted_data['adapted'] = True
        adapted_data['adaptation_similarity'] = similarity
        
        return adapted_data
    
    async def cache_result(self, prompt: str, optimization_data: Dict[str, Any]):
        """Cache optimization result in both Redis and ChromaDB"""
        with tracer.start_as_current_span("cache_store"):
            try:
                prompt_hash = self._hash_prompt(prompt)
                
                # Store exact match in Redis
                await self._store_exact_match(prompt_hash, optimization_data)
                
                # Store embedding in ChromaDB for similarity search
                await self._store_semantic_embedding(prompt, prompt_hash, optimization_data)
                
                self.stats['total_entries'] += 1
                logger.debug(f"Cached result for prompt hash: {prompt_hash}")
                
            except Exception as e:
                logger.error(f"Cache storage failed: {e}")
    
    async def _store_exact_match(self, prompt_hash: str, data: Dict[str, Any]):
        """Store exact match in Redis with TTL"""
        try:
            await self.redis_client.setex(
                f"exact:{prompt_hash}",
                self.settings.cache_ttl_seconds,
                json.dumps(data)
            )
        except Exception as e:
            logger.error(f"Redis storage failed: {e}")
    
    async def _store_semantic_embedding(self, prompt: str, prompt_hash: str, data: Dict[str, Any]):
        """Store embedding in ChromaDB"""
        try:
            # Generate embedding
            embedding = self.embedding_model.encode(prompt).tolist()
            
            # Store in ChromaDB
            self.chroma_collection.add(
                embeddings=[embedding],
                metadatas=[{
                    'prompt_hash': prompt_hash,
                    'cached_data': json.dumps(data),
                    'timestamp': datetime.utcnow().isoformat(),
                    'prompt_length': len(prompt)
                }],
                ids=[prompt_hash]
            )
            
        except Exception as e:
            logger.error(f"ChromaDB storage failed: {e}")
    
    def _hash_prompt(self, prompt: str) -> str:
        """Generate hash for prompt"""
        return hashlib.sha256(prompt.encode()).hexdigest()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        hit_rate = (
            self.stats['cache_hits'] / self.stats['total_requests']
            if self.stats['total_requests'] > 0 else 0
        )
        
        miss_rate = 1 - hit_rate
        
        # Get Redis memory usage
        redis_info = await self.redis_client.info('memory') if self.redis_client else {}
        memory_usage_mb = redis_info.get('used_memory', 0) / (1024 * 1024)
        
        return {
            'hit_rate': hit_rate,
            'miss_rate': miss_rate,
            'total_entries': self.stats['total_entries'],
            'memory_usage_mb': memory_usage_mb,
            'exact_matches': self.stats['exact_matches'],
            'high_similarity_matches': self.stats['high_similarity_matches'],
            'medium_similarity_matches': self.stats['medium_similarity_matches'],
            'total_requests': self.stats['total_requests']
        }
    
    async def cleanup(self):
        """Cleanup cache connections"""
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Cache Manager cleanup completed")


# Global cache manager instance
_cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager
