"""
Configuration Management
Production-grade configuration with environment-specific settings, validation,
hot-reloading, and comprehensive secret management. Built for FAANG-level operations.
"""

import os
import logging
from functools import lru_cache
from typing import List, Optional, Dict, Any
from pathlib import Path

from pydantic import validator, Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


def load_environment_config():
    """Load environment-specific configuration files"""
    # Get current environment
    environment = os.getenv('ENVIRONMENT', 'development')

    # Load base .env file if it exists
    base_env_path = Path('.env')
    if base_env_path.exists():
        load_dotenv(base_env_path)
        logger.info(f"Loaded base environment from {base_env_path}")

    # Load environment-specific config
    env_config_path = Path(f'config/{environment}.env')
    if env_config_path.exists():
        load_dotenv(env_config_path, override=True)
        logger.info(f"Loaded {environment} environment config from {env_config_path}")
    else:
        logger.warning(f"Environment config file not found: {env_config_path}")

    # Validate environment configuration
    try:
        from .env_validator import validate_environment
        validate_environment()
    except ImportError:
        logger.warning("Environment validator not available")
    except SystemExit:
        # Re-raise system exit from validation
        raise
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        raise


class Settings(BaseSettings):
    """
    Production-grade configuration with comprehensive validation and security

    Features:
    - Environment-specific configurations
    - Comprehensive validation with detailed error messages
    - Secret management with secure defaults
    - Hot-reloading support for development
    - FAANG-level operational practices
    """

    # =============================================================================
    # CORE APPLICATION SETTINGS
    # =============================================================================

    app_name: str = Field("Claude Sonnet Cost Optimizer", description="Application name")
    version: str = Field("1.0.0", description="Application version")
    environment: str = Field("development", description="Environment: development, staging, production")
    debug: bool = Field(False, description="Debug mode - NEVER enable in production")

    # Server configuration
    host: str = Field("0.0.0.0", description="Application host")
    port: int = Field(8000, ge=1, le=65535, description="Application port")
    allowed_origins: List[str] = Field(["*"], description="CORS allowed origins")

    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================

    secret_key: str = Field("your-secret-key-change-in-production", min_length=32,
                           description="JWT secret key - must be 32+ chars in production")
    api_key_header: str = Field("X-API-Key", description="API key header name")
    jwt_algorithm: str = Field("HS256", description="JWT signing algorithm")
    jwt_expiration_hours: int = Field(24, ge=1, le=168, description="JWT expiration in hours")

    # =============================================================================
    # DATABASE CONFIGURATION
    # =============================================================================

    database_url: str = Field(..., description="PostgreSQL connection string with pgvector")
    database_pool_size: int = Field(20, ge=1, le=100, description="Database connection pool size")
    database_max_overflow: int = Field(30, ge=0, le=100, description="Database max overflow connections")
    database_pool_timeout: int = Field(30, ge=1, le=300, description="Database pool timeout seconds")
    database_pool_recycle: int = Field(3600, ge=300, description="Database connection recycle seconds")

    # =============================================================================
    # CACHE CONFIGURATION
    # =============================================================================

    # Redis configuration
    redis_url: str = Field("redis://localhost:6379", description="Redis connection string")
    redis_pool_size: int = Field(10, ge=1, le=50, description="Redis connection pool size")
    redis_max_connections: int = Field(50, ge=1, le=200, description="Redis max connections")
    redis_socket_timeout: int = Field(5, ge=1, le=30, description="Redis socket timeout")
    redis_socket_connect_timeout: int = Field(5, ge=1, le=30, description="Redis connect timeout")

    # ChromaDB configuration
    chromadb_host: str = Field("localhost", description="ChromaDB host")
    chromadb_port: int = Field(8001, ge=1, le=65535, description="ChromaDB port")
    chromadb_collection_name: str = Field("cost_optimization_cache", description="ChromaDB collection name")

    # Qdrant configuration
    qdrant_host: str = Field("localhost", description="Qdrant host")
    qdrant_port: int = Field(6333, ge=1, le=65535, description="Qdrant port")
    qdrant_collection_name: str = Field("semantic_cache", description="Qdrant collection name")
    qdrant_api_key: Optional[str] = Field(None, description="Qdrant API key")

    # Weaviate configuration
    weaviate_host: str = Field("localhost", description="Weaviate host")
    weaviate_port: int = Field(8080, ge=1, le=65535, description="Weaviate port")
    weaviate_api_key: Optional[str] = Field(None, description="Weaviate API key")

    # Milvus configuration
    milvus_host: str = Field("localhost", description="Milvus host")
    milvus_port: int = Field(19530, ge=1, le=65535, description="Milvus port")
    milvus_collection_name: str = Field("optimization_vectors", description="Milvus collection name")

    # Elasticsearch configuration
    elasticsearch_host: str = Field("localhost", description="Elasticsearch host")
    elasticsearch_port: int = Field(9200, ge=1, le=65535, description="Elasticsearch port")
    elasticsearch_username: Optional[str] = Field(None, description="Elasticsearch username")
    elasticsearch_password: Optional[str] = Field(None, description="Elasticsearch password")
    elasticsearch_index_name: str = Field("cost_optimization", description="Elasticsearch index name")

    # =============================================================================
    # MODEL ROUTING & API KEYS
    # =============================================================================

    # OpenRouter configuration
    openrouter_api_key: str = Field(..., description="OpenRouter.ai API key (REQUIRED)")
    openrouter_base_url: str = Field("https://openrouter.ai/api/v1", description="OpenRouter base URL")
    openrouter_site_url: str = Field("https://costopt.example.com", description="OpenRouter site URL")
    openrouter_app_name: str = Field("Claude Cost Optimizer", description="OpenRouter app name")
    openrouter_timeout: int = Field(60, ge=5, le=300, description="OpenRouter request timeout")
    openrouter_max_retries: int = Field(3, ge=0, le=10, description="OpenRouter max retries")

    # Model configuration
    claude_sonnet_model: str = Field("anthropic/claude-3.5-sonnet", description="Claude Sonnet model ID")
    deepseek_v3_model: str = Field("deepseek/deepseek-v3", description="DeepSeek V3 model ID")
    llama_free_model: str = Field("meta-llama/llama-3.1-8b-instruct:free", description="Llama free model ID")
    mistral_free_model: str = Field("mistralai/mistral-7b-instruct:free", description="Mistral free model ID")
    deepseek_coder_model: str = Field("deepseek/deepseek-coder-33b-instruct", description="DeepSeek Coder model ID")

    # =============================================================================
    # OPTIMIZATION SETTINGS
    # =============================================================================

    default_quality_threshold: float = Field(0.85, ge=0.0, le=1.0, description="Default quality threshold")
    max_compression_ratio: float = Field(0.7, ge=0.0, le=1.0, description="Maximum compression ratio (70%)")
    cache_similarity_threshold: float = Field(0.75, ge=0.0, le=1.0, description="Cache similarity threshold")
    cache_ttl_seconds: int = Field(3600, ge=60, description="Cache TTL in seconds (1 hour)")

    # =============================================================================
    # PERFORMANCE SETTINGS
    # =============================================================================

    max_concurrent_requests: int = Field(100, ge=1, le=1000, description="Max concurrent requests")
    max_concurrent_optimizations: int = Field(50, ge=1, le=500, description="Max concurrent optimizations")
    request_timeout_seconds: int = Field(30, ge=5, le=300, description="Request timeout seconds")
    circuit_breaker_failure_threshold: int = Field(5, ge=1, le=20, description="Circuit breaker failure threshold")
    circuit_breaker_recovery_timeout: int = Field(60, ge=10, le=600, description="Circuit breaker recovery timeout")

    # =============================================================================
    # RATE LIMITING
    # =============================================================================

    rate_limit_requests_per_minute: int = Field(100, ge=1, le=10000, description="Rate limit requests per minute")
    rate_limit_burst_size: int = Field(20, ge=1, le=100, description="Rate limit burst size")

    # =============================================================================
    # MONITORING & OBSERVABILITY
    # =============================================================================

    jaeger_host: str = Field("localhost", description="Jaeger host")
    jaeger_port: int = Field(14268, ge=1, le=65535, description="Jaeger port")
    jaeger_enabled: bool = Field(True, description="Enable Jaeger tracing")
    metrics_enabled: bool = Field(True, description="Enable Prometheus metrics")
    log_level: str = Field("INFO", description="Logging level")
    log_format: str = Field("json", description="Log format: json or text")

    # =============================================================================
    # COST OPTIMIZATION TARGETS
    # =============================================================================

    initial_cost_reduction_target: float = Field(200.0, ge=100.0, description="Initial cost reduction target (200%)")
    max_cost_reduction_target: float = Field(800.0, ge=100.0, description="Max cost reduction target (800%)")
    learning_rate: float = Field(0.1, ge=0.01, le=1.0, description="Adaptive learning rate")

    # =============================================================================
    # EXTERNAL INTEGRATIONS
    # =============================================================================

    # N8N workflow automation
    n8n_webhook_url: Optional[str] = Field(None, description="N8N webhook URL")
    n8n_api_key: Optional[str] = Field(None, description="N8N API key")

    # Terminal monitoring
    terminal_monitor_enabled: bool = Field(True, description="Enable terminal monitoring")
    terminal_monitor_interval: int = Field(60, ge=10, le=3600, description="Terminal monitor interval seconds")

    # =============================================================================
    # FEATURE FLAGS
    # =============================================================================

    compression_enabled: bool = Field(True, description="Enable compression")
    adaptive_learning_enabled: bool = Field(True, description="Enable adaptive learning")
    vector_caching_enabled: bool = Field(True, description="Enable vector caching")
    quality_assessment_enabled: bool = Field(True, description="Enable quality assessment")
    performance_monitoring_enabled: bool = Field(True, description="Enable performance monitoring")

    # =============================================================================
    # VALIDATION METHODS
    # =============================================================================

    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from comma-separated string or list"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @validator("openrouter_api_key")
    def validate_openrouter_api_key(cls, v):
        """Validate OpenRouter API key is present and properly formatted"""
        # Allow placeholder for development
        if v == "your_openrouter_api_key_here":
            logger.warning("Using placeholder OpenRouter API key - update for production")
            return v
        if not v:
            raise ValueError(
                "OpenRouter API key is required. Get one from https://openrouter.ai/keys"
            )
        if not v.startswith(("sk-", "or-")):
            logger.warning("OpenRouter API key format may be incorrect")
        return v

    @validator("database_url")
    def validate_database_url(cls, v):
        """Validate database URL format and requirements"""
        valid_prefixes = ("postgresql://", "postgresql+asyncpg://", "sqlite://", "sqlite+aiosqlite://")
        if not v.startswith(valid_prefixes):
            raise ValueError(
                "Database URL must be a valid connection string (PostgreSQL or SQLite)"
            )
        if "localhost" in v and os.getenv("ENVIRONMENT") == "production":
            raise ValueError("Production environment cannot use localhost database")
        return v

    @validator("redis_url")
    def validate_redis_url(cls, v):
        """Validate Redis URL format"""
        if not v.startswith("redis://"):
            raise ValueError("Redis URL must be a valid Redis connection string")
        return v

    @validator("secret_key")
    def validate_secret_key(cls, v):
        """Validate secret key security requirements"""
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        if v == "your-secret-key-change-in-production" and os.getenv("ENVIRONMENT") == "production":
            raise ValueError("Must change default secret key in production")
        return v

    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level is supported"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()

    @validator("log_format")
    def validate_log_format(cls, v):
        """Validate log format"""
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f"Log format must be one of: {valid_formats}")
        return v.lower()

    class Config:
        """Pydantic configuration"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        validate_assignment = True
        extra = "forbid"  # Prevent unknown configuration keys


# =============================================================================
# ENVIRONMENT-SPECIFIC CONFIGURATIONS
# =============================================================================

class DevelopmentSettings(Settings):
    """Development environment settings with relaxed security and enhanced debugging"""

    environment: str = "development"
    debug: bool = True
    log_level: str = "DEBUG"

    # Development database - SQLite for easy local development
    database_url: str = "sqlite+aiosqlite:///./data/costopt_dev.db"
    redis_url: str = "redis://localhost:6379/0"

    # Relaxed security for development
    secret_key: str = "development-secret-key-32-characters-long"
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:3000"]

    # Development-friendly timeouts
    request_timeout_seconds: int = 60
    circuit_breaker_failure_threshold: int = 10

    # Mock external services if needed
    openrouter_api_key: str = "sk-or-dev-key-for-testing-purposes-only"

    @validator("openrouter_api_key")
    def validate_dev_openrouter_api_key(cls, v):
        """Allow development API key for testing"""
        return v


class StagingSettings(Settings):
    """Staging environment settings - production-like but with relaxed constraints"""

    environment: str = "staging"
    debug: bool = False
    log_level: str = "INFO"

    # Staging database
    database_url: str = Field(..., description="Staging database URL")
    redis_url: str = Field(..., description="Staging Redis URL")

    # Production-like security
    secret_key: str = Field(..., min_length=32, description="Staging secret key")
    allowed_origins: List[str] = ["https://staging.yourdomain.com"]

    # Moderate performance settings
    database_pool_size: int = 30
    database_max_overflow: int = 50
    redis_max_connections: int = 100

    # Moderate rate limiting
    rate_limit_requests_per_minute: int = 200


class ProductionSettings(Settings):
    """Production environment settings with maximum security and performance"""

    environment: str = "production"
    debug: bool = False
    log_level: str = "INFO"

    # Production database with SSL
    database_url: str = Field(..., description="Production database URL with SSL")
    redis_url: str = Field(..., description="Production Redis URL")

    # Maximum security
    secret_key: str = Field(..., min_length=64, description="Production secret key (64+ chars)")
    allowed_origins: List[str] = Field(..., description="Production allowed origins")

    # High-performance settings
    database_pool_size: int = 50
    database_max_overflow: int = 100
    redis_max_connections: int = 200
    max_concurrent_requests: int = 500
    max_concurrent_optimizations: int = 250

    # Strict rate limiting
    rate_limit_requests_per_minute: int = 1000
    rate_limit_burst_size: int = 50

    # Production monitoring
    jaeger_enabled: bool = True
    metrics_enabled: bool = True
    performance_monitoring_enabled: bool = True

    @validator("secret_key")
    def validate_production_secret_key(cls, v):
        """Ensure production secret key meets security requirements"""
        if len(v) < 64:
            raise ValueError("Production secret key must be at least 64 characters")
        if any(word in v.lower() for word in ["password", "secret", "key", "default"]):
            raise ValueError("Production secret key cannot contain common words")
        return v


class TestSettings(Settings):
    """Test environment settings optimized for fast test execution"""

    environment: str = "test"
    debug: bool = True
    log_level: str = "WARNING"  # Reduce test noise

    # Test database
    database_url: str = "postgresql://costopt:costopt123@localhost:5432/costopt_test"
    redis_url: str = "redis://localhost:6379/1"

    # Test-friendly settings
    secret_key: str = "test-secret-key-32-characters-long"
    openrouter_api_key: str = "test-key-for-unit-tests-only"

    # Fast timeouts for testing
    request_timeout_seconds: int = 5
    cache_ttl_seconds: int = 60
    circuit_breaker_failure_threshold: int = 2
    circuit_breaker_recovery_timeout: int = 10

    # Disable external services in tests
    jaeger_enabled: bool = False
    metrics_enabled: bool = False
    terminal_monitor_enabled: bool = False


# =============================================================================
# CONFIGURATION FACTORY AND UTILITIES
# =============================================================================

@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings based on environment

    Returns appropriate settings class based on ENVIRONMENT variable:
    - development: DevelopmentSettings
    - staging: StagingSettings
    - production: ProductionSettings
    - test: TestSettings
    """
    env = os.getenv("ENVIRONMENT", "development").lower()

    settings_map = {
        "development": DevelopmentSettings,
        "staging": StagingSettings,
        "production": ProductionSettings,
        "test": TestSettings,
    }

    settings_class = settings_map.get(env, DevelopmentSettings)

    try:
        settings = settings_class()
        logger.info(f"Loaded {env} configuration successfully")
        return settings
    except Exception as e:
        logger.error(f"Failed to load {env} configuration: {e}")
        raise


def validate_configuration() -> bool:
    """
    Validate configuration on startup

    Returns:
        bool: True if configuration is valid, raises exception otherwise
    """
    try:
        settings = get_settings()

        # Validate critical settings
        required_settings = [
            "database_url",
            "redis_url",
            "openrouter_api_key",
            "secret_key"
        ]

        for setting in required_settings:
            value = getattr(settings, setting)
            if not value:
                raise ValueError(f"Required setting '{setting}' is not configured")

        logger.info("Configuration validation passed")
        return True

    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise


def get_environment_settings() -> Settings:
    """Legacy function for backward compatibility"""
    return get_settings()
