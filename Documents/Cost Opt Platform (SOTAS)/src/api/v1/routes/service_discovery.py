"""
Service Discovery API Routes
Internal service monitoring and health checking endpoints
"""

import logging
from typing import Dict, List, Any
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse

from src.core.service_discovery import get_service_registry
from src.middleware.auth import verify_api_key

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/services", dependencies=[Depends(verify_api_key)])
async def get_all_services() -> Dict[str, Any]:
    """
    Get status of all registered services
    
    Returns comprehensive service health information for monitoring
    """
    try:
        registry = get_service_registry()
        service_status = registry.get_service_status()
        
        # Calculate summary statistics
        total_services = len(service_status)
        healthy_services = sum(
            1 for service in service_status.values()
            if service["status"] == "healthy"
        )
        unhealthy_services = total_services - healthy_services
        
        # Group by service type
        services_by_type = {}
        for name, service in service_status.items():
            service_type = service["service_type"]
            if service_type not in services_by_type:
                services_by_type[service_type] = []
            services_by_type[service_type].append({
                "name": name,
                **service
            })
        
        return {
            "summary": {
                "total_services": total_services,
                "healthy_services": healthy_services,
                "unhealthy_services": unhealthy_services,
                "health_percentage": (healthy_services / total_services * 100) if total_services > 0 else 0
            },
            "services": service_status,
            "services_by_type": services_by_type,
            "timestamp": registry._health_check_interval
        }
        
    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve service status"
        )


@router.get("/services/{service_name}", dependencies=[Depends(verify_api_key)])
async def get_service_details(service_name: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific service
    """
    try:
        registry = get_service_registry()
        service = registry.get_service(service_name)
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service_name}' not found"
            )
        
        return {
            "name": service.name,
            "url": service.url,
            "health_url": service.health_url,
            "status": service.status.value,
            "service_type": service.service_type,
            "version": service.version,
            "tags": service.tags,
            "host": service.host,
            "port": service.port,
            "protocol": service.protocol,
            "health_check_path": service.health_check_path,
            "timeout_seconds": service.timeout_seconds,
            "retry_attempts": service.retry_attempts,
            "retry_delay_seconds": service.retry_delay_seconds,
            "last_health_check": service.last_health_check,
            "consecutive_failures": service.consecutive_failures,
            "is_healthy": service.is_healthy()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get service details for {service_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve service details"
        )


@router.get("/services/type/{service_type}", dependencies=[Depends(verify_api_key)])
async def get_services_by_type(service_type: str) -> Dict[str, Any]:
    """
    Get all services of a specific type
    """
    try:
        registry = get_service_registry()
        services = registry.get_services_by_type(service_type)
        healthy_services = registry.get_healthy_services_by_type(service_type)
        
        service_list = []
        for service in services:
            service_list.append({
                "name": service.name,
                "url": service.url,
                "status": service.status.value,
                "is_healthy": service.is_healthy(),
                "last_health_check": service.last_health_check,
                "consecutive_failures": service.consecutive_failures,
                "tags": service.tags
            })
        
        return {
            "service_type": service_type,
            "total_services": len(services),
            "healthy_services": len(healthy_services),
            "services": service_list
        }
        
    except Exception as e:
        logger.error(f"Failed to get services by type {service_type}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve services by type"
        )


@router.post("/services/{service_name}/health-check", dependencies=[Depends(verify_api_key)])
async def trigger_health_check(service_name: str) -> Dict[str, Any]:
    """
    Manually trigger a health check for a specific service
    """
    try:
        registry = get_service_registry()
        service = registry.get_service(service_name)
        
        if not service:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service_name}' not found"
            )
        
        # Trigger health check
        await registry._check_service_health(service)
        
        return {
            "service_name": service_name,
            "status": service.status.value,
            "is_healthy": service.is_healthy(),
            "last_health_check": service.last_health_check,
            "consecutive_failures": service.consecutive_failures,
            "message": "Health check completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger health check for {service_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger health check"
        )


@router.get("/health/summary", dependencies=[Depends(verify_api_key)])
async def get_health_summary() -> Dict[str, Any]:
    """
    Get a summary of overall system health
    """
    try:
        registry = get_service_registry()
        service_status = registry.get_service_status()
        
        # Calculate health metrics
        total_services = len(service_status)
        healthy_count = sum(
            1 for service in service_status.values()
            if service["status"] == "healthy"
        )
        
        # Group by service type for detailed breakdown
        type_health = {}
        for service_name, service in service_status.items():
            service_type = service["service_type"]
            if service_type not in type_health:
                type_health[service_type] = {"total": 0, "healthy": 0}
            
            type_health[service_type]["total"] += 1
            if service["status"] == "healthy":
                type_health[service_type]["healthy"] += 1
        
        # Calculate health percentages
        for service_type in type_health:
            total = type_health[service_type]["total"]
            healthy = type_health[service_type]["healthy"]
            type_health[service_type]["health_percentage"] = (
                (healthy / total * 100) if total > 0 else 0
            )
        
        # Determine overall system health
        overall_health_percentage = (healthy_count / total_services * 100) if total_services > 0 else 0
        
        if overall_health_percentage >= 90:
            system_status = "healthy"
        elif overall_health_percentage >= 70:
            system_status = "degraded"
        else:
            system_status = "unhealthy"
        
        return {
            "system_status": system_status,
            "overall_health_percentage": overall_health_percentage,
            "total_services": total_services,
            "healthy_services": healthy_count,
            "unhealthy_services": total_services - healthy_count,
            "service_types": type_health,
            "critical_services": {
                "database": service_status.get("database", {}).get("status", "unknown"),
                "redis": service_status.get("redis", {}).get("status", "unknown"),
                "chromadb": service_status.get("chromadb", {}).get("status", "unknown")
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get health summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve health summary"
        )


@router.get("/discovery/config")
async def get_discovery_config() -> Dict[str, Any]:
    """
    Get service discovery configuration (public endpoint for monitoring)
    """
    try:
        registry = get_service_registry()
        
        return {
            "health_check_interval": registry._health_check_interval,
            "total_registered_services": len(registry.services),
            "service_groups": registry.service_groups,
            "monitoring_enabled": registry._health_check_task is not None,
            "service_types": list(set(
                service.service_type for service in registry.services.values()
            ))
        }
        
    except Exception as e:
        logger.error(f"Failed to get discovery config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve discovery configuration"
        )


@router.get("/network/connectivity")
async def check_network_connectivity() -> Dict[str, Any]:
    """
    Check internal network connectivity between services
    """
    try:
        registry = get_service_registry()
        connectivity_results = {}
        
        # Test connectivity to each service
        for service_name, service in registry.services.items():
            try:
                # Use the existing health check mechanism
                await registry._check_service_health(service)
                connectivity_results[service_name] = {
                    "reachable": service.is_healthy(),
                    "response_time": "< 1s",  # Simplified for now
                    "last_check": service.last_health_check,
                    "endpoint": f"{service.host}:{service.port}"
                }
            except Exception as e:
                connectivity_results[service_name] = {
                    "reachable": False,
                    "error": str(e),
                    "endpoint": f"{service.host}:{service.port}"
                }
        
        # Calculate overall connectivity
        total_services = len(connectivity_results)
        reachable_services = sum(
            1 for result in connectivity_results.values()
            if result.get("reachable", False)
        )
        
        connectivity_percentage = (
            (reachable_services / total_services * 100) if total_services > 0 else 0
        )
        
        return {
            "overall_connectivity": connectivity_percentage,
            "reachable_services": reachable_services,
            "total_services": total_services,
            "connectivity_details": connectivity_results,
            "network_status": "healthy" if connectivity_percentage >= 90 else "degraded"
        }
        
    except Exception as e:
        logger.error(f"Failed to check network connectivity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check network connectivity"
        )
