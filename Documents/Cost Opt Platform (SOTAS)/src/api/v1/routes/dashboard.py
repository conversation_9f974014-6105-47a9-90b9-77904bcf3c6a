"""
Dashboard routes for serving the web interface
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, FileResponse
from pathlib import Path

router = APIRouter()

# Get the static directory path
STATIC_DIR = Path(__file__).parent.parent.parent.parent / "static"


@router.get("/", response_class=HTMLResponse)
@router.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Serve the main dashboard HTML file"""
    html_file = STATIC_DIR / "index.html"
    if html_file.exists():
        return FileResponse(html_file, media_type="text/html")
    else:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Dashboard Not Found</title></head>
                <body>
                    <h1>Dashboard files not found</h1>
                    <p>Please ensure the static files are properly deployed.</p>
                </body>
            </html>
            """,
            status_code=404
        )


@router.get("/app", response_class=HTMLResponse)
@router.get("/optimize", response_class=HTMLResponse)
@router.get("/analytics", response_class=HTMLResponse)
@router.get("/settings", response_class=HTMLResponse)
async def spa_routes():
    """Serve the main dashboard for SPA routes"""
    return await dashboard()
