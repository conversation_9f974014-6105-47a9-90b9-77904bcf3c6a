"""
Health Check API Routes
System health monitoring and status endpoints
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from src.core.models import HealthResponse, ServiceStatus
from src.core.database import get_database
from src.core.cache import get_cache_manager

logger = logging.getLogger(__name__)

router = APIRouter()

# Track service start time for uptime calculation
SERVICE_START_TIME = time.time()


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    Comprehensive health check endpoint
    
    Checks the health of all critical services:
    - Database connectivity
    - Redis cache
    - ChromaDB vector store
    - Model router
    - Overall system status
    """
    try:
        start_time = time.time()
        
        # Check all services in parallel
        health_checks = await asyncio.gather(
            check_database_health(),
            check_cache_health(),
            check_chromadb_health(),
            check_model_router_health(),
            return_exceptions=True
        )
        
        # Process results
        services = {}
        overall_status = ServiceStatus.HEALTHY
        
        service_names = ["database", "redis", "chromadb", "model_router"]
        for i, (service_name, result) in enumerate(zip(service_names, health_checks)):
            if isinstance(result, Exception):
                services[service_name] = ServiceStatus.UNHEALTHY.value
                overall_status = ServiceStatus.UNHEALTHY
                logger.error(f"{service_name} health check failed: {result}")
            else:
                services[service_name] = result.value
                if result == ServiceStatus.DEGRADED and overall_status == ServiceStatus.HEALTHY:
                    overall_status = ServiceStatus.DEGRADED
        
        # Calculate uptime
        uptime_seconds = int(time.time() - SERVICE_START_TIME)
        
        # Get version info
        version = "1.0.0"  # This could be read from a version file
        
        health_response = HealthResponse(
            status=overall_status,
            uptime_seconds=uptime_seconds,
            services=services,
            version=version
        )
        
        # Set appropriate HTTP status code
        status_code = 200
        if overall_status == ServiceStatus.DEGRADED:
            status_code = 200  # Still operational
        elif overall_status == ServiceStatus.UNHEALTHY:
            status_code = 503  # Service unavailable
        
        check_time = time.time() - start_time
        logger.info(f"Health check completed in {check_time:.3f}s: {overall_status.value}")
        
        return JSONResponse(
            content=health_response.dict(),
            status_code=status_code
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}", exc_info=True)
        return JSONResponse(
            content={
                "status": ServiceStatus.UNHEALTHY.value,
                "error": "Health check system failure",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=503
        )


@router.get("/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint
    
    Returns 200 if the service is ready to accept traffic,
    503 if it's still starting up or has critical issues.
    """
    try:
        # Quick checks for readiness
        db_ready = await quick_database_check()
        cache_ready = await quick_cache_check()
        
        if db_ready and cache_ready:
            return JSONResponse(
                content={"status": "ready", "timestamp": datetime.utcnow().isoformat()},
                status_code=200
            )
        else:
            return JSONResponse(
                content={"status": "not_ready", "timestamp": datetime.utcnow().isoformat()},
                status_code=503
            )
            
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            content={"status": "not_ready", "error": str(e)},
            status_code=503
        )


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint
    
    Returns 200 if the service is alive and should not be restarted,
    500+ if it should be restarted.
    """
    try:
        # Basic liveness check - just verify the service is responding
        return JSONResponse(
            content={
                "status": "alive",
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_seconds": int(time.time() - SERVICE_START_TIME)
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Liveness check failed: {e}")
        return JSONResponse(
            content={"status": "dead", "error": str(e)},
            status_code=500
        )


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check with comprehensive system information
    
    Provides detailed information about:
    - Service health and response times
    - Resource usage
    - Performance metrics
    - Configuration status
    """
    try:
        start_time = time.time()
        
        # Gather detailed health information
        detailed_info = {
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": int(time.time() - SERVICE_START_TIME),
            "version": "1.0.0",
            "services": {},
            "resources": {},
            "performance": {}
        }
        
        # Check each service with timing
        services_to_check = [
            ("database", check_database_health_detailed),
            ("redis", check_cache_health_detailed),
            ("chromadb", check_chromadb_health_detailed),
            ("model_router", check_model_router_health_detailed)
        ]
        
        for service_name, check_func in services_to_check:
            service_start = time.time()
            try:
                service_info = await check_func()
                service_time = time.time() - service_start
                detailed_info["services"][service_name] = {
                    "status": "healthy",
                    "response_time_ms": round(service_time * 1000, 2),
                    "details": service_info
                }
            except Exception as e:
                service_time = time.time() - service_start
                detailed_info["services"][service_name] = {
                    "status": "unhealthy",
                    "response_time_ms": round(service_time * 1000, 2),
                    "error": str(e)
                }
        
        # Add resource information
        detailed_info["resources"] = await get_resource_usage()
        
        # Add performance metrics
        detailed_info["performance"] = await get_performance_summary()
        
        total_time = time.time() - start_time
        detailed_info["check_duration_ms"] = round(total_time * 1000, 2)
        
        return JSONResponse(content=detailed_info)
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        return JSONResponse(
            content={"error": "Detailed health check failed", "details": str(e)},
            status_code=500
        )


# Service-specific health check functions
async def check_database_health() -> ServiceStatus:
    """Check database connectivity"""
    try:
        db = get_database()
        is_healthy = await db.health_check()
        return ServiceStatus.HEALTHY if is_healthy else ServiceStatus.UNHEALTHY
    except Exception:
        return ServiceStatus.UNHEALTHY


async def check_cache_health() -> ServiceStatus:
    """Check Redis cache connectivity"""
    try:
        cache_manager = get_cache_manager()
        if cache_manager.redis_client:
            await cache_manager.redis_client.ping()
            return ServiceStatus.HEALTHY
        return ServiceStatus.UNHEALTHY
    except Exception:
        return ServiceStatus.UNHEALTHY


async def check_chromadb_health() -> ServiceStatus:
    """Check ChromaDB connectivity"""
    try:
        cache_manager = get_cache_manager()
        if cache_manager.chroma_client and cache_manager.chroma_collection:
            # Try to query the collection
            cache_manager.chroma_collection.count()
            return ServiceStatus.HEALTHY
        return ServiceStatus.UNHEALTHY
    except Exception:
        return ServiceStatus.UNHEALTHY


async def check_model_router_health() -> ServiceStatus:
    """Check model router health"""
    try:
        # This would check the model router's health
        # For now, assume healthy if we can import it
        from src.services.model_router import ModelRouter
        return ServiceStatus.HEALTHY
    except Exception:
        return ServiceStatus.UNHEALTHY


# Quick check functions for readiness
async def quick_database_check() -> bool:
    """Quick database connectivity check"""
    try:
        db = get_database()
        return await db.health_check()
    except Exception:
        return False


async def quick_cache_check() -> bool:
    """Quick cache connectivity check"""
    try:
        cache_manager = get_cache_manager()
        if cache_manager.redis_client:
            await cache_manager.redis_client.ping()
            return True
        return False
    except Exception:
        return False


# Detailed check functions
async def check_database_health_detailed() -> Dict[str, Any]:
    """Detailed database health check"""
    db = get_database()
    stats = await db.get_connection_stats()
    return {
        "connection_pool": stats,
        "status": "connected"
    }


async def check_cache_health_detailed() -> Dict[str, Any]:
    """Detailed cache health check"""
    cache_manager = get_cache_manager()
    stats = await cache_manager.get_stats()
    return {
        "hit_rate": stats["hit_rate"],
        "total_entries": stats["total_entries"],
        "memory_usage_mb": stats["memory_usage_mb"]
    }


async def check_chromadb_health_detailed() -> Dict[str, Any]:
    """Detailed ChromaDB health check"""
    cache_manager = get_cache_manager()
    if cache_manager.chroma_collection:
        count = cache_manager.chroma_collection.count()
        return {
            "collection_count": count,
            "status": "connected"
        }
    return {"status": "disconnected"}


async def check_model_router_health_detailed() -> Dict[str, Any]:
    """Detailed model router health check"""
    # This would return detailed model router stats
    return {
        "models_available": ["claude-sonnet", "deepseek-v3", "llama-free"],
        "circuit_breakers": "all_closed",
        "status": "operational"
    }


async def get_resource_usage() -> Dict[str, Any]:
    """Get current resource usage"""
    # This would get actual resource usage
    # Placeholder implementation
    return {
        "memory_usage_mb": 512.0,
        "cpu_usage_percent": 25.5,
        "disk_usage_percent": 45.2
    }


async def get_performance_summary() -> Dict[str, Any]:
    """Get performance summary"""
    # This would get actual performance metrics
    # Placeholder implementation
    return {
        "avg_response_time_ms": 85.5,
        "requests_per_second": 45.2,
        "error_rate_percent": 0.1
    }
