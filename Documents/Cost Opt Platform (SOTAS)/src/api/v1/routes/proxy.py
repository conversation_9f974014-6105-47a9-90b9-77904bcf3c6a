"""
Internal Service Proxy Routes
Secure proxy for accessing internal monitoring services through the main API
"""

import httpx
from fastapi import APIRouter, Request, HTTPException, Response
from fastapi.responses import StreamingResponse
import logging
from typing import Optional

logger = logging.getLogger(__name__)

router = APIRouter()

# Internal service configurations
INTERNAL_SERVICES = {
    "grafana": {
        "host": "grafana",
        "port": 3000,
        "base_path": "/grafana"
    },
    "prometheus": {
        "host": "prometheus", 
        "port": 9090,
        "base_path": "/prometheus"
    },
    "jaeger": {
        "host": "jaeger",
        "port": 16686,
        "base_path": "/jaeger"
    },
    "n8n": {
        "host": "n8n",
        "port": 5678,
        "base_path": "/n8n"
    }
}


async def proxy_request(service_name: str, path: str, request: Request) -> Response:
    """Proxy requests to internal services"""
    
    if service_name not in INTERNAL_SERVICES:
        raise HTTPException(status_code=404, detail=f"Service {service_name} not found")
    
    service = INTERNAL_SERVICES[service_name]
    target_url = f"http://{service['host']}:{service['port']}{path}"
    
    # Prepare headers (exclude host header)
    headers = dict(request.headers)
    headers.pop('host', None)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Forward the request to internal service
            response = await client.request(
                method=request.method,
                url=target_url,
                headers=headers,
                params=request.query_params,
                content=await request.body() if request.method in ["POST", "PUT", "PATCH"] else None
            )
            
            # Return response with appropriate headers
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.headers.get("content-type")
            )
            
    except httpx.RequestError as e:
        logger.error(f"Error proxying to {service_name}: {e}")
        raise HTTPException(status_code=503, detail=f"Service {service_name} unavailable")


@router.get("/grafana/{path:path}")
@router.post("/grafana/{path:path}")
async def grafana_proxy(path: str, request: Request):
    """Proxy requests to Grafana dashboard"""
    return await proxy_request("grafana", f"/{path}", request)


@router.get("/prometheus/{path:path}")
@router.post("/prometheus/{path:path}")
async def prometheus_proxy(path: str, request: Request):
    """Proxy requests to Prometheus metrics"""
    return await proxy_request("prometheus", f"/{path}", request)


@router.get("/jaeger/{path:path}")
@router.post("/jaeger/{path:path}")
async def jaeger_proxy(path: str, request: Request):
    """Proxy requests to Jaeger tracing"""
    return await proxy_request("jaeger", f"/{path}", request)


@router.get("/n8n/{path:path}")
@router.post("/n8n/{path:path}")
async def n8n_proxy(path: str, request: Request):
    """Proxy requests to N8N workflows"""
    return await proxy_request("n8n", f"/{path}", request)


@router.get("/services/status")
async def services_status():
    """Check status of all internal services"""
    status = {}
    
    for service_name, config in INTERNAL_SERVICES.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                url = f"http://{config['host']}:{config['port']}"
                response = await client.get(url)
                status[service_name] = {
                    "status": "healthy" if response.status_code < 400 else "unhealthy",
                    "url": f"{config['base_path']}",
                    "internal_url": url
                }
        except Exception as e:
            status[service_name] = {
                "status": "unavailable",
                "error": str(e),
                "url": f"{config['base_path']}",
                "internal_url": f"http://{config['host']}:{config['port']}"
            }
    
    return status


@router.get("/services/links")
async def services_links():
    """Get links to all internal services for dashboard"""
    return {
        "services": [
            {
                "name": "Grafana Dashboards",
                "description": "Performance monitoring and metrics visualization",
                "url": "/grafana/",
                "icon": "📊",
                "category": "monitoring"
            },
            {
                "name": "Prometheus Metrics", 
                "description": "Raw metrics and alerting",
                "url": "/prometheus/",
                "icon": "📈",
                "category": "monitoring"
            },
            {
                "name": "Jaeger Tracing",
                "description": "Distributed request tracing",
                "url": "/jaeger/",
                "icon": "🔍",
                "category": "monitoring"
            },
            {
                "name": "N8N Workflows",
                "description": "Automation and workflow management", 
                "url": "/n8n/",
                "icon": "🔧",
                "category": "automation"
            }
        ]
    }
