"""
Metrics and Analytics API Routes
Endpoints for system performance and cost analytics
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse
from opentelemetry import trace

from src.core.models import UsageMetrics, CacheStats, ErrorResponse
from src.core.optimizer import CostOptimizer
from src.core.cache import get_cache_manager
from src.core.database import get_db_session
from src.middleware.auth import verify_api_key
from src.services.model_router import ModelRouter

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

router = APIRouter()


async def get_optimizer() -> CostOptimizer:
    """Dependency to get optimizer instance"""
    from src.main import app
    return app.state.optimizer


@router.get("/metrics", response_model=UsageMetrics)
async def get_usage_metrics(
    timeframe: str = Query("day", regex="^(hour|day|week|month)$"),
    optimizer: CostOptimizer = Depends(get_optimizer),
    api_key: str = Depends(verify_api_key),
    db_session = Depends(get_db_session)
):
    """
    Get comprehensive usage metrics and analytics
    
    Returns detailed metrics including:
    - Total requests and cost savings
    - Model usage distribution
    - Cache performance
    - Quality scores
    - Processing times
    """
    with tracer.start_as_current_span("get_usage_metrics") as span:
        span.set_attribute("timeframe", timeframe)
        
        try:
            # Get optimizer stats
            optimizer_stats = await optimizer.get_optimization_stats()
            
            # Calculate timeframe-specific metrics
            end_time = datetime.utcnow()
            if timeframe == "hour":
                start_time = end_time - timedelta(hours=1)
            elif timeframe == "day":
                start_time = end_time - timedelta(days=1)
            elif timeframe == "week":
                start_time = end_time - timedelta(weeks=1)
            else:  # month
                start_time = end_time - timedelta(days=30)
            
            # Get timeframe-specific data from database
            timeframe_stats = await get_timeframe_stats(
                db_session, start_time, end_time
            )
            
            # Combine stats
            metrics = UsageMetrics(
                timeframe=timeframe,
                total_requests=timeframe_stats.get("total_requests", 0),
                total_savings=timeframe_stats.get("total_savings", 0.0),
                average_savings_percentage=timeframe_stats.get("avg_savings", 0.0),
                cache_hit_rate=optimizer_stats.get("cache_hit_rate", 0.0),
                model_distribution=timeframe_stats.get("model_distribution", {}),
                quality_scores=timeframe_stats.get("quality_scores", {}),
                avg_processing_time_ms=timeframe_stats.get("avg_processing_time", 0.0)
            )
            
            span.set_attribute("total_requests", metrics.total_requests)
            span.set_attribute("cache_hit_rate", metrics.cache_hit_rate)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get usage metrics: {e}", exc_info=True)
            span.record_exception(e)
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="metrics_retrieval_failed",
                    message="Failed to retrieve usage metrics"
                ).dict()
            )


@router.get("/metrics/cache", response_model=CacheStats)
async def get_cache_metrics(
    api_key: str = Depends(verify_api_key)
):
    """Get detailed cache performance statistics"""
    with tracer.start_as_current_span("get_cache_metrics"):
        try:
            cache_manager = get_cache_manager()
            cache_stats = await cache_manager.get_stats()
            
            return CacheStats(
                hit_rate=cache_stats["hit_rate"],
                miss_rate=cache_stats["miss_rate"],
                total_entries=cache_stats["total_entries"],
                memory_usage_mb=cache_stats["memory_usage_mb"],
                average_retrieval_time_ms=cache_stats.get("avg_retrieval_time", 0.0)
            )
            
        except Exception as e:
            logger.error(f"Failed to get cache metrics: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="cache_metrics_failed",
                    message="Failed to retrieve cache metrics"
                ).dict()
            )


@router.get("/metrics/models")
async def get_model_metrics(
    api_key: str = Depends(verify_api_key)
):
    """Get model performance and health metrics"""
    with tracer.start_as_current_span("get_model_metrics"):
        try:
            # This would get model router from app state
            from src.main import app
            if hasattr(app.state, 'optimizer') and app.state.optimizer.model_router:
                model_stats = await app.state.optimizer.model_router.get_model_stats()
                return JSONResponse(content=model_stats)
            else:
                return JSONResponse(content={"error": "Model router not available"})
            
        except Exception as e:
            logger.error(f"Failed to get model metrics: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="model_metrics_failed",
                    message="Failed to retrieve model metrics"
                ).dict()
            )


@router.get("/metrics/learning")
async def get_learning_metrics(
    optimizer: CostOptimizer = Depends(get_optimizer),
    api_key: str = Depends(verify_api_key)
):
    """Get adaptive learning statistics and progress"""
    with tracer.start_as_current_span("get_learning_metrics"):
        try:
            if optimizer.adaptive_learner:
                learning_stats = await optimizer.adaptive_learner.get_stats()
                return JSONResponse(content=learning_stats)
            else:
                return JSONResponse(content={"error": "Adaptive learner not available"})
            
        except Exception as e:
            logger.error(f"Failed to get learning metrics: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="learning_metrics_failed",
                    message="Failed to retrieve learning metrics"
                ).dict()
            )


@router.get("/metrics/cost-analysis")
async def get_cost_analysis(
    timeframe: str = Query("day", regex="^(hour|day|week|month)$"),
    api_key: str = Depends(verify_api_key),
    db_session = Depends(get_db_session)
):
    """Get detailed cost analysis and savings breakdown"""
    with tracer.start_as_current_span("get_cost_analysis") as span:
        span.set_attribute("timeframe", timeframe)
        
        try:
            # Calculate timeframe
            end_time = datetime.utcnow()
            if timeframe == "hour":
                start_time = end_time - timedelta(hours=1)
            elif timeframe == "day":
                start_time = end_time - timedelta(days=1)
            elif timeframe == "week":
                start_time = end_time - timedelta(weeks=1)
            else:  # month
                start_time = end_time - timedelta(days=30)
            
            # Get cost analysis from database
            cost_analysis = await get_cost_analysis_data(
                db_session, start_time, end_time
            )
            
            return JSONResponse(content=cost_analysis)
            
        except Exception as e:
            logger.error(f"Failed to get cost analysis: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="cost_analysis_failed",
                    message="Failed to retrieve cost analysis"
                ).dict()
            )


@router.get("/metrics/performance")
async def get_performance_metrics(
    api_key: str = Depends(verify_api_key),
    db_session = Depends(get_db_session)
):
    """Get system performance metrics"""
    with tracer.start_as_current_span("get_performance_metrics"):
        try:
            # Get performance data
            performance_data = await get_performance_data(db_session)
            
            return JSONResponse(content=performance_data)
            
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="performance_metrics_failed",
                    message="Failed to retrieve performance metrics"
                ).dict()
            )


# Helper functions for database queries
async def get_timeframe_stats(db_session, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get statistics for a specific timeframe"""
    # This would query the database for timeframe-specific stats
    # Placeholder implementation
    return {
        "total_requests": 100,
        "total_savings": 50.0,
        "avg_savings": 75.0,
        "model_distribution": {
            "deepseek-v3": 60,
            "claude-sonnet": 25,
            "llama-free": 15
        },
        "quality_scores": {
            "average": 0.87,
            "min": 0.65,
            "max": 0.98
        },
        "avg_processing_time": 85.5
    }


async def get_cost_analysis_data(db_session, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get detailed cost analysis data"""
    # This would query the database for cost analysis
    # Placeholder implementation
    return {
        "total_original_cost": 100.0,
        "total_optimized_cost": 15.0,
        "total_savings": 85.0,
        "savings_percentage": 85.0,
        "savings_by_method": {
            "deepseek_routing": 60.0,
            "compression": 15.0,
            "cache_hits": 10.0
        },
        "cost_by_model": {
            "claude-sonnet": 15.0,
            "deepseek-v3": 0.0,
            "llama-free": 0.0
        },
        "projected_monthly_savings": 2550.0
    }


async def get_performance_data(db_session) -> Dict[str, Any]:
    """Get system performance data"""
    # This would query system performance metrics
    # Placeholder implementation
    return {
        "avg_response_time_ms": 85.5,
        "p95_response_time_ms": 150.0,
        "p99_response_time_ms": 250.0,
        "requests_per_second": 45.2,
        "error_rate": 0.001,
        "uptime_percentage": 99.95,
        "memory_usage_mb": 512.0,
        "cpu_usage_percentage": 25.5
    }
