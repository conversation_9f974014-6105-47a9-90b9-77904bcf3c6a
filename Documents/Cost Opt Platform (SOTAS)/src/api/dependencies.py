"""
FastAPI Dependency Injection System
Production-grade dependency management with FAANG+ patterns
"""

import logging
from typing import Optional, Dict, Any, AsyncGenerator
from functools import lru_cache

from fastapi import Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from opentelemetry import trace

from src.core.config import get_settings
from src.core.database import DatabaseManager, get_db_session
from src.core.optimizer import CostOptimizer
from src.services.cache_manager import CacheManager
from src.services.openrouter_client import OpenRouterClient
from src.services.quality_assessor import QualityAssessor
from src.services.adaptive_learner import AdaptiveLearner
from src.services.compression_engine import CompressionEngine
from src.middleware.auth import verify_api_key, verify_admin_permission
from src.middleware.rate_limiting import check_rate_limit
from src.monitoring.metrics import get_metrics_handler, MetricsCollector
from src.monitoring.health import get_health_checker, HealthChecker

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)


# =============================================================================
# Core Service Dependencies
# =============================================================================

@lru_cache()
def get_settings_dependency():
    """Get application settings (cached)"""
    return get_settings()


async def get_database_manager() -> DatabaseManager:
    """Get database manager instance"""
    try:
        # In production, this would be managed by the application state
        from src.main import app
        if hasattr(app.state, 'database_manager'):
            return app.state.database_manager
        
        # Fallback: create new instance
        db_manager = DatabaseManager()
        await db_manager.initialize()
        return db_manager
    except Exception as e:
        logger.error(f"Failed to get database manager: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database service unavailable"
        )


async def get_cache_manager() -> CacheManager:
    """Get cache manager instance"""
    try:
        from src.main import app
        if hasattr(app.state, 'cache_manager'):
            return app.state.cache_manager
        
        # Fallback: create new instance
        cache_manager = CacheManager()
        await cache_manager.initialize()
        return cache_manager
    except Exception as e:
        logger.error(f"Failed to get cache manager: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service unavailable"
        )


async def get_openrouter_client() -> OpenRouterClient:
    """Get OpenRouter client instance"""
    try:
        from src.main import app
        if hasattr(app.state, 'openrouter_client'):
            return app.state.openrouter_client
        
        # Fallback: create new instance
        settings = get_settings()
        client = OpenRouterClient(api_key=settings.openrouter_api_key)
        await client.initialize()
        return client
    except Exception as e:
        logger.error(f"Failed to get OpenRouter client: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model routing service unavailable"
        )


async def get_quality_assessor() -> QualityAssessor:
    """Get quality assessor instance"""
    try:
        from src.main import app
        if hasattr(app.state, 'quality_assessor'):
            return app.state.quality_assessor
        
        # Fallback: create new instance
        assessor = QualityAssessor()
        await assessor.initialize()
        return assessor
    except Exception as e:
        logger.error(f"Failed to get quality assessor: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Quality assessment service unavailable"
        )


async def get_adaptive_learner() -> AdaptiveLearner:
    """Get adaptive learner instance"""
    try:
        from src.main import app
        if hasattr(app.state, 'adaptive_learner'):
            return app.state.adaptive_learner
        
        # Fallback: create new instance
        learner = AdaptiveLearner()
        await learner.initialize()
        return learner
    except Exception as e:
        logger.error(f"Failed to get adaptive learner: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Adaptive learning service unavailable"
        )


async def get_compression_engine() -> CompressionEngine:
    """Get compression engine instance"""
    try:
        from src.main import app
        if hasattr(app.state, 'compression_engine'):
            return app.state.compression_engine
        
        # Fallback: create new instance
        engine = CompressionEngine()
        await engine.initialize()
        return engine
    except Exception as e:
        logger.error(f"Failed to get compression engine: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Compression service unavailable"
        )


async def get_cost_optimizer(
    cache_manager: CacheManager = Depends(get_cache_manager),
    openrouter_client: OpenRouterClient = Depends(get_openrouter_client),
    quality_assessor: QualityAssessor = Depends(get_quality_assessor),
    adaptive_learner: AdaptiveLearner = Depends(get_adaptive_learner),
    compression_engine: CompressionEngine = Depends(get_compression_engine)
) -> CostOptimizer:
    """Get cost optimizer instance with all dependencies"""
    try:
        from src.main import app
        if hasattr(app.state, 'cost_optimizer'):
            return app.state.cost_optimizer
        
        # Fallback: create new instance with dependencies
        optimizer = CostOptimizer(
            cache_manager=cache_manager,
            openrouter_client=openrouter_client,
            quality_assessor=quality_assessor,
            adaptive_learner=adaptive_learner,
            compression_engine=compression_engine
        )
        await optimizer.initialize()
        return optimizer
    except Exception as e:
        logger.error(f"Failed to get cost optimizer: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Optimization service unavailable"
        )


# =============================================================================
# Monitoring Dependencies
# =============================================================================

def get_metrics_collector() -> MetricsCollector:
    """Get metrics collector instance"""
    try:
        return get_metrics_handler()
    except Exception as e:
        logger.error(f"Failed to get metrics collector: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Metrics service unavailable"
        )


def get_health_checker_dependency() -> HealthChecker:
    """Get health checker instance"""
    try:
        return get_health_checker()
    except Exception as e:
        logger.error(f"Failed to get health checker: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Health check service unavailable"
        )


# =============================================================================
# Authentication & Authorization Dependencies
# =============================================================================

async def get_current_user(
    request: Request,
    api_key: str = Depends(verify_api_key)
) -> Dict[str, Any]:
    """Get current authenticated user information"""
    try:
        # Extract user info from API key
        api_key_info = getattr(request.state, 'api_key_info', {})
        
        return {
            'api_key': api_key,
            'api_key_name': api_key_info.get('name', 'Unknown'),
            'permissions': api_key_info.get('permissions', []),
            'rate_limit_multiplier': api_key_info.get('rate_limit_multiplier', 1.0),
            'user_id': api_key_info.get('user_id', 'anonymous')
        }
    except Exception as e:
        logger.error(f"Failed to get current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


async def require_admin_user(
    api_key: str = Depends(verify_admin_permission)
) -> str:
    """Require admin permissions"""
    return api_key


async def require_write_permission(
    request: Request,
    api_key: str = Depends(verify_api_key)
) -> str:
    """Require write permissions"""
    api_key_info = getattr(request.state, 'api_key_info', {})
    permissions = api_key_info.get('permissions', [])
    
    if 'write' not in permissions and 'admin' not in permissions:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Write permissions required"
        )
    
    return api_key


# =============================================================================
# Request Context Dependencies
# =============================================================================

async def get_request_context(request: Request) -> Dict[str, Any]:
    """Get request context information"""
    return {
        'request_id': getattr(request.state, 'request_id', 'unknown'),
        'client_ip': request.client.host if request.client else 'unknown',
        'user_agent': request.headers.get('user-agent', 'unknown'),
        'method': request.method,
        'url': str(request.url),
        'headers': dict(request.headers)
    }


async def get_tracing_context(request: Request) -> trace.Span:
    """Get current tracing span"""
    return trace.get_current_span()


# =============================================================================
# Rate Limiting Dependencies
# =============================================================================

async def apply_rate_limiting(
    request: Request,
    api_key: str = Depends(verify_api_key)
) -> bool:
    """Apply rate limiting based on API key tier"""
    try:
        api_key_info = getattr(request.state, 'api_key_info', {})
        rate_limit_multiplier = api_key_info.get('rate_limit_multiplier', 1.0)
        
        # Apply rate limiting with multiplier
        return await check_rate_limit(request, rate_limit_multiplier)
    except Exception as e:
        logger.error(f"Rate limiting failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )


# =============================================================================
# Database Session Dependencies
# =============================================================================

async def get_db_session_dependency() -> AsyncGenerator[AsyncSession, None]:
    """Get database session with proper cleanup"""
    async with get_db_session() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


# =============================================================================
# Validation Dependencies
# =============================================================================

async def validate_optimization_request(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Validate optimization request parameters"""
    try:
        # Get request body
        body = await request.json() if request.method in ['POST', 'PUT', 'PATCH'] else {}
        
        # Basic validation
        if 'prompt' in body:
            prompt_length = len(body['prompt'])
            if prompt_length > 100000:  # 100k character limit
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="Prompt too long (max 100,000 characters)"
                )
            
            if prompt_length < 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Prompt cannot be empty"
                )
        
        return {
            'validated': True,
            'prompt_length': len(body.get('prompt', '')),
            'user_permissions': current_user.get('permissions', [])
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Request validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request format"
        )


# =============================================================================
# Service Health Dependencies
# =============================================================================

async def check_service_health(
    health_checker: HealthChecker = Depends(get_health_checker_dependency)
) -> Dict[str, Any]:
    """Check overall service health"""
    try:
        health_status = await health_checker.get_overall_health()
        
        if health_status['status'] not in ['healthy', 'degraded']:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service is currently unhealthy"
            )
        
        return health_status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Health check service unavailable"
        )


# =============================================================================
# Composite Dependencies
# =============================================================================

async def get_optimization_dependencies(
    optimizer: CostOptimizer = Depends(get_cost_optimizer),
    current_user: Dict[str, Any] = Depends(get_current_user),
    rate_limit_check: bool = Depends(apply_rate_limiting),
    request_context: Dict[str, Any] = Depends(get_request_context),
    db_session: AsyncSession = Depends(get_db_session_dependency),
    metrics: MetricsCollector = Depends(get_metrics_collector)
) -> Dict[str, Any]:
    """Get all dependencies needed for optimization endpoints"""
    return {
        'optimizer': optimizer,
        'current_user': current_user,
        'rate_limit_check': rate_limit_check,
        'request_context': request_context,
        'db_session': db_session,
        'metrics': metrics
    }


async def get_admin_dependencies(
    admin_user: str = Depends(require_admin_user),
    request_context: Dict[str, Any] = Depends(get_request_context),
    db_session: AsyncSession = Depends(get_db_session_dependency),
    metrics: MetricsCollector = Depends(get_metrics_collector)
) -> Dict[str, Any]:
    """Get all dependencies needed for admin endpoints"""
    return {
        'admin_user': admin_user,
        'request_context': request_context,
        'db_session': db_session,
        'metrics': metrics
    }
