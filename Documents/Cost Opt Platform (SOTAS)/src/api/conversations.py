"""
Conversation API Endpoints
ChatGPT/Claude Desktop style conversation management
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from src.models.conversation import (
    ConversationCreate, ConversationUpdate, ConversationResponse,
    ConversationWithMessages, ConversationListResponse,
    MessageCreate, MessageResponse, MessageSearchRequest,
    ConversationStats
)
from src.services.conversation_service import ConversationService
from src.api.dependencies import get_db_session_dependency, check_rate_limit
from src.core.security import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/conversations", tags=["conversations"])

# Initialize service
conversation_service = ConversationService()

@router.post("/", response_model=ConversationResponse)
async def create_conversation(
    conversation_data: ConversationCreate,
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Create a new conversation"""
    try:
        return await conversation_service.create_conversation(conversation_data, db)
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to create conversation")

@router.get("/", response_model=ConversationListResponse)
async def list_conversations(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    include_archived: bool = Query(False, description="Include archived conversations"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """List conversations with pagination"""
    try:
        return await conversation_service.list_conversations(
            user_id=user_id,
            include_archived=include_archived,
            page=page,
            page_size=page_size,
            db=db
        )
    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to list conversations")

@router.get("/{conversation_id}", response_model=ConversationWithMessages)
async def get_conversation(
    conversation_id: str,
    include_messages: bool = Query(True, description="Include message history"),
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Get a conversation by ID"""
    try:
        conversation = await conversation_service.get_conversation(
            conversation_id=conversation_id,
            include_messages=include_messages,
            db=db
        )
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get conversation")

@router.put("/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(
    conversation_id: str,
    update_data: ConversationUpdate,
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Update a conversation"""
    try:
        conversation = await conversation_service.update_conversation(
            conversation_id=conversation_id,
            update_data=update_data,
            db=db
        )
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update conversation")

@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Delete a conversation"""
    try:
        success = await conversation_service.delete_conversation(
            conversation_id=conversation_id,
            db=db
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        return {"message": "Conversation deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete conversation")

@router.post("/{conversation_id}/messages", response_model=MessageResponse)
async def add_message(
    conversation_id: str,
    message_data: MessageCreate,
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Add a message to a conversation"""
    try:
        return await conversation_service.add_message(
            conversation_id=conversation_id,
            message_data=message_data,
            db=db
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding message to conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to add message")

@router.get("/{conversation_id}/messages", response_model=list[MessageResponse])
async def get_messages(
    conversation_id: str,
    limit: int = Query(100, ge=1, le=1000, description="Maximum messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Get messages from a conversation"""
    try:
        messages = await conversation_service.get_messages(
            conversation_id=conversation_id,
            limit=limit,
            offset=offset,
            db=db
        )
        return messages
    except Exception as e:
        logger.error(f"Error getting messages for conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get messages")

@router.post("/search", response_model=list[MessageResponse])
async def search_messages(
    search_request: MessageSearchRequest,
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Search messages across conversations"""
    try:
        messages = await conversation_service.search_messages(
            search_request=search_request,
            db=db
        )
        return messages
    except Exception as e:
        logger.error(f"Error searching messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to search messages")

@router.get("/stats/overview", response_model=ConversationStats)
async def get_conversation_stats(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    db: Session = Depends(get_db_session_dependency),
    _: dict = Depends(check_rate_limit)
):
    """Get conversation statistics"""
    try:
        stats = await conversation_service.get_conversation_stats(
            user_id=user_id,
            db=db
        )
        return stats
    except Exception as e:
        logger.error(f"Error getting conversation stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")
