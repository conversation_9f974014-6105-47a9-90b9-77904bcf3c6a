"""
Production-Grade Alerting System
FAANG+ level alerting with intelligent escalation and noise reduction
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime, timedelta

import aiohttp
from prometheus_client import Counter, Histogram, Gauge

logger = logging.getLogger(__name__)

# Alerting metrics
ALERTS_FIRED = Counter(
    'alerts_fired_total',
    'Total alerts fired',
    ['alert_name', 'severity', 'service']
)

ALERT_RESOLUTION_TIME = Histogram(
    'alert_resolution_time_seconds',
    'Time to resolve alerts',
    ['alert_name', 'severity']
)

ACTIVE_ALERTS = Gauge(
    'active_alerts_total',
    'Number of active alerts',
    ['severity']
)


class AlertSeverity(Enum):
    """Alert severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertStatus(Enum):
    """Alert status"""
    FIRING = "firing"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"
    SILENCED = "silenced"


@dataclass
class AlertRule:
    """Alert rule definition"""
    name: str
    description: str
    severity: AlertSeverity
    condition: Callable[[Dict[str, Any]], bool]
    threshold: float
    duration_seconds: int = 300  # 5 minutes
    cooldown_seconds: int = 900  # 15 minutes
    
    # Notification settings
    notify_channels: List[str] = field(default_factory=list)
    escalation_delay_seconds: int = 1800  # 30 minutes
    max_escalations: int = 3
    
    # Metadata
    service: str = "cost-optimizer"
    tags: Dict[str, str] = field(default_factory=dict)
    runbook_url: Optional[str] = None
    
    # State tracking
    last_fired: float = 0.0
    last_resolved: float = 0.0
    fire_count: int = 0


@dataclass
class Alert:
    """Active alert instance"""
    rule_name: str
    severity: AlertSeverity
    status: AlertStatus
    message: str
    service: str
    
    # Timing
    fired_at: float
    resolved_at: Optional[float] = None
    acknowledged_at: Optional[float] = None
    
    # Context
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    
    # Escalation tracking
    escalation_level: int = 0
    notifications_sent: List[str] = field(default_factory=list)
    
    @property
    def duration_seconds(self) -> float:
        """Get alert duration in seconds"""
        end_time = self.resolved_at or time.time()
        return end_time - self.fired_at
    
    @property
    def is_active(self) -> bool:
        """Check if alert is active"""
        return self.status == AlertStatus.FIRING


class NotificationChannel:
    """Base notification channel"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.enabled = config.get("enabled", True)
    
    async def send_notification(self, alert: Alert, escalation_level: int = 0) -> bool:
        """Send notification for alert"""
        raise NotImplementedError


class SlackNotificationChannel(NotificationChannel):
    """Slack notification channel"""
    
    async def send_notification(self, alert: Alert, escalation_level: int = 0) -> bool:
        """Send Slack notification"""
        if not self.enabled:
            return False
        
        webhook_url = self.config.get("webhook_url")
        if not webhook_url:
            logger.error("Slack webhook URL not configured")
            return False
        
        # Build Slack message
        color = self._get_color_for_severity(alert.severity)
        
        message = {
            "attachments": [
                {
                    "color": color,
                    "title": f"🚨 {alert.severity.value.upper()} Alert: {alert.rule_name}",
                    "text": alert.message,
                    "fields": [
                        {
                            "title": "Service",
                            "value": alert.service,
                            "short": True
                        },
                        {
                            "title": "Duration",
                            "value": f"{alert.duration_seconds:.0f}s",
                            "short": True
                        },
                        {
                            "title": "Status",
                            "value": alert.status.value,
                            "short": True
                        },
                        {
                            "title": "Escalation Level",
                            "value": str(escalation_level),
                            "short": True
                        }
                    ],
                    "footer": "Cost Optimization Platform",
                    "ts": int(alert.fired_at)
                }
            ]
        }
        
        # Add labels as fields
        for key, value in alert.labels.items():
            message["attachments"][0]["fields"].append({
                "title": key,
                "value": value,
                "short": True
            })
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=message) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent for alert: {alert.rule_name}")
                        return True
                    else:
                        logger.error(f"Failed to send Slack notification: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False
    
    def _get_color_for_severity(self, severity: AlertSeverity) -> str:
        """Get color for alert severity"""
        colors = {
            AlertSeverity.CRITICAL: "#FF0000",  # Red
            AlertSeverity.HIGH: "#FF8C00",      # Orange
            AlertSeverity.MEDIUM: "#FFD700",    # Yellow
            AlertSeverity.LOW: "#32CD32",       # Green
            AlertSeverity.INFO: "#87CEEB"       # Light Blue
        }
        return colors.get(severity, "#808080")  # Gray default


class EmailNotificationChannel(NotificationChannel):
    """Email notification channel"""
    
    async def send_notification(self, alert: Alert, escalation_level: int = 0) -> bool:
        """Send email notification"""
        if not self.enabled:
            return False
        
        # Email implementation would go here
        # For now, just log
        logger.info(f"Email notification would be sent for alert: {alert.rule_name}")
        return True


class AlertManager:
    """
    Production-grade alert manager with intelligent escalation
    """
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.notification_channels: Dict[str, NotificationChannel] = {}
        
        # Alert processing
        self.evaluation_interval = 30  # seconds
        self.evaluation_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Initialize default rules and channels
        self._setup_default_rules()
        self._setup_default_channels()
    
    def _setup_default_rules(self):
        """Setup default alert rules"""
        
        # Critical alerts
        self.add_rule(AlertRule(
            name="high_error_rate",
            description="Error rate exceeds 5% for 5 minutes",
            severity=AlertSeverity.CRITICAL,
            condition=lambda metrics: metrics.get("error_rate", 0) > 0.05,
            threshold=0.05,
            duration_seconds=300,
            notify_channels=["slack", "email"],
            runbook_url="https://docs.company.com/runbooks/high-error-rate"
        ))
        
        self.add_rule(AlertRule(
            name="high_latency",
            description="P99 latency exceeds 1 second for 5 minutes",
            severity=AlertSeverity.CRITICAL,
            condition=lambda metrics: metrics.get("latency_p99", 0) > 1.0,
            threshold=1.0,
            duration_seconds=300,
            notify_channels=["slack", "email"]
        ))
        
        self.add_rule(AlertRule(
            name="service_down",
            description="Service health check failing",
            severity=AlertSeverity.CRITICAL,
            condition=lambda metrics: not metrics.get("service_healthy", True),
            threshold=0,
            duration_seconds=60,
            notify_channels=["slack", "email"]
        ))
        
        # High severity alerts
        self.add_rule(AlertRule(
            name="high_memory_usage",
            description="Memory usage exceeds 85%",
            severity=AlertSeverity.HIGH,
            condition=lambda metrics: metrics.get("memory_usage_percent", 0) > 85,
            threshold=85,
            duration_seconds=600,
            notify_channels=["slack"]
        ))
        
        self.add_rule(AlertRule(
            name="high_cpu_usage",
            description="CPU usage exceeds 80% for 10 minutes",
            severity=AlertSeverity.HIGH,
            condition=lambda metrics: metrics.get("cpu_usage_percent", 0) > 80,
            threshold=80,
            duration_seconds=600,
            notify_channels=["slack"]
        ))
        
        # Medium severity alerts
        self.add_rule(AlertRule(
            name="cost_optimization_degraded",
            description="Cost optimization effectiveness below 70%",
            severity=AlertSeverity.MEDIUM,
            condition=lambda metrics: metrics.get("cost_savings_percent", 100) < 70,
            threshold=70,
            duration_seconds=900,
            notify_channels=["slack"]
        ))
        
        self.add_rule(AlertRule(
            name="cache_hit_rate_low",
            description="Cache hit rate below 80%",
            severity=AlertSeverity.MEDIUM,
            condition=lambda metrics: metrics.get("cache_hit_rate", 100) < 80,
            threshold=80,
            duration_seconds=900,
            notify_channels=["slack"]
        ))
    
    def _setup_default_channels(self):
        """Setup default notification channels"""
        
        # Slack channel
        slack_config = {
            "enabled": True,
            "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
        }
        self.notification_channels["slack"] = SlackNotificationChannel("slack", slack_config)
        
        # Email channel
        email_config = {
            "enabled": True,
            "smtp_server": "smtp.company.com",
            "smtp_port": 587,
            "username": "<EMAIL>",
            "password": "password",
            "recipients": ["<EMAIL>"]
        }
        self.notification_channels["email"] = EmailNotificationChannel("email", email_config)
    
    def add_rule(self, rule: AlertRule):
        """Add alert rule"""
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove alert rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    async def start(self):
        """Start alert manager"""
        if not self.running:
            self.running = True
            self.evaluation_task = asyncio.create_task(self._evaluation_loop())
            logger.info("Alert manager started")
    
    async def stop(self):
        """Stop alert manager"""
        self.running = False
        if self.evaluation_task:
            self.evaluation_task.cancel()
            try:
                await self.evaluation_task
            except asyncio.CancelledError:
                pass
        logger.info("Alert manager stopped")
    
    async def _evaluation_loop(self):
        """Main alert evaluation loop"""
        while self.running:
            try:
                # Get current metrics
                metrics = await self._collect_metrics()
                
                # Evaluate all rules
                await self._evaluate_rules(metrics)
                
                # Process escalations
                await self._process_escalations()
                
                # Update metrics
                self._update_alert_metrics()
                
                await asyncio.sleep(self.evaluation_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Alert evaluation error: {e}")
                await asyncio.sleep(self.evaluation_interval)
    
    async def _collect_metrics(self) -> Dict[str, Any]:
        """Collect current metrics for evaluation"""
        try:
            # Import here to avoid circular imports
            from src.monitoring.metrics import get_metrics_handler

            handler = get_metrics_handler()

            # Collect key metrics for alerting
            return {
                "error_rate": handler._calculate_error_rate(),
                "latency_p99": handler._calculate_latency_percentile(0.99),
                "service_healthy": await self._check_service_health(),
                "memory_usage_percent": handler._get_memory_usage(),
                "cpu_usage_percent": handler._get_cpu_usage(),
                "cost_savings_percent": handler._calculate_cost_savings(),
                "cache_hit_rate": handler._calculate_cache_hit_rate()
            }
        except Exception as e:
            logger.error(f"Failed to collect metrics for alerting: {e}")
            # Return safe defaults
            return {
                "error_rate": 0.0,
                "latency_p99": 0.1,
                "service_healthy": True,
                "memory_usage_percent": 50,
                "cpu_usage_percent": 30,
                "cost_savings_percent": 80,
                "cache_hit_rate": 85
            }

    async def _check_service_health(self) -> bool:
        """Check overall service health"""
        try:
            from src.monitoring.health import get_health_checker

            checker = get_health_checker()
            health_data = await checker.get_overall_health()
            return health_data.get("status") == "healthy"
        except Exception:
            return True  # Assume healthy if check fails
    
    async def _evaluate_rules(self, metrics: Dict[str, Any]):
        """Evaluate all alert rules"""
        current_time = time.time()
        
        for rule_name, rule in self.rules.items():
            try:
                # Check if condition is met
                condition_met = rule.condition(metrics)
                
                if condition_met:
                    # Check if we should fire the alert
                    if rule_name not in self.active_alerts:
                        # Check cooldown period
                        if current_time - rule.last_fired > rule.cooldown_seconds:
                            await self._fire_alert(rule, metrics)
                else:
                    # Check if we should resolve the alert
                    if rule_name in self.active_alerts:
                        await self._resolve_alert(rule_name)
                        
            except Exception as e:
                logger.error(f"Error evaluating rule {rule_name}: {e}")
    
    async def _fire_alert(self, rule: AlertRule, metrics: Dict[str, Any]):
        """Fire an alert"""
        current_time = time.time()
        
        # Create alert
        alert = Alert(
            rule_name=rule.name,
            severity=rule.severity,
            status=AlertStatus.FIRING,
            message=rule.description,
            service=rule.service,
            fired_at=current_time,
            labels=rule.tags.copy(),
            annotations={
                "threshold": str(rule.threshold),
                "runbook_url": rule.runbook_url or ""
            }
        )
        
        # Add current metric values to labels
        for key, value in metrics.items():
            alert.labels[f"current_{key}"] = str(value)
        
        # Store alert
        self.active_alerts[rule.name] = alert
        
        # Update rule state
        rule.last_fired = current_time
        rule.fire_count += 1
        
        # Send notifications
        await self._send_notifications(alert)
        
        # Update metrics
        ALERTS_FIRED.labels(
            alert_name=rule.name,
            severity=rule.severity.value,
            service=rule.service
        ).inc()
        
        logger.warning(f"Alert fired: {rule.name} - {rule.description}")
    
    async def _resolve_alert(self, rule_name: str):
        """Resolve an alert"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            current_time = time.time()
            
            # Update alert
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = current_time
            
            # Update rule
            rule = self.rules[rule_name]
            rule.last_resolved = current_time
            
            # Record resolution time
            ALERT_RESOLUTION_TIME.labels(
                alert_name=rule_name,
                severity=alert.severity.value
            ).observe(alert.duration_seconds)
            
            # Remove from active alerts
            del self.active_alerts[rule_name]
            
            logger.info(f"Alert resolved: {rule_name}")
    
    async def _send_notifications(self, alert: Alert):
        """Send notifications for alert"""
        rule = self.rules[alert.rule_name]
        
        for channel_name in rule.notify_channels:
            if channel_name in self.notification_channels:
                channel = self.notification_channels[channel_name]
                try:
                    success = await channel.send_notification(alert, alert.escalation_level)
                    if success:
                        alert.notifications_sent.append(channel_name)
                except Exception as e:
                    logger.error(f"Failed to send notification via {channel_name}: {e}")
    
    async def _process_escalations(self):
        """Process alert escalations"""
        current_time = time.time()
        
        for alert in self.active_alerts.values():
            rule = self.rules[alert.rule_name]
            
            # Check if escalation is needed
            time_since_fired = current_time - alert.fired_at
            escalation_interval = rule.escalation_delay_seconds
            
            expected_escalations = int(time_since_fired // escalation_interval)
            
            if expected_escalations > alert.escalation_level and alert.escalation_level < rule.max_escalations:
                alert.escalation_level = expected_escalations
                await self._send_notifications(alert)
                logger.warning(f"Alert escalated: {alert.rule_name} (level {alert.escalation_level})")
    
    def _update_alert_metrics(self):
        """Update Prometheus metrics"""
        # Count active alerts by severity
        severity_counts = {}
        for severity in AlertSeverity:
            severity_counts[severity] = 0
        
        for alert in self.active_alerts.values():
            severity_counts[alert.severity] += 1
        
        for severity, count in severity_counts.items():
            ACTIVE_ALERTS.labels(severity=severity.value).set(count)
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get list of active alerts"""
        return [
            {
                "rule_name": alert.rule_name,
                "severity": alert.severity.value,
                "status": alert.status.value,
                "message": alert.message,
                "service": alert.service,
                "fired_at": alert.fired_at,
                "duration_seconds": alert.duration_seconds,
                "escalation_level": alert.escalation_level,
                "labels": alert.labels,
                "annotations": alert.annotations
            }
            for alert in self.active_alerts.values()
        ]


# Global alert manager instance
_alert_manager: Optional[AlertManager] = None


def get_alert_manager() -> AlertManager:
    """Get global alert manager instance"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager()
    return _alert_manager


async def initialize_alerting():
    """Initialize alerting system"""
    manager = get_alert_manager()
    await manager.start()
    logger.info("Alerting system initialized")


async def shutdown_alerting():
    """Shutdown alerting system"""
    manager = get_alert_manager()
    await manager.stop()
    logger.info("Alerting system shutdown")
