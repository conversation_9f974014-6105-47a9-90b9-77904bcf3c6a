"""
Analytics Manager
Production-grade analytics and insights for cost optimization platform
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict
import json

from opentelemetry import trace

from src.core.config import get_settings
from src.monitoring.metrics_registry import get_counter, get_histogram, get_gauge

logger = logging.getLogger(__name__)
tracer = trace.get_tracer(__name__)

# Metrics
ANALYTICS_QUERIES = get_counter(
    'analytics_queries_total',
    'Total analytics queries',
    ['query_type', 'status']
)

ANALYTICS_LATENCY = get_histogram(
    'analytics_query_duration_seconds',
    'Analytics query duration',
    ['query_type']
)


@dataclass
class CostSavingsMetrics:
    """Cost savings analytics"""
    total_savings: float
    average_savings_percentage: float
    total_requests: int
    successful_optimizations: int
    failed_optimizations: int
    cache_hit_rate: float
    top_models: List[Tuple[str, int]]
    savings_by_model: Dict[str, float]
    time_period: str


@dataclass
class PerformanceMetrics:
    """Performance analytics"""
    average_latency_ms: float
    p95_latency_ms: float
    p99_latency_ms: float
    throughput_rps: float
    error_rate: float
    uptime_percentage: float
    time_period: str


@dataclass
class UsageMetrics:
    """Usage analytics"""
    total_users: int
    active_users: int
    requests_per_user: float
    top_users: List[Tuple[str, int]]
    usage_by_hour: Dict[int, int]
    usage_by_day: Dict[str, int]
    time_period: str


class AnalyticsManager:
    """
    Production-grade analytics manager for cost optimization insights
    """
    
    def __init__(self):
        self.settings = get_settings()
        
    async def get_cost_savings_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> CostSavingsMetrics:
        """Get cost savings analytics for time period"""
        with tracer.start_as_current_span("get_cost_savings_metrics") as span:
            span.set_attribute("start_date", start_date.isoformat())
            span.set_attribute("end_date", end_date.isoformat())
            
            try:
                # Mock implementation - in production, query actual database
                total_savings = 12500.75
                total_requests = 1500
                successful_optimizations = 1425
                failed_optimizations = 75
                
                average_savings_percentage = (total_savings / (total_requests * 0.10)) * 100
                cache_hit_rate = 0.85
                
                top_models = [
                    ("deepseek/deepseek-v3", 800),
                    ("meta-llama/llama-3.1-8b-instruct:free", 400),
                    ("anthropic/claude-3.5-sonnet", 300)
                ]
                
                savings_by_model = {
                    "deepseek/deepseek-v3": 8500.50,
                    "meta-llama/llama-3.1-8b-instruct:free": 2000.25,
                    "anthropic/claude-3.5-sonnet": 2000.00
                }
                
                time_period = f"{start_date.date()} to {end_date.date()}"
                
                ANALYTICS_QUERIES.labels(query_type="cost_savings", status="success").inc()
                
                return CostSavingsMetrics(
                    total_savings=total_savings,
                    average_savings_percentage=average_savings_percentage,
                    total_requests=total_requests,
                    successful_optimizations=successful_optimizations,
                    failed_optimizations=failed_optimizations,
                    cache_hit_rate=cache_hit_rate,
                    top_models=top_models,
                    savings_by_model=savings_by_model,
                    time_period=time_period
                )
                
            except Exception as e:
                ANALYTICS_QUERIES.labels(query_type="cost_savings", status="error").inc()
                logger.error(f"Failed to get cost savings metrics: {e}")
                raise
    
    async def get_performance_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> PerformanceMetrics:
        """Get performance analytics for time period"""
        with tracer.start_as_current_span("get_performance_metrics") as span:
            try:
                # Mock implementation - in production, query actual metrics
                average_latency_ms = 85.5
                p95_latency_ms = 150.0
                p99_latency_ms = 250.0
                throughput_rps = 25.5
                error_rate = 0.02
                uptime_percentage = 99.95
                time_period = f"{start_date.date()} to {end_date.date()}"
                
                ANALYTICS_QUERIES.labels(query_type="performance", status="success").inc()
                
                return PerformanceMetrics(
                    average_latency_ms=average_latency_ms,
                    p95_latency_ms=p95_latency_ms,
                    p99_latency_ms=p99_latency_ms,
                    throughput_rps=throughput_rps,
                    error_rate=error_rate,
                    uptime_percentage=uptime_percentage,
                    time_period=time_period
                )
                
            except Exception as e:
                ANALYTICS_QUERIES.labels(query_type="performance", status="error").inc()
                logger.error(f"Failed to get performance metrics: {e}")
                raise
    
    async def get_usage_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> UsageMetrics:
        """Get usage analytics for time period"""
        with tracer.start_as_current_span("get_usage_metrics") as span:
            try:
                # Mock implementation - in production, query actual database
                total_users = 150
                active_users = 125
                requests_per_user = 12.0
                
                top_users = [
                    ("user_123", 45),
                    ("user_456", 38),
                    ("user_789", 32)
                ]
                
                usage_by_hour = {
                    hour: 50 + (hour * 2) for hour in range(24)
                }
                
                usage_by_day = {
                    "Monday": 200,
                    "Tuesday": 180,
                    "Wednesday": 220,
                    "Thursday": 190,
                    "Friday": 160,
                    "Saturday": 80,
                    "Sunday": 70
                }
                
                time_period = f"{start_date.date()} to {end_date.date()}"
                
                ANALYTICS_QUERIES.labels(query_type="usage", status="success").inc()
                
                return UsageMetrics(
                    total_users=total_users,
                    active_users=active_users,
                    requests_per_user=requests_per_user,
                    top_users=top_users,
                    usage_by_hour=usage_by_hour,
                    usage_by_day=usage_by_day,
                    time_period=time_period
                )
                
            except Exception as e:
                ANALYTICS_QUERIES.labels(query_type="usage", status="error").inc()
                logger.error(f"Failed to get usage metrics: {e}")
                raise
    
    async def generate_insights(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Generate AI-powered insights from analytics data"""
        with tracer.start_as_current_span("generate_insights") as span:
            try:
                cost_metrics = await self.get_cost_savings_metrics(start_date, end_date)
                performance_metrics = await self.get_performance_metrics(start_date, end_date)
                usage_metrics = await self.get_usage_metrics(start_date, end_date)
                
                insights = {
                    "cost_optimization": {
                        "status": "excellent" if cost_metrics.average_savings_percentage > 80 else "good",
                        "recommendation": "Continue current optimization strategy",
                        "potential_improvement": "Consider implementing more aggressive caching"
                    },
                    "performance": {
                        "status": "excellent" if performance_metrics.average_latency_ms < 100 else "good",
                        "recommendation": "Performance is within target SLA",
                        "potential_improvement": "Monitor P99 latency trends"
                    },
                    "usage_patterns": {
                        "peak_hours": [hour for hour, count in usage_metrics.usage_by_hour.items() if count > 100],
                        "growth_trend": "steady",
                        "recommendation": "Scale resources during peak hours"
                    }
                }
                
                ANALYTICS_QUERIES.labels(query_type="insights", status="success").inc()
                
                return insights
                
            except Exception as e:
                ANALYTICS_QUERIES.labels(query_type="insights", status="error").inc()
                logger.error(f"Failed to generate insights: {e}")
                raise
