"""
Production-Grade Logging Configuration
Structured logging with correlation IDs, performance tracking, and security audit trails
"""

import logging
import logging.config
import sys
import os
import json
from typing import Dict, Any, Optional
from datetime import datetime
import traceback
from pathlib import Path

from pythonjsonlogger import jsonlogger


class CorrelationIdFilter(logging.Filter):
    """Add correlation ID to log records"""
    
    def filter(self, record):
        # Try to get correlation ID from context
        correlation_id = getattr(record, 'correlation_id', None)
        if not correlation_id:
            # Try to get from request context if available
            try:
                from contextvars import copy_context
                ctx = copy_context()
                correlation_id = ctx.get('correlation_id', 'unknown')
            except:
                correlation_id = 'unknown'
        
        record.correlation_id = correlation_id
        return True


class SecurityAuditFilter(logging.Filter):
    """Filter for security-related events"""
    
    def filter(self, record):
        # Mark security-related log entries
        security_keywords = [
            'authentication', 'authorization', 'login', 'logout',
            'access_denied', 'permission', 'security', 'breach',
            'attack', 'suspicious', 'blocked', 'rate_limit'
        ]
        
        message = record.getMessage().lower()
        record.is_security_event = any(keyword in message for keyword in security_keywords)
        
        return True


class PerformanceFilter(logging.Filter):
    """Filter for performance-related events"""
    
    def filter(self, record):
        # Mark performance-related log entries
        performance_keywords = [
            'latency', 'response_time', 'duration', 'slow',
            'timeout', 'performance', 'optimization', 'cache'
        ]
        
        message = record.getMessage().lower()
        record.is_performance_event = any(keyword in message for keyword in performance_keywords)
        
        return True


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields"""
    
    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp in ISO format
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add service information
        log_record['service'] = 'cost-optimizer'
        log_record['version'] = '1.0.0'
        
        # Add environment
        log_record['environment'] = os.getenv('ENVIRONMENT', 'development')
        
        # Add correlation ID
        log_record['correlation_id'] = getattr(record, 'correlation_id', 'unknown')
        
        # Add security and performance flags
        log_record['is_security_event'] = getattr(record, 'is_security_event', False)
        log_record['is_performance_event'] = getattr(record, 'is_performance_event', False)
        
        # Add exception information if present
        if record.exc_info:
            log_record['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'correlation_id',
                          'is_security_event', 'is_performance_event']:
                log_record[key] = value


class LoggingConfig:
    """Centralized logging configuration"""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # Log levels by environment
        self.log_levels = {
            "development": "DEBUG",
            "staging": "INFO",
            "production": "INFO"
        }
        
        # Configure logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        
        log_level = self.log_levels.get(self.environment, "INFO")
        
        config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
                },
                'detailed': {
                    'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d %(funcName)s() - %(message)s'
                },
                'json': {
                    '()': CustomJsonFormatter,
                    'format': '%(timestamp)s %(level)s %(name)s %(message)s'
                }
            },
            'filters': {
                'correlation_id': {
                    '()': CorrelationIdFilter,
                },
                'security_audit': {
                    '()': SecurityAuditFilter,
                },
                'performance': {
                    '()': PerformanceFilter,
                }
            },
            'handlers': {
                'console': {
                    'level': log_level,
                    'class': 'logging.StreamHandler',
                    'formatter': 'standard' if self.environment == 'development' else 'json',
                    'stream': sys.stdout,
                    'filters': ['correlation_id', 'security_audit', 'performance']
                },
                'file_all': {
                    'level': 'DEBUG',
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(self.log_dir / 'application.log'),
                    'maxBytes': 50 * 1024 * 1024,  # 50MB
                    'backupCount': 10,
                    'formatter': 'json',
                    'filters': ['correlation_id', 'security_audit', 'performance']
                },
                'file_error': {
                    'level': 'ERROR',
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(self.log_dir / 'error.log'),
                    'maxBytes': 10 * 1024 * 1024,  # 10MB
                    'backupCount': 5,
                    'formatter': 'json',
                    'filters': ['correlation_id', 'security_audit', 'performance']
                },
                'file_security': {
                    'level': 'INFO',
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(self.log_dir / 'security.log'),
                    'maxBytes': 20 * 1024 * 1024,  # 20MB
                    'backupCount': 10,
                    'formatter': 'json',
                    'filters': ['correlation_id', 'security_audit']
                },
                'file_performance': {
                    'level': 'INFO',
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(self.log_dir / 'performance.log'),
                    'maxBytes': 20 * 1024 * 1024,  # 20MB
                    'backupCount': 5,
                    'formatter': 'json',
                    'filters': ['correlation_id', 'performance']
                },
                'file_audit': {
                    'level': 'INFO',
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(self.log_dir / 'audit.log'),
                    'maxBytes': 50 * 1024 * 1024,  # 50MB
                    'backupCount': 20,  # Keep more audit logs
                    'formatter': 'json',
                    'filters': ['correlation_id']
                }
            },
            'loggers': {
                # Root logger
                '': {
                    'handlers': ['console', 'file_all'],
                    'level': log_level,
                    'propagate': False
                },
                # Application loggers
                'src': {
                    'handlers': ['console', 'file_all', 'file_error'],
                    'level': log_level,
                    'propagate': False
                },
                # Security logger
                'security': {
                    'handlers': ['console', 'file_security', 'file_audit'],
                    'level': 'INFO',
                    'propagate': False
                },
                # Performance logger
                'performance': {
                    'handlers': ['console', 'file_performance'],
                    'level': 'INFO',
                    'propagate': False
                },
                # Audit logger
                'audit': {
                    'handlers': ['file_audit'],
                    'level': 'INFO',
                    'propagate': False
                },
                # Third-party loggers
                'uvicorn': {
                    'handlers': ['console', 'file_all'],
                    'level': 'INFO',
                    'propagate': False
                },
                'fastapi': {
                    'handlers': ['console', 'file_all'],
                    'level': 'INFO',
                    'propagate': False
                },
                'sqlalchemy': {
                    'handlers': ['file_all'],
                    'level': 'WARNING',
                    'propagate': False
                },
                'redis': {
                    'handlers': ['file_all'],
                    'level': 'WARNING',
                    'propagate': False
                },
                'openai': {
                    'handlers': ['file_all'],
                    'level': 'INFO',
                    'propagate': False
                },
                'httpx': {
                    'handlers': ['file_all'],
                    'level': 'WARNING',
                    'propagate': False
                }
            }
        }
        
        # Apply configuration
        logging.config.dictConfig(config)
        
        # Set up specialized loggers
        self._setup_specialized_loggers()
    
    def _setup_specialized_loggers(self):
        """Setup specialized loggers for different purposes"""
        
        # Security logger
        self.security_logger = logging.getLogger('security')
        
        # Performance logger
        self.performance_logger = logging.getLogger('performance')
        
        # Audit logger
        self.audit_logger = logging.getLogger('audit')
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          level: str = "WARNING", correlation_id: str = None):
        """Log security event"""
        extra = {
            'event_type': event_type,
            'security_details': details,
            'correlation_id': correlation_id or 'unknown'
        }
        
        message = f"Security event: {event_type}"
        
        if level.upper() == "CRITICAL":
            self.security_logger.critical(message, extra=extra)
        elif level.upper() == "ERROR":
            self.security_logger.error(message, extra=extra)
        elif level.upper() == "WARNING":
            self.security_logger.warning(message, extra=extra)
        else:
            self.security_logger.info(message, extra=extra)
    
    def log_performance_event(self, metric_name: str, value: float, 
                            threshold: float = None, correlation_id: str = None):
        """Log performance event"""
        extra = {
            'metric_name': metric_name,
            'metric_value': value,
            'threshold': threshold,
            'correlation_id': correlation_id or 'unknown'
        }
        
        message = f"Performance metric: {metric_name}={value}"
        if threshold and value > threshold:
            message += f" (exceeds threshold: {threshold})"
            self.performance_logger.warning(message, extra=extra)
        else:
            self.performance_logger.info(message, extra=extra)
    
    def log_audit_event(self, action: str, user_id: str = None, 
                       resource: str = None, details: Dict[str, Any] = None,
                       correlation_id: str = None):
        """Log audit event"""
        extra = {
            'action': action,
            'user_id': user_id,
            'resource': resource,
            'audit_details': details or {},
            'correlation_id': correlation_id or 'unknown'
        }
        
        message = f"Audit: {action}"
        if user_id:
            message += f" by user {user_id}"
        if resource:
            message += f" on resource {resource}"
        
        self.audit_logger.info(message, extra=extra)


# Global logging configuration
_logging_config: Optional[LoggingConfig] = None


def setup_logging(environment: str = None) -> LoggingConfig:
    """Setup global logging configuration"""
    global _logging_config
    
    if environment is None:
        environment = os.getenv('ENVIRONMENT', 'production')
    
    _logging_config = LoggingConfig(environment)
    return _logging_config


def get_logging_config() -> LoggingConfig:
    """Get global logging configuration"""
    global _logging_config
    
    if _logging_config is None:
        _logging_config = setup_logging()
    
    return _logging_config


# Convenience functions
def log_security_event(event_type: str, details: Dict[str, Any], 
                      level: str = "WARNING", correlation_id: str = None):
    """Log security event using global config"""
    config = get_logging_config()
    config.log_security_event(event_type, details, level, correlation_id)


def log_performance_event(metric_name: str, value: float, 
                         threshold: float = None, correlation_id: str = None):
    """Log performance event using global config"""
    config = get_logging_config()
    config.log_performance_event(metric_name, value, threshold, correlation_id)


def log_audit_event(action: str, user_id: str = None, 
                   resource: str = None, details: Dict[str, Any] = None,
                   correlation_id: str = None):
    """Log audit event using global config"""
    config = get_logging_config()
    config.log_audit_event(action, user_id, resource, details, correlation_id)
