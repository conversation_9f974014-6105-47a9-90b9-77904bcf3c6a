"""
Centralized Metrics Registry
Prevents duplicate metric registration and provides a single source of truth for all metrics
"""

import logging
from typing import Dict, Optional
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, REGISTRY
from prometheus_client.metrics import MetricWrapperBase

logger = logging.getLogger(__name__)

class MetricsRegistry:
    """
    Centralized registry for all Prometheus metrics to prevent duplicates
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or REGISTRY
        self._metrics: Dict[str, MetricWrapperBase] = {}
        
    def get_or_create_counter(self, name: str, description: str, labelnames: list = None) -> Counter:
        """Get existing counter or create new one"""
        if name in self._metrics:
            return self._metrics[name]

        # Check if metric already exists in registry
        for collector in list(self.registry._collector_to_names.keys()):
            if hasattr(collector, '_name') and collector._name == name:
                self._metrics[name] = collector
                logger.debug(f"Found existing counter metric: {name}")
                return collector

        try:
            counter = Counter(name, description, labelnames=labelnames or [], registry=self.registry)
            self._metrics[name] = counter
            logger.debug(f"Created counter metric: {name}")
            return counter
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Last resort: try to find by name in registry
                for collector in list(self.registry._collector_to_names.keys()):
                    if hasattr(collector, '_name') and collector._name == name:
                        self._metrics[name] = collector
                        logger.debug(f"Found existing counter metric after error: {name}")
                        return collector
                # If still not found, clear and recreate
                logger.warning(f"Clearing registry due to metric collision: {name}")
                self.clear_registry()
                counter = Counter(name, description, labelnames=labelnames or [], registry=self.registry)
                self._metrics[name] = counter
                return counter
            raise e
    
    def get_or_create_histogram(self, name: str, description: str, labelnames: list = None, buckets: tuple = None) -> Histogram:
        """Get existing histogram or create new one"""
        if name in self._metrics:
            return self._metrics[name]
            
        try:
            histogram = Histogram(name, description, labelnames=labelnames or [], buckets=buckets, registry=self.registry)
            self._metrics[name] = histogram
            logger.debug(f"Created histogram metric: {name}")
            return histogram
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Try to find existing metric in registry
                for collector in self.registry._collector_to_names:
                    if hasattr(collector, '_name') and collector._name == name:
                        self._metrics[name] = collector
                        logger.debug(f"Found existing histogram metric: {name}")
                        return collector
                raise e
            raise e
    
    def get_or_create_gauge(self, name: str, description: str, labelnames: list = None) -> Gauge:
        """Get existing gauge or create new one"""
        if name in self._metrics:
            return self._metrics[name]
            
        try:
            gauge = Gauge(name, description, labelnames=labelnames or [], registry=self.registry)
            self._metrics[name] = gauge
            logger.debug(f"Created gauge metric: {name}")
            return gauge
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Try to find existing metric in registry
                for collector in self.registry._collector_to_names:
                    if hasattr(collector, '_name') and collector._name == name:
                        self._metrics[name] = collector
                        logger.debug(f"Found existing gauge metric: {name}")
                        return collector
                raise e
            raise e
    
    def clear_registry(self):
        """Clear all metrics from registry (useful for testing)"""
        self.registry._collector_to_names.clear()
        self.registry._names_to_collectors.clear()
        self._metrics.clear()
        logger.debug("Cleared metrics registry")

# Global metrics registry instance
_global_metrics_registry = None

def get_metrics_registry() -> MetricsRegistry:
    """Get the global metrics registry instance"""
    global _global_metrics_registry
    if _global_metrics_registry is None:
        _global_metrics_registry = MetricsRegistry()
    return _global_metrics_registry

def reset_metrics_registry():
    """Reset the global metrics registry (useful for testing)"""
    global _global_metrics_registry
    if _global_metrics_registry:
        _global_metrics_registry.clear_registry()
    _global_metrics_registry = None

# Convenience functions for common metrics
def get_counter(name: str, description: str, labelnames: list = None) -> Counter:
    """Get or create a counter metric"""
    return get_metrics_registry().get_or_create_counter(name, description, labelnames)

def get_histogram(name: str, description: str, labelnames: list = None, buckets: tuple = None) -> Histogram:
    """Get or create a histogram metric"""
    return get_metrics_registry().get_or_create_histogram(name, description, labelnames, buckets)

def get_gauge(name: str, description: str, labelnames: list = None) -> Gauge:
    """Get or create a gauge metric"""
    return get_metrics_registry().get_or_create_gauge(name, description, labelnames)
