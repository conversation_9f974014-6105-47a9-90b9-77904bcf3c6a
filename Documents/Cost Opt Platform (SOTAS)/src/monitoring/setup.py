"""
Monitoring Setup and Initialization
Production-grade monitoring setup with FAANG+ operational excellence
"""

import asyncio
import logging
import os
from typing import Optional, Dict, Any

from fastapi import FastAP<PERSON>
from prometheus_client import start_http_server, CollectorRegistry

from src.core.config import get_settings
from src.monitoring.metrics import setup_metrics, get_metrics_handler
from src.monitoring.tracing import setup_tracing, get_tracer
from src.monitoring.health import HealthChecker

logger = logging.getLogger(__name__)


class MonitoringManager:
    """
    Comprehensive monitoring manager for production deployments
    Orchestrates metrics, tracing, health checks, and alerting
    """
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.settings = get_settings()
        self.metrics_collector = None
        self.tracing_manager = None
        self.health_checker = None
        self.prometheus_server = None
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """
        Initialize all monitoring components
        
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info("Initializing monitoring system...")
            
            # Initialize metrics collection
            await self._setup_metrics()
            
            # Initialize distributed tracing
            await self._setup_tracing()
            
            # Initialize health checking
            await self._setup_health_checks()
            
            # Setup Prometheus metrics server
            await self._setup_prometheus_server()
            
            # Setup middleware for automatic instrumentation
            await self._setup_middleware()
            
            self.is_initialized = True
            logger.info("Monitoring system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize monitoring: {e}")
            return False
    
    async def _setup_metrics(self):
        """Setup Prometheus metrics collection"""
        try:
            # Determine if we're running in multiprocess mode
            enable_multiprocess = os.getenv('PROMETHEUS_MULTIPROC_DIR') is not None
            
            self.metrics_collector = setup_metrics(enable_multiprocess)
            
            # Add metrics endpoint to FastAPI app
            @self.app.get("/metrics")
            async def metrics_endpoint():
                """Prometheus metrics endpoint"""
                return self.metrics_collector.get_metrics_response()
            
            logger.info("Metrics collection setup complete")
            
        except Exception as e:
            logger.error(f"Failed to setup metrics: {e}")
            raise
    
    async def _setup_tracing(self):
        """Setup distributed tracing with OpenTelemetry"""
        try:
            if self.settings.jaeger_enabled:
                self.tracing_manager = setup_tracing()
                logger.info("Distributed tracing setup complete")
            else:
                logger.info("Distributed tracing disabled")
                
        except Exception as e:
            logger.error(f"Failed to setup tracing: {e}")
            # Don't raise - tracing is not critical for basic operation
    
    async def _setup_health_checks(self):
        """Setup comprehensive health checking"""
        try:
            self.health_checker = HealthChecker()
            await self.health_checker.initialize()
            
            # Add health check endpoints to FastAPI app
            @self.app.get("/health")
            async def health_endpoint():
                """Comprehensive health check"""
                return await self.health_checker.get_health_status()
            
            @self.app.get("/health/ready")
            async def readiness_endpoint():
                """Kubernetes readiness probe"""
                return await self.health_checker.get_readiness_status()
            
            @self.app.get("/health/live")
            async def liveness_endpoint():
                """Kubernetes liveness probe"""
                return await self.health_checker.get_liveness_status()
            
            logger.info("Health checks setup complete")
            
        except Exception as e:
            logger.error(f"Failed to setup health checks: {e}")
            raise
    
    async def _setup_prometheus_server(self):
        """Setup dedicated Prometheus metrics server"""
        try:
            if self.settings.metrics_enabled:
                # Start Prometheus metrics server on separate port
                metrics_port = getattr(self.settings, 'prometheus_port', 9090)
                
                # Only start if not already running
                try:
                    self.prometheus_server = start_http_server(metrics_port)
                    logger.info(f"Prometheus metrics server started on port {metrics_port}")
                except OSError as e:
                    if "Address already in use" in str(e):
                        logger.info(f"Prometheus server already running on port {metrics_port}")
                    else:
                        raise
            else:
                logger.info("Prometheus metrics server disabled")
                
        except Exception as e:
            logger.error(f"Failed to setup Prometheus server: {e}")
            # Don't raise - metrics can still be served via FastAPI endpoint
    
    async def _setup_middleware(self):
        """Setup monitoring middleware for automatic instrumentation"""
        try:
            from src.middleware.monitoring import MonitoringMiddleware
            
            # Add monitoring middleware
            self.app.add_middleware(MonitoringMiddleware)
            
            logger.info("Monitoring middleware setup complete")
            
        except ImportError:
            logger.warning("Monitoring middleware not available")
        except Exception as e:
            logger.error(f"Failed to setup monitoring middleware: {e}")
    
    def record_optimization_metrics(
        self,
        model: str,
        task_complexity: str,
        status: str,
        latency_seconds: float,
        cost_savings_percent: float,
        quality_score: float,
        compression_ratio: float
    ):
        """Record optimization metrics"""
        if self.metrics_collector:
            self.metrics_collector.record_optimization_request(
                model=model,
                task_complexity=task_complexity,
                status=status,
                latency_seconds=latency_seconds,
                cost_savings_percent=cost_savings_percent,
                quality_score=quality_score,
                compression_ratio=compression_ratio
            )
    
    def record_cache_metrics(self, cache_layer: str, hit: bool, cache_type: str = "semantic"):
        """Record cache performance metrics"""
        if self.metrics_collector:
            self.metrics_collector.record_cache_metrics(cache_layer, hit, cache_type)
    
    def record_http_metrics(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration_seconds: float,
        request_size_bytes: int,
        response_size_bytes: int
    ):
        """Record HTTP request metrics"""
        if self.metrics_collector:
            self.metrics_collector.record_http_request(
                method=method,
                endpoint=endpoint,
                status_code=status_code,
                duration_seconds=duration_seconds,
                request_size_bytes=request_size_bytes,
                response_size_bytes=response_size_bytes
            )
    
    def get_tracer(self, name: str = "cost-optimizer"):
        """Get tracer instance"""
        if self.tracing_manager:
            return self.tracing_manager.get_tracer(name)
        else:
            from opentelemetry import trace
            return trace.NoOpTracer()
    
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get comprehensive monitoring status"""
        status = {
            "monitoring_initialized": self.is_initialized,
            "metrics_enabled": self.metrics_collector is not None,
            "tracing_enabled": self.tracing_manager is not None,
            "health_checks_enabled": self.health_checker is not None,
            "prometheus_server_running": self.prometheus_server is not None
        }
        
        if self.metrics_collector:
            status["metrics_summary"] = self.metrics_collector.get_metrics_summary()
        
        if self.health_checker:
            status["health_status"] = await self.health_checker.get_health_status()
        
        return status
    
    async def shutdown(self):
        """Shutdown monitoring system gracefully"""
        try:
            logger.info("Shutting down monitoring system...")
            
            if self.tracing_manager:
                self.tracing_manager.shutdown()
            
            if self.health_checker:
                await self.health_checker.shutdown()
            
            # Note: Prometheus server shutdown is handled by the process
            
            logger.info("Monitoring system shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during monitoring shutdown: {e}")


# Global monitoring manager instance
_monitoring_manager: Optional[MonitoringManager] = None


async def setup_monitoring(app: FastAPI) -> MonitoringManager:
    """
    Setup global monitoring manager
    
    Args:
        app: FastAPI application instance
        
    Returns:
        MonitoringManager instance
    """
    global _monitoring_manager
    _monitoring_manager = MonitoringManager(app)
    
    success = await _monitoring_manager.initialize()
    if not success:
        logger.error("Failed to initialize monitoring system")
        raise RuntimeError("Monitoring initialization failed")
    
    return _monitoring_manager


def get_monitoring_manager() -> MonitoringManager:
    """Get global monitoring manager"""
    if _monitoring_manager is None:
        raise RuntimeError("Monitoring not initialized. Call setup_monitoring() first.")
    return _monitoring_manager


# Convenience functions for common monitoring operations
def record_optimization_metrics(
    model: str,
    task_complexity: str,
    status: str,
    latency_seconds: float,
    cost_savings_percent: float,
    quality_score: float,
    compression_ratio: float
):
    """Record optimization metrics"""
    try:
        manager = get_monitoring_manager()
        manager.record_optimization_metrics(
            model, task_complexity, status, latency_seconds,
            cost_savings_percent, quality_score, compression_ratio
        )
    except Exception as e:
        logger.debug(f"Failed to record optimization metrics: {e}")


def record_cache_hit(cache_layer: str, cache_type: str = "semantic"):
    """Record cache hit"""
    try:
        manager = get_monitoring_manager()
        manager.record_cache_metrics(cache_layer, True, cache_type)
    except Exception as e:
        logger.debug(f"Failed to record cache hit: {e}")


def record_cache_miss(cache_layer: str, cache_type: str = "semantic"):
    """Record cache miss"""
    try:
        manager = get_monitoring_manager()
        manager.record_cache_metrics(cache_layer, False, cache_type)
    except Exception as e:
        logger.debug(f"Failed to record cache miss: {e}")


def get_tracer(name: str = "cost-optimizer"):
    """Get tracer instance"""
    try:
        manager = get_monitoring_manager()
        return manager.get_tracer(name)
    except Exception:
        from opentelemetry import trace
        return trace.NoOpTracer()
