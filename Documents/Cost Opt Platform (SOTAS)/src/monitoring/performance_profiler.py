"""
Production-Grade Performance Profiler
FAANG+ level performance monitoring and optimization for 100M+ user scale
"""

import asyncio
import time
import logging
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import asynccontextmanager
import functools
import cProfile
import pstats
import io
from datetime import datetime, timedelta

from prometheus_client import Counter, Histogram, Gauge, Summary

logger = logging.getLogger(__name__)

# Performance metrics
FUNCTION_CALLS = Counter(
    'function_calls_total',
    'Total function calls',
    ['function_name', 'module']
)

FUNCTION_DURATION = Histogram(
    'function_duration_seconds',
    'Function execution time',
    ['function_name', 'module'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

MEMORY_USAGE = Gauge(
    'memory_usage_bytes',
    'Memory usage in bytes',
    ['component']
)

CPU_USAGE = Gauge(
    'cpu_usage_percent',
    'CPU usage percentage',
    ['component']
)

DATABASE_QUERY_TIME = Histogram(
    'database_query_duration_seconds',
    'Database query execution time',
    ['query_type', 'table'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Cache operations',
    ['operation', 'cache_type', 'result']
)

API_REQUEST_SIZE = Histogram(
    'api_request_size_bytes',
    'API request size in bytes',
    buckets=[100, 1000, 10000, 100000, 1000000, 10000000]
)

API_RESPONSE_SIZE = Histogram(
    'api_response_size_bytes',
    'API response size in bytes',
    buckets=[100, 1000, 10000, 100000, 1000000, 10000000]
)


@dataclass
class PerformanceMetric:
    """Performance metric data point"""
    name: str
    value: float
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FunctionProfile:
    """Function performance profile"""
    name: str
    module: str
    call_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    p95_time: float = 0.0
    p99_time: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def add_timing(self, duration: float):
        """Add timing measurement"""
        self.call_count += 1
        self.total_time += duration
        self.min_time = min(self.min_time, duration)
        self.max_time = max(self.max_time, duration)
        self.avg_time = self.total_time / self.call_count
        self.recent_times.append(duration)
        
        # Calculate percentiles from recent times
        if len(self.recent_times) >= 20:
            sorted_times = sorted(self.recent_times)
            self.p95_time = sorted_times[int(len(sorted_times) * 0.95)]
            self.p99_time = sorted_times[int(len(sorted_times) * 0.99)]


class PerformanceProfiler:
    """
    Comprehensive performance profiler for production systems
    """
    
    def __init__(self):
        self.enabled = True
        self.function_profiles: Dict[str, FunctionProfile] = {}
        self.metrics_buffer: List[PerformanceMetric] = []
        self.max_buffer_size = 10000
        
        # System monitoring
        self.system_metrics_interval = 10  # seconds
        self.monitoring_task: Optional[asyncio.Task] = None
        self.monitoring_active = False
        
        # Profiling configuration
        self.profile_threshold = 0.001  # 1ms - only profile functions taking longer
        self.max_profiles = 1000  # Maximum number of function profiles to keep
        
        # Memory tracking
        self.memory_snapshots: deque = deque(maxlen=100)
        self.memory_baseline: Optional[float] = None
        
        # Lock for thread safety
        self._lock = threading.Lock()
    
    def enable(self):
        """Enable profiling"""
        self.enabled = True
        logger.info("Performance profiling enabled")
    
    def disable(self):
        """Disable profiling"""
        self.enabled = False
        logger.info("Performance profiling disabled")
    
    def profile_function(self, func: Callable) -> Callable:
        """Decorator to profile function performance"""
        if not self.enabled:
            return func
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                if duration >= self.profile_threshold:
                    self._record_function_timing(func, duration)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                if duration >= self.profile_threshold:
                    self._record_function_timing(func, duration)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    def _record_function_timing(self, func: Callable, duration: float):
        """Record function timing"""
        function_name = func.__name__
        module_name = func.__module__
        profile_key = f"{module_name}.{function_name}"
        
        with self._lock:
            if profile_key not in self.function_profiles:
                if len(self.function_profiles) >= self.max_profiles:
                    # Remove least frequently called function
                    min_calls = min(p.call_count for p in self.function_profiles.values())
                    for key, profile in list(self.function_profiles.items()):
                        if profile.call_count == min_calls:
                            del self.function_profiles[key]
                            break
                
                self.function_profiles[profile_key] = FunctionProfile(
                    name=function_name,
                    module=module_name
                )
            
            self.function_profiles[profile_key].add_timing(duration)
        
        # Record Prometheus metrics
        FUNCTION_CALLS.labels(
            function_name=function_name,
            module=module_name
        ).inc()
        
        FUNCTION_DURATION.labels(
            function_name=function_name,
            module=module_name
        ).observe(duration)
    
    @asynccontextmanager
    async def profile_block(self, block_name: str):
        """Context manager to profile code blocks"""
        start_time = time.time()
        
        try:
            yield
        finally:
            if self.enabled:
                duration = time.time() - start_time
                self._record_metric(
                    name=f"block_{block_name}_duration",
                    value=duration,
                    labels={"block": block_name}
                )
    
    def _record_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record performance metric"""
        if not self.enabled:
            return
        
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            labels=labels or {}
        )
        
        with self._lock:
            self.metrics_buffer.append(metric)
            
            # Trim buffer if too large
            if len(self.metrics_buffer) > self.max_buffer_size:
                self.metrics_buffer = self.metrics_buffer[-self.max_buffer_size//2:]
    
    async def start_system_monitoring(self):
        """Start system resource monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_task = asyncio.create_task(self._system_monitoring_loop())
            logger.info("System monitoring started")
    
    async def stop_system_monitoring(self):
        """Stop system resource monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("System monitoring stopped")
    
    async def _system_monitoring_loop(self):
        """System monitoring loop"""
        while self.monitoring_active:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.system_metrics_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System monitoring error: {e}")
                await asyncio.sleep(self.system_metrics_interval)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            CPU_USAGE.labels(component="system").set(cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            MEMORY_USAGE.labels(component="system").set(memory.used)
            
            # Process-specific metrics
            process = psutil.Process()
            process_memory = process.memory_info().rss
            process_cpu = process.cpu_percent()
            
            MEMORY_USAGE.labels(component="process").set(process_memory)
            CPU_USAGE.labels(component="process").set(process_cpu)
            
            # Track memory snapshots
            self.memory_snapshots.append({
                'timestamp': time.time(),
                'system_memory': memory.used,
                'process_memory': process_memory,
                'memory_percent': memory.percent
            })
            
            # Set baseline if not set
            if self.memory_baseline is None:
                self.memory_baseline = process_memory
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    def get_function_profiles(self, top_n: int = 20) -> List[Dict[str, Any]]:
        """Get top function profiles by total time"""
        with self._lock:
            profiles = list(self.function_profiles.values())
        
        # Sort by total time
        profiles.sort(key=lambda p: p.total_time, reverse=True)
        
        return [
            {
                'name': p.name,
                'module': p.module,
                'call_count': p.call_count,
                'total_time': p.total_time,
                'avg_time': p.avg_time,
                'min_time': p.min_time,
                'max_time': p.max_time,
                'p95_time': p.p95_time,
                'p99_time': p.p99_time
            }
            for p in profiles[:top_n]
        ]
    
    def get_memory_analysis(self) -> Dict[str, Any]:
        """Get memory usage analysis"""
        if not self.memory_snapshots:
            return {}
        
        recent_snapshots = list(self.memory_snapshots)
        current_memory = recent_snapshots[-1]['process_memory']
        
        # Calculate memory growth
        memory_growth = 0
        if self.memory_baseline:
            memory_growth = current_memory - self.memory_baseline
        
        # Calculate memory trend
        if len(recent_snapshots) >= 10:
            old_memory = recent_snapshots[-10]['process_memory']
            memory_trend = current_memory - old_memory
        else:
            memory_trend = 0
        
        return {
            'current_memory_mb': current_memory / (1024 * 1024),
            'baseline_memory_mb': (self.memory_baseline or 0) / (1024 * 1024),
            'memory_growth_mb': memory_growth / (1024 * 1024),
            'memory_trend_mb': memory_trend / (1024 * 1024),
            'snapshots_count': len(recent_snapshots)
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        function_profiles = self.get_function_profiles(10)
        memory_analysis = self.get_memory_analysis()
        
        # Calculate total metrics
        total_function_calls = sum(p['call_count'] for p in function_profiles)
        total_function_time = sum(p['total_time'] for p in function_profiles)
        
        return {
            'summary': {
                'total_function_calls': total_function_calls,
                'total_function_time': total_function_time,
                'avg_function_time': total_function_time / total_function_calls if total_function_calls > 0 else 0,
                'profiled_functions': len(self.function_profiles),
                'metrics_buffer_size': len(self.metrics_buffer)
            },
            'top_functions': function_profiles,
            'memory_analysis': memory_analysis,
            'profiler_config': {
                'enabled': self.enabled,
                'profile_threshold': self.profile_threshold,
                'max_profiles': self.max_profiles,
                'monitoring_active': self.monitoring_active
            }
        }
    
    def reset_profiles(self):
        """Reset all performance profiles"""
        with self._lock:
            self.function_profiles.clear()
            self.metrics_buffer.clear()
            self.memory_snapshots.clear()
            self.memory_baseline = None
        
        logger.info("Performance profiles reset")


# Global profiler instance
_profiler: Optional[PerformanceProfiler] = None


def get_profiler() -> PerformanceProfiler:
    """Get global profiler instance"""
    global _profiler
    if _profiler is None:
        _profiler = PerformanceProfiler()
    return _profiler


def profile(func: Callable) -> Callable:
    """Decorator to profile function performance"""
    profiler = get_profiler()
    return profiler.profile_function(func)


async def profile_block(block_name: str):
    """Context manager to profile code blocks"""
    profiler = get_profiler()
    return profiler.profile_block(block_name)
