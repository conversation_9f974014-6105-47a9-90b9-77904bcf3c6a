"""
Distributed Tracing System
Production-grade OpenTelemetry tracing for FAANG+ observability
"""

import logging
import os
from typing import Optional, Dict, Any

from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import J<PERSON>gerExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource

from src.core.config import get_settings

logger = logging.getLogger(__name__)


class TracingManager:
    """
    Comprehensive tracing management for distributed systems
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.tracer_provider: Optional[TracerProvider] = None
        self.jaeger_exporter: Optional[JaegerExporter] = None
        self.is_initialized = False
    
    def setup_tracing(self) -> bool:
        """
        Setup OpenTelemetry tracing with Jaeger
        
        Returns:
            bool: True if tracing setup successful
        """
        try:
            # Create resource with service information
            resource = Resource.create({
                "service.name": "cost-optimizer",
                "service.version": "1.0.0",
                "service.environment": self.settings.environment,
                "service.instance.id": os.getenv("HOSTNAME", "unknown"),
                "deployment.environment": self.settings.environment
            })
            
            # Create tracer provider
            self.tracer_provider = TracerProvider(resource=resource)
            trace.set_tracer_provider(self.tracer_provider)
            
            # Setup Jaeger exporter if enabled
            if self.settings.jaeger_enabled:
                self.jaeger_exporter = JaegerExporter(
                    agent_host_name=self.settings.jaeger_host,
                    agent_port=self.settings.jaeger_port,
                    collector_endpoint=f"http://{self.settings.jaeger_host}:{self.settings.jaeger_port}/api/traces",
                )
                
                # Add batch span processor
                span_processor = BatchSpanProcessor(
                    self.jaeger_exporter,
                    max_queue_size=2048,
                    max_export_batch_size=512,
                    export_timeout_millis=30000,
                    schedule_delay_millis=5000
                )
                
                self.tracer_provider.add_span_processor(span_processor)
                logger.info(f"Jaeger tracing enabled: {self.settings.jaeger_host}:{self.settings.jaeger_port}")
            
            # Auto-instrument common libraries
            self._setup_auto_instrumentation()
            
            self.is_initialized = True
            logger.info("Distributed tracing initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup tracing: {e}")
            return False
    
    def _setup_auto_instrumentation(self):
        """Setup automatic instrumentation for common libraries"""
        try:
            # Instrument FastAPI
            FastAPIInstrumentor.instrument()
            
            # Instrument HTTP requests
            RequestsInstrumentor().instrument()
            
            # Instrument SQLAlchemy (if available)
            try:
                SQLAlchemyInstrumentor().instrument()
            except Exception as e:
                logger.debug(f"SQLAlchemy instrumentation not available: {e}")
            
            # Instrument Redis (if available)
            try:
                RedisInstrumentor().instrument()
            except Exception as e:
                logger.debug(f"Redis instrumentation not available: {e}")
            
            logger.info("Auto-instrumentation setup complete")
            
        except Exception as e:
            logger.warning(f"Some auto-instrumentation failed: {e}")
    
    def get_tracer(self, name: str = "cost-optimizer") -> trace.Tracer:
        """
        Get tracer instance
        
        Args:
            name: Tracer name
            
        Returns:
            Tracer instance
        """
        if not self.is_initialized:
            logger.warning("Tracing not initialized, using no-op tracer")
            return trace.NoOpTracer()
        
        return trace.get_tracer(name)
    
    def create_span(
        self,
        name: str,
        attributes: Optional[Dict[str, Any]] = None,
        tracer_name: str = "cost-optimizer"
    ):
        """
        Create a new span with optional attributes
        
        Args:
            name: Span name
            attributes: Optional span attributes
            tracer_name: Tracer name to use
            
        Returns:
            Span context manager
        """
        tracer = self.get_tracer(tracer_name)
        span = tracer.start_span(name)
        
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        
        return span
    
    def add_span_attributes(self, span: trace.Span, attributes: Dict[str, Any]):
        """
        Add attributes to an existing span
        
        Args:
            span: Span to add attributes to
            attributes: Attributes to add
        """
        for key, value in attributes.items():
            span.set_attribute(key, value)
    
    def record_exception(self, span: trace.Span, exception: Exception):
        """
        Record an exception in a span
        
        Args:
            span: Span to record exception in
            exception: Exception to record
        """
        span.record_exception(exception)
        span.set_status(trace.Status(trace.StatusCode.ERROR, str(exception)))
    
    def shutdown(self):
        """Shutdown tracing and flush remaining spans"""
        if self.tracer_provider:
            try:
                self.tracer_provider.shutdown()
                logger.info("Tracing shutdown complete")
            except Exception as e:
                logger.error(f"Error during tracing shutdown: {e}")


# Global tracing manager instance
_tracing_manager: Optional[TracingManager] = None


def setup_tracing() -> TracingManager:
    """Setup global tracing manager"""
    global _tracing_manager
    _tracing_manager = TracingManager()
    _tracing_manager.setup_tracing()
    return _tracing_manager


def get_tracer(name: str = "cost-optimizer") -> trace.Tracer:
    """Get tracer instance"""
    if _tracing_manager is None:
        logger.warning("Tracing not initialized, using no-op tracer")
        return trace.NoOpTracer()
    
    return _tracing_manager.get_tracer(name)


def create_span(
    name: str,
    attributes: Optional[Dict[str, Any]] = None,
    tracer_name: str = "cost-optimizer"
):
    """Create a new span with optional attributes"""
    if _tracing_manager is None:
        # Return a no-op context manager
        from contextlib import nullcontext
        return nullcontext()
    
    return _tracing_manager.create_span(name, attributes, tracer_name)


# Convenience decorators for tracing
def trace_function(name: Optional[str] = None, attributes: Optional[Dict[str, Any]] = None):
    """
    Decorator to trace function execution
    
    Args:
        name: Optional span name (defaults to function name)
        attributes: Optional span attributes
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            tracer = get_tracer()
            with tracer.start_as_current_span(span_name) as span:
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                
                # Add function metadata
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                try:
                    result = func(*args, **kwargs)
                    span.set_status(trace.Status(trace.StatusCode.OK))
                    return result
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    raise
        
        return wrapper
    return decorator


def trace_async_function(name: Optional[str] = None, attributes: Optional[Dict[str, Any]] = None):
    """
    Decorator to trace async function execution
    
    Args:
        name: Optional span name (defaults to function name)
        attributes: Optional span attributes
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            tracer = get_tracer()
            with tracer.start_as_current_span(span_name) as span:
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                
                # Add function metadata
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                span.set_attribute("function.async", True)
                
                try:
                    result = await func(*args, **kwargs)
                    span.set_status(trace.Status(trace.StatusCode.OK))
                    return result
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    raise
        
        return wrapper
    return decorator
