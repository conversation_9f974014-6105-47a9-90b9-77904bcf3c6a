/**
 * Chunkie - Text Chunking Utility
 * 
 * This utility provides functions for chunking large text inputs.
 * It helps handle large text inputs efficiently by breaking them into manageable chunks.
 */

/**
 * Default chunking options
 */
const DEFAULT_CHUNKING_OPTIONS = {
  chunkSize: 4000,
  chunkOverlap: 200,
  lengthFunction: (text) => text.length,
  separators: ['\n\n', '\n', '. ', ' ', ''],
};

/**
 * Text Chunker class
 */
class TextChunker {
  /**
   * Constructor
   * @param {Object} options - Chunking options
   */
  constructor(options = {}) {
    this.options = { ...DEFAULT_CHUNKING_OPTIONS, ...options };
  }

  /**
   * Split text into chunks
   * @param {string} text - Text to split
   * @param {string} strategy - Chunking strategy ('recursive' or 'simple')
   * @returns {string[]} Array of text chunks
   */
  splitText(text, strategy = 'recursive') {
    if (strategy === 'recursive') {
      return this.recursiveSplit(text);
    } else {
      return this.simpleSplit(text);
    }
  }

  /**
   * Split text into chunks with metadata
   * @param {string} text - Text to split
   * @param {string} strategy - Chunking strategy ('recursive' or 'simple')
   * @returns {Array<{chunk: string, metadata: {start: number, end: number}}>} Array of chunks with metadata
   */
  splitTextWithMetadata(text, strategy = 'recursive') {
    const chunks = this.splitText(text, strategy);
    const result = [];
    
    let startIndex = 0;
    for (const chunk of chunks) {
      const endIndex = startIndex + this.options.lengthFunction(chunk);
      result.push({
        chunk,
        metadata: {
          start: startIndex,
          end: endIndex,
        },
      });
      startIndex = endIndex - this.options.chunkOverlap;
    }
    
    return result;
  }

  /**
   * Split text recursively using separators
   * @param {string} text - Text to split
   * @returns {string[]} Array of text chunks
   * @private
   */
  recursiveSplit(text) {
    const { chunkSize, chunkOverlap, lengthFunction, separators } = this.options;
    
    // If text is small enough, return it as a single chunk
    if (lengthFunction(text) <= chunkSize) {
      return [text];
    }
    
    // Try each separator in order
    for (const separator of separators) {
      if (!separator) {
        // If separator is empty, split by character
        return this.splitByCharacter(text);
      }
      
      const chunks = this.splitBySeparator(text, separator);
      if (chunks.length > 1) {
        return chunks;
      }
    }
    
    // If no separator worked, split by character
    return this.splitByCharacter(text);
  }

  /**
   * Split text by separator
   * @param {string} text - Text to split
   * @param {string} separator - Separator to use
   * @returns {string[]} Array of text chunks
   * @private
   */
  splitBySeparator(text, separator) {
    const { chunkSize, chunkOverlap, lengthFunction } = this.options;
    
    // Split text by separator
    const segments = text.split(separator);
    
    // If only one segment, return it
    if (segments.length === 1) {
      return segments;
    }
    
    const chunks = [];
    let currentChunk = '';
    
    for (const segment of segments) {
      const segmentWithSeparator = segment + (separator || '');
      const newChunkLength = lengthFunction(currentChunk + segmentWithSeparator);
      
      if (newChunkLength <= chunkSize || !currentChunk) {
        // Add segment to current chunk
        currentChunk += segmentWithSeparator;
      } else {
        // Current chunk is full, start a new one
        chunks.push(currentChunk);
        
        // Add overlap from previous chunk
        const overlapSize = Math.min(chunkOverlap, lengthFunction(currentChunk));
        const overlap = currentChunk.slice(-overlapSize);
        
        currentChunk = overlap + segmentWithSeparator;
      }
    }
    
    // Add the last chunk if not empty
    if (currentChunk) {
      chunks.push(currentChunk);
    }
    
    return chunks;
  }

  /**
   * Split text by character
   * @param {string} text - Text to split
   * @returns {string[]} Array of text chunks
   * @private
   */
  splitByCharacter(text) {
    const { chunkSize, chunkOverlap, lengthFunction } = this.options;
    
    const chunks = [];
    let i = 0;
    
    while (i < text.length) {
      // Get chunk of size chunkSize
      const chunk = text.slice(i, i + chunkSize);
      chunks.push(chunk);
      
      // Move to next chunk, accounting for overlap
      i += chunkSize - chunkOverlap;
    }
    
    return chunks;
  }

  /**
   * Simple split by chunk size
   * @param {string} text - Text to split
   * @returns {string[]} Array of text chunks
   * @private
   */
  simpleSplit(text) {
    const { chunkSize, chunkOverlap, lengthFunction } = this.options;
    
    const chunks = [];
    let i = 0;
    
    while (i < text.length) {
      // Get chunk of size chunkSize
      const chunk = text.slice(i, i + chunkSize);
      chunks.push(chunk);
      
      // Move to next chunk, accounting for overlap
      i += chunkSize - chunkOverlap;
    }
    
    return chunks;
  }

  /**
   * Create a new chunker with different options
   * @param {Object} options - Chunking options
   * @returns {TextChunker} New TextChunker instance
   */
  withOptions(options) {
    return new TextChunker({ ...this.options, ...options });
  }
}

/**
 * Create a text chunker with default options
 * @returns {TextChunker} TextChunker instance
 */
function createTextChunker() {
  return new TextChunker();
}

/**
 * Create a text chunker with custom options
 * @param {Object} options - Chunking options
 * @returns {TextChunker} TextChunker instance
 */
function createTextChunkerWithOptions(options) {
  return new TextChunker(options);
}

/**
 * Split text into chunks using default options
 * @param {string} text - Text to split
 * @param {string} strategy - Chunking strategy ('recursive' or 'simple')
 * @returns {string[]} Array of text chunks
 */
function splitText(text, strategy = 'recursive') {
  const chunker = createTextChunker();
  return chunker.splitText(text, strategy);
}

/**
 * Split text into chunks with metadata using default options
 * @param {string} text - Text to split
 * @param {string} strategy - Chunking strategy ('recursive' or 'simple')
 * @returns {Array<{chunk: string, metadata: {start: number, end: number}}>} Array of chunks with metadata
 */
function splitTextWithMetadata(text, strategy = 'recursive') {
  const chunker = createTextChunker();
  return chunker.splitTextWithMetadata(text, strategy);
}

/**
 * Merge chunks back into a single text
 * @param {string[]} chunks - Array of text chunks
 * @returns {string} Merged text
 */
function mergeChunks(chunks) {
  return chunks.join(' ');
}

/**
 * Process text in chunks
 * @param {string} text - Text to process
 * @param {Function} processor - Function to process each chunk
 * @param {Object} options - Chunking options
 * @returns {Promise<Array>} Array of processed chunks
 */
async function processTextInChunks(text, processor, options = {}) {
  const chunker = new TextChunker(options);
  const chunks = chunker.splitText(text);
  
  const results = [];
  for (const chunk of chunks) {
    const result = await processor(chunk);
    results.push(result);
  }
  
  return results;
}

module.exports = {
  TextChunker,
  createTextChunker,
  createTextChunkerWithOptions,
  splitText,
  splitTextWithMetadata,
  mergeChunks,
  processTextInChunks,
  DEFAULT_CHUNKING_OPTIONS,
};
