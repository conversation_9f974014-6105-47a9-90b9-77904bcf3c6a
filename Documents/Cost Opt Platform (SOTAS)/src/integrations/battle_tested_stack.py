"""
Battle-tested Open Source Stack Integration
High-starred GitHub repositories (10k+ stars) for production deployment
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import json

# ChromaDB - Vector Database (10k+ stars)
import chromadb
from chromadb.config import Settings

# Redis Clustering (10k+ stars)
import redis
from rediscluster import RedisCluster

# Celery - Message Queue (10k+ stars)
from celery import Celery

# Prometheus - Monitoring (10k+ stars)
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# Jaeger - Distributed Tracing (10k+ stars)
from jaeger_client import Config as JaegerConfig

logger = logging.getLogger(__name__)

class BattleTestedStack:
    """
    Integration manager for battle-tested open source solutions
    All components from repositories with 10k+ GitHub stars
    """
    
    def __init__(self):
        self.chroma_client = None
        self.redis_cluster = None
        self.celery_app = None
        self.prometheus_metrics = {}
        self.jaeger_tracer = None
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all battle-tested components"""
        try:
            logger.info("🚀 Initializing Battle-tested Open Source Stack")
            
            # Initialize ChromaDB (Vector Database)
            await self._init_chromadb()
            
            # Initialize Redis Cluster
            await self._init_redis_cluster()
            
            # Initialize Celery (Message Queue)
            await self._init_celery()
            
            # Initialize Prometheus (Monitoring)
            await self._init_prometheus()
            
            # Initialize Jaeger (Tracing)
            await self._init_jaeger()
            
            self.initialized = True
            logger.info("✅ Battle-tested stack initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize battle-tested stack: {e}")
            return False
    
    async def _init_chromadb(self):
        """Initialize ChromaDB for vector operations"""
        try:
            # ChromaDB configuration for production
            self.chroma_client = chromadb.Client(Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory="./data/chromadb",
                anonymized_telemetry=False
            ))
            
            # Create collections for different use cases
            collections = [
                "conversation_embeddings",
                "prompt_cache",
                "cost_optimization_patterns"
            ]
            
            for collection_name in collections:
                try:
                    collection = self.chroma_client.get_collection(collection_name)
                    logger.info(f"✅ ChromaDB collection '{collection_name}' loaded")
                except:
                    collection = self.chroma_client.create_collection(collection_name)
                    logger.info(f"✅ ChromaDB collection '{collection_name}' created")
            
            logger.info("✅ ChromaDB initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ ChromaDB initialization failed: {e}")
            raise
    
    async def _init_redis_cluster(self):
        """Initialize Redis cluster for distributed caching"""
        try:
            # Redis cluster configuration
            startup_nodes = [
                {"host": "localhost", "port": "7000"},
                {"host": "localhost", "port": "7001"},
                {"host": "localhost", "port": "7002"}
            ]
            
            # Fallback to single Redis instance for development
            try:
                self.redis_cluster = RedisCluster(
                    startup_nodes=startup_nodes,
                    decode_responses=True,
                    skip_full_coverage_check=True
                )
                await asyncio.to_thread(self.redis_cluster.ping)
                logger.info("✅ Redis cluster initialized")
            except:
                # Fallback to single Redis instance
                self.redis_cluster = redis.Redis(
                    host='localhost',
                    port=6379,
                    decode_responses=True
                )
                await asyncio.to_thread(self.redis_cluster.ping)
                logger.info("✅ Redis single instance initialized (fallback)")
            
        except Exception as e:
            logger.error(f"❌ Redis initialization failed: {e}")
            raise
    
    async def _init_celery(self):
        """Initialize Celery for message queuing"""
        try:
            # Celery configuration
            self.celery_app = Celery(
                'cost_optimizer',
                broker='redis://localhost:6379/0',
                backend='redis://localhost:6379/0'
            )
            
            # Configure Celery
            self.celery_app.conf.update(
                task_serializer='json',
                accept_content=['json'],
                result_serializer='json',
                timezone='UTC',
                enable_utc=True,
                task_routes={
                    'cost_optimization.*': {'queue': 'optimization'},
                    'conversation.*': {'queue': 'conversation'},
                    'monitoring.*': {'queue': 'monitoring'}
                }
            )
            
            logger.info("✅ Celery initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Celery initialization failed: {e}")
            raise
    
    async def _init_prometheus(self):
        """Initialize Prometheus metrics"""
        try:
            # Define Prometheus metrics
            self.prometheus_metrics = {
                'requests_total': Counter(
                    'cost_optimizer_requests_total',
                    'Total requests',
                    ['method', 'endpoint', 'status']
                ),
                'request_duration': Histogram(
                    'cost_optimizer_request_duration_seconds',
                    'Request duration',
                    ['method', 'endpoint']
                ),
                'cost_savings': Gauge(
                    'cost_optimizer_savings_percentage',
                    'Cost savings percentage'
                ),
                'active_conversations': Gauge(
                    'cost_optimizer_active_conversations',
                    'Number of active conversations'
                ),
                'cache_hits': Counter(
                    'cost_optimizer_cache_hits_total',
                    'Cache hits',
                    ['cache_type']
                ),
                'model_usage': Counter(
                    'cost_optimizer_model_usage_total',
                    'Model usage',
                    ['model', 'provider']
                )
            }
            
            # Start Prometheus metrics server
            start_http_server(8001)
            logger.info("✅ Prometheus metrics server started on port 8001")
            
        except Exception as e:
            logger.error(f"❌ Prometheus initialization failed: {e}")
            raise
    
    async def _init_jaeger(self):
        """Initialize Jaeger distributed tracing"""
        try:
            # Jaeger configuration
            config = JaegerConfig(
                config={
                    'sampler': {
                        'type': 'const',
                        'param': 1,
                    },
                    'logging': True,
                    'local_agent': {
                        'reporting_host': 'localhost',
                        'reporting_port': 6831,
                    },
                },
                service_name='cost-optimizer',
                validate=True,
            )
            
            self.jaeger_tracer = config.initialize_tracer()
            logger.info("✅ Jaeger tracing initialized")
            
        except Exception as e:
            logger.warning(f"⚠️ Jaeger initialization failed (optional): {e}")
            # Jaeger is optional, don't fail the entire stack
    
    # High-level operations using the battle-tested stack
    
    async def cache_conversation(self, conversation_id: str, data: Dict[str, Any]) -> bool:
        """Cache conversation data using Redis"""
        try:
            key = f"conversation:{conversation_id}"
            await asyncio.to_thread(
                self.redis_cluster.setex,
                key,
                3600,  # 1 hour TTL
                json.dumps(data)
            )
            
            # Update metrics
            self.prometheus_metrics['cache_hits'].labels(cache_type='conversation').inc()
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache conversation {conversation_id}: {e}")
            return False
    
    async def get_cached_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve cached conversation data"""
        try:
            key = f"conversation:{conversation_id}"
            data = await asyncio.to_thread(self.redis_cluster.get, key)
            
            if data:
                self.prometheus_metrics['cache_hits'].labels(cache_type='conversation').inc()
                return json.loads(data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached conversation {conversation_id}: {e}")
            return None
    
    async def store_embedding(self, collection_name: str, text: str, metadata: Dict[str, Any]) -> bool:
        """Store text embedding in ChromaDB"""
        try:
            collection = self.chroma_client.get_collection(collection_name)
            
            # Generate embedding ID
            embedding_id = f"{collection_name}_{hash(text)}"
            
            collection.add(
                documents=[text],
                metadatas=[metadata],
                ids=[embedding_id]
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to store embedding: {e}")
            return False
    
    async def search_similar(self, collection_name: str, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar embeddings"""
        try:
            collection = self.chroma_client.get_collection(collection_name)
            
            results = collection.query(
                query_texts=[query],
                n_results=n_results
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search embeddings: {e}")
            return []
    
    async def queue_optimization_task(self, task_data: Dict[str, Any]) -> str:
        """Queue cost optimization task using Celery"""
        try:
            task = self.celery_app.send_task(
                'cost_optimization.optimize_prompt',
                args=[task_data],
                queue='optimization'
            )
            
            return task.id
            
        except Exception as e:
            logger.error(f"Failed to queue optimization task: {e}")
            return None
    
    def record_metrics(self, method: str, endpoint: str, status: int, duration: float):
        """Record request metrics"""
        try:
            self.prometheus_metrics['requests_total'].labels(
                method=method,
                endpoint=endpoint,
                status=str(status)
            ).inc()
            
            self.prometheus_metrics['request_duration'].labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
        except Exception as e:
            logger.error(f"Failed to record metrics: {e}")
    
    def update_cost_savings(self, savings_percentage: float):
        """Update cost savings metric"""
        try:
            self.prometheus_metrics['cost_savings'].set(savings_percentage)
        except Exception as e:
            logger.error(f"Failed to update cost savings metric: {e}")
    
    async def health_check(self) -> Dict[str, bool]:
        """Comprehensive health check of all components"""
        health = {
            'chromadb': False,
            'redis': False,
            'celery': False,
            'prometheus': False,
            'jaeger': False
        }
        
        # Check ChromaDB
        try:
            collections = self.chroma_client.list_collections()
            health['chromadb'] = True
        except:
            pass
        
        # Check Redis
        try:
            await asyncio.to_thread(self.redis_cluster.ping)
            health['redis'] = True
        except:
            pass
        
        # Check Celery
        try:
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            health['celery'] = bool(stats)
        except:
            pass
        
        # Check Prometheus
        health['prometheus'] = bool(self.prometheus_metrics)
        
        # Check Jaeger
        health['jaeger'] = self.jaeger_tracer is not None
        
        return health
    
    async def shutdown(self):
        """Graceful shutdown of all components"""
        logger.info("🔄 Shutting down battle-tested stack...")
        
        try:
            if self.jaeger_tracer:
                self.jaeger_tracer.close()
            
            if self.celery_app:
                self.celery_app.control.shutdown()
            
            logger.info("✅ Battle-tested stack shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

# Global instance
battle_tested_stack = BattleTestedStack()
