<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Optimizer - Cost Optimization Platform</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-brain"></i>
                    <span><PERSON> Optimizer</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#dashboard" class="nav-link" data-section="dashboard">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#optimize" class="nav-link" data-section="optimize">
                        <i class="fas fa-magic"></i>
                        <span>Optimize Request</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link" data-section="analytics">
                        <i class="fas fa-analytics"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#monitoring" class="nav-link" data-section="monitoring">
                        <i class="fas fa-chart-bar"></i>
                        <span>Monitoring</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 class="page-title">Dashboard</h1>
                    <p class="page-subtitle">Monitor your Claude optimization performance</p>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>System Online</span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <!-- Key Metrics Cards -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value" id="total-savings">$0.00</h3>
                            <p class="metric-label">Total Savings</p>
                            <span class="metric-change positive">+0%</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value" id="savings-rate">0%</h3>
                            <p class="metric-label">Savings Rate</p>
                            <span class="metric-change positive">+0%</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value" id="avg-latency">0ms</h3>
                            <p class="metric-label">Avg Latency</p>
                            <span class="metric-change positive">-0ms</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-memory"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value" id="cache-hit-rate">0%</h3>
                            <p class="metric-label">Cache Hit Rate</p>
                            <span class="metric-change positive">+0%</span>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="charts-row">
                    <div class="chart-container">
                        <h3>Cost Savings Over Time</h3>
                        <canvas id="savingsChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>Model Usage Distribution</h3>
                        <canvas id="modelChart"></canvas>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="activity-section">
                    <h3>Recent Optimizations</h3>
                    <div class="activity-list" id="recent-activity">
                        <!-- Activity items will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Optimize Section -->
            <section id="optimize" class="content-section">
                <div class="optimize-container">
                    <div class="optimize-form">
                        <h2>Optimize Your Request</h2>
                        <p>Enter your prompt below to get optimized results with massive cost savings</p>
                        
                        <form id="optimization-form">
                            <div class="form-group">
                                <label for="prompt">Your Prompt</label>
                                <textarea 
                                    id="prompt" 
                                    name="prompt" 
                                    placeholder="Enter your prompt here..."
                                    rows="6"
                                    required
                                ></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="quality-threshold">Quality Threshold</label>
                                    <input 
                                        type="range" 
                                        id="quality-threshold" 
                                        name="quality_threshold" 
                                        min="0.6" 
                                        max="1.0" 
                                        step="0.05" 
                                        value="0.85"
                                    >
                                    <span class="range-value">0.85</span>
                                </div>
                                
                                <div class="form-group">
                                    <label for="optimization-level">Optimization Level</label>
                                    <select id="optimization-level" name="optimization_level">
                                        <option value="1">Conservative (Level 1)</option>
                                        <option value="2">Balanced (Level 2)</option>
                                        <option value="3" selected>Aggressive (Level 3)</option>
                                        <option value="4">Maximum (Level 4)</option>
                                        <option value="5">Extreme (Level 5)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <button type="submit" class="optimize-btn">
                                <i class="fas fa-magic"></i>
                                Optimize Request
                            </button>
                        </form>
                    </div>
                    
                    <div class="results-container" id="results-container" style="display: none;">
                        <h3>Optimization Results</h3>
                        <div class="results-content" id="results-content">
                            <!-- Results will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="content-section">
                <h2>Advanced Analytics</h2>
                <p>Deep dive into your optimization performance</p>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h4>Cost Breakdown</h4>
                        <canvas id="costBreakdownChart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h4>Performance Metrics</h4>
                        <canvas id="performanceChart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h4>Quality Scores</h4>
                        <canvas id="qualityChart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h4>Cache Performance</h4>
                        <canvas id="cacheChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Monitoring Section -->
            <section id="monitoring" class="content-section">
                <h2>System Monitoring</h2>
                <p>Access internal monitoring and management tools</p>

                <div class="monitoring-grid">
                    <div class="monitoring-card">
                        <div class="monitoring-icon">📊</div>
                        <h4>Grafana Dashboards</h4>
                        <p>Performance monitoring and metrics visualization</p>
                        <a href="/api/v1/proxy/grafana/" target="_blank" class="monitoring-btn">
                            <i class="fas fa-external-link-alt"></i>
                            Open Grafana
                        </a>
                    </div>

                    <div class="monitoring-card">
                        <div class="monitoring-icon">📈</div>
                        <h4>Prometheus Metrics</h4>
                        <p>Raw metrics and alerting system</p>
                        <a href="/api/v1/proxy/prometheus/" target="_blank" class="monitoring-btn">
                            <i class="fas fa-external-link-alt"></i>
                            Open Prometheus
                        </a>
                    </div>

                    <div class="monitoring-card">
                        <div class="monitoring-icon">🔍</div>
                        <h4>Jaeger Tracing</h4>
                        <p>Distributed request tracing and debugging</p>
                        <a href="/api/v1/proxy/jaeger/" target="_blank" class="monitoring-btn">
                            <i class="fas fa-external-link-alt"></i>
                            Open Jaeger
                        </a>
                    </div>

                    <div class="monitoring-card">
                        <div class="monitoring-icon">🔧</div>
                        <h4>N8N Workflows</h4>
                        <p>Automation and workflow management</p>
                        <a href="/api/v1/proxy/n8n/" target="_blank" class="monitoring-btn">
                            <i class="fas fa-external-link-alt"></i>
                            Open N8N
                        </a>
                    </div>
                </div>

                <div class="services-status">
                    <h3>Service Status</h3>
                    <div id="services-status-list">
                        <!-- Status will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <h2>Settings</h2>
                <p>Configure your optimization preferences</p>
                
                <div class="settings-grid">
                    <div class="settings-card">
                        <h4>API Configuration</h4>
                        <div class="form-group">
                            <label for="openrouter-key">OpenRouter API Key</label>
                            <input type="password" id="openrouter-key" placeholder="Enter your API key">
                        </div>
                        <button class="save-btn">Save Configuration</button>
                    </div>
                    
                    <div class="settings-card">
                        <h4>Default Optimization</h4>
                        <div class="form-group">
                            <label for="default-quality">Default Quality Threshold</label>
                            <input type="range" id="default-quality" min="0.6" max="1.0" step="0.05" value="0.85">
                        </div>
                        <div class="form-group">
                            <label for="default-level">Default Optimization Level</label>
                            <select id="default-level">
                                <option value="1">Conservative</option>
                                <option value="2">Balanced</option>
                                <option value="3" selected>Aggressive</option>
                                <option value="4">Maximum</option>
                                <option value="5">Extreme</option>
                            </select>
                        </div>
                        <button class="save-btn">Save Defaults</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-brain fa-spin"></i>
            <p>Optimizing your request...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
