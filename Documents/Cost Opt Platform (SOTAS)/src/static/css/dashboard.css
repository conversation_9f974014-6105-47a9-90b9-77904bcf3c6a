/* Modern Dashboard CSS */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --sidebar-width: 260px;
    --header-height: 80px;
    --border-radius: 12px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    font-size: 1.5rem;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.nav-item.active .nav-link {
    background-color: var(--background-color);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
    font-weight: 500;
}

.nav-link i {
    width: 1.25rem;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
}

.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--header-height);
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Content Sections */
.content-section {
    display: none;
    padding: 2rem;
}

.content-section.active {
    display: block;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.metric-change.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--secondary-color);
}

.metric-change.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Charts */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.chart-container h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Activity Section */
.activity-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.activity-section h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.activity-list {
    space-y: 0.75rem;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--background-color);
    margin-bottom: 0.75rem;
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.activity-savings {
    font-weight: 600;
    color: var(--secondary-color);
}

/* Optimize Section */
.optimize-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1200px;
}

.optimize-form {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.optimize-form h2 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.optimize-form p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.range-value {
    display: inline-block;
    margin-left: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.optimize-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.optimize-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.optimize-btn:active {
    transform: translateY(0);
}

/* Results Container */
.results-container {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.results-container h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.analytics-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.analytics-card h4 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Monitoring Grid */
.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.monitoring-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.monitoring-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.monitoring-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.monitoring-card h4 {
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.monitoring-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.monitoring-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.monitoring-btn:hover {
    background: var(--primary-dark);
}

.services-status {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.services-status h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.service-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--background-color);
    margin-bottom: 0.75rem;
}

.service-status-name {
    font-weight: 500;
}

.service-status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.service-status-badge.healthy {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--secondary-color);
}

.service-status-badge.unhealthy {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.service-status-badge.unavailable {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.settings-card h4 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.save-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.save-btn:hover {
    background: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-row {
        grid-template-columns: 1fr;
    }
    
    .optimize-container {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}
