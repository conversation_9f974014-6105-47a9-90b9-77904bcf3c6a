// Dashboard JavaScript
class CostOptimizerDashboard {
    constructor() {
        this.apiBase = '/api/v1';
        this.charts = {};
        this.metrics = {
            totalSavings: 0,
            savingsRate: 0,
            avgLatency: 0,
            cacheHitRate: 0
        };
        
        this.init();
    }

    async init() {
        this.setupNavigation();
        this.setupOptimizationForm();
        this.setupRangeSliders();
        await this.loadDashboardData();
        this.initializeCharts();
        this.startRealTimeUpdates();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const sections = document.querySelectorAll('.content-section');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all nav items and sections
                document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                sections.forEach(section => section.classList.remove('active'));
                
                // Add active class to clicked nav item
                link.parentElement.classList.add('active');
                
                // Show corresponding section
                const sectionId = link.dataset.section;
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.classList.add('active');
                    this.updatePageTitle(sectionId);
                }
            });
        });
    }

    updatePageTitle(sectionId) {
        const titles = {
            dashboard: 'Dashboard',
            optimize: 'Optimize Request',
            analytics: 'Analytics',
            monitoring: 'System Monitoring',
            settings: 'Settings'
        };

        const subtitles = {
            dashboard: 'Monitor your Claude optimization performance',
            optimize: 'Get optimized results with massive cost savings',
            analytics: 'Deep dive into your optimization performance',
            monitoring: 'Access internal monitoring and management tools',
            settings: 'Configure your optimization preferences'
        };

        document.querySelector('.page-title').textContent = titles[sectionId] || 'Dashboard';
        document.querySelector('.page-subtitle').textContent = subtitles[sectionId] || '';

        // Load service status when monitoring section is opened
        if (sectionId === 'monitoring') {
            this.loadServiceStatus();
        }
    }

    setupOptimizationForm() {
        const form = document.getElementById('optimization-form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleOptimization(new FormData(form));
        });
    }

    setupRangeSliders() {
        const sliders = document.querySelectorAll('input[type="range"]');
        sliders.forEach(slider => {
            const updateValue = () => {
                const valueSpan = slider.parentElement.querySelector('.range-value');
                if (valueSpan) {
                    valueSpan.textContent = slider.value;
                }
            };
            
            slider.addEventListener('input', updateValue);
            updateValue(); // Set initial value
        });
    }

    async handleOptimization(formData) {
        const loadingOverlay = document.getElementById('loading-overlay');
        const resultsContainer = document.getElementById('results-container');
        
        try {
            // Show loading
            loadingOverlay.classList.add('active');
            
            // Prepare request data
            const requestData = {
                prompt: formData.get('prompt'),
                quality_threshold: parseFloat(formData.get('quality_threshold')),
                optimization_level: parseInt(formData.get('optimization_level'))
            };

            // Make API request
            const response = await fetch(`${this.apiBase}/optimize`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            this.displayOptimizationResults(result);
            
            // Show results
            resultsContainer.style.display = 'block';
            
            // Update metrics
            await this.loadDashboardData();
            
        } catch (error) {
            console.error('Optimization failed:', error);
            this.showError('Optimization failed. Please try again.');
        } finally {
            // Hide loading
            loadingOverlay.classList.remove('active');
        }
    }

    displayOptimizationResults(result) {
        const resultsContent = document.getElementById('results-content');
        if (!resultsContent) return;

        const savingsPercentage = ((result.original_cost - result.optimized_cost) / result.original_cost * 100).toFixed(1);
        
        resultsContent.innerHTML = `
            <div class="results-grid">
                <div class="result-card">
                    <h4>Selected Model</h4>
                    <p class="result-value">${result.selected_model}</p>
                </div>
                <div class="result-card">
                    <h4>Original Cost</h4>
                    <p class="result-value">$${result.original_cost.toFixed(4)}</p>
                </div>
                <div class="result-card">
                    <h4>Optimized Cost</h4>
                    <p class="result-value">$${result.optimized_cost.toFixed(4)}</p>
                </div>
                <div class="result-card">
                    <h4>Savings</h4>
                    <p class="result-value savings">${savingsPercentage}%</p>
                </div>
                <div class="result-card">
                    <h4>Quality Score</h4>
                    <p class="result-value">${(result.quality_score * 100).toFixed(1)}%</p>
                </div>
                <div class="result-card">
                    <h4>Processing Time</h4>
                    <p class="result-value">${result.processing_time_ms}ms</p>
                </div>
            </div>
            
            <div class="optimized-prompt">
                <h4>Optimized Prompt</h4>
                <div class="prompt-content">${result.optimized_prompt}</div>
            </div>
        `;
    }

    async loadDashboardData() {
        try {
            const response = await fetch(`${this.apiBase}/metrics`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.updateMetrics(data);
            this.updateRecentActivity(data.recent_activity || []);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            // Use mock data for demo
            this.loadMockData();
        }
    }

    loadMockData() {
        const mockData = {
            total_savings: 1247.83,
            average_savings_percentage: 87.3,
            average_latency_ms: 45,
            cache_hit_rate: 0.923,
            recent_activity: [
                {
                    prompt: "Analyze this code for optimization...",
                    model: "deepseek-v3",
                    savings: 92.1,
                    timestamp: new Date().toISOString()
                },
                {
                    prompt: "Generate a marketing email...",
                    model: "claude-3-sonnet",
                    savings: 78.5,
                    timestamp: new Date(Date.now() - 300000).toISOString()
                }
            ]
        };
        
        this.updateMetrics(mockData);
        this.updateRecentActivity(mockData.recent_activity);
    }

    updateMetrics(data) {
        // Update metric values
        document.getElementById('total-savings').textContent = `$${(data.total_savings || 0).toFixed(2)}`;
        document.getElementById('savings-rate').textContent = `${(data.average_savings_percentage || 0).toFixed(1)}%`;
        document.getElementById('avg-latency').textContent = `${data.average_latency_ms || 0}ms`;
        document.getElementById('cache-hit-rate').textContent = `${((data.cache_hit_rate || 0) * 100).toFixed(1)}%`;
        
        // Update metric changes (mock positive changes for demo)
        const changes = document.querySelectorAll('.metric-change');
        changes.forEach((change, index) => {
            const values = ['+12.3%', '****%', '-8ms', '****%'];
            change.textContent = values[index] || '+0%';
        });
    }

    updateRecentActivity(activities) {
        const activityList = document.getElementById('recent-activity');
        if (!activityList) return;

        if (!activities || activities.length === 0) {
            activityList.innerHTML = '<p class="no-activity">No recent activity</p>';
            return;
        }

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-info">
                    <div class="activity-title">${this.truncateText(activity.prompt, 50)}</div>
                    <div class="activity-meta">
                        Model: ${activity.model} • ${this.formatTime(activity.timestamp)}
                    </div>
                </div>
                <div class="activity-savings">${activity.savings.toFixed(1)}% saved</div>
            </div>
        `).join('');
    }

    initializeCharts() {
        this.initSavingsChart();
        this.initModelChart();
        this.initAnalyticsCharts();
    }

    initSavingsChart() {
        const ctx = document.getElementById('savingsChart');
        if (!ctx) return;

        this.charts.savings = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Savings ($)',
                    data: [120, 190, 300, 500, 800, 1247],
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e2e8f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    initModelChart() {
        const ctx = document.getElementById('modelChart');
        if (!ctx) return;

        this.charts.model = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['DeepSeek V3', 'Claude Sonnet', 'GPT-4', 'Others'],
                datasets: [{
                    data: [65, 20, 10, 5],
                    backgroundColor: [
                        '#10b981',
                        '#6366f1',
                        '#f59e0b',
                        '#64748b'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    initAnalyticsCharts() {
        // Initialize other charts for analytics section
        // This would include cost breakdown, performance metrics, etc.
    }

    async loadServiceStatus() {
        try {
            const response = await fetch(`${this.apiBase}/proxy/services/status`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const services = await response.json();
            this.updateServiceStatus(services);

        } catch (error) {
            console.error('Failed to load service status:', error);
            this.showServiceStatusError();
        }
    }

    updateServiceStatus(services) {
        const statusList = document.getElementById('services-status-list');
        if (!statusList) return;

        statusList.innerHTML = Object.entries(services).map(([name, service]) => `
            <div class="service-status-item">
                <div class="service-status-name">${this.formatServiceName(name)}</div>
                <div class="service-status-badge ${service.status}">
                    ${service.status.toUpperCase()}
                </div>
            </div>
        `).join('');
    }

    formatServiceName(name) {
        const names = {
            grafana: 'Grafana Dashboards',
            prometheus: 'Prometheus Metrics',
            jaeger: 'Jaeger Tracing',
            n8n: 'N8N Workflows'
        };
        return names[name] || name;
    }

    showServiceStatusError() {
        const statusList = document.getElementById('services-status-list');
        if (!statusList) return;

        statusList.innerHTML = `
            <div class="service-status-item">
                <div class="service-status-name">Unable to load service status</div>
                <div class="service-status-badge unavailable">ERROR</div>
            </div>
        `;
    }

    startRealTimeUpdates() {
        // Update metrics every 30 seconds
        setInterval(() => {
            this.loadDashboardData();
        }, 30000);

        // Update service status every 60 seconds if monitoring section is active
        setInterval(() => {
            const monitoringSection = document.getElementById('monitoring');
            if (monitoringSection && monitoringSection.classList.contains('active')) {
                this.loadServiceStatus();
            }
        }, 60000);
    }

    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        return date.toLocaleDateString();
    }

    showError(message) {
        // Simple error display - could be enhanced with a toast system
        alert(message);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CostOptimizerDashboard();
});

// Add some additional CSS for results
const additionalCSS = `
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.result-card {
    background: var(--background-color);
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
}

.result-card h4 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.result-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.result-value.savings {
    color: var(--secondary-color);
}

.optimized-prompt {
    background: var(--background-color);
    border-radius: 0.5rem;
    padding: 1rem;
}

.optimized-prompt h4 {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
}

.prompt-content {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: pre-wrap;
}

.no-activity {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
    font-style: italic;
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);
