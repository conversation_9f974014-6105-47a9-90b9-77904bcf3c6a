"""
Conversation Management Models
ChatGPT/Claude Desktop style conversation persistence
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean, Index
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field
import uuid

from src.core.database import Base

class Conversation(Base):
    """Conversation model for storing chat sessions"""
    __tablename__ = "conversations"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    user_id = Column(String(255), nullable=True)  # For multi-user support
    is_archived = Column(<PERSON><PERSON><PERSON>, default=False)
    metadata = Column(JSON, default=dict)  # Store additional conversation metadata
    
    # Relationship to messages
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_conversation_user_created', 'user_id', 'created_at'),
        Index('idx_conversation_updated', 'updated_at'),
        Index('idx_conversation_archived', 'is_archived'),
    )

class Message(Base):
    """Message model for storing individual messages in conversations"""
    __tablename__ = "messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Cost optimization tracking
    token_count = Column(Integer, default=0)
    cost_usd = Column(String(20), default="0.00")  # Store as string for precision
    model_used = Column(String(100), nullable=True)
    
    # Message metadata
    metadata = Column(JSON, default=dict)  # Store prompt optimization, compression info
    
    # Relationship to conversation
    conversation = relationship("Conversation", back_populates="messages")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_message_conversation_created', 'conversation_id', 'created_at'),
        Index('idx_message_role', 'role'),
        Index('idx_message_created', 'created_at'),
    )

# Pydantic models for API

class MessageCreate(BaseModel):
    """Schema for creating a new message"""
    role: str = Field(..., description="Message role: user, assistant, or system")
    content: str = Field(..., description="Message content")
    token_count: Optional[int] = Field(0, description="Token count for cost tracking")
    cost_usd: Optional[str] = Field("0.00", description="Cost in USD")
    model_used: Optional[str] = Field(None, description="Model used for generation")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

class MessageResponse(BaseModel):
    """Schema for message response"""
    id: str
    conversation_id: str
    role: str
    content: str
    created_at: datetime
    token_count: int
    cost_usd: str
    model_used: Optional[str]
    metadata: Dict[str, Any]
    
    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    """Schema for creating a new conversation"""
    title: str = Field(..., description="Conversation title")
    user_id: Optional[str] = Field(None, description="User ID for multi-user support")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

class ConversationUpdate(BaseModel):
    """Schema for updating a conversation"""
    title: Optional[str] = Field(None, description="Updated conversation title")
    is_archived: Optional[bool] = Field(None, description="Archive status")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Updated metadata")

class ConversationResponse(BaseModel):
    """Schema for conversation response"""
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    user_id: Optional[str]
    is_archived: bool
    metadata: Dict[str, Any]
    message_count: Optional[int] = Field(None, description="Number of messages in conversation")
    total_cost: Optional[str] = Field(None, description="Total cost of conversation")

    class Config:
        from_attributes = True

class ConversationWithMessages(ConversationResponse):
    """Schema for conversation with full message history"""
    messages: List[MessageResponse] = Field(default_factory=list, description="All messages in conversation")

class ConversationListResponse(BaseModel):
    """Schema for paginated conversation list"""
    conversations: List[ConversationResponse]
    total: int
    page: int
    page_size: int
    has_next: bool
    has_prev: bool

class MessageSearchRequest(BaseModel):
    """Schema for message search"""
    query: str = Field(..., description="Search query")
    conversation_id: Optional[str] = Field(None, description="Limit search to specific conversation")
    role: Optional[str] = Field(None, description="Filter by message role")
    start_date: Optional[datetime] = Field(None, description="Search from date")
    end_date: Optional[datetime] = Field(None, description="Search to date")
    limit: Optional[int] = Field(50, description="Maximum results to return")

class ConversationStats(BaseModel):
    """Schema for conversation statistics"""
    total_conversations: int
    total_messages: int
    total_cost_usd: str
    avg_messages_per_conversation: float
    most_used_model: Optional[str]
    cost_by_model: Dict[str, str]
    conversations_by_date: Dict[str, int]
