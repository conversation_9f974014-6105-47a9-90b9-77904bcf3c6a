apiVersion: 1

providers:
  - name: 'cost-optimizer-dashboards'
    orgId: 1
    folder: 'Cost Optimizer'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/cost-optimizer

  - name: 'system-dashboards'
    orgId: 1
    folder: 'System Monitoring'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/system

  - name: 'business-dashboards'
    orgId: 1
    folder: 'Business Intelligence'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/business
