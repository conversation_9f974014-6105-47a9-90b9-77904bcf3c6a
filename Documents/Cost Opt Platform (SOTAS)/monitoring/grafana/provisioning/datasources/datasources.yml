apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 15s
    secureJsonData: {}

  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'loki'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [
          {key: 'service.name', value: 'service'},
          {key: 'service.namespace', value: 'namespace'}
        ]
        mapTagNamesEnabled: true
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [
          {key: 'service.name', value: 'service'},
          {key: 'job'}
        ]
        queries: [
          {
            name: 'Sample query',
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
          }
        ]
      nodeGraph:
        enabled: true
      search:
        hide: false
      spanBar:
        type: 'Tag'
        tag: 'http.path'

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: 'jaeger'
          matcherRegex: 'traceID=(\w+)'
          name: 'TraceID'
          url: '$${__value.raw}'
          urlDisplayLabel: 'View Trace'

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: cost_optimizer
    user: cost_optimizer
    editable: true
    jsonData:
      sslmode: 'disable'
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      postgresVersion: 1400
      timescaledb: false
    secureJsonData:
      password: '${POSTGRES_PASSWORD}'

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis:6379
    editable: true
    jsonData:
      client: 'standalone'
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: '${REDIS_PASSWORD}'

  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData: {}
    secureJsonData: {}
