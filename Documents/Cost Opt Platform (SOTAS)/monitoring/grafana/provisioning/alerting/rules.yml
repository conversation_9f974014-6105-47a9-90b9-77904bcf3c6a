apiVersion: 1

groups:
  - name: cost-optimizer-alerts
    orgId: 1
    folder: Cost Optimizer Alerts
    interval: 1m
    rules:
      - uid: cost-optimizer-high-error-rate
        title: Cost Optimizer High Error Rate
        condition: C
        data:
          - refId: A
            queryType: ''
            relativeTimeRange:
              from: 300
              to: 0
            datasourceUid: prometheus
            model:
              expr: |
                (
                  sum(rate(http_requests_total{job="cost-optimizer",code!~"2.."}[5m])) /
                  sum(rate(http_requests_total{job="cost-optimizer"}[5m]))
                ) > 0.01
              intervalMs: 1000
              maxDataPoints: 43200
              refId: A
          - refId: B
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              conditions:
                - evaluator:
                    params: [0.01]
                    type: gt
                  operator:
                    type: and
                  query:
                    params: [A]
                  reducer:
                    params: []
                    type: last
                  type: query
              datasource:
                type: __expr__
                uid: __expr__
              expression: A
              intervalMs: 1000
              maxDataPoints: 43200
              reducer: last
              refId: B
              type: reduce
          - refId: C
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              conditions:
                - evaluator:
                    params: [0, 0]
                    type: gt
                  operator:
                    type: and
                  query:
                    params: [B]
                  reducer:
                    params: []
                    type: last
                  type: query
              datasource:
                type: __expr__
                uid: __expr__
              expression: B
              intervalMs: 1000
              maxDataPoints: 43200
              refId: C
              type: threshold
        noDataState: NoData
        execErrState: Alerting
        for: 2m
        annotations:
          description: 'Error rate is {{ $values.A.Value | humanizePercentage }} (SLA: <1%)'
          runbook_url: 'https://runbooks.company.com/cost-optimizer/high-error-rate'
          summary: 'Cost Optimizer error rate exceeds SLA'
        labels:
          severity: critical
          service: cost-optimizer
          sla: error_rate

      - uid: cost-optimizer-high-latency
        title: Cost Optimizer High Latency
        condition: C
        data:
          - refId: A
            queryType: ''
            relativeTimeRange:
              from: 300
              to: 0
            datasourceUid: prometheus
            model:
              expr: |
                histogram_quantile(0.99,
                  sum(rate(http_request_duration_seconds_bucket{job="cost-optimizer"}[5m]))
                  by (le)
                ) > 0.1
              intervalMs: 1000
              maxDataPoints: 43200
              refId: A
          - refId: B
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: A
              intervalMs: 1000
              maxDataPoints: 43200
              reducer: last
              refId: B
              type: reduce
          - refId: C
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: B
              intervalMs: 1000
              maxDataPoints: 43200
              refId: C
              type: threshold
        noDataState: NoData
        execErrState: Alerting
        for: 3m
        annotations:
          description: 'P99 latency is {{ $values.A.Value }}s (SLA: <100ms)'
          runbook_url: 'https://runbooks.company.com/cost-optimizer/high-latency'
          summary: 'Cost Optimizer P99 latency exceeds SLA'
        labels:
          severity: critical
          service: cost-optimizer
          sla: latency

      - uid: cost-optimizer-service-down
        title: Cost Optimizer Service Down
        condition: C
        data:
          - refId: A
            queryType: ''
            relativeTimeRange:
              from: 60
              to: 0
            datasourceUid: prometheus
            model:
              expr: up{job="cost-optimizer"} == 0
              intervalMs: 1000
              maxDataPoints: 43200
              refId: A
          - refId: B
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: A
              intervalMs: 1000
              maxDataPoints: 43200
              reducer: last
              refId: B
              type: reduce
          - refId: C
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: B
              intervalMs: 1000
              maxDataPoints: 43200
              refId: C
              type: threshold
        noDataState: Alerting
        execErrState: Alerting
        for: 1m
        annotations:
          description: 'Service {{ $labels.instance }} has been down for more than 1 minute'
          runbook_url: 'https://runbooks.company.com/cost-optimizer/service-down'
          summary: 'Cost Optimizer service is down'
        labels:
          severity: critical
          service: cost-optimizer
          sla: availability

      - uid: cost-savings-target-missed
        title: Cost Savings Target Missed
        condition: C
        data:
          - refId: A
            queryType: ''
            relativeTimeRange:
              from: 3600
              to: 0
            datasourceUid: prometheus
            model:
              expr: avg_over_time(cost_savings_percentage[1h]) < 200
              intervalMs: 1000
              maxDataPoints: 43200
              refId: A
          - refId: B
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: A
              intervalMs: 1000
              maxDataPoints: 43200
              reducer: last
              refId: B
              type: reduce
          - refId: C
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: B
              intervalMs: 1000
              maxDataPoints: 43200
              refId: C
              type: threshold
        noDataState: NoData
        execErrState: Alerting
        for: 30m
        annotations:
          description: 'Average cost savings {{ $values.A.Value }}% is below 200% target'
          runbook_url: 'https://runbooks.company.com/cost-optimizer/low-savings'
          summary: 'Cost savings below target'
        labels:
          severity: warning
          service: cost-optimizer
          type: business_kpi

      - uid: cache-efficiency-low
        title: Cache Efficiency Low
        condition: C
        data:
          - refId: A
            queryType: ''
            relativeTimeRange:
              from: 600
              to: 0
            datasourceUid: prometheus
            model:
              expr: cache_hit_ratio < 0.7
              intervalMs: 1000
              maxDataPoints: 43200
              refId: A
          - refId: B
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: A
              intervalMs: 1000
              maxDataPoints: 43200
              reducer: last
              refId: B
              type: reduce
          - refId: C
            queryType: ''
            relativeTimeRange:
              from: 0
              to: 0
            datasourceUid: __expr__
            model:
              expression: B
              intervalMs: 1000
              maxDataPoints: 43200
              refId: C
              type: threshold
        noDataState: NoData
        execErrState: Alerting
        for: 10m
        annotations:
          description: 'Cache hit ratio {{ $values.A.Value | humanizePercentage }} for {{ $labels.cache_layer }}'
          runbook_url: 'https://runbooks.company.com/cost-optimizer/cache-efficiency'
          summary: 'Cache hit ratio below target'
        labels:
          severity: warning
          service: cost-optimizer
          type: performance
