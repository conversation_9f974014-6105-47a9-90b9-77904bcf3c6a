{"dashboard": {"id": null, "title": "Cost Optimization Platform - System Performance", "tags": ["cost-optimization", "system", "performance"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "HTTP Request Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (method, endpoint)", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 2, "title": "HTTP Response Times", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P99", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 3, "title": "Database Connections", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "database_connections_active", "legendFormat": "{{database}} - {{pool}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 4, "title": "Database Query Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m]))", "legendFormat": "P95 - {{operation}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 5, "title": "Memory Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "memory_usage_bytes", "legendFormat": "{{component}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 6, "title": "CPU Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "cpu_usage_percent", "legendFormat": "{{component}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 7, "title": "Circuit Breaker Status", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "circuit_breaker_state", "legendFormat": "{{component}} - {{breaker_name}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"text": "Closed", "color": "green"}, "1": {"text": "Open", "color": "red"}, "2": {"text": "Half-Open", "color": "yellow"}}, "type": "value"}]}}}, {"id": 8, "title": "HTTP Status Codes", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (status_code)", "legendFormat": "{{status_code}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 9, "title": "Request/Response Size", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_size_bytes_bucket[5m]))", "legendFormat": "P95 Request Size", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_response_size_bytes_bucket[5m]))", "legendFormat": "P95 Response Size", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "bytes", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "endpoint", "type": "query", "query": "label_values(http_requests_total, endpoint)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}}}