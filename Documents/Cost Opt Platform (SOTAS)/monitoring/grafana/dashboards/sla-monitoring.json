{"dashboard": {"id": null, "title": "Cost Optimization Platform - SLA Monitoring", "tags": ["cost-optimizer", "sla", "monitoring", "compliance"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Overall SLA Compliance", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "targets": [{"expr": "sla_compliance_percentage{sla_type=\"overall\"}", "legendFormat": "Overall SLA", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 95, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 95}, {"color": "yellow", "value": 99}, {"color": "green", "value": 99.9}]}}}, "options": {"showThresholdLabels": true, "showThresholdMarkers": true}}, {"id": 2, "title": "Latency SLA Compliance", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 8, "y": 0}, "targets": [{"expr": "(sum(rate(http_request_duration_seconds_bucket{le=\"0.1\"}[5m])) / sum(rate(http_request_duration_seconds_count[5m]))) * 100", "legendFormat": "P99 < 100ms", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}}, {"id": 3, "title": "Error Rate SLA Compliance", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 16, "y": 0}, "targets": [{"expr": "(1 - (sum(rate(http_requests_total{code!~\"2..\"}[5m])) / sum(rate(http_requests_total[5m])))) * 100", "legendFormat": "Error Rate < 1%", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}}, {"id": 4, "title": "Availability SLA Compliance", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 8, "y": 4}, "targets": [{"expr": "avg(up{job=\"cost-optimizer\"}) * 100", "legendFormat": "Uptime > 99.9%", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 99}, {"color": "green", "value": 99.9}]}}}}, {"id": 5, "title": "Quality SLA Compliance", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 16, "y": 4}, "targets": [{"expr": "(sum(rate(quality_scores_bucket{le=\"0.8\"}[5m])) / sum(rate(quality_scores_count[5m]))) * 100", "legendFormat": "Quality > 80%", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}}}}, {"id": 6, "title": "SLA Violations Timeline", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"expr": "ALERTS{alertname=~\".*SLA.*\", alertstate=\"firing\"}", "legendFormat": "{{alertname}}", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "points", "pointSize": 8}}}, "options": {"legend": {"displayMode": "table", "placement": "bottom"}}}, {"id": 7, "title": "Response Time Distribution", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate(http_request_duration_seconds_bucket[5m])) by (le)", "legendFormat": "{{le}}", "refId": "A"}], "options": {"calculate": true, "yAxis": {"unit": "s"}}}, {"id": 8, "title": "SLA Metrics Summary", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sla_compliance_percentage", "legendFormat": "{{sla_type}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}}, {"id": 9, "title": "Monthly SLA Trend", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "avg_over_time(sla_compliance_percentage[1d])", "legendFormat": "Daily Average", "refId": "A"}, {"expr": "vector(99.9)", "legendFormat": "SLA Target", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 2}}}}], "templating": {"list": [{"name": "sla_type", "type": "query", "query": "label_values(sla_compliance_percentage, sla_type)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}, "annotations": {"list": [{"name": "SLA Violations", "datasource": "prometheus", "enable": true, "expr": "ALERTS{alertname=~\".*SLA.*\", alertstate=\"firing\"}", "iconColor": "red", "titleFormat": "SLA Violation", "textFormat": "{{alertname}}: {{summary}}"}, {"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "changes(cost_optimizer_info[1h]) > 0", "iconColor": "blue", "titleFormat": "Deployment", "textFormat": "New version deployed"}]}}}