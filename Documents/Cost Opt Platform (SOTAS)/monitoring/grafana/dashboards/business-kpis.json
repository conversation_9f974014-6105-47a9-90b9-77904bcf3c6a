{"dashboard": {"id": null, "title": "Cost Optimization Platform - Business KPIs", "tags": ["cost-optimizer", "business", "kpis", "revenue"], "style": "dark", "timezone": "browser", "refresh": "1m", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Revenue Impact - Cost Savings", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "sum(increase(total_cost_saved_usd[24h]))", "legendFormat": "24h Cost Savings", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "min": 0, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 500}, {"color": "green", "value": 1000}]}, "custom": {"displayMode": "gradient"}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 2, "title": "Cost Optimization ROI", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "sum(rate(cost_savings_percentage_sum[1h])) / sum(rate(cost_savings_percentage_count[1h]))", "legendFormat": "Average ROI %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 200}, {"color": "green", "value": 500}]}}}}, {"id": 3, "title": "SLA Compliance", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "sla_compliance_percentage", "legendFormat": "{{sla_type}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 95, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 95}, {"color": "yellow", "value": 99}, {"color": "green", "value": 99.9}]}}}, "options": {"showThresholdLabels": true, "showThresholdMarkers": true}}, {"id": 4, "title": "Customer Satisfaction", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "avg(customer_satisfaction_score)", "legendFormat": "Satisfaction Score", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "max": 10, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 6}, {"color": "green", "value": 8}]}}}}, {"id": 5, "title": "Cost Savings Trend", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "sum(rate(total_cost_saved_usd[1h])) * 24", "legendFormat": "Daily Projection", "refId": "A"}, {"expr": "sum(increase(total_cost_saved_usd[1h]))", "legendFormat": "Hourly Actual", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 2, "fillOpacity": 20, "gradientMode": "opacity"}}}}, {"id": 6, "title": "API Usage by Customer Tier", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(api_usage_total[5m])) by (api_key_tier)", "legendFormat": "{{api_key_tier}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 7, "title": "Quality Score Distribution", "type": "histogram", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "histogram_quantile(0.50, rate(quality_scores_bucket[5m]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(quality_scores_bucket[5m]))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(quality_scores_bucket[5m]))", "legendFormat": "P99", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 8, "title": "Cost Savings by Model", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate(cost_savings_percentage_sum[1h])) by (model)", "legendFormat": "{{model}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"displayMode": "table", "pieType": "pie", "tooltipMode": "single"}}}}, {"id": 9, "title": "Performance vs SLA Targets", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P99 Latency", "refId": "A"}, {"expr": "vector(0.1)", "legendFormat": "SLA Target (100ms)", "refId": "B"}, {"expr": "rate(http_requests_total{code!~\"2..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "Error Rate %", "refId": "C"}, {"expr": "vector(1)", "legendFormat": "Error Rate SLA (1%)", "refId": "D"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 Latency"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.axisPlacement", "value": "left"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate %"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.axisPlacement", "value": "right"}]}]}}, {"id": 10, "title": "Business Metrics Summary", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "sum(increase(total_cost_saved_usd[24h]))", "legendFormat": "Total Cost Saved (24h)", "refId": "A", "format": "table"}, {"expr": "sum(increase(optimization_requests_total[24h]))", "legendFormat": "Total Requests (24h)", "refId": "B", "format": "table"}, {"expr": "avg(sla_compliance_percentage)", "legendFormat": "SLA Compliance", "refId": "C", "format": "table"}, {"expr": "avg(customer_satisfaction_score)", "legendFormat": "Customer Satisfaction", "refId": "D", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background", "inspect": false}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Cost Saved (24h)"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SLA Compliance"}, "properties": [{"id": "unit", "value": "percent"}]}]}}], "templating": {"list": [{"name": "customer_tier", "type": "query", "query": "label_values(api_usage_total, api_key_tier)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "model", "type": "query", "query": "label_values(optimization_requests_total, model)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "time_range", "type": "custom", "options": [{"text": "1 Hour", "value": "1h"}, {"text": "6 Hours", "value": "6h"}, {"text": "24 Hours", "value": "24h"}, {"text": "7 Days", "value": "7d"}, {"text": "30 Days", "value": "30d"}], "current": {"text": "24 Hours", "value": "24h"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "changes(cost_optimizer_info[1h]) > 0", "iconColor": "blue", "titleFormat": "Deployment", "textFormat": "New version deployed"}, {"name": "SLA Violations", "datasource": "prometheus", "enable": true, "expr": "ALERTS{alertname=~\".*SLA.*\", alertstate=\"firing\"}", "iconColor": "red", "titleFormat": "SLA Violation", "textFormat": "{{alertname}}: {{summary}}"}]}}}