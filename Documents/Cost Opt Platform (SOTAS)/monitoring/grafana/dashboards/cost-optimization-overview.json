{"dashboard": {"id": null, "title": "Cost Optimization Platform - Overview", "tags": ["cost-optimization", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Cost Savings Overview", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(cost_savings_percentage_sum[5m])) / sum(rate(cost_savings_percentage_count[5m]))", "legendFormat": "Average Cost Savings %", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 1000, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 200}, {"color": "green", "value": 500}]}}}}, {"id": 2, "title": "Request Volume", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate(optimization_requests_total[5m]))", "legendFormat": "Requests/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}}, {"id": 3, "title": "<PERSON><PERSON>", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"expr": "cache_hit_ratio", "legendFormat": "{{cache_layer}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}}}}, {"id": 4, "title": "Response Time Distribution", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "histogram_quantile(0.50, rate(optimization_latency_seconds_bucket[5m]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(optimization_latency_seconds_bucket[5m]))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(optimization_latency_seconds_bucket[5m]))", "legendFormat": "P99", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 5, "title": "Model Usage Distribution", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum by (model) (rate(optimization_requests_total[5m]))", "legendFormat": "{{model}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "custom": {"displayMode": "table", "pieType": "pie", "tooltipMode": "single", "tooltipSort": "none", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}}}}}, {"id": 6, "title": "Quality Scores", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "histogram_quantile(0.50, rate(quality_scores_bucket[5m]))", "legendFormat": "Median Quality", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(quality_scores_bucket[5m]))", "legendFormat": "P95 Quality", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.8}, {"color": "green", "value": 0.9}]}}}}, {"id": 7, "title": "System Health", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "up", "legendFormat": "{{instance}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background", "inspect": false}, "mappings": [{"options": {"0": {"text": "Down", "color": "red"}, "1": {"text": "Up", "color": "green"}}, "type": "value"}]}}}, {"id": 8, "title": "Error Rate", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "sum(rate(optimization_requests_total{status!=\"success\"}[5m])) / sum(rate(optimization_requests_total[5m]))", "legendFormat": "Error Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}}}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "model", "type": "query", "query": "label_values(optimization_requests_total, model)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "changes(cost_optimizer_info[1h]) > 0", "iconColor": "blue", "titleFormat": "Deployment", "textFormat": "New version deployed"}]}}}