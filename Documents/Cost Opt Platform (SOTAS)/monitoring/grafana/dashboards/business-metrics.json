{"dashboard": {"id": null, "title": "Cost Optimization Platform - Business Metrics", "tags": ["cost-optimization", "business", "kpi"], "style": "dark", "timezone": "browser", "refresh": "5m", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Total Cost Saved (24h)", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "sum(increase(total_cost_saved_usd[24h]))", "legendFormat": "Total Saved", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "min": 0, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 100}, {"color": "green", "value": 500}]}}}}, {"id": 2, "title": "Average Cost Savings %", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "sum(rate(cost_savings_percentage_sum[1h])) / sum(rate(cost_savings_percentage_count[1h]))", "legendFormat": "Avg Savings", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 200}, {"color": "green", "value": 500}]}}}}, {"id": 3, "title": "SLA Compliance", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "avg(sla_compliance_percentage)", "legendFormat": "SLA Compliance", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}}, {"id": 4, "title": "System Uptime", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "avg(uptime_percentage)", "legendFormat": "Uptime", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 99}, {"color": "green", "value": 99.9}]}}}}, {"id": 5, "title": "Cost Savings Trend", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "sum(rate(total_cost_saved_usd[1h]))", "legendFormat": "Cost Saved/hour", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 20, "gradientMode": "opacity"}}}}, {"id": 6, "title": "API Usage by Customer Tier", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(api_usage_total[5m])) by (api_key_tier)", "legendFormat": "{{api_key_tier}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 7, "title": "Customer Satisfaction Scores", "type": "gauge", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "avg(customer_satisfaction_score) by (customer_tier)", "legendFormat": "{{customer_tier}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "max": 10, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 6}, {"color": "green", "value": 8}]}}}}, {"id": 8, "title": "Performance vs SLA Targets", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "latency_p99_seconds", "legendFormat": "P99 Latency - {{endpoint}}", "refId": "A"}, {"expr": "vector(0.1)", "legendFormat": "SLA Target (100ms)", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 9, "title": "Cost Savings by Model", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "sum(rate(cost_savings_percentage_sum[1h])) by (model)", "legendFormat": "{{model}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"displayMode": "table", "pieType": "pie", "tooltipMode": "single"}}}}, {"id": 10, "title": "Error Rate vs SLA", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "error_rate_percentage", "legendFormat": "Error Rate - {{component}}", "refId": "A"}, {"expr": "vector(1)", "legendFormat": "SLA Target (1%)", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 5, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}}}}, {"id": 11, "title": "Monthly Projected Savings", "type": "stat", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "sum(rate(total_cost_saved_usd[1h])) * 24 * 30", "legendFormat": "Monthly Projection", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "min": 0, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 10000}, {"color": "green", "value": 50000}]}}}}], "templating": {"list": [{"name": "customer_tier", "type": "query", "query": "label_values(api_usage_total, api_key_tier)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "time_window", "type": "custom", "options": [{"text": "1 Hour", "value": "1h"}, {"text": "6 Hours", "value": "6h"}, {"text": "24 Hours", "value": "24h"}, {"text": "7 Days", "value": "7d"}], "current": {"text": "24 Hours", "value": "24h"}}]}, "annotations": {"list": [{"name": "SLA Violations", "datasource": "prometheus", "enable": true, "expr": "sla_compliance_percentage < 99", "iconColor": "red", "titleFormat": "SLA Violation", "textFormat": "SLA compliance dropped below 99%"}]}}}