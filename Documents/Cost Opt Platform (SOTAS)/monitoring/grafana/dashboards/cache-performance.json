{"dashboard": {"id": null, "title": "Cost Optimization Platform - Cache Performance", "tags": ["cost-optimizer", "cache", "performance", "optimization"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Overall Cache Hit Rate", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "targets": [{"expr": "sum(cache_hits_total) / (sum(cache_hits_total) + sum(cache_misses_total))", "legendFormat": "Hit Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}}}, "options": {"showThresholdLabels": true, "showThresholdMarkers": true}}, {"id": 2, "title": "<PERSON><PERSON> Hit Rate by Layer", "type": "timeseries", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 0}, "targets": [{"expr": "rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))", "legendFormat": "{{cache_layer}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 2, "fillOpacity": 10}}}, "options": {"legend": {"displayMode": "table", "placement": "right", "values": ["current", "mean"]}}}, {"id": 3, "title": "<PERSON><PERSON>", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))", "legendFormat": "{{cache_layer}}", "refId": "A", "format": "table"}, {"expr": "histogram_quantile(0.95, rate(cache_latency_seconds_bucket[5m]))", "legendFormat": "{{cache_layer}}", "refId": "B", "format": "table"}, {"expr": "rate(cache_requests_total[5m])", "legendFormat": "{{cache_layer}}", "refId": "C", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Hit Rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "thresholds", "value": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 Latency"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "unit", "value": "reqps"}]}]}}, {"id": 4, "title": "<PERSON><PERSON> Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "cache_memory_usage_mb", "legendFormat": "{{cache_layer}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "decbytes", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 20, "gradientMode": "opacity"}}}}, {"id": 5, "title": "Cache Operations Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "rate(cache_hits_total[5m])", "legendFormat": "Hits - {{cache_layer}}", "refId": "A"}, {"expr": "rate(cache_misses_total[5m])", "legendFormat": "Misses - {{cache_layer}}", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "ops", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 6, "title": "Cache Efficiency Score", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 16}, "targets": [{"expr": "cache_efficiency_score", "legendFormat": "Efficiency Score", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "max": 10, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 6}, {"color": "green", "value": 8}]}}}}, {"id": 7, "title": "<PERSON><PERSON>", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 16}, "targets": [{"expr": "rate(cache_evictions_total[5m])", "legendFormat": "Evictions/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}, {"id": 8, "title": "Semantic Similarity Distribution", "type": "histogram", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "targets": [{"expr": "sum(rate(semantic_similarity_bucket[5m])) by (le)", "legendFormat": "{{le}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short"}}, "options": {"bucketSize": 0.1, "bucketOffset": 0}}, {"id": 9, "title": "<PERSON><PERSON>er Latency Comparison", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "targets": [{"expr": "histogram_quantile(0.50, rate(cache_latency_seconds_bucket[5m]))", "legendFormat": "P50 - {{cache_layer}}", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(cache_latency_seconds_bucket[5m]))", "legendFormat": "P95 - {{cache_layer}}", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(cache_latency_seconds_bucket[5m]))", "legendFormat": "P99 - {{cache_layer}}", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2}}}}, {"id": 10, "title": "Cache Optimization Recommendations", "type": "text", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "options": {"content": "## Cache Optimization Recommendations\n\n### Current Performance Analysis\n- **Memory Cache (L1)**: Fastest layer, optimize for frequently accessed patterns\n- **Redis (L2)**: Network latency impact, consider connection pooling\n- **Vector DBs (L3-L6)**: Semantic similarity tuning needed for better hit rates\n- **Elasticsearch (L7)**: Full-text search optimization for complex queries\n\n### Optimization Actions\n1. **Increase Memory Cache Size**: Better L1 performance for hot data\n2. **Tune Similarity Thresholds**: Optimize semantic matching accuracy\n3. **Implement Cache Warming**: Pre-populate frequently accessed data\n4. **Monitor Eviction Patterns**: Adjust TTL and size limits\n\n### Performance Targets\n- **Overall Hit Rate**: >85% (Current: varies by layer)\n- **L1 Latency**: <1ms P99\n- **L2 Latency**: <5ms P99\n- **Vector DB Latency**: <25ms P99\n\n*Dashboard auto-updates every 30 seconds*", "mode": "markdown"}}], "templating": {"list": [{"name": "cache_layer", "type": "query", "query": "label_values(cache_hits_total, cache_layer)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "cache_type", "type": "query", "query": "label_values(cache_hits_total, cache_type)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}, "annotations": {"list": [{"name": "Cache Optimizations", "datasource": "prometheus", "enable": true, "expr": "changes(cache_configuration_version[1h]) > 0", "iconColor": "green", "titleFormat": "<PERSON><PERSON> Config <PERSON>", "textFormat": "Cache configuration updated"}]}}}