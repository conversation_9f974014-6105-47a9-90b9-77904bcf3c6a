# Grafana Configuration for Cost Optimization Platform
# Production-grade configuration with security and performance optimizations

[default]
# Instance name and environment
instance_name = cost-optimizer-grafana
app_mode = production

[paths]
# Data and logs paths
data = /var/lib/grafana
temp_data_lifetime = 24h
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning

[server]
# Server configuration
protocol = http
http_addr = 0.0.0.0
http_port = 3000
domain = grafana.cost-optimizer.com
enforce_domain = false
root_url = https://grafana.cost-optimizer.com/
serve_from_sub_path = false
router_logging = false
static_root_path = public
enable_gzip = true
cert_file =
cert_key =
socket = /tmp/grafana.sock
cdn_url =
read_timeout = 0

[database]
# Database configuration (PostgreSQL)
type = postgres
host = postgres:5432
name = grafana
user = grafana
password = ${GRAFANA_DB_PASSWORD}
ssl_mode = disable
ca_cert_path =
client_key_path =
client_cert_path =
server_cert_name =
path =
max_idle_conn = 2
max_open_conn = 0
conn_max_lifetime = 14400
log_queries =
cache_mode = private

[session]
# Session configuration
provider = file
provider_config = sessions
cookie_name = grafana_sess
cookie_secure = true
session_life_time = 86400
gc_interval_time = 86400
conn_max_lifetime = 14400

[dataproxy]
# Data proxy configuration
logging = false
timeout = 30
dialTimeout = 10
keep_alive_seconds = 30
tls_handshake_timeout_seconds = 10
expect_continue_timeout_seconds = 1
max_conns_per_host = 0
max_idle_connections = 100
idle_conn_timeout_seconds = 90
send_user_header = false

[analytics]
# Analytics configuration
reporting_enabled = false
check_for_updates = false
google_analytics_ua_id =
google_tag_manager_id =
rudderstack_write_key =
rudderstack_data_plane_url =
rudderstack_sdk_url =
rudderstack_config_url =
application_insights_connection_string =
application_insights_endpoint_url =

[security]
# Security configuration
disable_initial_admin_creation = false
admin_user = admin
admin_password = ${GRAFANA_ADMIN_PASSWORD}
admin_email = <EMAIL>
secret_key = ${GRAFANA_SECRET_KEY}
encryption_provider = secretKey.v1
available_encryption_providers = secretKey.v1
login_remember_days = 7
login_maximum_inactive_lifetime_duration =
login_maximum_lifetime_duration =
token_rotation_interval_minutes = 10
disable_gravatar = true
disable_brute_force_login_protection = false
cookie_secure = true
cookie_samesite = lax
allow_embedding = false
strict_transport_security = true
strict_transport_security_max_age_seconds = 86400
strict_transport_security_preload = false
strict_transport_security_subdomains = false
x_content_type_options = true
x_xss_protection = true
content_security_policy = true
content_security_policy_template = """script-src 'self' 'unsafe-eval' 'unsafe-inline' 'strict-dynamic' $NONCE;object-src 'none';font-src 'self';style-src 'self' 'unsafe-inline' blob:;img-src * data:;base-uri 'self';connect-src 'self' grafana.com ws://localhost:3000/ wss://localhost:3000/;manifest-src 'self';media-src 'none';form-action 'self';"""

[snapshots]
# Snapshots configuration
external_enabled = false
external_snapshot_url = https://snapshots-origin.raintank.io
external_snapshot_name = Publish to snapshot.raintank.io
public_mode = false
snapshot_remove_expired = true

[dashboards]
# Dashboard configuration
versions_to_keep = 20
min_refresh_interval = 5s
default_home_dashboard_path =

[users]
# User management
allow_sign_up = false
allow_org_create = false
auto_assign_org = true
auto_assign_org_id = 1
auto_assign_org_role = Viewer
verify_email_enabled = false
login_hint = email or username
password_hint = password
default_theme = dark
external_manage_link_url =
external_manage_link_name =
external_manage_info =
viewers_can_edit = false
editors_can_admin = false
user_invite_max_lifetime_duration = 24h

[auth]
# Authentication configuration
login_cookie_name = grafana_session
login_maximum_inactive_lifetime_duration =
login_maximum_lifetime_duration =
token_rotation_interval_minutes = 10
disable_login_form = false
disable_signout_menu = false
signout_redirect_url =
oauth_auto_login = false
oauth_state_cookie_max_age = 600
api_key_max_seconds_to_live = -1
sigv4_auth_enabled = false

[auth.anonymous]
# Anonymous authentication
enabled = false
org_name = Main Org.
org_role = Viewer
hide_version = false

[auth.github]
# GitHub OAuth (disabled by default)
enabled = false
allow_sign_up = false
client_id =
client_secret =
scopes = user:email,read:org
auth_url = https://github.com/login/oauth/authorize
token_url = https://github.com/login/oauth/access_token
api_url = https://api.github.com/user
allowed_domains =
team_ids =
allowed_organizations =

[auth.basic]
# Basic authentication
enabled = true

[auth.proxy]
# Proxy authentication
enabled = false
header_name = X-WEBAUTH-USER
header_property = username
auto_sign_up = true
sync_ttl = 60
whitelist =
headers =
headers_encoded = false
enable_login_token = false

[auth.jwt]
# JWT authentication
enabled = false
header_name =
email_claim =
username_claim =
jwk_set_url =
jwk_set_file =
cache_ttl = 60m
expected_claims = {}
key_file =
role_attribute_path =
role_attribute_strict = false
auto_sign_up = false
url_login = false

[smtp]
# SMTP configuration
enabled = false
host = localhost:587
user =
password =
cert_file =
key_file =
skip_verify = false
from_address = <EMAIL>
from_name = Grafana
ehlo_identity =
startTLS_policy =

[emails]
# Email configuration
welcome_email_on_sign_up = false
templates_pattern = emails/*.html, emails/*.txt
content_types = text/html

[log]
# Logging configuration
mode = console file
level = info
filters =

[log.console]
level = info
format = console

[log.file]
level = info
format = text
log_rotate = true
max_lines = 1000000
max_size_shift = 28
daily_rotate = true
max_days = 7

[log.syslog]
level =
format = text
network =
address =
facility =
tag =

[quota]
# Quota configuration
enabled = false
org_user = 10
org_dashboard = 100
org_data_source = 10
org_api_key = 10
org_alert_rule = 100
user_org = 10
global_user = -1
global_org = -1
global_dashboard = -1
global_api_key = -1
global_session = -1
global_alert_rule = -1

[alerting]
# Alerting configuration
enabled = true
execute_alerts = true
error_or_timeout = alerting
nodata_or_nullvalues = no_data
concurrent_render_limit = 5
evaluation_timeout_seconds = 30
notification_timeout_seconds = 30
max_attempts = 3
min_interval_seconds = 1
max_annotation_age =

[explore]
# Explore configuration
enabled = true

[help]
# Help configuration
enabled = true

[profile]
# Profiling configuration
enabled = false

[query_history]
# Query history configuration
enabled = true

[unified_alerting]
# Unified alerting configuration
enabled = true
disabled_orgs =
admin_config_poll_interval = 60s
alertmanager_config_poll_interval = 60s
ha_listen_address = "0.0.0.0:9094"
ha_advertise_address = ""
ha_peers = ""
ha_peer_timeout = "15s"
ha_gossip_interval = "200ms"
ha_push_pull_interval = "60s"
max_attempts = 3
min_interval_seconds = 10
execute_alerts = true
evaluation_timeout_seconds = 30
max_annotation_age = 0
base_interval = 10s

[feature_toggles]
# Feature toggles
enable = ngalert
disable =

[date_formats]
# Date format configuration
full_date = MMM Do, YYYY
interval_second = HH:mm:ss
interval_minute = HH:mm
interval_hour = MM/DD HH:mm
interval_day = MM/DD
interval_month = YYYY-MM
interval_year = YYYY
use_browser_locale = false
default_timezone = browser

[panels]
# Panel configuration
enable_alpha = false
disable_sanitize_html = false

[plugins]
# Plugin configuration
enable_alpha = false
app_tls_skip_verify_insecure = false
allow_loading_unsigned_plugins =
marketplace_url = https://grafana.com/grafana/plugins/

[enterprise]
# Enterprise configuration (if applicable)
license_path =

[rendering]
# Rendering configuration
server_url =
callback_url =
concurrent_render_request_limit = 30
