groups:
  - name: cost_optimization_alerts
    rules:
      # High Priority Alerts
      - alert: HighErrorRate
        expr: sum(rate(optimization_requests_total{status!="success"}[5m])) / sum(rate(optimization_requests_total[5m])) > 0.05
        for: 2m
        labels:
          severity: critical
          service: cost-optimizer
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} which is above the 5% threshold"
          runbook_url: "https://runbooks.example.com/high-error-rate"

      - alert: HighLatency
        expr: histogram_quantile(0.99, rate(optimization_latency_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: critical
          service: cost-optimizer
        annotations:
          summary: "High latency detected"
          description: "P99 latency is {{ $value }}s which is above the 10s threshold"
          runbook_url: "https://runbooks.example.com/high-latency"

      - alert: ServiceDown
        expr: up{job="cost-optimizer"} == 0
        for: 1m
        labels:
          severity: critical
          service: cost-optimizer
        annotations:
          summary: "Cost Optimizer service is down"
          description: "Cost Optimizer service on {{ $labels.instance }} has been down for more than 1 minute"
          runbook_url: "https://runbooks.example.com/service-down"

      # Medium Priority Alerts
      - alert: LowCacheHitRate
        expr: cache_hit_ratio < 0.7
        for: 10m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate for {{ $labels.cache_layer }} is {{ $value | humanizePercentage }} which is below 70%"
          runbook_url: "https://runbooks.example.com/low-cache-hit-rate"

      - alert: HighMemoryUsage
        expr: memory_usage_bytes / (1024*1024*1024) > 2
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "High memory usage"
          description: "Memory usage for {{ $labels.component }} is {{ $value }}GB which is above 2GB threshold"
          runbook_url: "https://runbooks.example.com/high-memory-usage"

      - alert: DatabaseConnectionPoolExhaustion
        expr: database_connections_active / 20 > 0.9
        for: 3m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Database connection pool {{ $labels.pool }} is {{ $value | humanizePercentage }} full"
          runbook_url: "https://runbooks.example.com/db-pool-exhaustion"

      # Business Metrics Alerts
      - alert: LowCostSavings
        expr: sum(rate(cost_savings_percentage_sum[1h])) / sum(rate(cost_savings_percentage_count[1h])) < 200
        for: 30m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Cost savings below target"
          description: "Average cost savings is {{ $value }}% which is below the 200% target"
          runbook_url: "https://runbooks.example.com/low-cost-savings"

      - alert: SLAViolation
        expr: sla_compliance_percentage < 99
        for: 5m
        labels:
          severity: critical
          service: cost-optimizer
        annotations:
          summary: "SLA compliance violation"
          description: "SLA compliance for {{ $labels.sla_type }} is {{ $value }}% which is below 99%"
          runbook_url: "https://runbooks.example.com/sla-violation"

      - alert: LowQualityScores
        expr: histogram_quantile(0.95, rate(quality_scores_bucket[5m])) < 0.8
        for: 15m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Quality scores below threshold"
          description: "P95 quality score is {{ $value }} which is below 0.8 threshold"
          runbook_url: "https://runbooks.example.com/low-quality-scores"

      # Infrastructure Alerts
      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state == 1
        for: 1m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Circuit breaker is open"
          description: "Circuit breaker {{ $labels.breaker_name }} in {{ $labels.component }} is open"
          runbook_url: "https://runbooks.example.com/circuit-breaker-open"

      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "High CPU usage"
          description: "CPU usage for {{ $labels.component }} is {{ $value }}% which is above 80%"
          runbook_url: "https://runbooks.example.com/high-cpu-usage"

      - alert: DatabaseQuerySlow
        expr: histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
        annotations:
          summary: "Slow database queries"
          description: "P95 database query time for {{ $labels.operation }} is {{ $value }}s"
          runbook_url: "https://runbooks.example.com/slow-db-queries"

  - name: model_router_alerts
    rules:
      - alert: ModelRouterDown
        expr: model_health_status == 0
        for: 2m
        labels:
          severity: critical
          service: model-router
        annotations:
          summary: "Model is unhealthy"
          description: "Model {{ $labels.model_id }} from {{ $labels.provider }} is unhealthy"
          runbook_url: "https://runbooks.example.com/model-unhealthy"

      - alert: HighModelLatency
        expr: histogram_quantile(0.95, rate(model_routing_latency_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
          service: model-router
        annotations:
          summary: "High model routing latency"
          description: "P95 model routing latency is {{ $value }}s which is above 5s threshold"
          runbook_url: "https://runbooks.example.com/high-model-latency"

  - name: cache_alerts
    rules:
      - alert: CacheLayerDown
        expr: up{job="chromadb"} == 0 or up{job="redis"} == 0 or up{job="qdrant"} == 0
        for: 2m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Cache layer is down"
          description: "Cache service {{ $labels.job }} on {{ $labels.instance }} is down"
          runbook_url: "https://runbooks.example.com/cache-layer-down"

      - alert: CacheMemoryHigh
        expr: cache_memory_usage_mb > 1000
        for: 5m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "High cache memory usage"
          description: "Cache memory usage is {{ $value }}MB which is above 1GB threshold"
          runbook_url: "https://runbooks.example.com/high-cache-memory"

  - name: compression_alerts
    rules:
      - alert: LowCompressionRatio
        expr: histogram_quantile(0.50, rate(compression_ratios_bucket[5m])) < 0.7
        for: 15m
        labels:
          severity: warning
          service: compression
        annotations:
          summary: "Low compression ratio"
          description: "Median compression ratio is {{ $value }} which is below 70% target"
          runbook_url: "https://runbooks.example.com/low-compression-ratio"

      - alert: CompressionQualityLow
        expr: compression_quality_score < 0.8
        for: 10m
        labels:
          severity: warning
          service: compression
        annotations:
          summary: "Low compression quality"
          description: "Compression quality score is {{ $value }} which is below 0.8 threshold"
          runbook_url: "https://runbooks.example.com/low-compression-quality"
