groups:
  - name: cost_optimization_critical_alerts
    interval: 30s
    rules:
      # Critical SLA Violations
      - alert: CostOptimizerHighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{job="cost-optimizer",code!~"2.."}[5m])) /
            sum(rate(http_requests_total{job="cost-optimizer"}[5m]))
          ) > 0.01
        for: 2m
        labels:
          severity: critical
          service: cost-optimizer
          sla: error_rate
        annotations:
          summary: "Cost Optimizer error rate exceeds SLA"
          description: "Error rate is {{ $value | humanizePercentage }} (SLA: <1%)"
          impact: "Customer experience degraded, potential revenue loss"
          runbook_url: "https://runbooks.company.com/cost-optimizer/high-error-rate"
          dashboard_url: "https://grafana.company.com/d/cost-optimizer-overview"

      - alert: CostOptimizerHighLatency
        expr: |
          histogram_quantile(0.99,
            sum(rate(http_request_duration_seconds_bucket{job="cost-optimizer"}[5m]))
            by (le)
          ) > 0.1
        for: 3m
        labels:
          severity: critical
          service: cost-optimizer
          sla: latency
        annotations:
          summary: "Cost Optimizer P99 latency exceeds SLA"
          description: "P99 latency is {{ $value }}s (SLA: <100ms)"
          impact: "User experience severely impacted"
          runbook_url: "https://runbooks.company.com/cost-optimizer/high-latency"

      - alert: CostOptimizerServiceDown
        expr: up{job="cost-optimizer"} == 0
        for: 1m
        labels:
          severity: critical
          service: cost-optimizer
          sla: availability
        annotations:
          summary: "Cost Optimizer service is down"
          description: "Service {{ $labels.instance }} has been down for more than 1 minute"
          impact: "Complete service outage, immediate revenue impact"
          runbook_url: "https://runbooks.company.com/cost-optimizer/service-down"

  - name: cost_optimization_business_alerts
    interval: 60s
    rules:
      # Business KPI Alerts
      - alert: CostSavingsTargetMissed
        expr: |
          avg_over_time(cost_savings_percentage[1h]) < 200
        for: 30m
        labels:
          severity: warning
          service: cost-optimizer
          type: business_kpi
        annotations:
          summary: "Cost savings below target"
          description: "Average cost savings {{ $value }}% is below 200% target"
          impact: "Business value proposition compromised"
          runbook_url: "https://runbooks.company.com/cost-optimizer/low-savings"

      - alert: QualityScoresDegraded
        expr: |
          histogram_quantile(0.95, rate(quality_scores_bucket[1h])) < 0.8
        for: 15m
        labels:
          severity: warning
          service: cost-optimizer
          type: quality
        annotations:
          summary: "Quality scores below acceptable threshold"
          description: "P95 quality score {{ $value }} is below 0.8 threshold"
          impact: "Customer satisfaction at risk"
          runbook_url: "https://runbooks.company.com/cost-optimizer/quality-degradation"

      - alert: CacheEfficiencyLow
        expr: |
          cache_hit_ratio < 0.7
        for: 10m
        labels:
          severity: warning
          service: cost-optimizer
          type: performance
        annotations:
          summary: "Cache hit ratio below target"
          description: "Cache hit ratio {{ $value | humanizePercentage }} for {{ $labels.cache_layer }}"
          impact: "Increased latency and costs"
          runbook_url: "https://runbooks.company.com/cost-optimizer/cache-efficiency"

  - name: cost_optimization_system_alerts
    interval: 30s
    rules:
      # System Health Alerts
      - alert: HighMemoryUsage
        expr: |
          (
            memory_usage_bytes{component="api"} / (1024*1024*1024)
          ) > 1.5
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
          type: resource
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage {{ $value }}GB exceeds 1.5GB threshold"
          runbook_url: "https://runbooks.company.com/cost-optimizer/high-memory"

      - alert: HighCPUUsage
        expr: cpu_usage_percent{component="api"} > 80
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
          type: resource
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage {{ $value }}% exceeds 80% threshold"
          runbook_url: "https://runbooks.company.com/cost-optimizer/high-cpu"

      - alert: DatabaseConnectionPoolExhaustion
        expr: |
          database_connections_active{database="postgres"} / 20 > 0.9
        for: 3m
        labels:
          severity: critical
          service: cost-optimizer
          type: database
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Connection pool {{ $value | humanizePercentage }} full"
          impact: "New requests may fail"
          runbook_url: "https://runbooks.company.com/cost-optimizer/db-pool-exhaustion"

      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state == 1
        for: 1m
        labels:
          severity: warning
          service: cost-optimizer
          type: circuit_breaker
        annotations:
          summary: "Circuit breaker is open"
          description: "Circuit breaker {{ $labels.breaker_name }} in {{ $labels.component }} is open"
          impact: "Dependent service calls are being blocked"
          runbook_url: "https://runbooks.company.com/cost-optimizer/circuit-breaker"

  - name: cost_optimization_adaptive_learning_alerts
    interval: 120s
    rules:
      # Adaptive Learning Alerts
      - alert: AdaptiveLearningStagnant
        expr: |
          increase(adaptive_learning_progress[24h]) < 0.01
        for: 2h
        labels:
          severity: warning
          service: cost-optimizer
          type: ml_performance
        annotations:
          summary: "Adaptive learning progress stagnant"
          description: "Learning progress has not improved in 24 hours"
          impact: "Cost optimization improvements may plateau"
          runbook_url: "https://runbooks.company.com/cost-optimizer/learning-stagnant"

      - alert: ModelSelectionAccuracyLow
        expr: model_selection_accuracy < 0.85
        for: 30m
        labels:
          severity: warning
          service: cost-optimizer
          type: ml_performance
        annotations:
          summary: "Model selection accuracy below threshold"
          description: "Model selection accuracy {{ $value | humanizePercentage }} is below 85%"
          impact: "Suboptimal model routing affecting cost savings"
          runbook_url: "https://runbooks.company.com/cost-optimizer/model-selection-accuracy"

  - name: cost_optimization_security_alerts
    interval: 60s
    rules:
      # Security Alerts
      - alert: UnusualAPIUsagePattern
        expr: |
          rate(api_usage_total[5m]) > 
          (avg_over_time(rate(api_usage_total[5m])[1h:5m]) * 3)
        for: 5m
        labels:
          severity: warning
          service: cost-optimizer
          type: security
        annotations:
          summary: "Unusual API usage pattern detected"
          description: "API usage rate {{ $value }} is 3x higher than normal"
          impact: "Potential abuse or attack"
          runbook_url: "https://runbooks.company.com/cost-optimizer/unusual-usage"

      - alert: HighFailedAuthenticationRate
        expr: |
          rate(http_requests_total{code="401"}[5m]) > 10
        for: 3m
        labels:
          severity: warning
          service: cost-optimizer
          type: security
        annotations:
          summary: "High failed authentication rate"
          description: "Failed authentication rate {{ $value }}/sec exceeds threshold"
          impact: "Potential brute force attack"
          runbook_url: "https://runbooks.company.com/cost-optimizer/failed-auth"

  - name: cost_optimization_business_continuity
    interval: 300s
    rules:
      # Business Continuity Alerts
      - alert: RevenueImpactHigh
        expr: |
          (
            rate(total_cost_saved_usd[1h]) * 24
          ) < 1000
        for: 1h
        labels:
          severity: critical
          service: cost-optimizer
          type: business_impact
        annotations:
          summary: "Daily revenue projection below target"
          description: "Projected daily cost savings ${{ $value }} below $1000 target"
          impact: "Significant business impact, revenue targets at risk"
          runbook_url: "https://runbooks.company.com/cost-optimizer/revenue-impact"

      - alert: CustomerSatisfactionLow
        expr: |
          avg(customer_satisfaction_score) < 7.0
        for: 2h
        labels:
          severity: warning
          service: cost-optimizer
          type: customer_experience
        annotations:
          summary: "Customer satisfaction below acceptable level"
          description: "Average satisfaction score {{ $value }} below 7.0 threshold"
          impact: "Customer retention at risk"
          runbook_url: "https://runbooks.company.com/cost-optimizer/customer-satisfaction"

      - alert: SLAComplianceViolation
        expr: sla_compliance_percentage < 99.0
        for: 5m
        labels:
          severity: critical
          service: cost-optimizer
          type: sla_violation
        annotations:
          summary: "SLA compliance violation"
          description: "SLA compliance {{ $value }}% below 99% requirement"
          impact: "Contractual obligations not met, potential penalties"
          runbook_url: "https://runbooks.company.com/cost-optimizer/sla-violation"
