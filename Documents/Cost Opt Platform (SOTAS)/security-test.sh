#!/bin/bash

# Security Validation Script for Claude Optimizer Platform
# Tests the secure, port-minimal configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo "🔒 Claude Optimizer Platform - Security Validation"
echo "=================================================="

# Test 1: Check that only port 8000 is exposed
print_status "Testing port exposure..."
EXPOSED_PORTS=$(docker-compose ps --format "table {{.Ports}}" | grep -v "PORTS" | grep -o "0.0.0.0:[0-9]*" | sort -u)

if [ "$(echo "$EXPOSED_PORTS" | wc -l)" -eq 1 ] && [ "$EXPOSED_PORTS" = "0.0.0.0:8000" ]; then
    print_success "Only port 8000 is exposed externally"
else
    print_error "Multiple ports exposed: $EXPOSED_PORTS"
    echo "Expected: Only 0.0.0.0:8000"
fi

# Test 2: Check that internal services are not accessible externally
print_status "Testing internal service isolation..."

INTERNAL_PORTS=(5432 6379 3000 9090 16686 5678 8001)
ACCESSIBLE_PORTS=()

for port in "${INTERNAL_PORTS[@]}"; do
    if curl -s --connect-timeout 2 "http://localhost:$port" >/dev/null 2>&1; then
        ACCESSIBLE_PORTS+=($port)
    fi
done

if [ ${#ACCESSIBLE_PORTS[@]} -eq 0 ]; then
    print_success "All internal services are properly isolated"
else
    print_error "Internal services accessible externally on ports: ${ACCESSIBLE_PORTS[*]}"
fi

# Test 3: Check main dashboard accessibility
print_status "Testing main dashboard accessibility..."
if curl -s --connect-timeout 5 "http://localhost:8000" >/dev/null; then
    print_success "Main dashboard is accessible on port 8000"
else
    print_error "Main dashboard is not accessible"
fi

# Test 4: Check API health endpoint
print_status "Testing API health endpoint..."
HEALTH_RESPONSE=$(curl -s --connect-timeout 5 "http://localhost:8000/health" || echo "FAILED")
if [[ "$HEALTH_RESPONSE" == *"healthy"* ]] || [[ "$HEALTH_RESPONSE" == *"status"* ]]; then
    print_success "API health endpoint is responding"
else
    print_error "API health endpoint is not responding properly"
fi

# Test 5: Check internal service proxy functionality
print_status "Testing internal service proxy..."
PROXY_SERVICES=("grafana" "prometheus" "jaeger" "n8n")
PROXY_WORKING=true

for service in "${PROXY_SERVICES[@]}"; do
    PROXY_RESPONSE=$(curl -s --connect-timeout 5 "http://localhost:8000/api/v1/proxy/services/status" || echo "FAILED")
    if [[ "$PROXY_RESPONSE" == *"$service"* ]]; then
        print_success "Proxy for $service is configured"
    else
        print_warning "Proxy for $service may not be working"
        PROXY_WORKING=false
    fi
done

if $PROXY_WORKING; then
    print_success "Internal service proxy is functional"
fi

# Test 6: Check Docker network configuration
print_status "Testing Docker network configuration..."
NETWORK_INFO=$(docker network inspect costopt-internal 2>/dev/null || echo "FAILED")
if [[ "$NETWORK_INFO" == *"costopt-internal"* ]]; then
    print_success "Internal Docker network is properly configured"
else
    print_error "Internal Docker network is not configured"
fi

# Test 7: Check service connectivity within Docker network
print_status "Testing internal service connectivity..."
INTERNAL_CONNECTIVITY=true

# Test database connectivity from API container
DB_TEST=$(docker-compose exec -T api sh -c "nc -z db 5432" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
if [ "$DB_TEST" = "SUCCESS" ]; then
    print_success "Database is accessible internally"
else
    print_error "Database is not accessible internally"
    INTERNAL_CONNECTIVITY=false
fi

# Test Redis connectivity from API container
REDIS_TEST=$(docker-compose exec -T api sh -c "nc -z redis 6379" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
if [ "$REDIS_TEST" = "SUCCESS" ]; then
    print_success "Redis is accessible internally"
else
    print_error "Redis is not accessible internally"
    INTERNAL_CONNECTIVITY=false
fi

# Test ChromaDB connectivity from API container
CHROMA_TEST=$(docker-compose exec -T api sh -c "nc -z chromadb 8000" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
if [ "$CHROMA_TEST" = "SUCCESS" ]; then
    print_success "ChromaDB is accessible internally"
else
    print_error "ChromaDB is not accessible internally"
    INTERNAL_CONNECTIVITY=false
fi

if $INTERNAL_CONNECTIVITY; then
    print_success "All internal services are properly connected"
fi

# Test 8: Check that monitoring services are accessible via proxy
print_status "Testing monitoring service proxy access..."
MONITORING_ACCESSIBLE=true

for service in "${PROXY_SERVICES[@]}"; do
    PROXY_URL="http://localhost:8000/api/v1/proxy/$service/"
    PROXY_RESPONSE=$(curl -s --connect-timeout 5 "$PROXY_URL" || echo "FAILED")
    if [[ "$PROXY_RESPONSE" != "FAILED" ]] && [[ ${#PROXY_RESPONSE} -gt 10 ]]; then
        print_success "$service is accessible via proxy"
    else
        print_warning "$service proxy may not be fully functional"
        MONITORING_ACCESSIBLE=false
    fi
done

# Test 9: Security headers check
print_status "Testing security headers..."
SECURITY_HEADERS=$(curl -s -I "http://localhost:8000" | grep -i "x-\|content-security\|strict-transport")
if [ -n "$SECURITY_HEADERS" ]; then
    print_success "Security headers are present"
else
    print_warning "Consider adding security headers"
fi

# Summary
echo ""
echo "🔒 Security Validation Summary"
echo "=============================="

if [ ${#ACCESSIBLE_PORTS[@]} -eq 0 ] && $INTERNAL_CONNECTIVITY && [[ "$EXPOSED_PORTS" = "0.0.0.0:8000" ]]; then
    print_success "✅ SECURITY VALIDATION PASSED"
    echo ""
    echo "✅ Only port 8000 is exposed externally"
    echo "✅ All internal services are isolated"
    echo "✅ Internal service communication is working"
    echo "✅ Monitoring services are accessible via secure proxy"
    echo ""
    echo "🎉 Your Claude Optimizer Platform is securely configured!"
    echo "🌐 Access dashboard: http://localhost:8000"
    echo "📊 Access monitoring: http://localhost:8000 → Monitoring section"
else
    print_error "❌ SECURITY VALIDATION FAILED"
    echo ""
    echo "Please review the failed tests above and fix the issues."
fi

echo ""
echo "🔧 Quick Commands:"
echo "  View logs: docker-compose logs -f"
echo "  Restart:   docker-compose restart"
echo "  Stop:      docker-compose down"
