#!/bin/bash

# Claude Optimizer Platform - Complete Setup Script for OrbStack
# This script sets up the entire cost optimization platform

set -e  # Exit on any error

echo "🚀 Claude Optimizer Platform Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS with OrbStack"
    exit 1
fi

# Check if Orb<PERSON>tack is installed
if ! command -v orb &> /dev/null; then
    print_error "OrbStack is not installed. Please install it from https://orbstack.dev/"
    exit 1
fi

print_success "OrbStack detected"

# Check if Docker is available through OrbStack
if ! docker info &> /dev/null; then
    print_error "Docker is not available. Please ensure OrbStack is running"
    exit 1
fi

print_success "Docker is available"

# Create necessary directories
print_status "Creating project directories..."
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/chromadb
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/datasources

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating environment configuration..."
    cat > .env << EOF
# Claude Optimizer Platform Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DATABASE_URL=***************************************/costopt
REDIS_URL=redis://redis:6379

# ChromaDB Configuration
CHROMADB_HOST=chromadb
CHROMADB_PORT=8000

# Monitoring Configuration
JAEGER_HOST=jaeger
JAEGER_PORT=14268

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALLOWED_ORIGINS=["http://localhost:8000", "http://127.0.0.1:8000"]

# Performance Configuration
MAX_WORKERS=4
CACHE_TTL=3600
EOF
    print_warning "Created .env file. Please update OPENROUTER_API_KEY with your actual API key"
else
    print_success "Environment file already exists"
fi

# Create Grafana datasource configuration
print_status "Setting up monitoring configuration..."
mkdir -p monitoring/grafana/datasources
cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

# Create Prometheus configuration
cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cost-optimizer-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
EOF

# Create database initialization script
print_status "Creating database initialization script..."
mkdir -p scripts
cat > scripts/init-db.sql << EOF
-- Initialize Claude Optimizer Database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgvector";

-- Create optimization requests table
CREATE TABLE IF NOT EXISTS optimization_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    original_prompt TEXT NOT NULL,
    optimized_prompt TEXT,
    selected_model VARCHAR(100) NOT NULL,
    original_cost DECIMAL(10,4),
    optimized_cost DECIMAL(10,4),
    savings_percentage DECIMAL(5,2),
    quality_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    cache_hit BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create model performance table
CREATE TABLE IF NOT EXISTS model_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(50),
    quality_score DECIMAL(3,2),
    cost_per_token DECIMAL(8,6),
    avg_response_time_ms INTEGER,
    success_rate DECIMAL(3,2),
    last_updated TIMESTAMP DEFAULT NOW()
);

-- Create cache entries table
CREATE TABLE IF NOT EXISTS cache_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prompt_hash VARCHAR(64) UNIQUE NOT NULL,
    prompt_embedding VECTOR(1536),
    response_data JSONB,
    model_used VARCHAR(100),
    quality_score DECIMAL(3,2),
    hit_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_optimization_requests_model ON optimization_requests(selected_model);
CREATE INDEX IF NOT EXISTS idx_optimization_requests_created_at ON optimization_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_model_performance_model_task ON model_performance(model_name, task_type);
CREATE INDEX IF NOT EXISTS idx_cache_entries_embedding ON cache_entries USING ivfflat (prompt_embedding vector_cosine_ops);

-- Insert some sample data for demo
INSERT INTO model_performance (model_name, task_type, quality_score, cost_per_token, avg_response_time_ms, success_rate) VALUES
('deepseek-v3', 'general', 0.92, 0.000001, 120, 0.98),
('claude-3-sonnet', 'general', 0.95, 0.003000, 80, 0.99),
('gpt-4', 'general', 0.94, 0.030000, 150, 0.97)
ON CONFLICT DO NOTHING;

COMMIT;
EOF

print_success "Database initialization script created"

# Build the Docker image
print_status "Building Docker image..."
if docker build -t claude-optimizer:latest .; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Start the services
print_status "Starting services with Docker Compose..."
if docker-compose up -d; then
    print_success "Services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check service health
print_status "Checking service health..."

# Check if API is responding
for i in {1..30}; do
    if curl -f http://localhost:8000/health &> /dev/null; then
        print_success "API service is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "API service health check timed out"
    fi
    sleep 2
done

# Check if database is ready
if docker-compose exec -T db pg_isready -U costopt &> /dev/null; then
    print_success "Database is ready"
else
    print_warning "Database might not be ready yet"
fi

# Check if Redis is ready
if docker-compose exec -T redis redis-cli ping &> /dev/null; then
    print_success "Redis is ready"
else
    print_warning "Redis might not be ready yet"
fi

# Display service URLs
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Your Claude Optimizer Platform is now running!"
echo ""
echo "📊 Dashboard:     http://localhost:8000"
echo "📚 API Docs:      http://localhost:8000/docs"
echo "📈 Grafana:       http://localhost:3000 (admin/admin123)"
echo "🔍 Jaeger:        http://localhost:16686"
echo "📊 Prometheus:    http://localhost:9090"
echo "🔧 N8N:           http://localhost:5678 (admin/admin123)"
echo ""
echo "🔑 Next Steps:"
echo "1. Update your OpenRouter API key in the .env file"
echo "2. Visit http://localhost:8000 to access the dashboard"
echo "3. Go to Settings to configure your API key"
echo "4. Start optimizing your Claude requests!"
echo ""
echo "📝 Logs: docker-compose logs -f api"
echo "🛑 Stop: docker-compose down"
echo ""

# Check if .env needs API key
if grep -q "your_openrouter_api_key_here" .env; then
    print_warning "⚠️  Don't forget to update your OpenRouter API key in .env file!"
    echo "   Edit .env and replace 'your_openrouter_api_key_here' with your actual API key"
fi
