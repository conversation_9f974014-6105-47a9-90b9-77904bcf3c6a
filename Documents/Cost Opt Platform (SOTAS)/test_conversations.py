#!/usr/bin/env python3
"""
Test Conversation Management System
ChatGPT/Claude Desktop style conversation persistence
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append('/home/<USER>/cost-opt-platform')

from src.services.conversation_service import ConversationService
from src.models.conversation import ConversationCreate, MessageCreate
from src.core.database import get_database

async def test_conversations():
    """Test the conversation management system"""
    print("🧪 Testing Conversation Management System")
    print("=" * 60)
    
    # Initialize database
    db = get_database()
    await db.connect()
    
    service = ConversationService()
    
    try:
        # Create a conversation
        print("\n1. Creating conversation...")
        conv_data = ConversationCreate(
            title="Test Conversation with Claude 4 Sonnet",
            user_id="test-user",
            metadata={"model": "claude-4-sonnet", "cost_optimization": True}
        )
        
        conversation = await service.create_conversation(conv_data)
        print(f"✅ Created conversation: {conversation.id}")
        print(f"   Title: {conversation.title}")
        
        # Add user message
        print("\n2. Adding user message...")
        user_msg = MessageCreate(
            role="user",
            content="Hello Claude! Please help me optimize costs for my AI applications.",
            token_count=15,
            cost_usd="0.001",
            model_used="claude-4-sonnet"
        )
        
        user_message = await service.add_message(conversation.id, user_msg)
        print(f"✅ Added user message: {user_message.id}")
        print(f"   Content: {user_message.content[:50]}...")
        
        # Add assistant response
        print("\n3. Adding assistant response...")
        assistant_msg = MessageCreate(
            role="assistant",
            content="Hello! I'd be happy to help you optimize costs for your AI applications. Here are some key strategies:\n\n1. **Prompt Optimization**: Compress prompts while maintaining quality\n2. **Model Selection**: Use the most cost-effective model for each task\n3. **Caching**: Implement intelligent caching to avoid redundant API calls\n4. **Batching**: Process multiple requests together when possible\n\nWould you like me to elaborate on any of these strategies?",
            token_count=85,
            cost_usd="0.008",
            model_used="claude-4-sonnet",
            metadata={
                "optimization_applied": True, 
                "compression_ratio": 0.85,
                "strategies_suggested": 4
            }
        )
        
        assistant_message = await service.add_message(conversation.id, assistant_msg)
        print(f"✅ Added assistant message: {assistant_message.id}")
        print(f"   Content: {assistant_message.content[:50]}...")
        
        # Add follow-up user message
        print("\n4. Adding follow-up message...")
        followup_msg = MessageCreate(
            role="user",
            content="That's great! Can you tell me more about prompt optimization techniques?",
            token_count=16,
            cost_usd="0.001",
            model_used="claude-4-sonnet"
        )
        
        followup_message = await service.add_message(conversation.id, followup_msg)
        print(f"✅ Added follow-up message: {followup_message.id}")
        
        # Get conversation with messages
        print("\n5. Retrieving full conversation...")
        full_conversation = await service.get_conversation(conversation.id, include_messages=True)
        print(f"✅ Retrieved conversation with {len(full_conversation.messages)} messages")
        print(f"   Total cost: ${full_conversation.total_cost}")
        print(f"   Updated: {full_conversation.updated_at}")
        
        # List conversations
        print("\n6. Listing conversations...")
        conversations_list = await service.list_conversations(
            user_id="test-user",
            page=1,
            page_size=10
        )
        print(f"✅ Found {conversations_list.total} conversations")
        print(f"   Page {conversations_list.page} of {conversations_list.page_size}")
        
        # Test conversation update
        print("\n7. Updating conversation title...")
        from src.models.conversation import ConversationUpdate
        update_data = ConversationUpdate(
            title="Cost Optimization Discussion - Claude 4 Sonnet",
            metadata={"updated": True, "topic": "cost_optimization"}
        )
        
        updated_conversation = await service.update_conversation(conversation.id, update_data)
        print(f"✅ Updated conversation title: {updated_conversation.title}")
        
        # Display conversation summary
        print("\n" + "=" * 60)
        print("📊 CONVERSATION SUMMARY")
        print("=" * 60)
        print(f"Conversation ID: {full_conversation.id}")
        print(f"Title: {full_conversation.title}")
        print(f"Messages: {len(full_conversation.messages)}")
        print(f"Total Cost: ${full_conversation.total_cost}")
        print(f"Created: {full_conversation.created_at}")
        print(f"Updated: {full_conversation.updated_at}")
        
        print("\nMessages:")
        for i, msg in enumerate(full_conversation.messages, 1):
            print(f"  {i}. [{msg.role.upper()}] {msg.content[:60]}...")
            print(f"     Tokens: {msg.token_count}, Cost: ${msg.cost_usd}")
            if msg.metadata:
                print(f"     Metadata: {msg.metadata}")
        
        print("\n🎉 All tests passed! Conversation system is working perfectly.")
        return conversation.id
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        await db.disconnect()

if __name__ == "__main__":
    conversation_id = asyncio.run(test_conversations())
    if conversation_id:
        print(f"\n✅ Test completed successfully. Conversation ID: {conversation_id}")
    else:
        print("\n❌ Test failed.")
        sys.exit(1)
