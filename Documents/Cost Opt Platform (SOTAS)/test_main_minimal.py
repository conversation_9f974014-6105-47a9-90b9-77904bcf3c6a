"""
Minimal FastAPI app for testing
"""

from fastapi import FastAPI

# Create minimal app
app = FastAPI(
    title="Claude Sonnet Cost Optimizer",
    description="Production-grade cost optimization platform",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "Cost Optimizer API is running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
