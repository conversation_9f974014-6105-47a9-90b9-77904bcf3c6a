[tool:pytest]
# Pytest configuration for FAANG+ testing standards

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Async support
asyncio_mode = auto

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    security: Security tests
    smoke: Smoke tests for basic functionality
    slow: Tests that take more than 1 second
    critical: Critical path tests that must pass
    regression: Regression tests
    load: Load testing
    stress: Stress testing

# Test output configuration
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=85
    --durations=10
    --maxfail=5
    --disable-warnings

# Minimum coverage requirements
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    class .*\(Protocol\):
    @(abc\.)?abstractmethod

# Performance test configuration
performance_thresholds =
    optimization_latency_p99 = 50.0
    cache_hit_rate = 0.95
    compression_ratio = 0.70
    quality_score = 0.85
    throughput_rps = 1000

# Security test configuration
security_test_patterns =
    sql_injection = true
    xss_detection = true
    csrf_protection = true
    input_validation = true
    encryption_tests = true

# Load test configuration
load_test_config =
    max_concurrent_users = 1000
    test_duration_seconds = 60
    ramp_up_time_seconds = 10
    target_rps = 1000
