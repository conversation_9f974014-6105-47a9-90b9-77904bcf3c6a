#!/bin/bash
# Entrypoint script for Cost Optimization Platform
# Handles initialization, migrations, and service startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Wait for service to be available
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log "Waiting for $service_name at $host:$port..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" >/dev/null 2>&1; then
            log "✅ $service_name is available"
            return 0
        fi
        
        if [ $i -eq $timeout ]; then
            warn "⚠️ $service_name not available after ${timeout}s (continuing anyway)"
            return 1
        fi
        
        sleep 1
    done
}

# Initialize database
init_database() {
    log "Initializing database..."
    
    # Create data directory if it doesn't exist
    mkdir -p /app/data
    
    # Run database migrations if alembic is available
    if [ -f "alembic.ini" ]; then
        log "Running database migrations..."
        python -m alembic upgrade head || {
            warn "⚠️ Database migrations failed (continuing with basic setup)"
        }
    else
        log "No alembic configuration found, skipping migrations"
    fi
    
    # Initialize database tables through the application
    python -c "
import asyncio
import sys
sys.path.append('/app')

async def init_db():
    try:
        from src.core.database import get_database
        db = get_database()
        await db.connect()
        print('✅ Database initialized successfully')
        await db.disconnect()
    except Exception as e:
        print(f'⚠️ Database initialization warning: {e}')

asyncio.run(init_db())
" || warn "⚠️ Database initialization had warnings"
}

# Initialize Redis cache
init_redis() {
    local redis_host=${REDIS_HOST:-redis}
    local redis_port=${REDIS_PORT:-6379}
    
    if wait_for_service "$redis_host" "$redis_port" "Redis" 10; then
        log "Testing Redis connection..."
        python -c "
import redis
import sys

try:
    r = redis.Redis(host='$redis_host', port=$redis_port, decode_responses=True)
    r.ping()
    r.set('health_check', 'ok', ex=60)
    value = r.get('health_check')
    if value == 'ok':
        print('✅ Redis connection successful')
    else:
        print('⚠️ Redis test failed')
        sys.exit(1)
except Exception as e:
    print(f'⚠️ Redis connection failed: {e}')
    sys.exit(1)
" || warn "⚠️ Redis connection failed (continuing without cache)"
    fi
}

# Initialize monitoring
init_monitoring() {
    log "Initializing monitoring..."
    
    # Start Prometheus metrics server if configured
    python -c "
import sys
sys.path.append('/app')

try:
    from prometheus_client import start_http_server
    start_http_server(8001)
    print('✅ Prometheus metrics server started on port 8001')
except Exception as e:
    print(f'⚠️ Prometheus metrics server failed: {e}')
" &
    
    # Initialize OpenTelemetry if configured
    python -c "
import sys
sys.path.append('/app')

try:
    from src.core.telemetry import init_telemetry
    init_telemetry()
    print('✅ OpenTelemetry initialized')
except Exception as e:
    print(f'⚠️ OpenTelemetry initialization failed: {e}')
" || warn "⚠️ Telemetry initialization failed"
}

# Validate environment
validate_environment() {
    log "Validating environment..."
    
    # Check required environment variables
    required_vars=("DATABASE_URL")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "❌ Required environment variable $var is not set"
            exit 1
        else
            log "✅ $var is configured"
        fi
    done
    
    # Check optional environment variables
    optional_vars=("REDIS_URL" "OPENROUTER_API_KEY" "SECRET_KEY")
    for var in "${optional_vars[@]}"; do
        if [ -z "${!var}" ]; then
            warn "⚠️ Optional environment variable $var is not set"
        else
            log "✅ $var is configured"
        fi
    done
}

# Pre-flight checks
preflight_checks() {
    log "Running pre-flight checks..."
    
    # Check Python version
    python_version=$(python --version 2>&1)
    log "Python version: $python_version"
    
    # Check if we can import the main application
    python -c "
import sys
sys.path.append('/app')

try:
    from src.main import app
    print('✅ Application imports successfully')
except Exception as e:
    print(f'❌ Application import failed: {e}')
    sys.exit(1)
" || {
        error "❌ Application import failed"
        exit 1
    }
    
    # Check critical dependencies
    python -c "
import sys
critical_modules = ['fastapi', 'sqlalchemy', 'pydantic', 'uvicorn']

for module in critical_modules:
    try:
        __import__(module)
        print(f'✅ {module} available')
    except ImportError:
        print(f'❌ {module} not available')
        sys.exit(1)
" || {
        error "❌ Critical dependencies missing"
        exit 1
    }
}

# Main initialization
main() {
    log "🚀 Starting Cost Optimization Platform initialization..."
    
    # Validate environment
    validate_environment
    
    # Run pre-flight checks
    preflight_checks
    
    # Wait for external services
    if [ -n "$REDIS_HOST" ]; then
        init_redis
    fi
    
    # Initialize database
    init_database
    
    # Initialize monitoring
    init_monitoring
    
    log "🎉 Initialization complete!"
    
    # Execute the main command
    log "Starting application with command: $*"
    exec "$@"
}

# Handle signals for graceful shutdown
cleanup() {
    log "🔄 Received shutdown signal, cleaning up..."
    
    # Kill background processes
    jobs -p | xargs -r kill
    
    log "✅ Cleanup complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Run main function
main "$@"
