#!/bin/bash
# Health check script for Cost Optimization Platform
# Validates all critical services and endpoints

set -e

# Configuration
API_HOST=${API_HOST:-localhost}
API_PORT=${API_PORT:-8000}
TIMEOUT=${TIMEOUT:-10}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[HEALTH]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service is responding
check_http_endpoint() {
    local endpoint=$1
    local expected_status=${2:-200}
    
    log "Checking endpoint: $endpoint"
    
    response=$(curl -s -o /dev/null -w "%{http_code}" \
        --max-time $TIMEOUT \
        --connect-timeout 5 \
        "$endpoint" || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log "✅ $endpoint responded with $response"
        return 0
    else
        error "❌ $endpoint responded with $response (expected $expected_status)"
        return 1
    fi
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    # Try to connect to the database through the API
    if check_http_endpoint "http://$API_HOST:$API_PORT/health/db"; then
        log "✅ Database connection healthy"
        return 0
    else
        error "❌ Database connection failed"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity..."
    
    if check_http_endpoint "http://$API_HOST:$API_PORT/health/redis"; then
        log "✅ Redis connection healthy"
        return 0
    else
        warn "⚠️ Redis connection failed (non-critical)"
        return 0  # Redis is not critical for basic operation
    fi
}

# Check API responsiveness
check_api() {
    log "Checking API responsiveness..."
    
    # Check main health endpoint
    if ! check_http_endpoint "http://$API_HOST:$API_PORT/health"; then
        return 1
    fi
    
    # Check API documentation
    if ! check_http_endpoint "http://$API_HOST:$API_PORT/docs" 200; then
        warn "⚠️ API documentation not accessible"
    fi
    
    # Check metrics endpoint
    if ! check_http_endpoint "http://$API_HOST:$API_PORT/metrics" 200; then
        warn "⚠️ Metrics endpoint not accessible"
    fi
    
    return 0
}

# Check system resources
check_resources() {
    log "Checking system resources..."
    
    # Check memory usage
    if command -v free >/dev/null 2>&1; then
        memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        if (( $(echo "$memory_usage > 90" | bc -l) )); then
            warn "⚠️ High memory usage: ${memory_usage}%"
        else
            log "✅ Memory usage: ${memory_usage}%"
        fi
    fi
    
    # Check disk space
    if command -v df >/dev/null 2>&1; then
        disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
        if [ "$disk_usage" -gt 90 ]; then
            warn "⚠️ High disk usage: ${disk_usage}%"
        else
            log "✅ Disk usage: ${disk_usage}%"
        fi
    fi
    
    return 0
}

# Main health check function
main() {
    log "Starting comprehensive health check..."
    
    local exit_code=0
    
    # Critical checks (failure means unhealthy)
    if ! check_api; then
        error "❌ API health check failed"
        exit_code=1
    fi
    
    if ! check_database; then
        error "❌ Database health check failed"
        exit_code=1
    fi
    
    # Non-critical checks (warnings only)
    check_redis
    check_resources
    
    if [ $exit_code -eq 0 ]; then
        log "🎉 All health checks passed!"
    else
        error "💥 Health check failed!"
    fi
    
    return $exit_code
}

# Run health check
main "$@"
