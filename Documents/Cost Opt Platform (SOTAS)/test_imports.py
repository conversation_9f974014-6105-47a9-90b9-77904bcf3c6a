#!/usr/bin/env python3
"""
Import Test Script
Tests all imports to identify missing dependencies before running full tests
"""

import sys
import traceback
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, '/app' if Path('/app').exists() else str(Path(__file__).parent))

def test_import(module_name, description=""):
    """Test importing a module and report results"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description}: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - {description}: {e}")
        return False

def main():
    print("🔍 Testing all imports for Claude Optimizer Platform")
    print("=" * 60)
    
    failed_imports = []
    
    # Test core dependencies
    print("\n📦 Core Dependencies:")
    core_deps = [
        ("fastapi", "FastAPI web framework"),
        ("uvicorn", "ASGI server"),
        ("pydantic", "Data validation"),
        ("pydantic_settings", "Settings management"),
        ("httpx", "HTTP client for proxy"),
        ("sqlalchemy", "Database ORM"),
        ("asyncpg", "PostgreSQL async driver"),
        ("alembic", "Database migrations"),
        ("psycopg2", "PostgreSQL driver"),
        ("redis", "Redis client"),
        ("chromadb", "Vector database"),
        ("sentence_transformers", "Text embeddings"),
        ("transformers", "Transformer models"),
        ("huggingface_hub", "HuggingFace model hub"),
        ("numpy", "Numerical computing"),
        ("qdrant_client", "Qdrant vector database"),
        ("weaviate", "Weaviate vector database"),
    ]
    
    for module, desc in core_deps:
        if not test_import(module, desc):
            failed_imports.append(module)
    
    # Test monitoring dependencies
    print("\n📊 Monitoring Dependencies:")
    monitoring_deps = [
        ("opentelemetry", "OpenTelemetry tracing"),
        ("prometheus_client", "Prometheus metrics"),
        ("structlog", "Structured logging"),
    ]
    
    for module, desc in monitoring_deps:
        if not test_import(module, desc):
            failed_imports.append(module)
    
    # Test ML dependencies
    print("\n🤖 Machine Learning Dependencies:")
    ml_deps = [
        ("sklearn", "Machine learning library"),
        ("nltk", "Natural language processing"),
        ("spacy", "Advanced NLP"),
    ]
    
    for module, desc in ml_deps:
        if not test_import(module, desc):
            failed_imports.append(module)
    
    # Test security dependencies
    print("\n🔒 Security Dependencies:")
    security_deps = [
        ("jose", "JWT handling"),
        ("passlib", "Password hashing"),
    ]
    
    for module, desc in security_deps:
        if not test_import(module, desc):
            failed_imports.append(module)
    
    # Test utility dependencies
    print("\n🛠️  Utility Dependencies:")
    utility_deps = [
        ("dotenv", "Environment variables"),
        ("click", "CLI framework"),
        ("pytest", "Testing framework"),
    ]
    
    for module, desc in utility_deps:
        if not test_import(module, desc):
            failed_imports.append(module)
    
    # Test application imports
    print("\n🏗️  Application Modules:")
    try:
        from src.core.config import get_settings
        print("✅ src.core.config - Configuration management")
    except Exception as e:
        print(f"❌ src.core.config: {e}")
        failed_imports.append("src.core.config")
    
    try:
        from src.core.cache import get_cache_manager
        print("✅ src.core.cache - Cache management")
    except Exception as e:
        print(f"❌ src.core.cache: {e}")
        failed_imports.append("src.core.cache")
    
    try:
        from src.main import app
        print("✅ src.main - Main application")
    except Exception as e:
        print(f"❌ src.main: {e}")
        failed_imports.append("src.main")
        traceback.print_exc()
    
    # Summary
    print("\n" + "=" * 60)
    if failed_imports:
        print(f"❌ {len(failed_imports)} imports failed:")
        for module in failed_imports:
            print(f"   - {module}")
        print("\n💡 Add missing dependencies to requirements.txt and rebuild")
        return False
    else:
        print("✅ All imports successful!")
        print("🎉 Ready to run comprehensive tests!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
