"""
Redis Cache Integration Tests
Comprehensive testing of Redis caching operations with mock instances
"""

import pytest
import asyncio
import json
import time
from decimal import Decimal
from unittest.mock import AsyncMock, patch
import fakeredis.aioredis

from src.core.cache import Cache<PERSON>anager
from src.core.optimizer import OptimizationResult


class TestRedisIntegration:
    """Integration tests for Redis cache operations"""
    
    @pytest.fixture
    async def mock_redis(self):
        """Create mock Redis instance using fakeredis"""
        # Use fakeredis for testing - it's a pure Python Redis implementation
        redis_client = fakeredis.aioredis.FakeRedis()
        return redis_client
    
    @pytest.fixture
    async def cache_manager(self, mock_redis):
        """Create cache manager with mock Redis"""
        manager = CacheManager()
        manager.redis_client = mock_redis
        manager.initialized = True
        return manager
    
    @pytest.fixture
    def sample_result(self):
        """Sample optimization result for caching"""
        return OptimizationResult(
            response="Paris is the capital of France.",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("0.001"),
            tokens_used=50,
            quality_score=0.95,
            cache_hit=False,
            optimization_ratio=10.0,
            processing_time_ms=45.5
        )
    
    @pytest.mark.asyncio
    async def test_redis_basic_operations(self, mock_redis):
        """Test basic Redis operations"""
        # Set
        await mock_redis.set("test_key", "test_value")
        
        # Get
        value = await mock_redis.get("test_key")
        assert value == b"test_value"
        
        # Delete
        await mock_redis.delete("test_key")
        
        # Verify deletion
        value = await mock_redis.get("test_key")
        assert value is None
    
    @pytest.mark.asyncio
    async def test_redis_cache_store_and_retrieve(self, cache_manager, sample_result):
        """Test storing and retrieving from Redis cache"""
        prompt = "What is the capital of France?"
        model = "claude-4-sonnet"
        
        # Store in cache
        await cache_manager._store_redis_cache(
            cache_manager._create_cache_key(prompt, model),
            sample_result
        )
        
        # Retrieve from cache
        cached_result = await cache_manager._get_exact_match(
            cache_manager._create_cache_key(prompt, model)
        )
        
        assert cached_result is not None
        assert cached_result.response == sample_result.response
        assert cached_result.model_used == sample_result.model_used
        assert cached_result.cost_usd == sample_result.cost_usd
    
    @pytest.mark.asyncio
    async def test_redis_cache_expiration(self, cache_manager, sample_result):
        """Test Redis cache expiration"""
        prompt = "What is the capital of France?"
        cache_key = cache_manager._create_cache_key(prompt)
        
        # Store with short TTL
        original_ttl = cache_manager.redis_ttl
        cache_manager.redis_ttl = 1  # 1 second
        
        try:
            await cache_manager._store_redis_cache(cache_key, sample_result)
            
            # Immediate retrieval should work
            cached_result = await cache_manager._get_exact_match(cache_key)
            assert cached_result is not None
            
            # Wait for expiration
            await asyncio.sleep(2)
            
            # Should be expired
            cached_result = await cache_manager._get_exact_match(cache_key)
            assert cached_result is None
            
        finally:
            cache_manager.redis_ttl = original_ttl
    
    @pytest.mark.asyncio
    async def test_redis_pipeline_operations(self, mock_redis, cache_manager, sample_result):
        """Test Redis pipeline operations for batch processing"""
        # Prepare multiple cache entries
        entries = []
        for i in range(10):
            prompt = f"Test prompt {i}"
            cache_key = cache_manager._create_cache_key(prompt)
            result = OptimizationResult(
                response=f"Response {i}",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.001"),
                tokens_used=50,
                quality_score=0.95,
                cache_hit=False,
                optimization_ratio=10.0
            )
            entries.append((cache_key, result))
        
        # Use pipeline for batch operations
        pipe = mock_redis.pipeline()
        for cache_key, result in entries:
            serialized = json.dumps({
                'response': result.response,
                'model_used': result.model_used,
                'cost_usd': str(result.cost_usd),
                'tokens_used': result.tokens_used,
                'quality_score': result.quality_score,
                'cache_hit': result.cache_hit,
                'optimization_ratio': result.optimization_ratio
            })
            pipe.set(cache_key, serialized, ex=3600)
        
        await pipe.execute()
        
        # Verify all entries were stored
        for cache_key, expected_result in entries:
            cached_data = await mock_redis.get(cache_key)
            assert cached_data is not None
            
            data = json.loads(cached_data)
            assert data['response'] == expected_result.response
    
    @pytest.mark.asyncio
    async def test_redis_concurrent_access(self, cache_manager, sample_result):
        """Test concurrent Redis access"""
        async def store_and_retrieve(i):
            prompt = f"Concurrent test {i}"
            cache_key = cache_manager._create_cache_key(prompt)
            
            # Store
            await cache_manager._store_redis_cache(cache_key, sample_result)
            
            # Retrieve
            cached_result = await cache_manager._get_exact_match(cache_key)
            return cached_result is not None
        
        # Execute 50 concurrent operations
        tasks = [store_and_retrieve(i) for i in range(50)]
        results = await asyncio.gather(*tasks)
        
        # All operations should succeed
        assert all(results)
        assert len(results) == 50
    
    @pytest.mark.asyncio
    async def test_redis_memory_usage_optimization(self, mock_redis, cache_manager):
        """Test Redis memory usage optimization"""
        # Store large number of entries
        large_response = "A" * 10000  # 10KB response
        
        for i in range(100):
            prompt = f"Large test {i}"
            cache_key = cache_manager._create_cache_key(prompt)
            
            result = OptimizationResult(
                response=large_response,
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.001"),
                tokens_used=5000,
                quality_score=0.95,
                cache_hit=False,
                optimization_ratio=10.0
            )
            
            await cache_manager._store_redis_cache(cache_key, result)
        
        # Check memory usage (simulated)
        info = await mock_redis.info("memory")
        # In real Redis, we would check used_memory_human
        # For fakeredis, we just verify operations completed
        assert info is not None
    
    @pytest.mark.asyncio
    async def test_redis_connection_resilience(self, cache_manager, sample_result):
        """Test Redis connection resilience"""
        prompt = "Connection test"
        cache_key = cache_manager._create_cache_key(prompt)
        
        # Simulate connection failure
        with patch.object(cache_manager.redis_client, 'get', side_effect=Exception("Connection lost")):
            # Should handle gracefully
            cached_result = await cache_manager._get_exact_match(cache_key)
            assert cached_result is None
        
        # Simulate connection recovery
        with patch.object(cache_manager.redis_client, 'set', side_effect=Exception("Connection lost")):
            # Should handle gracefully
            try:
                await cache_manager._store_redis_cache(cache_key, sample_result)
                # Should not raise exception
            except Exception:
                pytest.fail("Should handle Redis connection errors gracefully")
    
    @pytest.mark.asyncio
    async def test_redis_data_serialization(self, cache_manager, sample_result):
        """Test Redis data serialization and deserialization"""
        prompt = "Serialization test"
        cache_key = cache_manager._create_cache_key(prompt)
        
        # Test with complex data types
        complex_result = OptimizationResult(
            response="Complex response with unicode: 🚀 and special chars: <>&\"'",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("0.001234567890"),  # High precision decimal
            tokens_used=50,
            quality_score=0.987654321,  # High precision float
            cache_hit=False,
            optimization_ratio=10.123456789,
            processing_time_ms=45.678
        )
        
        # Store and retrieve
        await cache_manager._store_redis_cache(cache_key, complex_result)
        cached_result = await cache_manager._get_exact_match(cache_key)
        
        assert cached_result is not None
        assert cached_result.response == complex_result.response
        assert cached_result.cost_usd == complex_result.cost_usd
        assert abs(cached_result.quality_score - complex_result.quality_score) < 1e-9
    
    @pytest.mark.asyncio
    async def test_redis_cache_invalidation(self, cache_manager, sample_result):
        """Test Redis cache invalidation"""
        prompt = "Invalidation test"
        cache_key = cache_manager._create_cache_key(prompt)
        
        # Store in cache
        await cache_manager._store_redis_cache(cache_key, sample_result)
        
        # Verify it's cached
        cached_result = await cache_manager._get_exact_match(cache_key)
        assert cached_result is not None
        
        # Invalidate
        await cache_manager.redis_client.delete(cache_key)
        
        # Verify it's gone
        cached_result = await cache_manager._get_exact_match(cache_key)
        assert cached_result is None
    
    @pytest.mark.asyncio
    async def test_redis_performance_metrics(self, cache_manager, sample_result):
        """Test Redis performance metrics collection"""
        start_time = time.time()
        
        # Perform multiple operations
        for i in range(100):
            prompt = f"Performance test {i}"
            cache_key = cache_manager._create_cache_key(prompt)
            
            # Store
            store_start = time.time()
            await cache_manager._store_redis_cache(cache_key, sample_result)
            store_time = time.time() - store_start
            
            # Retrieve
            get_start = time.time()
            await cache_manager._get_exact_match(cache_key)
            get_time = time.time() - get_start
            
            # Verify operations are fast
            assert store_time < 0.01  # < 10ms
            assert get_time < 0.01    # < 10ms
        
        total_time = time.time() - start_time
        avg_time_per_operation = total_time / 200  # 100 stores + 100 gets
        
        # Average operation should be very fast
        assert avg_time_per_operation < 0.005  # < 5ms average
    
    @pytest.mark.asyncio
    async def test_redis_cache_hit_rate_tracking(self, cache_manager, sample_result):
        """Test Redis cache hit rate tracking"""
        # Store some entries
        stored_prompts = []
        for i in range(10):
            prompt = f"Hit rate test {i}"
            cache_key = cache_manager._create_cache_key(prompt)
            await cache_manager._store_redis_cache(cache_key, sample_result)
            stored_prompts.append(prompt)
        
        # Test cache hits
        hits = 0
        total_requests = 20
        
        for i in range(total_requests):
            if i < 10:
                # Should hit cache
                prompt = stored_prompts[i]
            else:
                # Should miss cache
                prompt = f"Miss test {i}"
            
            cache_key = cache_manager._create_cache_key(prompt)
            cached_result = await cache_manager._get_exact_match(cache_key)
            
            if cached_result is not None:
                hits += 1
        
        hit_rate = hits / total_requests
        assert hit_rate == 0.5  # 50% hit rate as expected
    
    @pytest.mark.asyncio
    async def test_redis_cleanup_operations(self, mock_redis, cache_manager):
        """Test Redis cleanup operations"""
        # Store entries with different TTLs
        for i in range(10):
            key = f"cleanup_test_{i}"
            await mock_redis.set(key, f"value_{i}", ex=1 if i < 5 else 3600)
        
        # Wait for some to expire
        await asyncio.sleep(2)
        
        # Count remaining keys
        keys = await mock_redis.keys("cleanup_test_*")
        # Should have 5 remaining (the ones with 3600s TTL)
        assert len(keys) == 5
