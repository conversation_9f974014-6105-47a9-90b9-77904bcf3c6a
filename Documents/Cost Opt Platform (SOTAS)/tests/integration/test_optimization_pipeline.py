"""
Integration Tests for Optimization Pipeline
End-to-end testing of the complete optimization workflow
"""

import pytest
from fastapi.testclient import TestClient

from src.main import app
from tests.conftest import TestUtils


class TestOptimizationPipeline:
    """Integration tests for the complete optimization pipeline"""
    
    @pytest.fixture
    def client(self):
        """Test client with all dependencies mocked"""
        return TestClient(app)
    
    @pytest.fixture
    def api_headers(self):
        """API headers for testing"""
        return {
            "Content-Type": "application/json",
            "Authorization": "Bearer demo-key-12345"
        }
    
    def test_single_optimization_success(self, client, api_headers):
        """Test successful single optimization request"""
        request_data = {
            "prompt": "Write a Python function to calculate factorial",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate response structure
        TestUtils.assert_optimization_response(data)
        
        # Validate optimization results
        assert data["savings_percentage"] > 0
        assert data["quality_score"] >= request_data["quality_threshold"]
        assert data["selected_model"] in ["deepseek/deepseek-v3", "claude-3-5-sonnet", "free-model"]
        assert data["processing_time_ms"] > 0
    
    def test_single_optimization_with_compression(self, client, api_headers):
        """Test optimization with compression enabled"""
        request_data = {
            "prompt": "Create a comprehensive technical documentation for a REST API that includes authentication, rate limiting, error handling, and detailed endpoint specifications with examples. The documentation should cover all HTTP methods, status codes, request/response formats, and include interactive examples.",
            "quality_threshold": 0.85,
            "optimization_level": 5,  # Maximum compression
            "user_id": "test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should have significant compression for long prompts
        assert data["compression_ratio"] > 0.3
        assert len(data["optimized_prompt"]) < len(request_data["prompt"])
        assert data["savings_percentage"] > 50  # Should achieve good savings
    
    def test_batch_optimization_parallel(self, client, api_headers):
        """Test batch optimization with parallel processing"""
        batch_request = {
            "requests": [
                {
                    "prompt": "Write a Python function to sort a list",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "test_user"
                },
                {
                    "prompt": "Create a JavaScript function to validate email",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "test_user"
                },
                {
                    "prompt": "Implement a binary search algorithm",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "test_user"
                }
            ],
            "parallel_processing": True
        }
        
        response = client.post(
            "/api/v1/optimize/batch",
            json=batch_request,
            headers=api_headers
        )

        assert response.status_code == 200
        data = response.json()

        # Verify batch response structure
        assert "results" in data
        assert "summary" in data
        assert len(data["results"]) == 3

        # Verify summary statistics
        summary = data["summary"]
        assert "total_requests" in summary
        assert "successful_requests" in summary
        assert "failed_requests" in summary
        assert "total_savings_percentage" in summary
        assert "average_quality_score" in summary
        assert "total_processing_time_ms" in summary

        # Verify each result
        for result in data["results"]:
            if result["status"] == "success":
                TestUtils.assert_optimization_response(result)
                assert result["quality_score"] >= 0.8

    def test_optimization_error_handling(self, client, api_headers):
        """Test optimization error handling"""
        # Test with invalid optimization level
        request_data = {
            "prompt": "Test prompt",
            "quality_threshold": 0.8,
            "optimization_level": 10,  # Invalid level
            "user_id": "test_user"
        }

        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )

        assert response.status_code == 422  # Validation error

        # Test with invalid quality threshold
        request_data = {
            "prompt": "Test prompt",
            "quality_threshold": 1.5,  # Invalid threshold
            "optimization_level": 3,
            "user_id": "test_user"
        }

        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )

        assert response.status_code == 422  # Validation error

    def test_optimization_with_different_quality_thresholds(self, client, api_headers):
        """Test optimization with different quality thresholds"""
        base_prompt = "Create a machine learning model for prediction"

        thresholds = [0.7, 0.8, 0.9, 0.95]
        results = {}

        for threshold in thresholds:
            request_data = {
                "prompt": base_prompt,
                "quality_threshold": threshold,
                "optimization_level": 3,
                "user_id": "test_user"
            }

            response = client.post(
                "/api/v1/optimize",
                json=request_data,
                headers=api_headers
            )

            assert response.status_code == 200
            data = response.json()
            results[threshold] = data

            # Quality score should meet or exceed threshold (with tolerance)
            assert data["quality_score"] >= threshold * 0.9

    def test_optimization_stats_endpoint(self, client, api_headers):
        """Test optimization statistics endpoint"""
        # First, perform some optimizations
        for i in range(3):
            request_data = {
                "prompt": f"Create a function for task {i}",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            }

            response = client.post(
                "/api/v1/optimize",
                json=request_data,
                headers=api_headers
            )
            assert response.status_code == 200

        # Get stats
        response = client.get("/api/v1/stats", headers=api_headers)
        assert response.status_code == 200

        stats = response.json()

        # Verify stats structure
        expected_fields = [
            "total_requests", "total_savings", "average_savings",
            "cache_hit_rate", "model_usage_distribution"
        ]

        for field in expected_fields:
            assert field in stats, f"Missing stats field: {field}"

    def test_health_check_integration(self, client):
        """Test health check endpoints"""
        # Test basic health check
        response = client.get("/health")
        assert response.status_code == 200

        health_data = response.json()
        assert "status" in health_data
        assert "timestamp" in health_data
        assert "components" in health_data

        # Test readiness probe
        response = client.get("/health/ready")
        assert response.status_code in [200, 503]  # May be unhealthy in test environment

        # Test liveness probe
        response = client.get("/health/live")
        assert response.status_code == 200
        
        # Validate batch response
        assert "results" in data
        assert "total_savings" in data
        assert "success_count" in data
        assert "failure_count" in data
        
        assert len(data["results"]) == len(batch_request["requests"])
        assert data["success_count"] > 0
        assert data["total_savings"] >= 0
        
        # Validate individual results
        for result in data["results"]:
            TestUtils.assert_optimization_response(result)
    
    def test_batch_optimization_sequential(self, client, api_headers):
        """Test batch optimization with sequential processing"""
        batch_request = {
            "requests": [
                {
                    "prompt": "Write a Python class for a simple calculator",
                    "quality_threshold": 0.8,
                    "optimization_level": 2,
                    "user_id": "test_user"
                },
                {
                    "prompt": "Create a function to parse JSON data",
                    "quality_threshold": 0.8,
                    "optimization_level": 2,
                    "user_id": "test_user"
                }
            ],
            "parallel_processing": False
        }
        
        response = client.post(
            "/api/v1/optimize/batch",
            json=batch_request,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["results"]) == len(batch_request["requests"])
        assert data["success_count"] == len(batch_request["requests"])
        assert data["failure_count"] == 0
    
    def test_optimization_with_cache_hit(self, client, api_headers):
        """Test optimization with cache hit scenario"""
        request_data = {
            "prompt": "Write a hello world function in Python",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "test_user"
        }
        
        # First request - should miss cache
        response1 = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        
        assert response1.status_code == 200
        data1 = response1.json()
        
        # Second identical request - should hit cache
        response2 = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        
        assert response2.status_code == 200
        data2 = response2.json()
        
        # Second request should be faster due to cache hit
        assert data2["processing_time_ms"] <= data1["processing_time_ms"]
        
        # Results should be consistent
        assert data1["optimized_prompt"] == data2["optimized_prompt"]
        assert data1["selected_model"] == data2["selected_model"]
    
    def test_optimization_quality_threshold_enforcement(self, client, api_headers):
        """Test quality threshold enforcement"""
        # High quality threshold request
        high_quality_request = {
            "prompt": "Create a production-ready microservice architecture",
            "quality_threshold": 0.95,  # Very high threshold
            "optimization_level": 1,  # Low compression to maintain quality
            "user_id": "test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=high_quality_request,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should meet the high quality threshold
        assert data["quality_score"] >= high_quality_request["quality_threshold"]
        
        # Should likely use a premium model for high quality
        assert data["selected_model"] in ["claude-3-5-sonnet", "gpt-4"]
    
    def test_optimization_error_handling(self, client, api_headers):
        """Test error handling in optimization pipeline"""
        # Test with invalid request
        invalid_request = {
            "prompt": "",  # Empty prompt
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=invalid_request,
            headers=api_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        TestUtils.assert_error_response(data)
    
    def test_optimization_rate_limiting(self, client, api_headers):
        """Test rate limiting functionality"""
        request_data = {
            "prompt": "Simple test prompt",
            "quality_threshold": 0.8,
            "optimization_level": 1,
            "user_id": "test_user"
        }
        
        # Make multiple rapid requests
        responses = []
        for _ in range(10):
            response = client.post(
                "/api/v1/optimize",
                json=request_data,
                headers=api_headers
            )
            responses.append(response)
            time.sleep(0.1)  # Small delay
        
        # Should have some successful responses
        successful_responses = [r for r in responses if r.status_code == 200]
        rate_limited_responses = [r for r in responses if r.status_code == 429]
        
        assert len(successful_responses) > 0
        # Rate limiting might kick in for rapid requests
        # This depends on the rate limiting configuration
    
    def test_optimization_authentication(self, client):
        """Test authentication requirements"""
        request_data = {
            "prompt": "Test prompt",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "test_user"
        }
        
        # Request without authentication
        response = client.post(
            "/api/v1/optimize",
            json=request_data
        )
        
        assert response.status_code == 401
        
        # Request with invalid API key
        invalid_headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer invalid-key"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=invalid_headers
        )
        
        assert response.status_code == 401
    
    def test_optimization_stats_endpoint(self, client, api_headers):
        """Test optimization statistics endpoint"""
        response = client.get(
            "/api/v1/optimize/stats",
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate stats structure
        expected_fields = [
            "total_requests",
            "total_savings",
            "average_savings",
            "cache_hit_rate",
            "model_usage_distribution"
        ]
        
        for field in expected_fields:
            assert field in data
    
    def test_optimization_performance_benchmarks(self, client, api_headers):
        """Test optimization performance benchmarks"""
        # Test different prompt sizes
        test_cases = [
            ("Short prompt", "Write a function"),
            ("Medium prompt", "Create a comprehensive function that handles multiple edge cases and includes proper error handling"),
            ("Long prompt", "Develop a complete microservice architecture with authentication, authorization, database integration, caching, monitoring, logging, error handling, rate limiting, API documentation, testing framework, deployment configuration, and scalability considerations for a production environment serving millions of users")
        ]
        
        results = []
        
        for name, prompt in test_cases:
            request_data = {
                "prompt": prompt,
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            }
            
            start_time = time.time()
            response = client.post(
                "/api/v1/optimize",
                json=request_data,
                headers=api_headers
            )
            end_time = time.time()
            
            assert response.status_code == 200
            data = response.json()
            
            results.append({
                "name": name,
                "prompt_length": len(prompt),
                "response_time_ms": (end_time - start_time) * 1000,
                "processing_time_ms": data["processing_time_ms"],
                "savings_percentage": data["savings_percentage"],
                "compression_ratio": data.get("compression_ratio", 0)
            })
        
        # Validate performance characteristics
        for result in results:
            # Response time should be reasonable (< 5 seconds)
            assert result["response_time_ms"] < 5000
            
            # Processing time should be tracked
            assert result["processing_time_ms"] > 0
            
            # Should achieve some savings
            assert result["savings_percentage"] >= 0
        
        # Longer prompts should generally have higher compression ratios
        short_compression = results[0]["compression_ratio"]
        long_compression = results[2]["compression_ratio"]
        assert long_compression >= short_compression
