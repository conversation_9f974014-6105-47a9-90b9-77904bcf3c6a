"""
Database Integration Tests
Comprehensive testing of database operations with test containers
"""

import pytest
import asyncio
import os
from decimal import Decimal
from datetime import datetime, timedelta
import sqlalchemy as sa
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.core.database import DatabaseManager, OptimizationRecord, CacheEntry
from src.core.models import ModelConfig, ModelTier


class TestDatabaseIntegration:
    """Integration tests for database operations"""
    
    @pytest.fixture(scope="session")
    async def test_engine(self):
        """Create test database engine with in-memory SQLite"""
        # Use in-memory SQLite for fast testing
        database_url = "sqlite+aiosqlite:///:memory:"
        engine = create_async_engine(database_url, echo=False)
        
        # Create tables
        from src.core.database import Base
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        yield engine
        
        # Cleanup
        await engine.dispose()
    
    @pytest.fixture
    async def db_session(self, test_engine):
        """Create database session for testing"""
        async_session = sessionmaker(
            test_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            yield session
            await session.rollback()
    
    @pytest.fixture
    async def db_manager(self, test_engine):
        """Create database manager for testing"""
        manager = DatabaseManager(engine=test_engine)
        await manager.initialize()
        return manager
    
    @pytest.mark.asyncio
    async def test_optimization_record_crud(self, db_session):
        """Test CRUD operations for optimization records"""
        # Create
        record = OptimizationRecord(
            prompt_hash="test_hash_123",
            prompt="What is the capital of France?",
            response="Paris is the capital of France.",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("0.001"),
            baseline_cost_usd=Decimal("0.01"),
            tokens_used=50,
            quality_score=0.95,
            optimization_ratio=10.0,
            cache_hit=False,
            processing_time_ms=45.5,
            created_at=datetime.utcnow()
        )
        
        db_session.add(record)
        await db_session.commit()
        
        # Read
        result = await db_session.execute(
            sa.select(OptimizationRecord).where(
                OptimizationRecord.prompt_hash == "test_hash_123"
            )
        )
        retrieved_record = result.scalar_one()
        
        assert retrieved_record.prompt == "What is the capital of France?"
        assert retrieved_record.cost_usd == Decimal("0.001")
        assert retrieved_record.optimization_ratio == 10.0
        
        # Update
        retrieved_record.quality_score = 0.98
        await db_session.commit()
        
        # Verify update
        result = await db_session.execute(
            sa.select(OptimizationRecord).where(
                OptimizationRecord.prompt_hash == "test_hash_123"
            )
        )
        updated_record = result.scalar_one()
        assert updated_record.quality_score == 0.98
        
        # Delete
        await db_session.delete(updated_record)
        await db_session.commit()
        
        # Verify deletion
        result = await db_session.execute(
            sa.select(OptimizationRecord).where(
                OptimizationRecord.prompt_hash == "test_hash_123"
            )
        )
        assert result.scalar_one_or_none() is None
    
    @pytest.mark.asyncio
    async def test_cost_analytics_queries(self, db_session):
        """Test cost analytics database queries"""
        # Insert test data
        records = [
            OptimizationRecord(
                prompt_hash=f"hash_{i}",
                prompt=f"Test prompt {i}",
                response=f"Test response {i}",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.001"),
                baseline_cost_usd=Decimal("0.01"),
                tokens_used=50,
                quality_score=0.95,
                optimization_ratio=10.0,
                cache_hit=i % 2 == 0,  # Alternate cache hits
                processing_time_ms=45.5,
                created_at=datetime.utcnow() - timedelta(days=i)
            )
            for i in range(10)
        ]
        
        for record in records:
            db_session.add(record)
        await db_session.commit()
        
        # Test total cost savings
        result = await db_session.execute(
            sa.select(
                sa.func.sum(OptimizationRecord.baseline_cost_usd - OptimizationRecord.cost_usd)
            )
        )
        total_savings = result.scalar()
        expected_savings = Decimal("0.009") * 10  # (0.01 - 0.001) * 10 records
        assert total_savings == expected_savings
        
        # Test average optimization ratio
        result = await db_session.execute(
            sa.select(sa.func.avg(OptimizationRecord.optimization_ratio))
        )
        avg_ratio = result.scalar()
        assert avg_ratio == 10.0
        
        # Test cache hit rate
        result = await db_session.execute(
            sa.select(
                sa.func.count(OptimizationRecord.id).filter(OptimizationRecord.cache_hit == True),
                sa.func.count(OptimizationRecord.id)
            )
        )
        cache_hits, total_requests = result.one()
        cache_hit_rate = cache_hits / total_requests
        assert cache_hit_rate == 0.5  # 50% cache hit rate
    
    @pytest.mark.asyncio
    async def test_performance_metrics_queries(self, db_session):
        """Test performance metrics database queries"""
        # Insert performance test data
        records = []
        for i in range(100):
            record = OptimizationRecord(
                prompt_hash=f"perf_hash_{i}",
                prompt=f"Performance test {i}",
                response=f"Performance response {i}",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.001"),
                baseline_cost_usd=Decimal("0.01"),
                tokens_used=50 + i,  # Varying token counts
                quality_score=0.9 + (i % 10) * 0.01,  # Varying quality scores
                optimization_ratio=8.0 + (i % 5),  # Varying optimization ratios
                cache_hit=i % 3 == 0,  # 33% cache hit rate
                processing_time_ms=30.0 + (i % 50),  # Varying processing times
                created_at=datetime.utcnow() - timedelta(minutes=i)
            )
            records.append(record)
        
        for record in records:
            db_session.add(record)
        await db_session.commit()
        
        # Test P99 latency calculation
        result = await db_session.execute(
            sa.text("""
                SELECT processing_time_ms 
                FROM optimization_records 
                ORDER BY processing_time_ms 
                LIMIT 1 OFFSET (SELECT COUNT(*) * 0.99 FROM optimization_records)
            """)
        )
        p99_latency = result.scalar()
        assert p99_latency is not None
        assert p99_latency < 100.0  # Should be under 100ms
        
        # Test average quality score
        result = await db_session.execute(
            sa.select(sa.func.avg(OptimizationRecord.quality_score))
        )
        avg_quality = result.scalar()
        assert avg_quality > 0.9  # Should maintain high quality
    
    @pytest.mark.asyncio
    async def test_concurrent_database_operations(self, db_manager):
        """Test concurrent database operations"""
        async def create_record(i):
            record = OptimizationRecord(
                prompt_hash=f"concurrent_hash_{i}",
                prompt=f"Concurrent test {i}",
                response=f"Concurrent response {i}",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.001"),
                baseline_cost_usd=Decimal("0.01"),
                tokens_used=50,
                quality_score=0.95,
                optimization_ratio=10.0,
                cache_hit=False,
                processing_time_ms=45.5,
                created_at=datetime.utcnow()
            )
            await db_manager.store_optimization_record(record)
            return i
        
        # Create 50 concurrent database operations
        tasks = [create_record(i) for i in range(50)]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 50
        assert all(isinstance(r, int) for r in results)
        
        # Verify all records were stored
        count = await db_manager.get_total_optimizations()
        assert count >= 50
    
    @pytest.mark.asyncio
    async def test_database_connection_pooling(self, test_engine):
        """Test database connection pooling under load"""
        async def execute_query(session_factory, query_id):
            async with session_factory() as session:
                result = await session.execute(
                    sa.text("SELECT :query_id as id"), {"query_id": query_id}
                )
                return result.scalar()
        
        # Create session factory
        async_session = sessionmaker(
            test_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # Execute 100 concurrent queries
        tasks = [
            execute_query(async_session, i) 
            for i in range(100)
        ]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 100
        assert results == list(range(100))
    
    @pytest.mark.asyncio
    async def test_database_transaction_rollback(self, db_session):
        """Test database transaction rollback functionality"""
        # Start transaction
        record = OptimizationRecord(
            prompt_hash="rollback_test",
            prompt="Rollback test",
            response="Should be rolled back",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("0.001"),
            baseline_cost_usd=Decimal("0.01"),
            tokens_used=50,
            quality_score=0.95,
            optimization_ratio=10.0,
            cache_hit=False,
            processing_time_ms=45.5,
            created_at=datetime.utcnow()
        )
        
        db_session.add(record)
        
        # Verify record exists in session
        result = await db_session.execute(
            sa.select(OptimizationRecord).where(
                OptimizationRecord.prompt_hash == "rollback_test"
            )
        )
        assert result.scalar_one_or_none() is not None
        
        # Rollback transaction
        await db_session.rollback()
        
        # Verify record was rolled back
        result = await db_session.execute(
            sa.select(OptimizationRecord).where(
                OptimizationRecord.prompt_hash == "rollback_test"
            )
        )
        assert result.scalar_one_or_none() is None
    
    @pytest.mark.asyncio
    async def test_database_migration_compatibility(self, test_engine):
        """Test database schema migration compatibility"""
        # Test that current schema can handle all expected data types
        async with test_engine.begin() as conn:
            # Test decimal precision
            await conn.execute(
                sa.text("""
                    INSERT INTO optimization_records 
                    (prompt_hash, prompt, response, model_used, cost_usd, 
                     baseline_cost_usd, tokens_used, quality_score, 
                     optimization_ratio, cache_hit, processing_time_ms, created_at)
                    VALUES 
                    ('precision_test', 'Test', 'Test', 'model', 
                     0.000000001, 0.999999999, 1, 0.999999999, 
                     999999.999999, 0, 0.001, datetime('now'))
                """)
            )
            
            # Verify precision is maintained
            result = await conn.execute(
                sa.text("""
                    SELECT cost_usd, baseline_cost_usd, quality_score, optimization_ratio
                    FROM optimization_records 
                    WHERE prompt_hash = 'precision_test'
                """)
            )
            row = result.one()
            
            assert row[0] == Decimal("0.000000001")  # cost_usd
            assert row[1] == Decimal("0.999999999")  # baseline_cost_usd
            assert abs(float(row[2]) - 0.999999999) < 1e-9  # quality_score
            assert abs(float(row[3]) - 999999.999999) < 1e-6  # optimization_ratio
