"""
Integration Tests for Complete Optimization Flow
End-to-end testing of the cost optimization pipeline
"""

import pytest
import asyncio
from decimal import Decimal
from unittest.mock import patch, AsyncMock

from src.core.optimizer import CostOptimizer, OptimizationRequest
from src.core.cache import CacheManager
from src.clients.openrouter import OpenRouterClient
from src.monitoring.analytics import AnalyticsManager


class TestOptimizationFlow:
    """Integration tests for complete optimization flow"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create fully initialized optimizer"""
        optimizer = CostOptimizer()
        await optimizer.initialize()
        return optimizer
    
    @pytest.fixture
    async def cache_manager(self):
        """Create initialized cache manager"""
        cache = CacheManager()
        await cache.initialize()
        return cache
    
    @pytest.fixture
    def openrouter_client(self):
        """Create OpenRouter client"""
        return OpenRouterClient(api_key="test_key")
    
    @pytest.mark.asyncio
    async def test_complete_optimization_pipeline(self, optimizer):
        """Test complete optimization pipeline from request to response"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8
        )
        
        # Mock external dependencies
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            result = await optimizer.optimize(request)
            
            assert result is not None
            assert result.response == "Paris is the capital of France."
            assert result.model_used == "claude-4-sonnet"
            assert result.tokens_used == 50
            assert result.cost_usd > 0
            assert result.quality_score > 0
    
    @pytest.mark.asyncio
    async def test_cache_integration(self, optimizer):
        """Test cache integration in optimization flow"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8
        )
        
        # First request - should miss cache and call API
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            result1 = await optimizer.optimize(request)
            assert result1.cache_hit is False
            assert mock_generate.called
        
        # Second request - should hit cache
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            result2 = await optimizer.optimize(request)
            assert result2.cache_hit is True
            assert not mock_generate.called
            assert result2.response == result1.response
    
    @pytest.mark.asyncio
    async def test_model_fallback_integration(self, optimizer):
        """Test model fallback integration"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            # First model fails, second succeeds
            mock_generate.side_effect = [
                Exception("Primary model unavailable"),
                AsyncMock(
                    content="Paris is the capital of France.",
                    model="fallback-model",
                    tokens_used=50,
                    prompt_tokens=10,
                    completion_tokens=40
                )
            ]
            
            result = await optimizer.optimize(request)
            
            assert result is not None
            assert result.model_used == "fallback-model"
            assert mock_generate.call_count == 2
    
    @pytest.mark.asyncio
    async def test_quality_assessment_integration(self, optimizer):
        """Test quality assessment integration"""
        high_quality_request = OptimizationRequest(
            prompt="Explain quantum computing in detail",
            max_tokens=500,
            temperature=0.3,
            quality_threshold=0.9
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Quantum computing is a revolutionary technology...",
                model="claude-4-sonnet",
                tokens_used=200,
                prompt_tokens=20,
                completion_tokens=180
            )
            
            result = await optimizer.optimize(high_quality_request)
            
            assert result.quality_score >= high_quality_request.quality_threshold
    
    @pytest.mark.asyncio
    async def test_cost_calculation_integration(self, optimizer):
        """Test cost calculation integration"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            with patch.object(optimizer.openrouter_client, 'calculate_cost') as mock_cost:
                mock_cost.return_value = Decimal("0.001")
                
                result = await optimizer.optimize(request)
                
                assert result.cost_usd == Decimal("0.001")
                assert mock_cost.called
    
    @pytest.mark.asyncio
    async def test_analytics_integration(self, optimizer):
        """Test analytics integration"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            with patch.object(optimizer.analytics, 'track_optimization') as mock_track:
                result = await optimizer.optimize(request)
                
                assert mock_track.called
                # Verify analytics data
                call_args = mock_track.call_args[0][0]
                assert call_args['model_used'] == "claude-4-sonnet"
                assert call_args['tokens_used'] == 50
    
    @pytest.mark.asyncio
    async def test_batch_optimization_integration(self, optimizer):
        """Test batch optimization integration"""
        requests = [
            OptimizationRequest(
                prompt=f"Question {i}",
                max_tokens=50,
                temperature=0.7
            )
            for i in range(5)
        ]
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Test response",
                model="claude-4-sonnet",
                tokens_used=25,
                prompt_tokens=5,
                completion_tokens=20
            )
            
            results = await optimizer.optimize_batch(requests)
            
            assert len(results) == 5
            assert all(result.response == "Test response" for result in results)
            assert mock_generate.call_count == 5
    
    @pytest.mark.asyncio
    async def test_concurrent_optimization_integration(self, optimizer):
        """Test concurrent optimization handling"""
        requests = [
            OptimizationRequest(
                prompt=f"Concurrent question {i}",
                max_tokens=50,
                temperature=0.7
            )
            for i in range(10)
        ]
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Concurrent response",
                model="claude-4-sonnet",
                tokens_used=25,
                prompt_tokens=5,
                completion_tokens=20
            )
            
            # Execute concurrent optimizations
            tasks = [optimizer.optimize(req) for req in requests]
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 10
            assert all(result.response == "Concurrent response" for result in results)
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, optimizer):
        """Test error recovery in optimization flow"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            # Simulate temporary failure followed by success
            mock_generate.side_effect = [
                Exception("Temporary API error"),
                AsyncMock(
                    content="Paris is the capital of France.",
                    model="claude-4-sonnet",
                    tokens_used=50,
                    prompt_tokens=10,
                    completion_tokens=40
                )
            ]
            
            # Should retry and succeed
            result = await optimizer.optimize(request)
            
            assert result is not None
            assert result.response == "Paris is the capital of France."
            assert mock_generate.call_count == 2
    
    @pytest.mark.asyncio
    async def test_semantic_cache_integration(self, optimizer):
        """Test semantic cache integration"""
        # Store original prompt
        original_request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            # First request
            result1 = await optimizer.optimize(original_request)
            assert result1.cache_hit is False
        
        # Similar prompt should hit semantic cache
        similar_request = OptimizationRequest(
            prompt="What's the capital city of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            result2 = await optimizer.optimize(similar_request)
            
            # Should hit semantic cache
            if result2.cache_hit:
                assert result2.response == result1.response
                assert not mock_generate.called
    
    @pytest.mark.asyncio
    async def test_optimization_metrics_integration(self, optimizer):
        """Test optimization metrics collection"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        # Get initial metrics
        initial_metrics = await optimizer.get_optimization_metrics()
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Paris is the capital of France.",
                model="claude-4-sonnet",
                tokens_used=50,
                prompt_tokens=10,
                completion_tokens=40
            )
            
            await optimizer.optimize(request)
        
        # Get updated metrics
        updated_metrics = await optimizer.get_optimization_metrics()
        
        assert updated_metrics['total_optimizations'] > initial_metrics['total_optimizations']
        assert updated_metrics['total_cost_saved'] >= initial_metrics['total_cost_saved']
    
    @pytest.mark.asyncio
    async def test_cleanup_integration(self, optimizer):
        """Test cleanup integration"""
        # Perform some optimizations
        request = OptimizationRequest(
            prompt="Test cleanup",
            max_tokens=50
        )
        
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = AsyncMock(
                content="Test response",
                model="claude-4-sonnet",
                tokens_used=25,
                prompt_tokens=5,
                completion_tokens=20
            )
            
            await optimizer.optimize(request)
        
        # Cleanup should work without errors
        await optimizer.cleanup()
        
        assert not optimizer.initialized
