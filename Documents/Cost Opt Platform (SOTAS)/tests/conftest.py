"""
Test configuration and fixtures
"""

import os
import asyncio
import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Test environment setup - MUST be before imports
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "testing"
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["SECRET_KEY"] = "test-secret-key-that-is-at-least-32-characters-long-for-testing-purposes"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"
os.environ["REDIS_URL"] = "redis://localhost:6379/15"
os.environ["OPENROUTER_API_KEY"] = "test-openrouter-key"
os.environ["ANTHROPIC_API_KEY"] = "test-anthropic-key"
os.environ["OPENAI_API_KEY"] = "test-openai-key"
os.environ["DISABLE_AUTH"] = "true"
os.environ["DISABLE_RATE_LIMITING"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"
os.environ["DISABLE_SECURITY_VALIDATION"] = "true"
os.environ["DISABLE_INPUT_VALIDATION"] = "true"

# Reset metrics registry to prevent collisions
from src.monitoring.metrics_registry import reset_metrics_registry
reset_metrics_registry()

from src.main import app
from src.core.database import Base, get_db_session
from src.core.config import get_settings
from src.core.models import OptimizationRequest


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


# Override dependencies
app.dependency_overrides[get_db_session] = override_get_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client():
    """Create a test client"""
    test_client = TestClient(app)
    yield test_client


@pytest.fixture
def sample_optimization_request():
    """Sample optimization request for testing"""
    return OptimizationRequest(
        prompt="Create a simple hello world function in Python",
        quality_threshold=0.8,
        optimization_level=3,
        user_id="test_user"
    )


@pytest.fixture
def complex_optimization_request():
    """Complex optimization request for testing"""
    return OptimizationRequest(
        prompt="""
        Create a comprehensive technical Product Requirements Document (PRD) for a mobile application 
        that includes user authentication, real-time messaging capabilities, push notifications, 
        analytics dashboard, and integration with third-party payment systems. The application 
        should support both iOS and Android platforms, handle up to 100,000 concurrent users, 
        and maintain 99.9% uptime. Include detailed technical specifications, API documentation, 
        database schema design, security considerations, and deployment architecture.
        """,
        quality_threshold=0.9,
        optimization_level=2,
        user_id="test_user"
    )


@pytest.fixture
def api_headers():
    """Standard API headers for testing"""
    return {
        "Content-Type": "application/json",
        "X-API-Key": "demo-key-12345"
    }


@pytest.fixture
def admin_headers():
    """Admin API headers for testing"""
    return {
        "Content-Type": "application/json", 
        "X-API-Key": "admin-key-67890"
    }


@pytest_asyncio.fixture
async def mock_optimizer():
    """Mock optimizer for testing"""
    from unittest.mock import AsyncMock, MagicMock
    from src.core.optimizer import CostOptimizer
    from src.core.models import OptimizationResponse, TaskComplexity, CacheHitType
    
    optimizer = MagicMock(spec=CostOptimizer)
    
    # Mock the optimize method
    async def mock_optimize(request):
        return OptimizationResponse(
            optimized_prompt="Optimized: " + request.prompt[:50] + "...",
            selected_model="deepseek/deepseek-v3",
            original_cost=0.10,
            optimized_cost=0.01,
            savings_percentage=90.0,
            quality_score=0.85,
            processing_time_ms=50,
            cache_hit=False,
            cache_hit_type=CacheHitType.MISS,
            optimization_steps=[],
            task_complexity=TaskComplexity.MEDIUM,
            routing_reason="test_routing",
            compression_ratio=0.5
        )
    
    optimizer.optimize = AsyncMock(side_effect=mock_optimize)
    
    # Mock stats method
    async def mock_get_stats():
        return {
            "total_requests": 100,
            "total_savings": 50.0,
            "average_savings": 75.0,
            "cache_hit_rate": 0.8,
            "model_usage_distribution": {
                "deepseek-v3": 60,
                "claude-sonnet": 25,
                "llama-free": 15
            }
        }
    
    optimizer.get_optimization_stats = AsyncMock(side_effect=mock_get_stats)
    
    return optimizer


@pytest.fixture
def mock_cache_manager():
    """Mock cache manager for testing"""
    from unittest.mock import AsyncMock, MagicMock
    from src.core.cache import CacheManager
    
    cache_manager = MagicMock(spec=CacheManager)
    
    # Mock cache methods
    async def mock_get_cached_result(prompt):
        return None  # No cache hit by default
    
    async def mock_cache_result(prompt, data):
        pass  # Successfully cached
    
    async def mock_get_stats():
        return {
            "hit_rate": 0.8,
            "miss_rate": 0.2,
            "total_entries": 1000,
            "memory_usage_mb": 256.0,
            "avg_retrieval_time": 5.0
        }
    
    cache_manager.get_cached_result = AsyncMock(side_effect=mock_get_cached_result)
    cache_manager.cache_result = AsyncMock(side_effect=mock_cache_result)
    cache_manager.get_stats = AsyncMock(side_effect=mock_get_stats)
    
    return cache_manager


@pytest.fixture
def mock_model_router():
    """Mock model router for testing"""
    from unittest.mock import AsyncMock, MagicMock
    from src.services.model_router import ModelRouter
    from src.core.models import ModelType
    
    router = MagicMock(spec=ModelRouter)
    
    # Mock routing method
    async def mock_route_request(prompt, task_complexity, quality_threshold=0.85, preferred_model=None):
        if quality_threshold > 0.9:
            return ModelType.CLAUDE_SONNET, "high_quality_requirement"
        else:
            return ModelType.DEEPSEEK_V3, "cost_optimization"
    
    router.route_request = AsyncMock(side_effect=mock_route_request)
    
    # Mock stats method
    async def mock_get_model_stats():
        return {
            "claude-sonnet": {
                "health": "healthy",
                "available": True,
                "circuit_breaker_state": "closed",
                "total_requests": 100,
                "success_rate": 0.95
            },
            "deepseek-v3": {
                "health": "healthy",
                "available": True,
                "circuit_breaker_state": "closed",
                "total_requests": 500,
                "success_rate": 0.92
            }
        }
    
    router.get_model_stats = AsyncMock(side_effect=mock_get_model_stats)
    
    return router


# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def assert_optimization_response(response_data: dict):
        """Assert that response contains expected optimization fields"""
        required_fields = [
            "optimized_prompt",
            "selected_model", 
            "original_cost",
            "optimized_cost",
            "savings_percentage",
            "quality_score",
            "processing_time_ms",
            "cache_hit",
            "task_complexity",
            "routing_reason"
        ]
        
        for field in required_fields:
            assert field in response_data, f"Missing required field: {field}"
        
        # Validate data types and ranges
        assert isinstance(response_data["savings_percentage"], (int, float))
        assert 0 <= response_data["savings_percentage"] <= 100
        assert isinstance(response_data["quality_score"], (int, float))
        assert 0 <= response_data["quality_score"] <= 1
        assert isinstance(response_data["processing_time_ms"], int)
        assert response_data["processing_time_ms"] >= 0
    
    @staticmethod
    def assert_error_response(response_data: dict):
        """Assert that response contains expected error fields"""
        required_fields = ["error", "message"]
        
        for field in required_fields:
            assert field in response_data, f"Missing required error field: {field}"


@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils

@pytest.fixture(autouse=True)
def reset_metrics():
    """Reset metrics registry before each test to prevent collisions"""
    reset_metrics_registry()
    yield
    reset_metrics_registry()
