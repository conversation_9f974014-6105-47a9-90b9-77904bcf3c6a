"""
Cost Savings Calculator and Reporting
Comprehensive cost analysis with detailed USD calculations and optimization metrics
"""

import asyncio
import json
import time
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict
import statistics

from src.core.optimizer import OptimizationResult
from src.core.models import ModelConfig, ModelTier


@dataclass
class CostAnalysisResult:
    """Detailed cost analysis result"""
    scenario_name: str
    baseline_cost_usd: Decimal
    optimized_cost_usd: Decimal
    savings_usd: Decimal
    optimization_ratio: float
    cost_reduction_percentage: float
    quality_score: float
    model_used: str
    tokens_used: int
    cache_hit: bool
    processing_time_ms: float


@dataclass
class CostSavingsReport:
    """Comprehensive cost savings report"""
    report_id: str
    generated_at: datetime
    total_baseline_cost_usd: Decimal
    total_optimized_cost_usd: Decimal
    total_savings_usd: Decimal
    overall_optimization_ratio: float
    overall_cost_reduction_percentage: float
    average_quality_score: float
    cache_hit_rate: float
    average_processing_time_ms: float
    scenarios: List[CostAnalysisResult]
    summary_metrics: Dict[str, Any]


class CostSavingsCalculator:
    """
    Advanced cost savings calculator with detailed USD analysis
    """
    
    def __init__(self):
        self.model_pricing = self._initialize_model_pricing()
        self.baseline_models = self._initialize_baseline_models()
        
    def _initialize_model_pricing(self) -> Dict[str, ModelConfig]:
        """Initialize realistic model pricing based on current market rates"""
        return {
            # Premium Models (Highest Quality, Highest Cost)
            "anthropic/claude-3.5-sonnet": ModelConfig(
                name="anthropic/claude-3.5-sonnet",
                provider="anthropic",
                cost_per_token=Decimal("0.000015"),  # $15 per 1M tokens
                tier=ModelTier.PREMIUM,
                quality_score=0.98
            ),
            "openai/gpt-4-turbo": ModelConfig(
                name="openai/gpt-4-turbo",
                provider="openai",
                cost_per_token=Decimal("0.00001"),   # $10 per 1M tokens
                tier=ModelTier.PREMIUM,
                quality_score=0.96
            ),
            
            # Standard Models (Good Quality, Moderate Cost)
            "openai/gpt-4": ModelConfig(
                name="openai/gpt-4",
                provider="openai",
                cost_per_token=Decimal("0.00003"),   # $30 per 1M tokens
                tier=ModelTier.STANDARD,
                quality_score=0.95
            ),
            "anthropic/claude-3-haiku": ModelConfig(
                name="anthropic/claude-3-haiku",
                provider="anthropic",
                cost_per_token=Decimal("0.00000025"), # $0.25 per 1M tokens
                tier=ModelTier.STANDARD,
                quality_score=0.90
            ),
            
            # Budget Models (Acceptable Quality, Low Cost)
            "meta-llama/llama-3.1-8b-instruct": ModelConfig(
                name="meta-llama/llama-3.1-8b-instruct",
                provider="meta",
                cost_per_token=Decimal("0.0000002"), # $0.2 per 1M tokens
                tier=ModelTier.BUDGET,
                quality_score=0.85
            ),
            "mistralai/mistral-7b-instruct": ModelConfig(
                name="mistralai/mistral-7b-instruct",
                provider="mistral",
                cost_per_token=Decimal("0.0000001"), # $0.1 per 1M tokens
                tier=ModelTier.BUDGET,
                quality_score=0.80
            )
        }
    
    def _initialize_baseline_models(self) -> Dict[str, str]:
        """Initialize baseline models for different use cases"""
        return {
            "general": "openai/gpt-4",  # Most common baseline
            "premium": "anthropic/claude-3.5-sonnet",  # Premium baseline
            "budget": "anthropic/claude-3-haiku"  # Budget baseline
        }
    
    def calculate_cost(self, model_name: str, tokens_used: int) -> Decimal:
        """Calculate cost for specific model and token usage"""
        if model_name not in self.model_pricing:
            # Default to GPT-4 pricing if model not found
            model_name = "openai/gpt-4"
        
        model_config = self.model_pricing[model_name]
        cost = model_config.cost_per_token * tokens_used
        return cost.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)
    
    def calculate_baseline_cost(self, tokens_used: int, baseline_type: str = "general") -> Decimal:
        """Calculate baseline cost using standard model"""
        baseline_model = self.baseline_models.get(baseline_type, "openai/gpt-4")
        return self.calculate_cost(baseline_model, tokens_used)
    
    def calculate_optimization_ratio(self, baseline_cost: Decimal, optimized_cost: Decimal) -> float:
        """Calculate optimization ratio (how many times cheaper)"""
        if optimized_cost == 0:
            return float('inf')
        return float(baseline_cost / optimized_cost)
    
    def calculate_cost_reduction_percentage(self, baseline_cost: Decimal, optimized_cost: Decimal) -> float:
        """Calculate cost reduction percentage"""
        if baseline_cost == 0:
            return 0.0
        reduction = (baseline_cost - optimized_cost) / baseline_cost * 100
        return float(reduction)
    
    def analyze_optimization_result(
        self, 
        result: OptimizationResult, 
        scenario_name: str,
        baseline_type: str = "general"
    ) -> CostAnalysisResult:
        """Analyze a single optimization result"""
        
        # Calculate baseline cost
        baseline_cost = self.calculate_baseline_cost(result.tokens_used, baseline_type)
        
        # Calculate optimized cost
        optimized_cost = self.calculate_cost(result.model_used, result.tokens_used)
        
        # Calculate savings and ratios
        savings = baseline_cost - optimized_cost
        optimization_ratio = self.calculate_optimization_ratio(baseline_cost, optimized_cost)
        cost_reduction_percentage = self.calculate_cost_reduction_percentage(baseline_cost, optimized_cost)
        
        return CostAnalysisResult(
            scenario_name=scenario_name,
            baseline_cost_usd=baseline_cost,
            optimized_cost_usd=optimized_cost,
            savings_usd=savings,
            optimization_ratio=optimization_ratio,
            cost_reduction_percentage=cost_reduction_percentage,
            quality_score=result.quality_score,
            model_used=result.model_used,
            tokens_used=result.tokens_used,
            cache_hit=result.cache_hit,
            processing_time_ms=result.processing_time_ms or 0.0
        )
    
    def generate_comprehensive_report(
        self, 
        results: List[Tuple[OptimizationResult, str]], 
        baseline_type: str = "general"
    ) -> CostSavingsReport:
        """Generate comprehensive cost savings report"""
        
        # Analyze each result
        analyses = []
        for result, scenario_name in results:
            analysis = self.analyze_optimization_result(result, scenario_name, baseline_type)
            analyses.append(analysis)
        
        # Calculate totals
        total_baseline_cost = sum(a.baseline_cost_usd for a in analyses)
        total_optimized_cost = sum(a.optimized_cost_usd for a in analyses)
        total_savings = total_baseline_cost - total_optimized_cost
        
        # Calculate overall metrics
        overall_optimization_ratio = self.calculate_optimization_ratio(
            total_baseline_cost, total_optimized_cost
        )
        overall_cost_reduction_percentage = self.calculate_cost_reduction_percentage(
            total_baseline_cost, total_optimized_cost
        )
        
        # Calculate averages
        average_quality_score = statistics.mean(a.quality_score for a in analyses)
        cache_hit_rate = sum(1 for a in analyses if a.cache_hit) / len(analyses)
        average_processing_time = statistics.mean(a.processing_time_ms for a in analyses)
        
        # Generate summary metrics
        summary_metrics = self._generate_summary_metrics(analyses)
        
        return CostSavingsReport(
            report_id=f"cost_report_{int(time.time())}",
            generated_at=datetime.utcnow(),
            total_baseline_cost_usd=total_baseline_cost,
            total_optimized_cost_usd=total_optimized_cost,
            total_savings_usd=total_savings,
            overall_optimization_ratio=overall_optimization_ratio,
            overall_cost_reduction_percentage=overall_cost_reduction_percentage,
            average_quality_score=average_quality_score,
            cache_hit_rate=cache_hit_rate,
            average_processing_time_ms=average_processing_time,
            scenarios=analyses,
            summary_metrics=summary_metrics
        )
    
    def _generate_summary_metrics(self, analyses: List[CostAnalysisResult]) -> Dict[str, Any]:
        """Generate detailed summary metrics"""
        
        # Model usage distribution
        model_usage = {}
        for analysis in analyses:
            model = analysis.model_used
            if model not in model_usage:
                model_usage[model] = {"count": 0, "total_cost": Decimal("0"), "total_savings": Decimal("0")}
            model_usage[model]["count"] += 1
            model_usage[model]["total_cost"] += analysis.optimized_cost_usd
            model_usage[model]["total_savings"] += analysis.savings_usd
        
        # Quality distribution
        quality_ranges = {
            "excellent": sum(1 for a in analyses if a.quality_score >= 0.95),
            "good": sum(1 for a in analyses if 0.85 <= a.quality_score < 0.95),
            "acceptable": sum(1 for a in analyses if 0.75 <= a.quality_score < 0.85),
            "poor": sum(1 for a in analyses if a.quality_score < 0.75)
        }
        
        # Optimization ratio distribution
        optimization_ratios = [a.optimization_ratio for a in analyses]
        
        # Cost savings distribution
        savings_amounts = [float(a.savings_usd) for a in analyses]
        
        return {
            "model_usage_distribution": {
                model: {
                    "usage_count": stats["count"],
                    "usage_percentage": (stats["count"] / len(analyses)) * 100,
                    "total_cost_usd": float(stats["total_cost"]),
                    "total_savings_usd": float(stats["total_savings"]),
                    "average_cost_per_request": float(stats["total_cost"] / stats["count"]) if stats["count"] > 0 else 0
                }
                for model, stats in model_usage.items()
            },
            "quality_distribution": {
                range_name: {
                    "count": count,
                    "percentage": (count / len(analyses)) * 100
                }
                for range_name, count in quality_ranges.items()
            },
            "optimization_metrics": {
                "min_optimization_ratio": min(optimization_ratios),
                "max_optimization_ratio": max(optimization_ratios),
                "median_optimization_ratio": statistics.median(optimization_ratios),
                "mean_optimization_ratio": statistics.mean(optimization_ratios),
                "std_optimization_ratio": statistics.stdev(optimization_ratios) if len(optimization_ratios) > 1 else 0
            },
            "cost_savings_metrics": {
                "min_savings_usd": min(savings_amounts),
                "max_savings_usd": max(savings_amounts),
                "median_savings_usd": statistics.median(savings_amounts),
                "mean_savings_usd": statistics.mean(savings_amounts),
                "total_requests": len(analyses),
                "cache_enabled_requests": sum(1 for a in analyses if a.cache_hit),
                "api_requests": sum(1 for a in analyses if not a.cache_hit)
            }
        }
    
    def print_detailed_report(self, report: CostSavingsReport):
        """Print detailed cost savings report"""
        
        print("\n" + "="*100)
        print("COMPREHENSIVE COST OPTIMIZATION ANALYSIS REPORT")
        print("="*100)
        print(f"Report ID: {report.report_id}")
        print(f"Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Total Scenarios Analyzed: {len(report.scenarios)}")
        
        print("\n" + "="*50 + " EXECUTIVE SUMMARY " + "="*50)
        print(f"💰 Total Baseline Cost:     ${float(report.total_baseline_cost_usd):.6f}")
        print(f"💰 Total Optimized Cost:    ${float(report.total_optimized_cost_usd):.6f}")
        print(f"💰 Total Savings:           ${float(report.total_savings_usd):.6f}")
        print(f"📈 Optimization Ratio:      {report.overall_optimization_ratio:.2f}x")
        print(f"📊 Cost Reduction:          {report.overall_cost_reduction_percentage:.1f}%")
        print(f"⭐ Average Quality Score:   {report.average_quality_score:.3f}")
        print(f"🚀 Cache Hit Rate:          {report.cache_hit_rate*100:.1f}%")
        print(f"⚡ Avg Processing Time:     {report.average_processing_time_ms:.2f}ms")
        
        print("\n" + "="*40 + " DETAILED SCENARIO ANALYSIS " + "="*40)
        for i, scenario in enumerate(report.scenarios, 1):
            print(f"\n{i}. {scenario.scenario_name.upper()}")
            print(f"   Baseline Cost:      ${float(scenario.baseline_cost_usd):.6f}")
            print(f"   Optimized Cost:     ${float(scenario.optimized_cost_usd):.6f}")
            print(f"   Savings:            ${float(scenario.savings_usd):.6f}")
            print(f"   Optimization Ratio: {scenario.optimization_ratio:.2f}x")
            print(f"   Cost Reduction:     {scenario.cost_reduction_percentage:.1f}%")
            print(f"   Quality Score:      {scenario.quality_score:.3f}")
            print(f"   Model Used:         {scenario.model_used}")
            print(f"   Tokens Used:        {scenario.tokens_used}")
            print(f"   Cache Hit:          {'Yes' if scenario.cache_hit else 'No'}")
            print(f"   Processing Time:    {scenario.processing_time_ms:.2f}ms")
        
        print("\n" + "="*40 + " MODEL USAGE ANALYSIS " + "="*40)
        for model, stats in report.summary_metrics["model_usage_distribution"].items():
            print(f"\n{model}:")
            print(f"   Usage Count:        {stats['usage_count']}")
            print(f"   Usage Percentage:   {stats['usage_percentage']:.1f}%")
            print(f"   Total Cost:         ${stats['total_cost_usd']:.6f}")
            print(f"   Total Savings:      ${stats['total_savings_usd']:.6f}")
            print(f"   Avg Cost/Request:   ${stats['average_cost_per_request']:.6f}")
        
        print("\n" + "="*40 + " QUALITY DISTRIBUTION " + "="*40)
        for quality_range, stats in report.summary_metrics["quality_distribution"].items():
            print(f"{quality_range.capitalize()}: {stats['count']} requests ({stats['percentage']:.1f}%)")
        
        print("\n" + "="*40 + " OPTIMIZATION METRICS " + "="*40)
        opt_metrics = report.summary_metrics["optimization_metrics"]
        print(f"Min Optimization Ratio:    {opt_metrics['min_optimization_ratio']:.2f}x")
        print(f"Max Optimization Ratio:    {opt_metrics['max_optimization_ratio']:.2f}x")
        print(f"Median Optimization Ratio: {opt_metrics['median_optimization_ratio']:.2f}x")
        print(f"Mean Optimization Ratio:   {opt_metrics['mean_optimization_ratio']:.2f}x")
        
        print("\n" + "="*40 + " COST SAVINGS BREAKDOWN " + "="*40)
        cost_metrics = report.summary_metrics["cost_savings_metrics"]
        print(f"Min Savings per Request:   ${cost_metrics['min_savings_usd']:.6f}")
        print(f"Max Savings per Request:   ${cost_metrics['max_savings_usd']:.6f}")
        print(f"Median Savings per Request: ${cost_metrics['median_savings_usd']:.6f}")
        print(f"Mean Savings per Request:   ${cost_metrics['mean_savings_usd']:.6f}")
        print(f"Total API Requests:        {cost_metrics['api_requests']}")
        print(f"Cache-Served Requests:     {cost_metrics['cache_enabled_requests']}")
        
        print("\n" + "="*100)
        print("🎯 KEY INSIGHTS:")
        print(f"   • Achieved {report.overall_optimization_ratio:.1f}x cost optimization")
        print(f"   • Saved ${float(report.total_savings_usd):.6f} in API costs")
        print(f"   • Maintained {report.average_quality_score:.1%} average quality")
        print(f"   • {report.cache_hit_rate:.1%} of requests served from cache")
        print(f"   • Average response time: {report.average_processing_time_ms:.0f}ms")
        print("="*100)
    
    def export_report_json(self, report: CostSavingsReport, filename: str = None) -> str:
        """Export report to JSON file"""
        if filename is None:
            filename = f"cost_savings_report_{report.report_id}.json"
        
        # Convert report to JSON-serializable format
        report_dict = asdict(report)
        
        # Convert Decimal to float for JSON serialization
        def convert_decimals(obj):
            if isinstance(obj, dict):
                return {k: convert_decimals(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_decimals(item) for item in obj]
            elif isinstance(obj, Decimal):
                return float(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        report_dict = convert_decimals(report_dict)
        
        with open(filename, 'w') as f:
            json.dump(report_dict, f, indent=2)
        
        return filename
