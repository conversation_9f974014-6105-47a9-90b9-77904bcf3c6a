"""
End-to-End Tests
Complete system testing with real dependencies
"""

import pytest
import pytest_asyncio
import asyncio
import time
from fastapi.testclient import TestClient
from unittest.mock import patch

from src.main import app


class TestEndToEnd:
    """End-to-end tests for the complete system"""
    
    @pytest.fixture(scope="class")
    def client(self):
        """Test client for E2E tests"""
        return TestClient(app)
    
    @pytest.fixture
    def api_headers(self):
        """API headers for testing"""
        return {
            "Content-Type": "application/json",
            "X-API-Key": "demo-key-12345"
        }
    
    @pytest.fixture
    def admin_headers(self):
        """Admin API headers for testing"""
        return {
            "Content-Type": "application/json",
            "X-API-Key": "admin-key-67890"
        }
    
    def test_complete_optimization_workflow(self, client, api_headers):
        """Test complete optimization workflow from request to response"""
        # Step 1: Submit optimization request
        request_data = {
            "prompt": "Create a comprehensive web application with user authentication, database integration, and real-time messaging capabilities",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "e2e_test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        
        assert response.status_code == 200
        optimization_result = response.json()
        
        # Verify optimization result
        assert "optimized_prompt" in optimization_result
        assert "selected_model" in optimization_result
        assert "savings_percentage" in optimization_result
        assert "quality_score" in optimization_result
        assert optimization_result["quality_score"] >= 0.8
        
        # Step 2: Check that stats are updated
        stats_response = client.get("/api/v1/stats", headers=api_headers)
        assert stats_response.status_code == 200
        
        stats = stats_response.json()
        assert stats["total_requests"] > 0
        
        # Step 3: Verify health check shows system is healthy
        health_response = client.get("/health")
        assert health_response.status_code == 200
        
        health_data = health_response.json()
        assert health_data["status"] == "healthy"
    
    def test_batch_optimization_workflow(self, client, api_headers):
        """Test batch optimization workflow"""
        batch_request = {
            "requests": [
                {
                    "prompt": "Create a Python function for data validation",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "e2e_test_user"
                },
                {
                    "prompt": "Design a REST API for user management",
                    "quality_threshold": 0.85,
                    "optimization_level": 2,
                    "user_id": "e2e_test_user"
                },
                {
                    "prompt": "Implement a caching layer for improved performance",
                    "quality_threshold": 0.8,
                    "optimization_level": 4,
                    "user_id": "e2e_test_user"
                }
            ],
            "parallel_processing": True
        }
        
        start_time = time.time()
        response = client.post(
            "/api/v1/optimize/batch",
            json=batch_request,
            headers=api_headers
        )
        end_time = time.time()
        
        assert response.status_code == 200
        batch_result = response.json()
        
        # Verify batch response structure
        assert "results" in batch_result
        assert "summary" in batch_result
        assert len(batch_result["results"]) == 3
        
        # Verify summary
        summary = batch_result["summary"]
        assert summary["total_requests"] == 3
        assert summary["successful_requests"] >= 0
        assert summary["failed_requests"] >= 0
        assert summary["successful_requests"] + summary["failed_requests"] == 3
        
        # Verify processing time is reasonable
        processing_time = end_time - start_time
        assert processing_time < 30  # Should complete within 30 seconds
    
    def test_authentication_and_authorization(self, client):
        """Test authentication and authorization workflow"""
        # Test without API key
        response = client.post(
            "/api/v1/optimize",
            json={
                "prompt": "Test prompt",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            }
        )
        assert response.status_code == 401
        
        # Test with invalid API key
        invalid_headers = {
            "Content-Type": "application/json",
            "X-API-Key": "invalid-key"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json={
                "prompt": "Test prompt",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            },
            headers=invalid_headers
        )
        assert response.status_code == 401
        
        # Test with valid API key
        valid_headers = {
            "Content-Type": "application/json",
            "X-API-Key": "demo-key-12345"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json={
                "prompt": "Test prompt",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            },
            headers=valid_headers
        )
        assert response.status_code == 200
    
    def test_rate_limiting_workflow(self, client, api_headers):
        """Test rate limiting functionality"""
        # Make multiple requests rapidly
        responses = []
        for i in range(10):
            response = client.post(
                "/api/v1/optimize",
                json={
                    "prompt": f"Test prompt {i}",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "rate_limit_test_user"
                },
                headers=api_headers
            )
            responses.append(response)
            
            # Small delay to avoid overwhelming the system
            time.sleep(0.1)
        
        # Most requests should succeed, but some might be rate limited
        success_count = sum(1 for r in responses if r.status_code == 200)
        rate_limited_count = sum(1 for r in responses if r.status_code == 429)
        
        # At least some requests should succeed
        assert success_count > 0
        
        # If rate limiting is active, check the response format
        for response in responses:
            if response.status_code == 429:
                error_data = response.json()
                assert "error" in error_data
                assert "retry_after" in error_data
    
    def test_error_handling_workflow(self, client, api_headers):
        """Test error handling across the system"""
        # Test with invalid request data
        invalid_requests = [
            # Missing required fields
            {
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "test_user"
            },
            # Invalid optimization level
            {
                "prompt": "Test prompt",
                "quality_threshold": 0.8,
                "optimization_level": 10,
                "user_id": "test_user"
            },
            # Invalid quality threshold
            {
                "prompt": "Test prompt",
                "quality_threshold": 1.5,
                "optimization_level": 3,
                "user_id": "test_user"
            }
        ]
        
        for invalid_request in invalid_requests:
            response = client.post(
                "/api/v1/optimize",
                json=invalid_request,
                headers=api_headers
            )
            
            # Should return validation error
            assert response.status_code == 422
            error_data = response.json()
            assert "detail" in error_data
    
    def test_monitoring_endpoints_workflow(self, client, api_headers, admin_headers):
        """Test monitoring and observability endpoints"""
        # Test metrics endpoint (should require admin)
        response = client.get("/metrics")
        assert response.status_code in [200, 401]  # May require auth
        
        # Test health endpoints
        health_response = client.get("/health")
        assert health_response.status_code == 200
        
        health_data = health_response.json()
        assert "status" in health_data
        assert "timestamp" in health_data
        assert "components" in health_data
        
        # Test readiness endpoint
        ready_response = client.get("/health/ready")
        assert ready_response.status_code in [200, 503]
        
        # Test liveness endpoint
        live_response = client.get("/health/live")
        assert live_response.status_code == 200
        
        # Test stats endpoint
        stats_response = client.get("/api/v1/stats", headers=api_headers)
        assert stats_response.status_code == 200
        
        stats = stats_response.json()
        required_stats = [
            "total_requests", "total_savings", "average_savings",
            "cache_hit_rate", "model_usage_distribution"
        ]
        
        for stat in required_stats:
            assert stat in stats
    
    def test_cache_behavior_workflow(self, client, api_headers):
        """Test caching behavior across requests"""
        # Make identical requests to test caching
        request_data = {
            "prompt": "Create a simple calculator function with basic operations",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "cache_test_user"
        }
        
        # First request
        start_time1 = time.time()
        response1 = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        end_time1 = time.time()
        
        assert response1.status_code == 200
        result1 = response1.json()
        processing_time1 = end_time1 - start_time1
        
        # Second identical request (should potentially hit cache)
        start_time2 = time.time()
        response2 = client.post(
            "/api/v1/optimize",
            json=request_data,
            headers=api_headers
        )
        end_time2 = time.time()
        
        assert response2.status_code == 200
        result2 = response2.json()
        processing_time2 = end_time2 - start_time2
        
        # Results should be consistent
        assert result1["optimized_prompt"] == result2["optimized_prompt"]
        assert result1["selected_model"] == result2["selected_model"]
        
        # If caching is working, second request might be faster
        # (though this is not guaranteed in all cases)
        assert processing_time2 <= processing_time1 * 2  # Allow some variance
    
    def test_concurrent_requests_workflow(self, client, api_headers):
        """Test system behavior under concurrent load"""
        import concurrent.futures
        import threading
        
        def make_request(prompt_suffix):
            """Make a single optimization request"""
            try:
                response = client.post(
                    "/api/v1/optimize",
                    json={
                        "prompt": f"Create a function for {prompt_suffix}",
                        "quality_threshold": 0.8,
                        "optimization_level": 3,
                        "user_id": f"concurrent_test_user_{prompt_suffix}"
                    },
                    headers=api_headers
                )
                return response.status_code, response.json() if response.status_code == 200 else None
            except Exception as e:
                return 500, str(e)
        
        # Make concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(make_request, f"task_{i}")
                for i in range(10)
            ]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        success_count = sum(1 for status, _ in results if status == 200)
        error_count = sum(1 for status, _ in results if status != 200)
        
        # Most requests should succeed
        assert success_count >= 7  # Allow for some failures under load
        
        # Successful responses should have valid structure
        for status, data in results:
            if status == 200 and data:
                assert "optimized_prompt" in data
                assert "savings_percentage" in data
                assert "quality_score" in data
