"""
End-to-End Cost Savings Validation
Comprehensive testing of cost optimization effectiveness with real metrics
"""

import pytest
import asyncio
import time
from decimal import Decimal
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock

from src.core.optimizer import CostOptimizer, OptimizationRequest, OptimizationResult
from src.monitoring.analytics import AnalyticsManager
from src.core.models import ModelConfig, ModelTier


class TestCostSavingsValidation:
    """End-to-end validation of cost savings and optimization effectiveness"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create optimizer for E2E testing"""
        optimizer = CostOptimizer()
        await optimizer.initialize()
        return optimizer
    
    @pytest.fixture
    def model_configs(self):
        """Model configurations with realistic pricing"""
        return {
            "premium": ModelConfig(
                name="anthropic/claude-3.5-sonnet",
                provider="anthropic",
                cost_per_token=Decimal("0.000015"),  # $15 per 1M tokens
                tier=ModelTier.PREMIUM,
                max_tokens=4096,
                quality_score=0.98
            ),
            "standard": ModelConfig(
                name="openai/gpt-4",
                provider="openai",
                cost_per_token=Decimal("0.00003"),   # $30 per 1M tokens
                tier=ModelTier.STANDARD,
                max_tokens=4096,
                quality_score=0.95
            ),
            "budget": ModelConfig(
                name="meta-llama/llama-3.1-8b-instruct",
                provider="meta",
                cost_per_token=Decimal("0.0000002"), # $0.2 per 1M tokens
                tier=ModelTier.BUDGET,
                max_tokens=2048,
                quality_score=0.85
            )
        }
    
    @pytest.fixture
    def test_scenarios(self):
        """Test scenarios with different complexity levels"""
        return [
            {
                "name": "simple_factual",
                "prompt": "What is the capital of France?",
                "expected_tokens": 10,
                "min_quality": 0.9,
                "complexity": "low"
            },
            {
                "name": "medium_analysis",
                "prompt": "Explain the economic impact of renewable energy adoption.",
                "expected_tokens": 200,
                "min_quality": 0.85,
                "complexity": "medium"
            },
            {
                "name": "complex_reasoning",
                "prompt": "Develop a comprehensive strategy for reducing carbon emissions in manufacturing while maintaining profitability.",
                "expected_tokens": 500,
                "min_quality": 0.9,
                "complexity": "high"
            },
            {
                "name": "code_generation",
                "prompt": "Write a Python function to implement a binary search tree with insert, delete, and search operations.",
                "expected_tokens": 300,
                "min_quality": 0.95,
                "complexity": "high"
            },
            {
                "name": "creative_writing",
                "prompt": "Write a short story about artificial intelligence discovering emotions.",
                "expected_tokens": 400,
                "min_quality": 0.8,
                "complexity": "medium"
            }
        ]
    
    @pytest.mark.asyncio
    async def test_cost_optimization_effectiveness(self, optimizer, model_configs, test_scenarios):
        """Test overall cost optimization effectiveness across scenarios"""
        
        cost_savings_report = {
            "total_baseline_cost": Decimal("0"),
            "total_optimized_cost": Decimal("0"),
            "total_savings": Decimal("0"),
            "optimization_ratio": 0.0,
            "scenarios": {}
        }
        
        for scenario in test_scenarios:
            scenario_name = scenario["name"]
            prompt = scenario["prompt"]
            expected_tokens = scenario["expected_tokens"]
            min_quality = scenario["min_quality"]
            
            # Calculate baseline cost (using most expensive model)
            baseline_model = model_configs["standard"]  # GPT-4 as baseline
            baseline_cost = baseline_model.cost_per_token * expected_tokens
            
            # Mock responses for different models
            mock_responses = {
                "premium": AsyncMock(
                    content=f"Premium response for {scenario_name}",
                    model=model_configs["premium"].name,
                    tokens_used=expected_tokens,
                    prompt_tokens=len(prompt.split()) * 2,
                    completion_tokens=expected_tokens - len(prompt.split()) * 2
                ),
                "standard": AsyncMock(
                    content=f"Standard response for {scenario_name}",
                    model=model_configs["standard"].name,
                    tokens_used=expected_tokens,
                    prompt_tokens=len(prompt.split()) * 2,
                    completion_tokens=expected_tokens - len(prompt.split()) * 2
                ),
                "budget": AsyncMock(
                    content=f"Budget response for {scenario_name}",
                    model=model_configs["budget"].name,
                    tokens_used=expected_tokens,
                    prompt_tokens=len(prompt.split()) * 2,
                    completion_tokens=expected_tokens - len(prompt.split()) * 2
                )
            }
            
            # Test optimization
            request = OptimizationRequest(
                prompt=prompt,
                max_tokens=expected_tokens * 2,
                temperature=0.7,
                quality_threshold=min_quality
            )
            
            # Mock model selection based on quality requirements
            if min_quality >= 0.95:
                selected_model = "premium"
            elif min_quality >= 0.9:
                selected_model = "standard"
            else:
                selected_model = "budget"
            
            with patch.object(optimizer.openrouter_client, 'generate', 
                            return_value=mock_responses[selected_model]):
                with patch.object(optimizer, '_select_optimal_model', 
                                return_value=model_configs[selected_model]):
                    
                    result = await optimizer.optimize(request)
                    
                    # Calculate actual costs
                    optimized_cost = model_configs[selected_model].cost_per_token * expected_tokens
                    savings = baseline_cost - optimized_cost
                    optimization_ratio = float(baseline_cost / optimized_cost) if optimized_cost > 0 else 0
                    
                    # Verify quality threshold met
                    assert result.quality_score >= min_quality
                    
                    # Record scenario results
                    scenario_results = {
                        "baseline_cost_usd": float(baseline_cost),
                        "optimized_cost_usd": float(optimized_cost),
                        "savings_usd": float(savings),
                        "optimization_ratio": optimization_ratio,
                        "quality_score": result.quality_score,
                        "model_used": result.model_used,
                        "tokens_used": result.tokens_used
                    }
                    
                    cost_savings_report["scenarios"][scenario_name] = scenario_results
                    cost_savings_report["total_baseline_cost"] += baseline_cost
                    cost_savings_report["total_optimized_cost"] += optimized_cost
        
        # Calculate overall metrics
        cost_savings_report["total_savings"] = (
            cost_savings_report["total_baseline_cost"] - 
            cost_savings_report["total_optimized_cost"]
        )
        cost_savings_report["optimization_ratio"] = float(
            cost_savings_report["total_baseline_cost"] / 
            cost_savings_report["total_optimized_cost"]
        ) if cost_savings_report["total_optimized_cost"] > 0 else 0
        
        # Print detailed cost savings report
        print("\n" + "="*80)
        print("COST OPTIMIZATION EFFECTIVENESS REPORT")
        print("="*80)
        print(f"Total Baseline Cost: ${float(cost_savings_report['total_baseline_cost']):.6f}")
        print(f"Total Optimized Cost: ${float(cost_savings_report['total_optimized_cost']):.6f}")
        print(f"Total Savings: ${float(cost_savings_report['total_savings']):.6f}")
        print(f"Overall Optimization Ratio: {cost_savings_report['optimization_ratio']:.2f}x")
        print(f"Cost Reduction Percentage: {((cost_savings_report['optimization_ratio'] - 1) * 100):.1f}%")
        print("\nScenario Breakdown:")
        print("-" * 80)
        
        for scenario_name, results in cost_savings_report["scenarios"].items():
            print(f"\n{scenario_name.upper()}:")
            print(f"  Baseline Cost: ${results['baseline_cost_usd']:.6f}")
            print(f"  Optimized Cost: ${results['optimized_cost_usd']:.6f}")
            print(f"  Savings: ${results['savings_usd']:.6f}")
            print(f"  Optimization Ratio: {results['optimization_ratio']:.2f}x")
            print(f"  Quality Score: {results['quality_score']:.3f}")
            print(f"  Model Used: {results['model_used']}")
        
        # Assertions for cost savings targets
        assert cost_savings_report["optimization_ratio"] >= 5.0  # At least 5x optimization
        assert float(cost_savings_report["total_savings"]) > 0  # Positive savings
        
        return cost_savings_report
    
    @pytest.mark.asyncio
    async def test_cache_impact_on_costs(self, optimizer, model_configs):
        """Test the impact of caching on cost savings"""
        
        # Test prompt that will be repeated
        prompt = "What are the benefits of renewable energy?"
        request = OptimizationRequest(
            prompt=prompt,
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.85
        )
        
        mock_response = AsyncMock(
            content="Renewable energy provides environmental and economic benefits...",
            model=model_configs["budget"].name,
            tokens_used=80,
            prompt_tokens=15,
            completion_tokens=65
        )
        
        cache_cost_analysis = {
            "first_request": {},
            "cached_requests": {},
            "total_requests": 100,
            "cache_savings": Decimal("0")
        }
        
        # First request (cache miss)
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_response):
            with patch.object(optimizer, '_select_optimal_model', return_value=model_configs["budget"]):
                start_time = time.time()
                result = await optimizer.optimize(request)
                first_request_time = time.time() - start_time
                
                first_request_cost = model_configs["budget"].cost_per_token * 80
                cache_cost_analysis["first_request"] = {
                    "cost_usd": float(first_request_cost),
                    "response_time_ms": first_request_time * 1000,
                    "cache_hit": result.cache_hit
                }
        
        # Subsequent requests (cache hits)
        cache_hit_times = []
        for i in range(99):  # 99 more requests
            start_time = time.time()
            result = await optimizer.optimize(request)
            cache_hit_time = time.time() - start_time
            cache_hit_times.append(cache_hit_time * 1000)
            
            # Cache hits should have zero API cost
            assert result.cache_hit is True
        
        # Calculate cache savings
        api_cost_per_request = first_request_cost
        total_api_cost_without_cache = api_cost_per_request * 100
        total_api_cost_with_cache = api_cost_per_request * 1  # Only first request
        cache_savings = total_api_cost_without_cache - total_api_cost_with_cache
        
        cache_cost_analysis["cached_requests"] = {
            "average_response_time_ms": sum(cache_hit_times) / len(cache_hit_times),
            "total_api_cost_saved_usd": float(cache_savings),
            "cache_hit_rate": 99/100
        }
        cache_cost_analysis["cache_savings"] = cache_savings
        
        print("\n" + "="*60)
        print("CACHE IMPACT ON COST SAVINGS")
        print("="*60)
        print(f"Total Requests: {cache_cost_analysis['total_requests']}")
        print(f"First Request Cost: ${cache_cost_analysis['first_request']['cost_usd']:.6f}")
        print(f"First Request Time: {cache_cost_analysis['first_request']['response_time_ms']:.2f}ms")
        print(f"Average Cache Hit Time: {cache_cost_analysis['cached_requests']['average_response_time_ms']:.2f}ms")
        print(f"Cache Hit Rate: {cache_cost_analysis['cached_requests']['cache_hit_rate']*100:.1f}%")
        print(f"Total API Cost Saved: ${cache_cost_analysis['cached_requests']['total_api_cost_saved_usd']:.6f}")
        print(f"Cost Reduction from Caching: {(float(cache_savings) / float(total_api_cost_without_cache) * 100):.1f}%")
        
        # Assertions
        assert cache_cost_analysis["cached_requests"]["cache_hit_rate"] >= 0.99  # 99% cache hit rate
        assert cache_cost_analysis["cached_requests"]["average_response_time_ms"] < 10  # <10ms cache hits
        assert float(cache_savings) > 0  # Positive cache savings
        
        return cache_cost_analysis
    
    @pytest.mark.asyncio
    async def test_semantic_similarity_cost_impact(self, optimizer, model_configs):
        """Test cost impact of semantic similarity caching"""
        
        # Related prompts that should trigger semantic similarity
        similar_prompts = [
            "What is the capital of France?",
            "What's the capital city of France?",
            "Tell me France's capital",
            "Which city is the capital of France?",
            "France capital city name?"
        ]
        
        mock_response = AsyncMock(
            content="Paris is the capital of France.",
            model=model_configs["budget"].name,
            tokens_used=25,
            prompt_tokens=8,
            completion_tokens=17
        )
        
        semantic_cost_analysis = {
            "original_request_cost": Decimal("0"),
            "semantic_matches": 0,
            "total_api_calls": 0,
            "semantic_savings": Decimal("0")
        }
        
        # First request (original)
        request = OptimizationRequest(
            prompt=similar_prompts[0],
            max_tokens=50,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_response):
            with patch.object(optimizer, '_select_optimal_model', return_value=model_configs["budget"]):
                result = await optimizer.optimize(request)
                
                original_cost = model_configs["budget"].cost_per_token * 25
                semantic_cost_analysis["original_request_cost"] = original_cost
                semantic_cost_analysis["total_api_calls"] = 1
                
                assert result.cache_hit is False
        
        # Test similar prompts
        for prompt in similar_prompts[1:]:
            request = OptimizationRequest(
                prompt=prompt,
                max_tokens=50,
                temperature=0.7
            )
            
            result = await optimizer.optimize(request)
            
            if result.cache_hit:
                semantic_cost_analysis["semantic_matches"] += 1
            else:
                semantic_cost_analysis["total_api_calls"] += 1
        
        # Calculate semantic similarity savings
        total_requests = len(similar_prompts)
        api_calls_saved = total_requests - semantic_cost_analysis["total_api_calls"]
        semantic_savings = original_cost * api_calls_saved
        semantic_cost_analysis["semantic_savings"] = semantic_savings
        
        print("\n" + "="*60)
        print("SEMANTIC SIMILARITY COST IMPACT")
        print("="*60)
        print(f"Total Similar Prompts: {total_requests}")
        print(f"Semantic Matches Found: {semantic_cost_analysis['semantic_matches']}")
        print(f"Total API Calls Made: {semantic_cost_analysis['total_api_calls']}")
        print(f"API Calls Saved: {api_calls_saved}")
        print(f"Cost per API Call: ${float(original_cost):.6f}")
        print(f"Total Semantic Savings: ${float(semantic_savings):.6f}")
        print(f"Semantic Hit Rate: {(semantic_cost_analysis['semantic_matches']/total_requests)*100:.1f}%")
        
        # Assertions
        assert semantic_cost_analysis["semantic_matches"] >= 2  # At least 50% semantic hit rate
        assert float(semantic_savings) > 0  # Positive semantic savings
        
        return semantic_cost_analysis
    
    @pytest.mark.asyncio
    async def test_quality_vs_cost_tradeoffs(self, optimizer, model_configs):
        """Test quality vs cost tradeoffs in model selection"""
        
        quality_thresholds = [0.7, 0.8, 0.85, 0.9, 0.95]
        prompt = "Explain quantum computing principles and applications."
        
        quality_cost_analysis = {
            "quality_levels": {},
            "cost_quality_efficiency": {}
        }
        
        for quality_threshold in quality_thresholds:
            request = OptimizationRequest(
                prompt=prompt,
                max_tokens=300,
                temperature=0.7,
                quality_threshold=quality_threshold
            )
            
            # Mock model selection based on quality threshold
            if quality_threshold >= 0.95:
                selected_model = model_configs["premium"]
                mock_quality = 0.98
            elif quality_threshold >= 0.85:
                selected_model = model_configs["standard"]
                mock_quality = 0.92
            else:
                selected_model = model_configs["budget"]
                mock_quality = 0.82
            
            mock_response = AsyncMock(
                content="Quantum computing explanation...",
                model=selected_model.name,
                tokens_used=250,
                prompt_tokens=50,
                completion_tokens=200
            )
            
            with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_response):
                with patch.object(optimizer, '_select_optimal_model', return_value=selected_model):
                    with patch.object(optimizer, '_assess_quality', return_value=mock_quality):
                        
                        result = await optimizer.optimize(request)
                        
                        cost = selected_model.cost_per_token * 250
                        efficiency = mock_quality / float(cost) if cost > 0 else 0
                        
                        quality_cost_analysis["quality_levels"][quality_threshold] = {
                            "model_used": selected_model.name,
                            "cost_usd": float(cost),
                            "quality_achieved": mock_quality,
                            "quality_efficiency": efficiency
                        }
        
        print("\n" + "="*60)
        print("QUALITY VS COST TRADEOFF ANALYSIS")
        print("="*60)
        
        for threshold, analysis in quality_cost_analysis["quality_levels"].items():
            print(f"\nQuality Threshold: {threshold}")
            print(f"  Model Selected: {analysis['model_used']}")
            print(f"  Cost: ${analysis['cost_usd']:.6f}")
            print(f"  Quality Achieved: {analysis['quality_achieved']:.3f}")
            print(f"  Quality/Cost Efficiency: {analysis['quality_efficiency']:.2f}")
        
        # Verify quality thresholds are met
        for threshold, analysis in quality_cost_analysis["quality_levels"].items():
            assert analysis["quality_achieved"] >= threshold
        
        return quality_cost_analysis
