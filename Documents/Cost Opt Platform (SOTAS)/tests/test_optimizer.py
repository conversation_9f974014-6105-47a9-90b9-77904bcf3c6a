"""
Cost Optimizer Unit Tests
Comprehensive testing for the core optimization engine
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import Mock, AsyncMock, patch
import time

from src.core.optimizer import CostOptimizer, OptimizationCircuitBreaker, OptimizationLayerState
from src.core.models import (
    OptimizationRequest, OptimizationResponse, OptimizationLevel, 
    TaskComplexity, CacheHitType
)
from tests.test_framework import MockServices, TestMetrics


class TestOptimizationCircuitBreaker:
    """Test circuit breaker functionality"""
    
    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization"""
        cb = OptimizationCircuitBreaker(failure_threshold=5, recovery_timeout=60)
        
        assert cb.failure_threshold == 5
        assert cb.recovery_timeout == 60
        assert cb.failure_count == 0
        assert cb.state == OptimizationLayerState.HEALTHY
        assert cb.can_execute() is True
    
    def test_circuit_breaker_failure_tracking(self):
        """Test failure tracking and state transitions"""
        cb = OptimizationCircuitBreaker(failure_threshold=3, recovery_timeout=60)
        
        # Record failures
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.state == OptimizationLayerState.HEALTHY
        assert cb.can_execute() is True
        
        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.state == OptimizationLayerState.DEGRADED
        assert cb.can_execute() is True
        
        cb.record_failure()
        assert cb.failure_count == 3
        assert cb.state == OptimizationLayerState.FAILED
        assert cb.can_execute() is False
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery"""
        cb = OptimizationCircuitBreaker(failure_threshold=2, recovery_timeout=1)
        
        # Trigger failure state
        cb.record_failure()
        cb.record_failure()
        assert cb.state == OptimizationLayerState.FAILED
        assert cb.can_execute() is False
        
        # Wait for recovery timeout
        time.sleep(1.1)
        
        # Should allow execution after timeout
        assert cb.can_execute() is True
        assert cb.state == OptimizationLayerState.DEGRADED
        
        # Record success to fully recover
        cb.record_success()
        assert cb.state == OptimizationLayerState.HEALTHY
        assert cb.failure_count == 0


class TestCostOptimizer:
    """Test cost optimizer functionality"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create optimizer with mocked services"""
        optimizer = CostOptimizer()
        
        # Mock services
        optimizer.cache_manager = MockServices.create_mock_cache_manager()
        optimizer.model_router = MockServices.create_mock_model_router()
        optimizer.compression_engine = MockServices.create_mock_compression_engine()
        optimizer.quality_assessor = AsyncMock()
        optimizer.adaptive_learner = AsyncMock()
        
        # Mock quality assessor
        optimizer.quality_assessor.assess_quality.return_value = 0.92
        
        # Mock adaptive learner
        optimizer.adaptive_learner.learn_from_optimization.return_value = None
        optimizer.adaptive_learner.get_optimization_suggestions.return_value = {}
        
        return optimizer
    
    @pytest.mark.asyncio
    async def test_basic_optimization(self, optimizer):
        """Test basic optimization functionality"""
        request = OptimizationRequest(
            prompt="Test prompt for optimization",
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8,
            user_id="test_user"
        )
        
        response = await optimizer.optimize(request)
        
        # Validate response structure
        assert isinstance(response, OptimizationResponse)
        assert response.optimized_prompt is not None
        assert response.selected_model is not None
        assert response.savings_percentage >= 0
        assert response.quality_score >= 0
        assert response.processing_time_ms > 0
        
        # Validate that services were called
        optimizer.model_router.route_request.assert_called_once()
        optimizer.compression_engine.compress.assert_called_once()
        optimizer.quality_assessor.assess_quality.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_hit_optimization(self, optimizer):
        """Test optimization with cache hit"""
        # Configure cache to return a hit
        cache_response = {
            'optimized_prompt': 'Cached optimized prompt',
            'selected_model': 'cached-model',
            'original_cost': 0.01,
            'quality_score': 0.95
        }
        optimizer.cache_manager.get_cached_result.return_value = (
            CacheHitType.REDIS, cache_response
        )
        
        request = OptimizationRequest(
            prompt="Test prompt for cache hit",
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        response = await optimizer.optimize(request)
        
        # Validate cache hit response
        assert response.cache_hit is True
        assert response.cache_hit_type == CacheHitType.REDIS
        assert response.optimized_cost == 0.0  # Cache hits are free
        assert response.savings_percentage == 99.0
        assert response.optimized_prompt == cache_response['optimized_prompt']
        
        # Verify that expensive operations were skipped
        optimizer.model_router.route_request.assert_not_called()
        optimizer.compression_engine.compress.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self, optimizer):
        """Test circuit breaker integration"""
        # Configure cache to fail
        optimizer.cache_manager.get_cached_result.side_effect = Exception("Cache failure")
        
        request = OptimizationRequest(
            prompt="Test circuit breaker",
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        # First few requests should trigger circuit breaker
        for i in range(5):
            response = await optimizer.optimize(request)
            # Should still complete despite cache failures
            assert isinstance(response, OptimizationResponse)
        
        # Check that circuit breaker state changed
        cache_cb = optimizer.circuit_breakers['cache']
        assert cache_cb.failure_count > 0
    
    @pytest.mark.asyncio
    async def test_optimization_levels(self, optimizer):
        """Test different optimization levels"""
        base_prompt = "Test prompt for optimization level testing"
        
        levels = [
            OptimizationLevel.CONSERVATIVE,
            OptimizationLevel.MODERATE,
            OptimizationLevel.AGGRESSIVE
        ]
        
        responses = []
        for level in levels:
            request = OptimizationRequest(
                prompt=base_prompt,
                optimization_level=level,
                quality_threshold=0.8
            )
            response = await optimizer.optimize(request)
            responses.append(response)
        
        # Validate that all levels work
        for response in responses:
            assert isinstance(response, OptimizationResponse)
            assert response.quality_score >= 0.8
        
        # More aggressive optimization should generally achieve higher savings
        # (though this depends on the specific implementation)
        conservative_savings = responses[0].savings_percentage
        aggressive_savings = responses[2].savings_percentage
        
        # At minimum, aggressive should not be worse than conservative
        assert aggressive_savings >= conservative_savings * 0.9
    
    @pytest.mark.asyncio
    async def test_quality_threshold_enforcement(self, optimizer):
        """Test quality threshold enforcement"""
        # Configure quality assessor to return low quality
        optimizer.quality_assessor.assess_quality.return_value = 0.6
        
        request = OptimizationRequest(
            prompt="Test quality threshold",
            optimization_level=OptimizationLevel.AGGRESSIVE,
            quality_threshold=0.8  # Higher than what assessor returns
        )
        
        response = await optimizer.optimize(request)
        
        # Should still complete but may use fallback strategies
        assert isinstance(response, OptimizationResponse)
        # Quality score might be lower than threshold if no good options available
        assert response.quality_score >= 0.0
    
    @pytest.mark.asyncio
    async def test_task_complexity_detection(self, optimizer):
        """Test task complexity detection"""
        test_cases = [
            ("Simple prompt", TaskComplexity.SIMPLE),
            ("This is a more complex prompt with multiple sentences and detailed requirements.", TaskComplexity.MEDIUM),
            ("This is a highly complex prompt with extensive requirements, multiple constraints, detailed specifications, and complex logic that requires sophisticated processing.", TaskComplexity.COMPLEX)
        ]
        
        for prompt, expected_complexity in test_cases:
            request = OptimizationRequest(
                prompt=prompt,
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8
            )
            
            response = await optimizer.optimize(request)
            assert response.task_complexity == expected_complexity
    
    @pytest.mark.asyncio
    async def test_error_handling(self, optimizer):
        """Test error handling in optimization pipeline"""
        # Configure model router to fail
        optimizer.model_router.route_request.side_effect = Exception("Model routing failed")
        
        request = OptimizationRequest(
            prompt="Test error handling",
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        # Should handle error gracefully and potentially use fallbacks
        response = await optimizer.optimize(request)
        
        # Should still return a response (possibly with fallback model)
        assert isinstance(response, OptimizationResponse)
    
    @pytest.mark.asyncio
    async def test_concurrent_optimization(self, optimizer):
        """Test concurrent optimization requests"""
        requests = [
            OptimizationRequest(
                prompt=f"Concurrent test prompt {i}",
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8,
                user_id=f"user_{i}"
            )
            for i in range(10)
        ]
        
        # Execute all requests concurrently
        tasks = [optimizer.optimize(request) for request in requests]
        responses = await asyncio.gather(*tasks)
        
        # Validate all responses
        assert len(responses) == 10
        for response in responses:
            assert isinstance(response, OptimizationResponse)
            assert response.quality_score >= 0
    
    @pytest.mark.asyncio
    async def test_optimization_statistics(self, optimizer):
        """Test optimization statistics collection"""
        # Perform several optimizations
        for i in range(5):
            request = OptimizationRequest(
                prompt=f"Stats test prompt {i}",
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8
            )
            await optimizer.optimize(request)
        
        # Get statistics
        stats = await optimizer.get_optimization_stats()
        
        # Validate statistics structure
        assert 'total_optimizations' in stats
        assert 'cache_hit_rate' in stats
        assert 'average_savings_percentage' in stats
        assert 'average_quality_score' in stats
        assert 'model_usage' in stats
        
        # Validate statistics values
        assert stats['total_optimizations'] >= 5
        assert 0 <= stats['cache_hit_rate'] <= 1
        assert stats['average_savings_percentage'] >= 0
        assert 0 <= stats['average_quality_score'] <= 1


class TestOptimizationPerformance:
    """Performance tests for optimization engine"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create optimizer for performance testing"""
        optimizer = CostOptimizer()
        
        # Use faster mock services for performance testing
        optimizer.cache_manager = MockServices.create_mock_cache_manager()
        optimizer.model_router = MockServices.create_mock_model_router()
        optimizer.compression_engine = MockServices.create_mock_compression_engine()
        optimizer.quality_assessor = AsyncMock()
        optimizer.adaptive_learner = AsyncMock()
        
        # Configure for fast responses
        optimizer.quality_assessor.assess_quality.return_value = 0.9
        
        return optimizer
    
    @pytest.mark.asyncio
    async def test_optimization_latency(self, optimizer):
        """Test optimization latency"""
        request = OptimizationRequest(
            prompt="Performance test prompt",
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        # Measure latency
        start_time = time.time()
        response = await optimizer.optimize(request)
        latency_ms = (time.time() - start_time) * 1000
        
        # Validate response
        assert isinstance(response, OptimizationResponse)
        
        # Validate latency (should be under 50ms for mocked services)
        assert latency_ms < 50, f"Optimization latency {latency_ms:.2f}ms exceeds 50ms threshold"
    
    @pytest.mark.asyncio
    async def test_throughput(self, optimizer):
        """Test optimization throughput"""
        num_requests = 100
        start_time = time.time()
        
        # Generate concurrent requests
        tasks = []
        for i in range(num_requests):
            request = OptimizationRequest(
                prompt=f"Throughput test {i}",
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8
            )
            tasks.append(optimizer.optimize(request))
        
        # Execute all requests
        responses = await asyncio.gather(*tasks)
        
        duration = time.time() - start_time
        throughput = num_requests / duration
        
        # Validate all responses
        assert len(responses) == num_requests
        for response in responses:
            assert isinstance(response, OptimizationResponse)
        
        # Validate throughput (should handle at least 500 RPS with mocked services)
        assert throughput >= 500, f"Throughput {throughput:.2f} RPS below 500 RPS threshold"
