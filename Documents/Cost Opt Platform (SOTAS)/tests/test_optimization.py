"""
Test optimization endpoints and core functionality
"""

import pytest
from fastapi.testclient import TestClient


class TestOptimizationEndpoints:
    """Test optimization API endpoints"""
    
    def test_optimize_simple_request(self, client: TestClient, api_headers: dict, sample_optimization_request: dict):
        """Test basic optimization request"""
        
        response = client.post(
            "/api/v1/optimize",
            json=sample_optimization_request.dict(),
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate response structure
        assert "optimized_prompt" in data
        assert "selected_model" in data
        assert "savings_percentage" in data
        assert "quality_score" in data
        assert "processing_time_ms" in data
        
        # Validate savings
        assert data["savings_percentage"] > 0
        assert data["quality_score"] >= sample_optimization_request.quality_threshold
    
    def test_optimize_complex_request(self, client: TestClient, api_headers: dict, complex_optimization_request: dict):
        """Test optimization with complex prompt"""
        
        response = client.post(
            "/api/v1/optimize", 
            json=complex_optimization_request.dict(),
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Complex requests should still achieve good savings
        assert data["savings_percentage"] > 50
        assert data["quality_score"] >= complex_optimization_request.quality_threshold
        
        # Should have compression for complex requests
        assert "compression_ratio" in data
    
    def test_optimize_without_api_key(self, client: TestClient, sample_optimization_request: dict):
        """Test optimization request without API key"""
        
        response = client.post(
            "/api/v1/optimize",
            json=sample_optimization_request.dict()
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "error" in data
        assert data["error"] == "missing_api_key"
    
    def test_optimize_invalid_request(self, client: TestClient, api_headers: dict):
        """Test optimization with invalid request data"""
        
        invalid_request = {
            "prompt": "",  # Empty prompt
            "quality_threshold": 1.5,  # Invalid threshold
            "optimization_level": 10  # Invalid level
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=invalid_request,
            headers=api_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_optimize_quality_threshold_enforcement(self, client: TestClient, api_headers: dict):
        """Test that quality threshold is enforced"""
        
        high_quality_request = {
            "prompt": "Create a simple function",
            "quality_threshold": 0.95,  # Very high threshold
            "optimization_level": 3
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=high_quality_request,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should meet the high quality threshold
        assert data["quality_score"] >= 0.95
    
    def test_batch_optimization(self, client: TestClient, api_headers: dict):
        """Test batch optimization endpoint"""
        
        batch_request = {
            "requests": [
                {
                    "prompt": "Create a hello world function",
                    "quality_threshold": 0.8,
                    "optimization_level": 3
                },
                {
                    "prompt": "Write a simple API endpoint",
                    "quality_threshold": 0.8,
                    "optimization_level": 3
                }
            ],
            "parallel_processing": True
        }
        
        response = client.post(
            "/api/v1/optimize/batch",
            json=batch_request,
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate batch response
        assert "results" in data
        assert "total_savings" in data
        assert "success_count" in data
        assert "failure_count" in data
        
        assert len(data["results"]) == 2
        assert data["success_count"] >= 0
        assert data["total_savings"] >= 0
    
    def test_optimization_stats(self, client: TestClient, api_headers: dict):
        """Test optimization statistics endpoint"""
        
        response = client.get(
            "/api/v1/optimize/stats",
            headers=api_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate stats structure
        expected_fields = [
            "total_requests",
            "total_savings", 
            "average_savings",
            "cache_hit_rate",
            "model_usage_distribution"
        ]
        
        for field in expected_fields:
            assert field in data


class TestOptimizationLogic:
    """Test optimization logic and algorithms"""
    
    @pytest.mark.asyncio
    async def test_model_selection_logic(self, mock_optimizer):
        """Test model selection based on task complexity"""
        
        from src.core.models import OptimizationRequest, TaskComplexity
        
        # Simple task should prefer free models
        simple_request = OptimizationRequest(
            prompt="What is 2+2?",
            quality_threshold=0.7
        )
        
        response = await mock_optimizer.optimize(simple_request)
        
        # Should achieve high savings (free model)
        assert response.savings_percentage >= 90
    
    @pytest.mark.asyncio
    async def test_compression_effectiveness(self, mock_optimizer):
        """Test compression effectiveness"""
        
        from src.core.models import OptimizationRequest
        
        # Long prompt should benefit from compression
        long_request = OptimizationRequest(
            prompt="Create a comprehensive technical specification document that includes detailed requirements, architecture diagrams, implementation guidelines, testing procedures, deployment instructions, and maintenance protocols for a large-scale distributed system.",
            quality_threshold=0.85
        )
        
        response = await mock_optimizer.optimize(long_request)
        
        # Should have significant compression
        assert response.compression_ratio > 0.3
        assert response.savings_percentage > 50
    
    @pytest.mark.asyncio
    async def test_quality_preservation(self, mock_optimizer):
        """Test that quality is preserved during optimization"""
        
        from src.core.models import OptimizationRequest
        
        request = OptimizationRequest(
            prompt="Create a production-ready API with authentication",
            quality_threshold=0.9
        )
        
        response = await mock_optimizer.optimize(request)
        
        # Quality should meet threshold
        assert response.quality_score >= request.quality_threshold
    
    @pytest.mark.asyncio
    async def test_cache_behavior(self, mock_cache_manager):
        """Test caching behavior"""
        
        # Test cache miss
        result = await mock_cache_manager.get_cached_result("test prompt")
        assert result is None
        
        # Test cache storage
        test_data = {"optimized_prompt": "test", "cost": 0.01}
        await mock_cache_manager.cache_result("test prompt", test_data)
        
        # Verify no exceptions
        assert True


class TestPerformanceRequirements:
    """Test performance requirements are met"""
    
    def test_response_time_requirement(self, client: TestClient, api_headers: dict, sample_optimization_request: dict):
        """Test that response time is under 100ms requirement"""
        
        import time
        
        start_time = time.time()
        
        response = client.post(
            "/api/v1/optimize",
            json=sample_optimization_request.dict(),
            headers=api_headers
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == 200
        
        # Should meet <100ms requirement (allowing some overhead for test environment)
        assert response_time_ms < 200  # Relaxed for test environment
        
        # Check reported processing time
        data = response.json()
        assert data["processing_time_ms"] < 100
    
    def test_concurrent_requests(self, client: TestClient, api_headers: dict):
        """Test handling of concurrent requests"""
        
        import concurrent.futures
        import threading
        
        def make_request():
            request_data = {
                "prompt": f"Test prompt {threading.current_thread().ident}",
                "quality_threshold": 0.8
            }
            
            response = client.post(
                "/api/v1/optimize",
                json=request_data,
                headers=api_headers
            )
            return response.status_code == 200
        
        # Test 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        assert all(results)
        assert len(results) == 10


class TestErrorHandling:
    """Test error handling and edge cases"""
    
    def test_invalid_api_key(self, client: TestClient, sample_optimization_request: dict):
        """Test handling of invalid API key"""
        
        invalid_headers = {
            "Content-Type": "application/json",
            "X-API-Key": "invalid-key"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=sample_optimization_request.dict(),
            headers=invalid_headers
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "error" in data
        assert data["error"] == "invalid_api_key"
    
    def test_rate_limiting(self, client: TestClient, api_headers: dict, sample_optimization_request: dict):
        """Test rate limiting behavior"""
        
        # Make multiple rapid requests
        responses = []
        for _ in range(5):
            response = client.post(
                "/api/v1/optimize",
                json=sample_optimization_request.dict(),
                headers=api_headers
            )
            responses.append(response)
        
        # Most should succeed, but rate limiting might kick in
        success_count = sum(1 for r in responses if r.status_code == 200)
        rate_limited_count = sum(1 for r in responses if r.status_code == 429)
        
        # Should have at least some successful requests
        assert success_count > 0
        
        # If rate limited, should have proper error response
        for response in responses:
            if response.status_code == 429:
                data = response.json()
                assert "error" in data
                assert "retry_after" in data
    
    def test_large_prompt_handling(self, client: TestClient, api_headers: dict):
        """Test handling of very large prompts"""
        
        # Create a very large prompt (close to limit)
        large_prompt = "Create a detailed specification. " * 1000  # ~30KB
        
        large_request = {
            "prompt": large_prompt,
            "quality_threshold": 0.8,
            "optimization_level": 3
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=large_request,
            headers=api_headers
        )
        
        # Should either succeed or fail gracefully
        assert response.status_code in [200, 400, 413]  # OK, Bad Request, or Payload Too Large
        
        if response.status_code == 200:
            data = response.json()
            # Should achieve good compression on large prompts
            assert data["compression_ratio"] > 0.5
