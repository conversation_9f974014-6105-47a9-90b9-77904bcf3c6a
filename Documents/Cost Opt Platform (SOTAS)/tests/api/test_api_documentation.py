"""
API Documentation Tests
Comprehensive testing of API documentation and examples
FAANG+ standards for API documentation validation
"""

import pytest
import requests
import json
from typing import Dict, Any
from fastapi.testclient import TestClient

from src.main import app


class TestAPIDocumentation:
    """Test suite for API documentation completeness and accuracy"""
    
    @pytest.fixture
    def client(self):
        """Test client for API testing"""
        return TestClient(app)
    
    @pytest.fixture
    def api_headers(self):
        """API headers for testing"""
        return {
            "Content-Type": "application/json",
            "X-API-Key": "demo-key-12345"
        }
    
    def test_openapi_spec_available(self, client):
        """Test that OpenAPI specification is available"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        spec = response.json()
        assert "openapi" in spec
        assert "info" in spec
        assert "paths" in spec
        
        # Verify basic info
        info = spec["info"]
        assert "title" in info
        assert "description" in info
        assert "version" in info
        assert "contact" in info
        assert "license_info" in info
    
    def test_docs_ui_available(self, client):
        """Test that documentation UI is available"""
        # Test Swagger UI
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # Test ReDoc
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_api_endpoints_documented(self, client):
        """Test that all API endpoints are properly documented"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        paths = spec.get("paths", {})
        
        # Core endpoints should be documented
        expected_endpoints = [
            "/api/v1/optimize",
            "/api/v1/optimize/batch",
            "/api/v1/stats",
            "/health",
            "/health/ready",
            "/health/live"
        ]
        
        for endpoint in expected_endpoints:
            assert endpoint in paths, f"Endpoint {endpoint} not documented"
            
            # Check that endpoint has proper documentation
            endpoint_spec = paths[endpoint]
            for method, method_spec in endpoint_spec.items():
                if method in ["get", "post", "put", "delete", "patch"]:
                    assert "summary" in method_spec, f"{method.upper()} {endpoint} missing summary"
                    assert "description" in method_spec, f"{method.upper()} {endpoint} missing description"
                    assert "responses" in method_spec, f"{method.upper()} {endpoint} missing responses"
    
    def test_request_models_documented(self, client):
        """Test that request models are properly documented"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        components = spec.get("components", {})
        schemas = components.get("schemas", {})
        
        # Core models should be documented
        expected_models = [
            "OptimizationRequest",
            "OptimizationResponse",
            "BatchOptimizationRequest",
            "BatchOptimizationResponse"
        ]
        
        for model in expected_models:
            assert model in schemas, f"Model {model} not documented"
            
            model_schema = schemas[model]
            assert "type" in model_schema
            assert "properties" in model_schema
            
            # Check that properties have descriptions
            properties = model_schema.get("properties", {})
            for prop_name, prop_spec in properties.items():
                assert "description" in prop_spec or "title" in prop_spec, \
                    f"Property {prop_name} in {model} missing description"
    
    def test_response_examples_present(self, client):
        """Test that response examples are present in documentation"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        paths = spec.get("paths", {})
        
        for path, methods in paths.items():
            for method, method_spec in methods.items():
                if method in ["post", "get"]:
                    responses = method_spec.get("responses", {})
                    
                    # Check 200 response has example
                    if "200" in responses:
                        response_spec = responses["200"]
                        content = response_spec.get("content", {})
                        
                        if "application/json" in content:
                            json_content = content["application/json"]
                            assert "example" in json_content or "examples" in json_content, \
                                f"{method.upper()} {path} 200 response missing example"
    
    def test_error_responses_documented(self, client):
        """Test that error responses are properly documented"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        paths = spec.get("paths", {})
        
        for path, methods in paths.items():
            for method, method_spec in methods.items():
                if method in ["post", "get"]:
                    responses = method_spec.get("responses", {})
                    
                    # Should have error responses documented
                    error_codes = ["400", "401", "429", "500"]
                    documented_errors = [code for code in error_codes if code in responses]
                    
                    assert len(documented_errors) > 0, \
                        f"{method.upper()} {path} missing error response documentation"
                    
                    # Check error responses have proper structure
                    for error_code in documented_errors:
                        error_response = responses[error_code]
                        assert "description" in error_response
                        
                        content = error_response.get("content", {})
                        if "application/json" in content:
                            json_content = content["application/json"]
                            assert "example" in json_content, \
                                f"{method.upper()} {path} {error_code} response missing example"
    
    def test_authentication_documented(self, client):
        """Test that authentication is properly documented"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        # Check security schemes
        components = spec.get("components", {})
        security_schemes = components.get("securitySchemes", {})
        
        assert len(security_schemes) > 0, "No security schemes documented"
        
        # Should have API key authentication
        api_key_schemes = [
            scheme for scheme in security_schemes.values()
            if scheme.get("type") == "apiKey"
        ]
        assert len(api_key_schemes) > 0, "API key authentication not documented"
    
    def test_rate_limiting_documented(self, client):
        """Test that rate limiting is documented"""
        response = client.get("/openapi.json")
        spec = response.json()
        
        # Check for rate limiting in responses
        paths = spec.get("paths", {})
        
        rate_limit_documented = False
        for path, methods in paths.items():
            for method, method_spec in methods.items():
                responses = method_spec.get("responses", {})
                if "429" in responses:
                    rate_limit_documented = True
                    
                    # Check 429 response is properly documented
                    rate_limit_response = responses["429"]
                    assert "description" in rate_limit_response
                    assert "rate limit" in rate_limit_response["description"].lower()
        
        assert rate_limit_documented, "Rate limiting not documented"
    
    def test_api_examples_work(self, client, api_headers):
        """Test that documented API examples actually work"""
        # Test optimization endpoint example
        optimization_example = {
            "prompt": "Create a Python function that calculates factorial",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "test_user"
        }
        
        response = client.post(
            "/api/v1/optimize",
            json=optimization_example,
            headers=api_headers
        )
        
        # Should work or return a documented error
        assert response.status_code in [200, 400, 401, 429, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Verify response matches documented structure
            expected_fields = [
                "optimized_prompt", "selected_model", "original_cost",
                "optimized_cost", "savings_percentage", "quality_score",
                "processing_time_ms", "cache_hit", "task_complexity"
            ]
            
            for field in expected_fields:
                assert field in result, f"Response missing documented field: {field}"
    
    def test_batch_api_examples_work(self, client, api_headers):
        """Test that batch API examples work"""
        batch_example = {
            "requests": [
                {
                    "prompt": "Create a Python function for sorting",
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": "test_user"
                }
            ],
            "parallel_processing": True
        }
        
        response = client.post(
            "/api/v1/optimize/batch",
            json=batch_example,
            headers=api_headers
        )
        
        # Should work or return a documented error
        assert response.status_code in [200, 400, 401, 429, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Verify response matches documented structure
            assert "results" in result
            assert "summary" in result
            
            summary = result["summary"]
            expected_summary_fields = [
                "total_requests", "successful_requests", "failed_requests"
            ]
            
            for field in expected_summary_fields:
                assert field in summary, f"Summary missing documented field: {field}"
    
    def test_health_endpoints_documented(self, client):
        """Test that health endpoints are documented and work"""
        health_endpoints = ["/health", "/health/ready", "/health/live"]
        
        for endpoint in health_endpoints:
            # Check documentation
            response = client.get("/openapi.json")
            spec = response.json()
            paths = spec.get("paths", {})
            
            assert endpoint in paths, f"Health endpoint {endpoint} not documented"
            
            # Check endpoint works
            response = client.get(endpoint)
            assert response.status_code in [200, 503], f"Health endpoint {endpoint} not working"
    
    def test_stats_endpoint_documented(self, client, api_headers):
        """Test that stats endpoint is documented and works"""
        # Check documentation
        response = client.get("/openapi.json")
        spec = response.json()
        paths = spec.get("paths", {})
        
        assert "/api/v1/stats" in paths, "Stats endpoint not documented"
        
        # Check endpoint works
        response = client.get("/api/v1/stats", headers=api_headers)
        assert response.status_code in [200, 401, 429], "Stats endpoint not working"
        
        if response.status_code == 200:
            stats = response.json()
            
            # Verify response structure matches documentation
            expected_fields = [
                "total_requests", "total_savings", "average_savings",
                "cache_hit_rate", "model_usage_distribution"
            ]
            
            for field in expected_fields:
                assert field in stats, f"Stats response missing documented field: {field}"
