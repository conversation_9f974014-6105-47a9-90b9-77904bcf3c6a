"""
Unit Tests for Security Service
Comprehensive test coverage for security validation and protection mechanisms
"""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from datetime import datetime, timedelta
import hashlib
import jwt

from src.services.security_service import SecurityService, SecurityThreat, ThreatLevel
from src.middleware.security import SecurityMiddleware
from src.middleware.input_validation import InputValidator
from src.middleware.rate_limiting import RateLimiter


class TestSecurityService:
    """Test suite for SecurityService class"""
    
    @pytest.fixture
    async def security_service(self):
        """Create security service instance for testing"""
        service = SecurityService()
        await service.initialize()
        return service
    
    @pytest.fixture
    def sample_request_data(self):
        """Sample request data for testing"""
        return {
            "prompt": "What is the capital of France?",
            "max_tokens": 100,
            "temperature": 0.7,
            "client_ip": "*************",
            "user_agent": "TestClient/1.0",
            "api_key": "test-api-key-123"
        }
    
    @pytest.mark.asyncio
    async def test_security_service_initialization(self):
        """Test security service initializes correctly"""
        service = SecurityService()
        assert not service.initialized
        
        await service.initialize()
        assert service.initialized
        assert service.threat_detector is not None
        assert service.input_validator is not None
        assert service.rate_limiter is not None
    
    @pytest.mark.asyncio
    async def test_input_validation_success(self, security_service, sample_request_data):
        """Test successful input validation"""
        is_valid, errors = await security_service.validate_input(sample_request_data)
        
        assert is_valid is True
        assert len(errors) == 0
    
    @pytest.mark.asyncio
    async def test_input_validation_malicious_content(self, security_service):
        """Test detection of malicious input content"""
        malicious_data = {
            "prompt": "<script>alert('xss')</script>",
            "max_tokens": 100,
            "client_ip": "*************"
        }
        
        is_valid, errors = await security_service.validate_input(malicious_data)
        
        assert is_valid is False
        assert len(errors) > 0
        assert any("malicious" in error.lower() or "script" in error.lower() for error in errors)
    
    @pytest.mark.asyncio
    async def test_sql_injection_detection(self, security_service):
        """Test SQL injection detection"""
        sql_injection_data = {
            "prompt": "'; DROP TABLE users; --",
            "max_tokens": 100,
            "client_ip": "*************"
        }
        
        is_valid, errors = await security_service.validate_input(sql_injection_data)
        
        assert is_valid is False
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_rate_limiting_enforcement(self, security_service, sample_request_data):
        """Test rate limiting enforcement"""
        client_ip = sample_request_data["client_ip"]
        
        # Make multiple requests rapidly
        for i in range(10):
            allowed = await security_service.check_rate_limit(client_ip, "api_request")
            if i < 5:  # First few should be allowed
                assert allowed is True
            # Later requests may be rate limited
        
        # Verify rate limit tracking
        assert client_ip in security_service.rate_limiter.client_requests
    
    @pytest.mark.asyncio
    async def test_threat_detection(self, security_service, sample_request_data):
        """Test threat detection capabilities"""
        # Normal request should not be flagged
        threat_level = await security_service.detect_threats(sample_request_data)
        assert threat_level == ThreatLevel.LOW
        
        # Suspicious request should be flagged
        suspicious_data = {
            **sample_request_data,
            "prompt": "How to hack into systems" * 100,  # Suspicious content
            "max_tokens": 10000  # Unusually high token request
        }
        
        threat_level = await security_service.detect_threats(suspicious_data)
        assert threat_level in [ThreatLevel.MEDIUM, ThreatLevel.HIGH]
    
    @pytest.mark.asyncio
    async def test_api_key_validation(self, security_service):
        """Test API key validation"""
        # Valid API key format
        valid_key = "sk-1234567890abcdef1234567890abcdef"
        is_valid = await security_service.validate_api_key(valid_key)
        assert is_valid is True
        
        # Invalid API key format
        invalid_key = "invalid-key"
        is_valid = await security_service.validate_api_key(invalid_key)
        assert is_valid is False
        
        # Empty API key
        is_valid = await security_service.validate_api_key("")
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_ip_blacklist_checking(self, security_service):
        """Test IP blacklist checking"""
        # Normal IP should not be blacklisted
        normal_ip = "*************"
        is_blacklisted = await security_service.is_ip_blacklisted(normal_ip)
        assert is_blacklisted is False
        
        # Add IP to blacklist
        await security_service.add_to_blacklist(normal_ip, "Testing")
        
        # IP should now be blacklisted
        is_blacklisted = await security_service.is_ip_blacklisted(normal_ip)
        assert is_blacklisted is True
    
    @pytest.mark.asyncio
    async def test_security_headers_validation(self, security_service):
        """Test security headers validation"""
        headers = {
            "User-Agent": "TestClient/1.0",
            "Content-Type": "application/json",
            "Authorization": "Bearer test-token"
        }
        
        is_valid = await security_service.validate_headers(headers)
        assert is_valid is True
        
        # Missing required headers
        incomplete_headers = {
            "User-Agent": "TestClient/1.0"
        }
        
        is_valid = await security_service.validate_headers(incomplete_headers)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_content_filtering(self, security_service):
        """Test content filtering for inappropriate content"""
        # Clean content should pass
        clean_content = "What is machine learning?"
        is_clean = await security_service.filter_content(clean_content)
        assert is_clean is True
        
        # Inappropriate content should be filtered
        inappropriate_content = "How to create malware"
        is_clean = await security_service.filter_content(inappropriate_content)
        assert is_clean is False
    
    @pytest.mark.asyncio
    async def test_session_management(self, security_service):
        """Test session management and validation"""
        user_id = "test-user-123"
        
        # Create session
        session_token = await security_service.create_session(user_id)
        assert session_token is not None
        assert len(session_token) > 0
        
        # Validate session
        is_valid = await security_service.validate_session(session_token)
        assert is_valid is True
        
        # Invalid session should fail
        is_valid = await security_service.validate_session("invalid-token")
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_encryption_decryption(self, security_service):
        """Test data encryption and decryption"""
        sensitive_data = "sensitive information"
        
        # Encrypt data
        encrypted = await security_service.encrypt_data(sensitive_data)
        assert encrypted != sensitive_data
        assert len(encrypted) > 0
        
        # Decrypt data
        decrypted = await security_service.decrypt_data(encrypted)
        assert decrypted == sensitive_data
    
    @pytest.mark.asyncio
    async def test_audit_logging(self, security_service, sample_request_data):
        """Test security audit logging"""
        # Perform security check
        await security_service.validate_input(sample_request_data)
        
        # Verify audit log entry
        audit_logs = await security_service.get_audit_logs(limit=1)
        assert len(audit_logs) > 0
        
        latest_log = audit_logs[0]
        assert "timestamp" in latest_log
        assert "event_type" in latest_log
        assert "client_ip" in latest_log
    
    @pytest.mark.asyncio
    async def test_brute_force_detection(self, security_service):
        """Test brute force attack detection"""
        client_ip = "*************"
        
        # Simulate multiple failed attempts
        for _ in range(10):
            await security_service.record_failed_attempt(client_ip, "login")
        
        # Should detect brute force
        is_brute_force = await security_service.detect_brute_force(client_ip)
        assert is_brute_force is True
        
        # IP should be temporarily blocked
        is_blocked = await security_service.is_temporarily_blocked(client_ip)
        assert is_blocked is True
    
    @pytest.mark.asyncio
    async def test_cors_validation(self, security_service):
        """Test CORS validation"""
        # Valid origin
        valid_origin = "https://trusted-domain.com"
        is_allowed = await security_service.validate_cors_origin(valid_origin)
        assert is_allowed is True
        
        # Invalid origin
        invalid_origin = "https://malicious-site.com"
        is_allowed = await security_service.validate_cors_origin(invalid_origin)
        assert is_allowed is False
    
    @pytest.mark.asyncio
    async def test_token_validation(self, security_service):
        """Test JWT token validation"""
        # Create valid token
        payload = {"user_id": "test-user", "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(payload, "secret-key", algorithm="HS256")
        
        is_valid = await security_service.validate_jwt_token(token)
        assert is_valid is True
        
        # Expired token
        expired_payload = {"user_id": "test-user", "exp": datetime.utcnow() - timedelta(hours=1)}
        expired_token = jwt.encode(expired_payload, "secret-key", algorithm="HS256")
        
        is_valid = await security_service.validate_jwt_token(expired_token)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_security_metrics_collection(self, security_service, sample_request_data):
        """Test security metrics collection"""
        initial_metrics = await security_service.get_security_metrics()
        
        # Perform security operations
        await security_service.validate_input(sample_request_data)
        await security_service.detect_threats(sample_request_data)
        
        # Verify metrics updated
        updated_metrics = await security_service.get_security_metrics()
        assert updated_metrics["total_validations"] >= initial_metrics["total_validations"]
        assert updated_metrics["total_threat_checks"] >= initial_metrics["total_threat_checks"]
    
    @pytest.mark.asyncio
    async def test_security_incident_response(self, security_service):
        """Test security incident response"""
        # Simulate security incident
        incident = SecurityThreat(
            threat_type="malicious_input",
            severity=ThreatLevel.HIGH,
            source_ip="192.168.1.300",
            description="Detected malicious script injection",
            timestamp=datetime.utcnow()
        )
        
        # Report incident
        incident_id = await security_service.report_security_incident(incident)
        assert incident_id is not None
        
        # Verify incident is recorded
        incidents = await security_service.get_security_incidents(limit=1)
        assert len(incidents) > 0
        assert incidents[0]["threat_type"] == "malicious_input"
    
    @pytest.mark.asyncio
    async def test_cleanup(self, security_service):
        """Test security service cleanup"""
        await security_service.cleanup()
        
        assert not security_service.initialized
        # Verify cleanup was called on dependencies
