"""
Unit Tests for QualityAssessor Service
Comprehensive test coverage with FAANG+ standards
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from src.services.quality_assessor import QualityAssessor, QualityMetrics
from src.core.models import OptimizationRequest


class TestQualityAssessor:
    """Test suite for QualityAssessor service"""
    
    @pytest.fixture
    async def quality_assessor(self):
        """Create QualityAssessor instance for testing"""
        assessor = QualityAssessor()
        await assessor.initialize()
        return assessor
    
    @pytest.fixture
    def sample_request(self):
        """Sample optimization request"""
        return OptimizationRequest(
            prompt="Write a Python function to calculate fibonacci numbers",
            quality_threshold=0.8,
            optimization_level=3,
            user_id="test_user"
        )
    
    @pytest.fixture
    def sample_response(self):
        """Sample optimized response"""
        return "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
    
    @pytest.mark.asyncio
    async def test_assess_quality_high_quality(self, quality_assessor, sample_request, sample_response):
        """Test quality assessment for high-quality response"""
        with patch.object(quality_assessor, '_calculate_semantic_similarity', return_value=0.95):
            with patch.object(quality_assessor, '_calculate_coherence_score', return_value=0.90):
                with patch.object(quality_assessor, '_calculate_completeness_score', return_value=0.88):
                    
                    metrics = await quality_assessor.assess_quality(
                        original_prompt=sample_request.prompt,
                        optimized_response=sample_response,
                        quality_threshold=sample_request.quality_threshold
                    )
                    
                    assert isinstance(metrics, QualityMetrics)
                    assert metrics.overall_score >= 0.8
                    assert metrics.semantic_similarity >= 0.9
                    assert metrics.coherence_score >= 0.85
                    assert metrics.completeness_score >= 0.8
                    assert metrics.meets_threshold is True
    
    @pytest.mark.asyncio
    async def test_assess_quality_low_quality(self, quality_assessor, sample_request):
        """Test quality assessment for low-quality response"""
        low_quality_response = "def fib(n): return n"
        
        with patch.object(quality_assessor, '_calculate_semantic_similarity', return_value=0.60):
            with patch.object(quality_assessor, '_calculate_coherence_score', return_value=0.65):
                with patch.object(quality_assessor, '_calculate_completeness_score', return_value=0.55):
                    
                    metrics = await quality_assessor.assess_quality(
                        original_prompt=sample_request.prompt,
                        optimized_response=low_quality_response,
                        quality_threshold=sample_request.quality_threshold
                    )
                    
                    assert metrics.overall_score < 0.8
                    assert metrics.meets_threshold is False
                    assert len(metrics.quality_issues) > 0
    
    @pytest.mark.asyncio
    async def test_semantic_similarity_calculation(self, quality_assessor):
        """Test semantic similarity calculation"""
        original = "Write a function to sort a list"
        optimized = "Create a function that sorts an array"
        
        # Mock sentence transformer
        with patch.object(quality_assessor.sentence_transformer, 'encode') as mock_encode:
            mock_encode.side_effect = [
                [0.1, 0.2, 0.3, 0.4],  # Original embedding
                [0.15, 0.25, 0.35, 0.45]  # Optimized embedding
            ]
            
            similarity = await quality_assessor._calculate_semantic_similarity(original, optimized)
            
            assert 0.0 <= similarity <= 1.0
            assert similarity > 0.8  # Should be high similarity
    
    @pytest.mark.asyncio
    async def test_coherence_score_calculation(self, quality_assessor):
        """Test coherence score calculation"""
        coherent_text = "This is a well-structured response. It follows logical flow. The conclusion is clear."
        incoherent_text = "Random words. No structure here. Conclusion what?"
        
        coherent_score = await quality_assessor._calculate_coherence_score(coherent_text)
        incoherent_score = await quality_assessor._calculate_coherence_score(incoherent_text)
        
        assert 0.0 <= coherent_score <= 1.0
        assert 0.0 <= incoherent_score <= 1.0
        assert coherent_score > incoherent_score
    
    @pytest.mark.asyncio
    async def test_completeness_score_calculation(self, quality_assessor):
        """Test completeness score calculation"""
        complete_prompt = "Write a Python function with error handling and documentation"
        complete_response = """
        def example_function(param):
            '''
            This function does something useful.
            
            Args:
                param: Input parameter
                
            Returns:
                Result of the operation
                
            Raises:
                ValueError: If param is invalid
            '''
            try:
                if not param:
                    raise ValueError("Parameter cannot be empty")
                return param.upper()
            except Exception as e:
                raise ValueError(f"Error processing: {e}")
        """
        
        incomplete_response = "def func(p): return p"
        
        complete_score = await quality_assessor._calculate_completeness_score(
            complete_prompt, complete_response
        )
        incomplete_score = await quality_assessor._calculate_completeness_score(
            complete_prompt, incomplete_response
        )
        
        assert 0.0 <= complete_score <= 1.0
        assert 0.0 <= incomplete_score <= 1.0
        assert complete_score > incomplete_score
    
    @pytest.mark.asyncio
    async def test_quality_threshold_validation(self, quality_assessor, sample_request, sample_response):
        """Test quality threshold validation"""
        # Test with different thresholds
        thresholds = [0.5, 0.7, 0.9, 0.95]
        
        for threshold in thresholds:
            with patch.object(quality_assessor, '_calculate_semantic_similarity', return_value=0.85):
                with patch.object(quality_assessor, '_calculate_coherence_score', return_value=0.80):
                    with patch.object(quality_assessor, '_calculate_completeness_score', return_value=0.75):
                        
                        metrics = await quality_assessor.assess_quality(
                            original_prompt=sample_request.prompt,
                            optimized_response=sample_response,
                            quality_threshold=threshold
                        )
                        
                        expected_meets_threshold = metrics.overall_score >= threshold
                        assert metrics.meets_threshold == expected_meets_threshold
    
    @pytest.mark.asyncio
    async def test_quality_issues_detection(self, quality_assessor):
        """Test quality issues detection"""
        problematic_response = "This response is too short and lacks detail."
        
        with patch.object(quality_assessor, '_calculate_semantic_similarity', return_value=0.40):
            with patch.object(quality_assessor, '_calculate_coherence_score', return_value=0.50):
                with patch.object(quality_assessor, '_calculate_completeness_score', return_value=0.30):
                    
                    metrics = await quality_assessor.assess_quality(
                        original_prompt="Write a comprehensive guide",
                        optimized_response=problematic_response,
                        quality_threshold=0.8
                    )
                    
                    assert len(metrics.quality_issues) > 0
                    assert any("semantic similarity" in issue.lower() for issue in metrics.quality_issues)
                    assert any("completeness" in issue.lower() for issue in metrics.quality_issues)
    
    @pytest.mark.asyncio
    async def test_batch_quality_assessment(self, quality_assessor):
        """Test batch quality assessment"""
        requests = [
            ("Write a function", "def func(): pass"),
            ("Create a class", "class MyClass: pass"),
            ("Implement sorting", "def sort(arr): return sorted(arr)")
        ]
        
        with patch.object(quality_assessor, 'assess_quality') as mock_assess:
            mock_assess.return_value = QualityMetrics(
                overall_score=0.85,
                semantic_similarity=0.90,
                coherence_score=0.85,
                completeness_score=0.80,
                meets_threshold=True,
                quality_issues=[],
                assessment_time_ms=50
            )
            
            results = await quality_assessor.batch_assess_quality(requests, quality_threshold=0.8)
            
            assert len(results) == len(requests)
            assert all(isinstance(result, QualityMetrics) for result in results)
            assert mock_assess.call_count == len(requests)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, quality_assessor):
        """Test error handling in quality assessment"""
        # Test with invalid inputs
        with pytest.raises(ValueError):
            await quality_assessor.assess_quality("", "response", 0.8)
        
        with pytest.raises(ValueError):
            await quality_assessor.assess_quality("prompt", "", 0.8)
        
        with pytest.raises(ValueError):
            await quality_assessor.assess_quality("prompt", "response", -0.1)
        
        with pytest.raises(ValueError):
            await quality_assessor.assess_quality("prompt", "response", 1.1)
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self, quality_assessor, sample_request, sample_response):
        """Test performance metrics collection"""
        with patch.object(quality_assessor, '_calculate_semantic_similarity', return_value=0.85):
            with patch.object(quality_assessor, '_calculate_coherence_score', return_value=0.80):
                with patch.object(quality_assessor, '_calculate_completeness_score', return_value=0.75):
                    
                    metrics = await quality_assessor.assess_quality(
                        original_prompt=sample_request.prompt,
                        optimized_response=sample_response,
                        quality_threshold=sample_request.quality_threshold
                    )
                    
                    assert metrics.assessment_time_ms > 0
                    assert isinstance(metrics.assessment_time_ms, (int, float))
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, quality_assessor):
        """Test circuit breaker functionality"""
        # Simulate multiple failures
        with patch.object(quality_assessor, '_calculate_semantic_similarity', side_effect=Exception("Service error")):
            
            # First few calls should fail and trigger circuit breaker
            for _ in range(6):  # Exceed threshold
                try:
                    await quality_assessor.assess_quality("prompt", "response", 0.8)
                except Exception:
                    pass
            
            # Circuit breaker should be open
            assert quality_assessor.circuit_open is True
            
            # Next call should be rejected immediately
            with pytest.raises(Exception):
                await quality_assessor.assess_quality("prompt", "response", 0.8)
    
    @pytest.mark.asyncio
    async def test_get_stats(self, quality_assessor):
        """Test statistics retrieval"""
        stats = await quality_assessor.get_stats()
        
        assert isinstance(stats, dict)
        required_fields = [
            'total_assessments',
            'average_quality_score',
            'average_assessment_time_ms',
            'threshold_pass_rate',
            'circuit_breaker_state'
        ]
        
        for field in required_fields:
            assert field in stats
    
    def test_quality_metrics_dataclass(self):
        """Test QualityMetrics dataclass"""
        metrics = QualityMetrics(
            overall_score=0.85,
            semantic_similarity=0.90,
            coherence_score=0.85,
            completeness_score=0.80,
            meets_threshold=True,
            quality_issues=[],
            assessment_time_ms=50
        )
        
        assert metrics.overall_score == 0.85
        assert metrics.meets_threshold is True
        assert len(metrics.quality_issues) == 0
        
        # Test dict conversion
        metrics_dict = metrics.to_dict()
        assert isinstance(metrics_dict, dict)
        assert metrics_dict['overall_score'] == 0.85
