"""
Unit tests for Cache Manager
Comprehensive testing of multi-layer caching system
"""

import pytest
import pytest_asyncio
import numpy as np
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.services.cache_manager import CacheManager, CacheEntry, MemoryCache, RedisCache
from src.core.models import OptimizationResponse, TaskComplexity, CacheHitType


class TestCacheEntry:
    """Test suite for CacheEntry"""
    
    def test_cache_entry_creation(self):
        """Test cache entry creation"""
        embedding = np.random.rand(384)
        data = {"test": "data"}
        
        entry = CacheEntry(
            key="test_key",
            data=data,
            embedding=embedding,
            cost_savings=50.0,
            quality_score=0.85
        )
        
        assert entry.key == "test_key"
        assert entry.data == data
        assert np.array_equal(entry.embedding, embedding)
        assert entry.cost_savings == 50.0
        assert entry.quality_score == 0.85
        assert entry.hit_count == 0
        assert isinstance(entry.created_at, datetime)
        assert isinstance(entry.last_accessed, datetime)
    
    def test_cache_entry_serialization(self):
        """Test cache entry serialization"""
        embedding = np.random.rand(384)
        data = {"optimized_prompt": "test", "model": "claude"}
        
        entry = CacheEntry(
            key="test_key",
            data=data,
            embedding=embedding
        )
        
        # Should be able to convert to dict
        entry_dict = entry.__dict__
        assert "key" in entry_dict
        assert "data" in entry_dict
        assert "embedding" in entry_dict


class TestMemoryCache:
    """Test suite for MemoryCache"""
    
    @pytest.fixture
    def memory_cache(self):
        """Create memory cache instance"""
        return MemoryCache(max_size=100)
    
    @pytest.mark.asyncio
    async def test_memory_cache_set_get(self, memory_cache):
        """Test basic set/get operations"""
        embedding = np.random.rand(384)
        entry = CacheEntry(
            key="test_key",
            data={"result": "test"},
            embedding=embedding
        )
        
        # Set entry
        await memory_cache.set(entry)
        
        # Get entry
        retrieved = await memory_cache.get("test_key", embedding)
        
        assert retrieved is not None
        assert retrieved.key == "test_key"
        assert retrieved.data == {"result": "test"}
    
    @pytest.mark.asyncio
    async def test_memory_cache_miss(self, memory_cache):
        """Test cache miss"""
        embedding = np.random.rand(384)
        
        # Get non-existent entry
        retrieved = await memory_cache.get("non_existent", embedding)
        
        assert retrieved is None
    
    @pytest.mark.asyncio
    async def test_memory_cache_size_limit(self, memory_cache):
        """Test cache size limit enforcement"""
        # Fill cache beyond limit
        for i in range(150):  # More than max_size of 100
            embedding = np.random.rand(384)
            entry = CacheEntry(
                key=f"key_{i}",
                data={"index": i},
                embedding=embedding
            )
            await memory_cache.set(entry)
        
        # Cache should not exceed max size
        assert len(memory_cache.cache) <= memory_cache.max_size
    
    @pytest.mark.asyncio
    async def test_memory_cache_lru_eviction(self, memory_cache):
        """Test LRU eviction policy"""
        # Fill cache to capacity
        entries = []
        for i in range(100):
            embedding = np.random.rand(384)
            entry = CacheEntry(
                key=f"key_{i}",
                data={"index": i},
                embedding=embedding
            )
            entries.append(entry)
            await memory_cache.set(entry)
        
        # Access first entry to make it recently used
        await memory_cache.get("key_0", entries[0].embedding)
        
        # Add one more entry to trigger eviction
        new_embedding = np.random.rand(384)
        new_entry = CacheEntry(
            key="new_key",
            data={"new": True},
            embedding=new_embedding
        )
        await memory_cache.set(new_entry)
        
        # First entry should still be there (recently accessed)
        retrieved = await memory_cache.get("key_0", entries[0].embedding)
        assert retrieved is not None
        
        # Some other entry should have been evicted
        assert len(memory_cache.cache) == memory_cache.max_size


class TestCacheManager:
    """Test suite for CacheManager"""
    
    @pytest_asyncio.fixture
    async def cache_manager(self):
        """Create cache manager instance with mocked dependencies"""
        with patch('src.services.cache_manager.redis.Redis') as mock_redis:
            mock_redis_client = MagicMock()
            mock_redis.return_value = mock_redis_client
            
            manager = CacheManager()
            await manager.initialize()
            return manager
    
    @pytest.mark.asyncio
    async def test_cache_manager_initialization(self, cache_manager):
        """Test cache manager initialization"""
        assert cache_manager.cache_layers is not None
        assert len(cache_manager.cache_layers) > 0
        assert cache_manager.embedding_model is not None
    
    @pytest.mark.asyncio
    async def test_get_cached_optimization(self, cache_manager):
        """Test getting cached optimization result"""
        prompt = "Create a simple function"
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = np.random.rand(384)
            
            # Mock cache layer response
            mock_response = OptimizationResponse(
                optimized_prompt="Optimized prompt",
                selected_model="claude-sonnet",
                original_cost=0.10,
                optimized_cost=0.05,
                savings_percentage=50.0,
                quality_score=0.85,
                processing_time_ms=100,
                cache_hit=True,
                cache_hit_type=CacheHitType.SEMANTIC,
                optimization_steps=[],
                task_complexity=TaskComplexity.SIMPLE,
                routing_reason="cached",
                compression_ratio=0.5
            )
            
            # Mock first cache layer to return result
            cache_manager.cache_layers[0].get = AsyncMock(return_value=CacheEntry(
                key=prompt,
                data=mock_response.dict(),
                embedding=np.random.rand(384)
            ))
            
            result = await cache_manager.get_cached_optimization(prompt)
            
            assert result is not None
            assert result.cache_hit is True
            assert result.selected_model == "claude-sonnet"
    
    @pytest.mark.asyncio
    async def test_cache_optimization_result(self, cache_manager):
        """Test caching optimization result"""
        prompt = "Create a simple function"
        response = OptimizationResponse(
            optimized_prompt="Optimized prompt",
            selected_model="claude-sonnet",
            original_cost=0.10,
            optimized_cost=0.05,
            savings_percentage=50.0,
            quality_score=0.85,
            processing_time_ms=100,
            cache_hit=False,
            cache_hit_type=CacheHitType.MISS,
            optimization_steps=[],
            task_complexity=TaskComplexity.SIMPLE,
            routing_reason="new_request",
            compression_ratio=0.5
        )
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = np.random.rand(384)
            
            # Mock cache layer set methods
            for layer in cache_manager.cache_layers:
                layer.set = AsyncMock()
            
            await cache_manager.cache_optimization_result(prompt, response)
            
            # Verify all layers were called
            for layer in cache_manager.cache_layers:
                layer.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_miss_fallthrough(self, cache_manager):
        """Test cache miss fallthrough to all layers"""
        prompt = "Create a complex application"
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = np.random.rand(384)
            
            # Mock all cache layers to return None (miss)
            for layer in cache_manager.cache_layers:
                layer.get = AsyncMock(return_value=None)
            
            result = await cache_manager.get_cached_optimization(prompt)
            
            assert result is None
            
            # Verify all layers were checked
            for layer in cache_manager.cache_layers:
                layer.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_stats_aggregation(self, cache_manager):
        """Test cache statistics aggregation"""
        # Mock cache layer stats
        for i, layer in enumerate(cache_manager.cache_layers):
            layer.get_stats = AsyncMock(return_value={
                "hit_count": i * 10,
                "miss_count": i * 5,
                "total_requests": i * 15,
                "hit_ratio": 0.67 if i > 0 else 0
            })
        
        stats = await cache_manager.get_cache_stats()
        
        assert "layers" in stats
        assert "overall" in stats
        assert len(stats["layers"]) == len(cache_manager.cache_layers)
        
        # Check overall stats calculation
        assert "total_hits" in stats["overall"]
        assert "total_misses" in stats["overall"]
        assert "overall_hit_ratio" in stats["overall"]
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self, cache_manager):
        """Test cache invalidation"""
        pattern = "test_pattern"
        
        # Mock cache layer invalidation
        for layer in cache_manager.cache_layers:
            layer.invalidate = AsyncMock(return_value=5)  # 5 entries invalidated
        
        total_invalidated = await cache_manager.invalidate_cache(pattern)
        
        # Should sum invalidations from all layers
        expected_total = len(cache_manager.cache_layers) * 5
        assert total_invalidated == expected_total
        
        # Verify all layers were called
        for layer in cache_manager.cache_layers:
            layer.invalidate.assert_called_once_with(pattern)
    
    @pytest.mark.asyncio
    async def test_embedding_generation_error_handling(self, cache_manager):
        """Test error handling in embedding generation"""
        prompt = "Test prompt"
        
        # Mock embedding generation to fail
        with patch.object(cache_manager, '_generate_embedding', side_effect=Exception("Embedding error")):
            result = await cache_manager.get_cached_optimization(prompt)
            
            # Should return None on embedding error
            assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_layer_error_resilience(self, cache_manager):
        """Test resilience to individual cache layer failures"""
        prompt = "Test prompt"
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = np.random.rand(384)
            
            # Mock first layer to fail, second to succeed
            cache_manager.cache_layers[0].get = AsyncMock(side_effect=Exception("Cache error"))
            
            if len(cache_manager.cache_layers) > 1:
                cache_manager.cache_layers[1].get = AsyncMock(return_value=CacheEntry(
                    key=prompt,
                    data={"test": "data"},
                    embedding=np.random.rand(384)
                ))
                
                result = await cache_manager.get_cached_optimization(prompt)
                
                # Should still get result from second layer
                assert result is not None
    
    @pytest.mark.asyncio
    async def test_semantic_similarity_threshold(self, cache_manager):
        """Test semantic similarity threshold enforcement"""
        prompt = "Create a function"
        
        # Create similar but not identical embedding
        base_embedding = np.random.rand(384)
        similar_embedding = base_embedding + np.random.rand(384) * 0.1  # Small variation
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = base_embedding
            
            # Mock cache layer to return entry with similar embedding
            cache_manager.cache_layers[0].get = AsyncMock(return_value=CacheEntry(
                key="similar_prompt",
                data={"test": "data"},
                embedding=similar_embedding
            ))
            
            # Should respect similarity threshold
            result = await cache_manager.get_cached_optimization(prompt)
            
            # Result depends on actual similarity calculation
            assert result is not None or result is None  # Either is valid
    
    @pytest.mark.asyncio
    async def test_concurrent_cache_operations(self, cache_manager):
        """Test concurrent cache operations"""
        import asyncio
        
        prompts = [
            "Create a web application",
            "Build a mobile app",
            "Design a database schema",
            "Implement an API"
        ]
        
        # Mock embedding generation
        with patch.object(cache_manager, '_generate_embedding') as mock_embedding:
            mock_embedding.return_value = np.random.rand(384)
            
            # Mock cache layers
            for layer in cache_manager.cache_layers:
                layer.get = AsyncMock(return_value=None)
            
            # Run concurrent cache lookups
            tasks = [
                cache_manager.get_cached_optimization(prompt)
                for prompt in prompts
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All operations should complete without exceptions
            assert len(results) == len(prompts)
            for result in results:
                assert not isinstance(result, Exception)
