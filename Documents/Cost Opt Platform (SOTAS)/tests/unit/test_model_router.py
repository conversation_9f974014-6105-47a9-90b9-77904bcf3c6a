"""
Unit Tests for Model Router Service
Comprehensive test coverage for intelligent model routing and circuit breaker functionality
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, timedelta

from src.services.model_router import ModelRouter, CircuitBreakerState, ModelHealth
from src.services.openrouter_client import OpenRouterClient, ModelTier, TaskType
from src.core.models import TaskComplexity, OptimizationRequest


class TestModelRouter:
    """Test suite for ModelRouter class"""
    
    @pytest.fixture
    def model_config(self):
        """Sample model configuration"""
        return {
            "claude-4-sonnet": {
                "tier": ModelTier.PREMIUM,
                "cost_per_token": Decimal("0.000015"),
                "max_tokens": 4096,
                "supports_streaming": True
            },
            "deepseek-v3": {
                "tier": ModelTier.STANDARD,
                "cost_per_token": Decimal("0.000002"),
                "max_tokens": 8192,
                "supports_streaming": False
            },
            "llama-free": {
                "tier": ModelTier.BUDGET,
                "cost_per_token": Decimal("0.000000"),
                "max_tokens": 2048,
                "supports_streaming": False
            }
        }
    
    @pytest.fixture
    async def router(self, model_config):
        """Create router instance for testing"""
        router = ModelRouter(model_config)
        await router.initialize()
        return router
    
    @pytest.fixture
    def sample_request(self):
        """Sample optimization request"""
        return OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8,
            task_complexity=TaskComplexity.SIMPLE
        )
    
    @pytest.mark.asyncio
    async def test_router_initialization(self, model_config):
        """Test router initializes correctly"""
        router = ModelRouter(model_config)
        assert not router.initialized
        
        await router.initialize()
        assert router.initialized
        assert router.openrouter_client is not None
        assert len(router.model_health) == len(model_config)
    
    @pytest.mark.asyncio
    async def test_model_selection_by_complexity(self, router, sample_request):
        """Test model selection based on task complexity"""
        # Simple task should prefer budget models
        simple_request = OptimizationRequest(
            prompt="Simple question",
            task_complexity=TaskComplexity.SIMPLE,
            quality_threshold=0.6
        )
        
        selected_model = await router.select_optimal_model(simple_request)
        assert selected_model in ["llama-free", "deepseek-v3"]
        
        # Complex task should prefer premium models
        complex_request = OptimizationRequest(
            prompt="Complex analysis required",
            task_complexity=TaskComplexity.EXPERT,
            quality_threshold=0.95
        )
        
        selected_model = await router.select_optimal_model(complex_request)
        assert selected_model == "claude-4-sonnet"
    
    @pytest.mark.asyncio
    async def test_model_selection_by_quality_threshold(self, router):
        """Test model selection based on quality threshold"""
        # High quality threshold
        high_quality_request = OptimizationRequest(
            prompt="Need high quality response",
            quality_threshold=0.95
        )
        
        selected_model = await router.select_optimal_model(high_quality_request)
        assert selected_model == "claude-4-sonnet"
        
        # Low quality threshold
        low_quality_request = OptimizationRequest(
            prompt="Simple response needed",
            quality_threshold=0.5
        )
        
        selected_model = await router.select_optimal_model(low_quality_request)
        assert selected_model in ["llama-free", "deepseek-v3"]
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, router, sample_request):
        """Test circuit breaker protection"""
        model_name = "claude-4-sonnet"
        
        # Simulate multiple failures to trigger circuit breaker
        with patch.object(router.openrouter_client, 'generate', side_effect=Exception("API Error")):
            for _ in range(5):  # Exceed failure threshold
                try:
                    await router.route_request(sample_request, model_name)
                except:
                    pass
        
        # Circuit breaker should be open
        assert router.circuit_breakers[model_name].state == CircuitBreakerState.OPEN
        
        # Requests should be rejected
        with pytest.raises(Exception):
            await router.route_request(sample_request, model_name)
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_recovery(self, router, sample_request):
        """Test circuit breaker recovery"""
        model_name = "claude-4-sonnet"
        
        # Force circuit breaker to open
        router.circuit_breakers[model_name].state = CircuitBreakerState.OPEN
        router.circuit_breakers[model_name].last_failure_time = datetime.utcnow() - timedelta(minutes=2)
        
        # Should transition to half-open after timeout
        with patch.object(router.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = Mock(content="Success", tokens_used=50)
            
            response = await router.route_request(sample_request, model_name)
            
            assert response is not None
            assert router.circuit_breakers[model_name].state == CircuitBreakerState.CLOSED
    
    @pytest.mark.asyncio
    async def test_model_health_monitoring(self, router):
        """Test model health monitoring"""
        model_name = "claude-4-sonnet"
        
        # Simulate successful request
        with patch.object(router.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = Mock(content="Success", tokens_used=50)
            
            await router._update_model_health(model_name, success=True, latency_ms=50.0)
            
            health = router.model_health[model_name]
            assert health.success_rate > 0
            assert health.avg_latency_ms == 50.0
            assert health.is_healthy is True
    
    @pytest.mark.asyncio
    async def test_fallback_model_selection(self, router, sample_request):
        """Test fallback when primary model fails"""
        # Make primary model unhealthy
        router.model_health["claude-4-sonnet"].is_healthy = False
        router.circuit_breakers["claude-4-sonnet"].state = CircuitBreakerState.OPEN
        
        # Should fallback to healthy model
        selected_model = await router.select_optimal_model(sample_request)
        assert selected_model != "claude-4-sonnet"
        assert selected_model in ["deepseek-v3", "llama-free"]
    
    @pytest.mark.asyncio
    async def test_cost_calculation(self, router):
        """Test cost calculation for different models"""
        tokens = 1000
        
        # Premium model cost
        premium_cost = router.calculate_cost("claude-4-sonnet", tokens)
        expected_premium = Decimal("0.000015") * tokens
        assert premium_cost == expected_premium
        
        # Free model cost
        free_cost = router.calculate_cost("llama-free", tokens)
        assert free_cost == Decimal("0")
    
    @pytest.mark.asyncio
    async def test_load_balancing(self, router, sample_request):
        """Test load balancing across healthy models"""
        # Make multiple requests and verify distribution
        selected_models = []
        
        for _ in range(10):
            model = await router.select_optimal_model(sample_request)
            selected_models.append(model)
        
        # Should use multiple models for load balancing
        unique_models = set(selected_models)
        assert len(unique_models) >= 1  # At least one model should be used
    
    @pytest.mark.asyncio
    async def test_concurrent_routing(self, router, sample_request):
        """Test concurrent request routing"""
        with patch.object(router.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = Mock(content="Success", tokens_used=50)
            
            # Create concurrent requests
            tasks = [
                router.route_request(sample_request, "claude-4-sonnet")
                for _ in range(10)
            ]
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All requests should complete
            assert len(responses) == 10
            successful_responses = [r for r in responses if not isinstance(r, Exception)]
            assert len(successful_responses) > 0
    
    @pytest.mark.asyncio
    async def test_model_availability_check(self, router):
        """Test model availability checking"""
        with patch.object(router.openrouter_client, 'is_model_available') as mock_available:
            mock_available.return_value = True
            
            is_available = await router.is_model_available("claude-4-sonnet")
            assert is_available is True
            
            mock_available.return_value = False
            is_available = await router.is_model_available("claude-4-sonnet")
            assert is_available is False
    
    @pytest.mark.asyncio
    async def test_routing_metrics_collection(self, router, sample_request):
        """Test routing metrics are collected"""
        with patch.object(router.openrouter_client, 'generate') as mock_generate:
            mock_generate.return_value = Mock(content="Success", tokens_used=50)
            
            initial_requests = router.routing_metrics.total_requests
            
            await router.route_request(sample_request, "claude-4-sonnet")
            
            assert router.routing_metrics.total_requests == initial_requests + 1
    
    @pytest.mark.asyncio
    async def test_adaptive_routing(self, router, sample_request):
        """Test adaptive routing based on performance"""
        # Simulate poor performance for one model
        router.model_health["claude-4-sonnet"].avg_latency_ms = 1000.0  # High latency
        router.model_health["deepseek-v3"].avg_latency_ms = 50.0  # Low latency
        
        # Should prefer faster model
        selected_model = await router.select_optimal_model(sample_request)
        
        # Adaptive routing should consider performance
        assert selected_model is not None
    
    @pytest.mark.asyncio
    async def test_error_handling(self, router, sample_request):
        """Test error handling in routing"""
        with patch.object(router.openrouter_client, 'generate', side_effect=Exception("Network error")):
            with pytest.raises(Exception):
                await router.route_request(sample_request, "claude-4-sonnet")
    
    @pytest.mark.asyncio
    async def test_cleanup(self, router):
        """Test router cleanup"""
        await router.cleanup()
        
        assert not router.initialized
        # Verify cleanup was called on dependencies
