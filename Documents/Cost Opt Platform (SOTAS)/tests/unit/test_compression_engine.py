"""
Unit tests for Compression Engine
Comprehensive testing of compression algorithms and quality preservation
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from src.services.compression_engine import CompressionEngine
from src.core.models import OptimizationLevel


class TestCompressionEngine:
    """Test suite for CompressionEngine"""
    
    @pytest_asyncio.fixture
    async def compression_engine(self):
        """Create compression engine instance"""
        engine = CompressionEngine()
        await engine.initialize()
        return engine
    
    @pytest.mark.asyncio
    async def test_basic_compression(self, compression_engine):
        """Test basic compression functionality"""
        text = "Create a simple hello world function in Python that prints hello world to the console"
        
        compressed, ratio = await compression_engine.compress(text, target_ratio=0.5)
        
        assert compressed is not None
        assert len(compressed) < len(text)
        assert 0 <= ratio <= 1
        assert "hello world" in compressed.lower() or "hello" in compressed.lower()
    
    @pytest.mark.asyncio
    async def test_compression_levels(self, compression_engine):
        """Test different optimization levels"""
        text = "Please create a comprehensive technical documentation for the application"
        
        # Test all optimization levels
        results = {}
        for level in OptimizationLevel:
            compressed, ratio = await compression_engine.compress(
                text, 
                target_ratio=0.7, 
                optimization_level=level
            )
            results[level] = (compressed, ratio)
        
        # More aggressive levels should achieve higher compression
        conservative_ratio = results[OptimizationLevel.CONSERVATIVE][1]
        maximum_ratio = results[OptimizationLevel.MAXIMUM][1]
        
        assert maximum_ratio >= conservative_ratio
    
    @pytest.mark.asyncio
    async def test_empty_input(self, compression_engine):
        """Test compression with empty input"""
        compressed, ratio = await compression_engine.compress("")
        
        assert compressed == ""
        assert ratio == 0.0
    
    @pytest.mark.asyncio
    async def test_whitespace_only(self, compression_engine):
        """Test compression with whitespace-only input"""
        compressed, ratio = await compression_engine.compress("   \n\t  ")
        
        assert compressed.strip() == ""
        assert ratio >= 0
    
    @pytest.mark.asyncio
    async def test_technical_abbreviations(self, compression_engine):
        """Test technical abbreviation replacement"""
        text = "application programming interface and database management system"
        
        compressed, ratio = await compression_engine.compress(
            text, 
            optimization_level=OptimizationLevel.MODERATE
        )
        
        # Should contain abbreviations
        assert "API" in compressed or "api" in compressed
        assert "db" in compressed.lower() or "database" in compressed.lower()
    
    @pytest.mark.asyncio
    async def test_structure_compression(self, compression_engine):
        """Test document structure compression"""
        text = """
        Create a detailed comprehensive application that includes the following features:
        - User authentication system
        - Database integration
        - API endpoints
        Please make sure to include proper error handling and validation.
        """
        
        compressed, ratio = await compression_engine.compress(
            text,
            optimization_level=OptimizationLevel.BALANCED
        )
        
        # Should be significantly shorter
        assert len(compressed) < len(text) * 0.8
        assert ratio > 0.2
    
    @pytest.mark.asyncio
    async def test_quality_preservation(self, compression_engine):
        """Test that compression preserves semantic meaning"""
        text = "Create a Python function that calculates the factorial of a number using recursion"
        
        compressed, ratio = await compression_engine.compress(text)
        
        # Key concepts should be preserved
        key_terms = ["python", "function", "factorial", "recursion"]
        preserved_count = sum(1 for term in key_terms if term in compressed.lower())
        
        # At least half of key terms should be preserved
        assert preserved_count >= len(key_terms) // 2
    
    @pytest.mark.asyncio
    async def test_compression_pipeline_error_handling(self, compression_engine):
        """Test error handling in compression pipeline"""
        # Mock a failure in the compression pipeline
        with patch.object(compression_engine, '_apply_compression_rules', side_effect=Exception("Test error")):
            text = "Test text for error handling"
            
            # Should return original text on error
            compressed, ratio = await compression_engine.compress(text)
            
            assert compressed == text
            assert ratio == 0.0
    
    @pytest.mark.asyncio
    async def test_target_ratio_achievement(self, compression_engine):
        """Test that compression attempts to achieve target ratio"""
        text = """
        Create a comprehensive web application with user authentication, database integration,
        real-time messaging, push notifications, analytics dashboard, payment processing,
        and administrative interface. Include proper error handling, logging, monitoring,
        and security measures throughout the application.
        """
        
        target_ratio = 0.8
        compressed, actual_ratio = await compression_engine.compress(
            text, 
            target_ratio=target_ratio,
            optimization_level=OptimizationLevel.MAXIMUM
        )
        
        # Should achieve significant compression
        assert actual_ratio > 0.5
        assert len(compressed) < len(text)
    
    @pytest.mark.asyncio
    async def test_compression_stats(self, compression_engine):
        """Test compression statistics tracking"""
        text = "Test compression statistics tracking functionality"
        
        # Get initial stats
        initial_stats = await compression_engine.get_stats()
        initial_compressions = initial_stats['total_compressions']
        
        # Perform compression
        await compression_engine.compress(text)
        
        # Check updated stats
        updated_stats = await compression_engine.get_stats()
        
        assert updated_stats['total_compressions'] == initial_compressions + 1
        assert updated_stats['total_original_length'] > initial_stats['total_original_length']
        assert updated_stats['total_compressed_length'] >= initial_stats['total_compressed_length']
    
    @pytest.mark.asyncio
    async def test_suspicious_input_handling(self, compression_engine):
        """Test handling of potentially malicious input"""
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "javascript:alert('xss')"
        ]
        
        for malicious_input in malicious_inputs:
            compressed, ratio = await compression_engine.compress(malicious_input)
            
            # Should not crash and should return some result
            assert compressed is not None
            assert isinstance(ratio, (int, float))
            assert 0 <= ratio <= 1
    
    @pytest.mark.asyncio
    async def test_unicode_handling(self, compression_engine):
        """Test compression with Unicode characters"""
        unicode_text = "Create a function that handles émojis 🚀 and spëcial characters ñ"
        
        compressed, ratio = await compression_engine.compress(unicode_text)
        
        assert compressed is not None
        assert isinstance(compressed, str)
        assert ratio >= 0
    
    @pytest.mark.asyncio
    async def test_very_long_input(self, compression_engine):
        """Test compression with very long input"""
        # Create a very long text
        long_text = "This is a test sentence. " * 1000
        
        compressed, ratio = await compression_engine.compress(
            long_text,
            optimization_level=OptimizationLevel.MAXIMUM
        )
        
        # Should handle long input without issues
        assert compressed is not None
        assert len(compressed) < len(long_text)
        assert ratio > 0
    
    def test_compression_rules_structure(self, compression_engine):
        """Test that compression rules are properly structured"""
        for level in OptimizationLevel:
            rules = compression_engine.compression_rules.get(level, [])
            
            for rule in rules:
                # Each rule should have required attributes
                assert hasattr(rule, 'pattern')
                assert hasattr(rule, 'replacement')
                assert hasattr(rule, 'description')
                assert hasattr(rule, 'savings_estimate')
                
                # Validate rule properties
                assert isinstance(rule.pattern, str)
                assert isinstance(rule.replacement, str)
                assert isinstance(rule.description, str)
                assert isinstance(rule.savings_estimate, (int, float))
                assert 0 <= rule.savings_estimate <= 1
    
    def test_technical_abbreviations_completeness(self, compression_engine):
        """Test that technical abbreviations are comprehensive"""
        abbreviations = compression_engine.technical_abbreviations
        
        # Should have common technical terms
        expected_terms = [
            'application',
            'database',
            'requirements',
            'documentation',
            'implementation'
        ]
        
        for term in expected_terms:
            assert term in abbreviations
            assert len(abbreviations[term]) < len(term)  # Abbreviation should be shorter
    
    @pytest.mark.asyncio
    async def test_concurrent_compression(self, compression_engine):
        """Test concurrent compression operations"""
        import asyncio
        
        texts = [
            "Create a web application with user authentication",
            "Implement a database management system",
            "Design a real-time messaging platform",
            "Build an analytics dashboard with charts"
        ]
        
        # Run compressions concurrently
        tasks = [
            compression_engine.compress(text, optimization_level=OptimizationLevel.BALANCED)
            for text in texts
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All compressions should succeed
        assert len(results) == len(texts)
        for compressed, ratio in results:
            assert compressed is not None
            assert 0 <= ratio <= 1
