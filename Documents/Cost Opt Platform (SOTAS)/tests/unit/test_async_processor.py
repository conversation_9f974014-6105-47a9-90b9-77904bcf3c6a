"""
Unit Tests for Async Processor Service
Comprehensive test coverage for asynchronous request processing and queue management
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from uuid import uuid4

from src.services.async_processor import Async<PERSON>rocessor, ProcessingTask, TaskStatus, TaskPriority
from src.core.models import OptimizationRequest, OptimizationResponse


class TestAsyncProcessor:
    """Test suite for AsyncProcessor class"""
    
    @pytest.fixture
    async def processor(self):
        """Create async processor instance for testing"""
        processor = AsyncProcessor(max_workers=4, queue_size=100)
        await processor.initialize()
        return processor
    
    @pytest.fixture
    def sample_task(self):
        """Sample processing task"""
        return ProcessingTask(
            task_id=str(uuid4()),
            task_type="optimization",
            payload={
                "prompt": "What is machine learning?",
                "max_tokens": 100,
                "temperature": 0.7
            },
            priority=TaskPriority.NORMAL,
            created_at=datetime.utcnow(),
            timeout_seconds=30
        )
    
    @pytest.fixture
    def sample_optimization_request(self):
        """Sample optimization request"""
        return OptimizationRequest(
            prompt="Explain neural networks",
            max_tokens=200,
            temperature=0.8,
            quality_threshold=0.85
        )
    
    @pytest.mark.asyncio
    async def test_processor_initialization(self):
        """Test processor initializes correctly"""
        processor = AsyncProcessor(max_workers=2, queue_size=50)
        assert not processor.initialized
        assert processor.max_workers == 2
        assert processor.queue_size == 50
        
        await processor.initialize()
        assert processor.initialized
        assert processor.task_queue is not None
        assert processor.worker_pool is not None
    
    @pytest.mark.asyncio
    async def test_task_submission(self, processor, sample_task):
        """Test task submission to queue"""
        # Submit task
        task_id = await processor.submit_task(sample_task)
        
        assert task_id == sample_task.task_id
        assert processor.get_queue_size() > 0
        
        # Verify task is in queue
        task_status = await processor.get_task_status(task_id)
        assert task_status.status in [TaskStatus.QUEUED, TaskStatus.PROCESSING]
    
    @pytest.mark.asyncio
    async def test_task_processing(self, processor, sample_task):
        """Test task processing execution"""
        # Mock task processor
        async def mock_processor(task):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {"result": "processed", "task_id": task.task_id}
        
        processor.register_task_processor("optimization", mock_processor)
        
        # Submit and process task
        task_id = await processor.submit_task(sample_task)
        
        # Wait for processing
        await asyncio.sleep(0.2)
        
        # Check task completion
        task_status = await processor.get_task_status(task_id)
        assert task_status.status == TaskStatus.COMPLETED
        assert task_status.result is not None
    
    @pytest.mark.asyncio
    async def test_priority_queue_ordering(self, processor):
        """Test priority queue ordering"""
        # Submit tasks with different priorities
        high_priority_task = ProcessingTask(
            task_id="high-priority",
            task_type="optimization",
            payload={"prompt": "High priority task"},
            priority=TaskPriority.HIGH
        )
        
        low_priority_task = ProcessingTask(
            task_id="low-priority",
            task_type="optimization",
            payload={"prompt": "Low priority task"},
            priority=TaskPriority.LOW
        )
        
        normal_priority_task = ProcessingTask(
            task_id="normal-priority",
            task_type="optimization",
            payload={"prompt": "Normal priority task"},
            priority=TaskPriority.NORMAL
        )
        
        # Submit in reverse priority order
        await processor.submit_task(low_priority_task)
        await processor.submit_task(normal_priority_task)
        await processor.submit_task(high_priority_task)
        
        # High priority should be processed first
        processing_order = []
        
        async def track_processor(task):
            processing_order.append(task.task_id)
            return {"result": "processed"}
        
        processor.register_task_processor("optimization", track_processor)
        
        # Wait for processing
        await asyncio.sleep(0.5)
        
        # Verify high priority was processed first
        assert processing_order[0] == "high-priority"
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, processor):
        """Test concurrent task processing"""
        # Create multiple tasks
        tasks = []
        for i in range(10):
            task = ProcessingTask(
                task_id=f"task-{i}",
                task_type="optimization",
                payload={"prompt": f"Task {i}"},
                priority=TaskPriority.NORMAL
            )
            tasks.append(task)
        
        # Mock processor with delay
        async def slow_processor(task):
            await asyncio.sleep(0.1)
            return {"result": f"processed-{task.task_id}"}
        
        processor.register_task_processor("optimization", slow_processor)
        
        # Submit all tasks
        start_time = datetime.utcnow()
        for task in tasks:
            await processor.submit_task(task)
        
        # Wait for all tasks to complete
        while processor.get_active_task_count() > 0:
            await asyncio.sleep(0.1)
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # With concurrent processing, should be faster than sequential
        # 10 tasks * 0.1s = 1s sequential, but with 4 workers should be ~0.3s
        assert processing_time < 0.8
    
    @pytest.mark.asyncio
    async def test_task_timeout_handling(self, processor):
        """Test task timeout handling"""
        # Create task with short timeout
        timeout_task = ProcessingTask(
            task_id="timeout-task",
            task_type="optimization",
            payload={"prompt": "Timeout test"},
            timeout_seconds=0.1  # Very short timeout
        )
        
        # Mock processor that takes longer than timeout
        async def slow_processor(task):
            await asyncio.sleep(0.2)  # Longer than timeout
            return {"result": "should not complete"}
        
        processor.register_task_processor("optimization", slow_processor)
        
        # Submit task
        task_id = await processor.submit_task(timeout_task)
        
        # Wait for timeout
        await asyncio.sleep(0.3)
        
        # Task should be marked as failed due to timeout
        task_status = await processor.get_task_status(task_id)
        assert task_status.status == TaskStatus.FAILED
        assert "timeout" in task_status.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_task_retry_mechanism(self, processor):
        """Test task retry mechanism"""
        retry_count = 0
        
        async def failing_processor(task):
            nonlocal retry_count
            retry_count += 1
            if retry_count < 3:
                raise Exception("Simulated failure")
            return {"result": "success after retries"}
        
        processor.register_task_processor("optimization", failing_processor)
        
        # Create task with retry configuration
        retry_task = ProcessingTask(
            task_id="retry-task",
            task_type="optimization",
            payload={"prompt": "Retry test"},
            max_retries=3
        )
        
        # Submit task
        task_id = await processor.submit_task(retry_task)
        
        # Wait for processing with retries
        await asyncio.sleep(0.5)
        
        # Task should eventually succeed
        task_status = await processor.get_task_status(task_id)
        assert task_status.status == TaskStatus.COMPLETED
        assert retry_count == 3
    
    @pytest.mark.asyncio
    async def test_queue_size_limit(self, processor):
        """Test queue size limit enforcement"""
        # Fill queue to capacity
        for i in range(processor.queue_size):
            task = ProcessingTask(
                task_id=f"fill-task-{i}",
                task_type="optimization",
                payload={"prompt": f"Fill task {i}"}
            )
            await processor.submit_task(task)
        
        # Try to submit one more task (should fail)
        overflow_task = ProcessingTask(
            task_id="overflow-task",
            task_type="optimization",
            payload={"prompt": "Overflow task"}
        )
        
        with pytest.raises(Exception) as exc_info:
            await processor.submit_task(overflow_task)
        
        assert "queue full" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_task_cancellation(self, processor, sample_task):
        """Test task cancellation"""
        # Submit task
        task_id = await processor.submit_task(sample_task)
        
        # Cancel task before processing
        success = await processor.cancel_task(task_id)
        assert success is True
        
        # Task should be marked as cancelled
        task_status = await processor.get_task_status(task_id)
        assert task_status.status == TaskStatus.CANCELLED
    
    @pytest.mark.asyncio
    async def test_batch_processing(self, processor):
        """Test batch processing capabilities"""
        # Create batch of tasks
        batch_tasks = []
        for i in range(5):
            task = ProcessingTask(
                task_id=f"batch-task-{i}",
                task_type="optimization",
                payload={"prompt": f"Batch task {i}"}
            )
            batch_tasks.append(task)
        
        # Mock batch processor
        async def batch_processor(tasks):
            results = []
            for task in tasks:
                results.append({"task_id": task.task_id, "result": "batch processed"})
            return results
        
        processor.register_batch_processor("optimization", batch_processor)
        
        # Submit batch
        batch_id = await processor.submit_batch(batch_tasks)
        
        # Wait for batch processing
        await asyncio.sleep(0.3)
        
        # Check batch completion
        batch_status = await processor.get_batch_status(batch_id)
        assert batch_status.status == TaskStatus.COMPLETED
        assert len(batch_status.results) == 5
    
    @pytest.mark.asyncio
    async def test_worker_health_monitoring(self, processor):
        """Test worker health monitoring"""
        # Get worker health status
        worker_health = await processor.get_worker_health()
        
        assert "total_workers" in worker_health
        assert "active_workers" in worker_health
        assert "idle_workers" in worker_health
        assert worker_health["total_workers"] == processor.max_workers
    
    @pytest.mark.asyncio
    async def test_processing_metrics(self, processor, sample_task):
        """Test processing metrics collection"""
        # Mock processor
        async def metric_processor(task):
            return {"result": "processed"}
        
        processor.register_task_processor("optimization", metric_processor)
        
        # Submit and process task
        await processor.submit_task(sample_task)
        await asyncio.sleep(0.2)
        
        # Get processing metrics
        metrics = await processor.get_processing_metrics()
        
        assert "total_tasks_processed" in metrics
        assert "avg_processing_time" in metrics
        assert "success_rate" in metrics
        assert "queue_utilization" in metrics
        assert metrics["total_tasks_processed"] > 0
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown(self, processor, sample_task):
        """Test graceful shutdown"""
        # Submit task
        await processor.submit_task(sample_task)
        
        # Mock processor with delay
        async def delayed_processor(task):
            await asyncio.sleep(0.2)
            return {"result": "processed"}
        
        processor.register_task_processor("optimization", delayed_processor)
        
        # Start shutdown
        shutdown_task = asyncio.create_task(processor.shutdown(timeout=1.0))
        
        # Wait for shutdown
        await shutdown_task
        
        assert not processor.initialized
        assert processor.get_active_task_count() == 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, processor):
        """Test error handling in task processing"""
        # Mock processor that always fails
        async def failing_processor(task):
            raise ValueError("Simulated processing error")
        
        processor.register_task_processor("optimization", failing_processor)
        
        # Create task
        error_task = ProcessingTask(
            task_id="error-task",
            task_type="optimization",
            payload={"prompt": "Error test"}
        )
        
        # Submit task
        task_id = await processor.submit_task(error_task)
        
        # Wait for processing
        await asyncio.sleep(0.2)
        
        # Task should be marked as failed
        task_status = await processor.get_task_status(task_id)
        assert task_status.status == TaskStatus.FAILED
        assert "Simulated processing error" in task_status.error_message
    
    @pytest.mark.asyncio
    async def test_cleanup(self, processor):
        """Test processor cleanup"""
        await processor.cleanup()
        
        assert not processor.initialized
        assert processor.get_queue_size() == 0
        assert processor.get_active_task_count() == 0
