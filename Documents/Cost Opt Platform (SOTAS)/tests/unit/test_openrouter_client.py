"""
Unit Tests for OpenRouter Client
Comprehensive test coverage for OpenRouter.ai API integration
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from decimal import Decimal
import aiohttp

from src.services.openrouter_client import OpenRouterClient, OpenRouterResponse, ModelTier
from src.core.models import ModelConfig


class TestOpenRouterClient:
    """Test suite for OpenRouterClient class"""

    @pytest.fixture
    def client(self):
        """Create OpenRouter client instance for testing"""
        return OpenRouterClient()

    @pytest.fixture
    def sample_model_config(self):
        """Sample model configuration"""
        return ModelConfig(
            name="anthropic/claude-3.5-sonnet",
            api_endpoint="https://openrouter.ai/api/v1",
            cost_per_input_token=0.000015,
            cost_per_output_token=0.000075,
            max_tokens=4096,
            quality_score=0.95,
            enabled=True
        )
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock API response data"""
        return {
            "id": "gen-123456789",
            "model": "anthropic/claude-3.5-sonnet",
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "Paris is the capital of France."
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 8,
                "total_tokens": 18
            },
            "created": 1234567890
        }
    
    def test_client_initialization(self):
        """Test client initializes correctly"""
        client = OpenRouterClient(api_key="test_key")
        
        assert client.api_key == "test_key"
        assert client.base_url == "https://openrouter.ai/api/v1"
        assert client.session is None
        assert not client.initialized
    
    @pytest.mark.asyncio
    async def test_client_initialization_async(self, client):
        """Test async client initialization"""
        await client.initialize()
        
        assert client.initialized
        assert client.session is not None
        assert isinstance(client.session, aiohttp.ClientSession)
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_successful_generation(self, client, sample_model_config, mock_response_data):
        """Test successful text generation"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_post.return_value.__aenter__.return_value = mock_response
            
            response = await client.generate(
                prompt="What is the capital of France?",
                model_config=sample_model_config,
                max_tokens=100,
                temperature=0.7
            )
            
            assert isinstance(response, OpenRouterResponse)
            assert response.content == "Paris is the capital of France."
            assert response.model == "anthropic/claude-3.5-sonnet"
            assert response.tokens_used == 18
            assert response.prompt_tokens == 10
            assert response.completion_tokens == 8
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, client, sample_model_config):
        """Test API error handling"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            # Mock error response
            mock_response = AsyncMock()
            mock_response.status = 400
            mock_response.json = AsyncMock(return_value={
                "error": {
                    "message": "Invalid request",
                    "type": "invalid_request_error",
                    "code": "invalid_request"
                }
            })
            mock_post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(OpenRouterError) as exc_info:
                await client.generate(
                    prompt="Test prompt",
                    model_config=sample_model_config
                )
            
            assert "Invalid request" in str(exc_info.value)
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, client, sample_model_config):
        """Test rate limit handling with retry"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            # Mock rate limit response followed by success
            rate_limit_response = AsyncMock()
            rate_limit_response.status = 429
            rate_limit_response.json = AsyncMock(return_value={
                "error": {
                    "message": "Rate limit exceeded",
                    "type": "rate_limit_error"
                }
            })
            
            success_response = AsyncMock()
            success_response.status = 200
            success_response.json = AsyncMock(return_value={
                "choices": [{"message": {"content": "Success after retry"}}],
                "usage": {"total_tokens": 10}
            })
            
            mock_post.return_value.__aenter__.side_effect = [
                rate_limit_response,
                success_response
            ]
            
            with patch('asyncio.sleep'):  # Mock sleep to speed up test
                response = await client.generate(
                    prompt="Test prompt",
                    model_config=sample_model_config
                )
            
            assert response.content == "Success after retry"
            assert mock_post.call_count == 2
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_model_availability_check(self, client):
        """Test model availability checking"""
        await client.initialize()
        
        with patch.object(client.session, 'get') as mock_get:
            # Mock models endpoint response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={
                "data": [
                    {
                        "id": "anthropic/claude-3.5-sonnet",
                        "name": "Claude 3.5 Sonnet",
                        "pricing": {
                            "prompt": "0.000015",
                            "completion": "0.000075"
                        }
                    }
                ]
            })
            mock_get.return_value.__aenter__.return_value = mock_response
            
            is_available = await client.is_model_available("anthropic/claude-3.5-sonnet")
            
            assert is_available is True
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_streaming_generation(self, client, sample_model_config):
        """Test streaming text generation"""
        await client.initialize()
        
        # Mock streaming response
        stream_data = [
            'data: {"choices":[{"delta":{"content":"Paris"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":" is"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":" the capital"}}]}\n\n',
            'data: [DONE]\n\n'
        ]
        
        with patch.object(client.session, 'post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.content.iter_any = AsyncMock(
                return_value=iter([chunk.encode() for chunk in stream_data])
            )
            mock_post.return_value.__aenter__.return_value = mock_response
            
            chunks = []
            async for chunk in client.generate_stream(
                prompt="What is the capital of France?",
                model_config=sample_model_config
            ):
                chunks.append(chunk)
            
            assert len(chunks) == 3  # Excluding [DONE]
            assert chunks[0] == "Paris"
            assert chunks[1] == " is"
            assert chunks[2] == " the capital"
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_cost_calculation(self, client, sample_model_config):
        """Test cost calculation accuracy"""
        prompt_tokens = 100
        completion_tokens = 50
        
        cost = client.calculate_cost(
            model_config=sample_model_config,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens
        )
        
        # Cost = (prompt_tokens * prompt_price) + (completion_tokens * completion_price)
        # Using same price for both for simplicity in test
        expected_cost = Decimal("0.000015") * (prompt_tokens + completion_tokens)
        
        assert cost == expected_cost
    
    @pytest.mark.asyncio
    async def test_batch_generation(self, client, sample_model_config, mock_response_data):
        """Test batch text generation"""
        await client.initialize()
        
        prompts = [
            "What is the capital of France?",
            "What is the capital of Germany?",
            "What is the capital of Italy?"
        ]
        
        with patch.object(client, 'generate') as mock_generate:
            mock_generate.return_value = OpenRouterResponse(
                content="Test response",
                model="anthropic/claude-3.5-sonnet",
                tokens_used=18,
                prompt_tokens=10,
                completion_tokens=8
            )
            
            responses = await client.generate_batch(
                prompts=prompts,
                model_config=sample_model_config
            )
            
            assert len(responses) == 3
            assert all(isinstance(r, OpenRouterResponse) for r in responses)
            assert mock_generate.call_count == 3
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_request_timeout_handling(self, client, sample_model_config):
        """Test request timeout handling"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            # Mock timeout
            mock_post.side_effect = asyncio.TimeoutError("Request timeout")
            
            with pytest.raises(OpenRouterError) as exc_info:
                await client.generate(
                    prompt="Test prompt",
                    model_config=sample_model_config
                )
            
            assert "timeout" in str(exc_info.value).lower()
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_connection_error_handling(self, client, sample_model_config):
        """Test connection error handling"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            # Mock connection error
            mock_post.side_effect = aiohttp.ClientConnectionError("Connection failed")
            
            with pytest.raises(OpenRouterError) as exc_info:
                await client.generate(
                    prompt="Test prompt",
                    model_config=sample_model_config
                )
            
            assert "connection" in str(exc_info.value).lower()
        
        await client.close()
    
    def test_request_headers(self, client):
        """Test request headers are set correctly"""
        headers = client._get_headers()
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_api_key"
        assert headers["Content-Type"] == "application/json"
        assert "User-Agent" in headers
    
    def test_request_payload_construction(self, client, sample_model_config):
        """Test request payload construction"""
        payload = client._build_request_payload(
            prompt="Test prompt",
            model_config=sample_model_config,
            max_tokens=100,
            temperature=0.7,
            top_p=0.9
        )
        
        assert payload["model"] == sample_model_config.name
        assert payload["messages"][0]["content"] == "Test prompt"
        assert payload["max_tokens"] == 100
        assert payload["temperature"] == 0.7
        assert payload["top_p"] == 0.9
    
    @pytest.mark.asyncio
    async def test_response_validation(self, client, sample_model_config):
        """Test response validation"""
        await client.initialize()
        
        # Test invalid response structure
        with patch.object(client.session, 'post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={
                "invalid": "response"
            })
            mock_post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(OpenRouterError) as exc_info:
                await client.generate(
                    prompt="Test prompt",
                    model_config=sample_model_config
                )
            
            assert "invalid response" in str(exc_info.value).lower()
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, client, sample_model_config, mock_response_data):
        """Test handling concurrent requests"""
        await client.initialize()
        
        with patch.object(client.session, 'post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # Create multiple concurrent requests
            tasks = [
                client.generate(
                    prompt=f"Test prompt {i}",
                    model_config=sample_model_config
                )
                for i in range(10)
            ]
            
            responses = await asyncio.gather(*tasks)
            
            assert len(responses) == 10
            assert all(isinstance(r, OpenRouterResponse) for r in responses)
        
        await client.close()
    
    @pytest.mark.asyncio
    async def test_client_cleanup(self, client):
        """Test client cleanup"""
        await client.initialize()
        assert client.initialized
        
        await client.close()
        assert not client.initialized
        assert client.session is None
