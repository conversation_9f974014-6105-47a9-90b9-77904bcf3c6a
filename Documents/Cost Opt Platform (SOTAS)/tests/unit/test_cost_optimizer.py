"""
Unit Tests for Cost Optimizer
Comprehensive test coverage for core optimization logic
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal

from src.core.optimizer import CostOptimizer
from src.core.models import OptimizationRequest, OptimizationResponse, ModelConfig


class TestCostOptimizer:
    """Test suite for CostOptimizer class"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create optimizer instance for testing"""
        optimizer = CostOptimizer()
        await optimizer.initialize()
        return optimizer
    
    @pytest.fixture
    def sample_request(self):
        """Sample optimization request"""
        return OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8
        )
    
    @pytest.mark.asyncio
    async def test_optimizer_initialization(self):
        """Test optimizer initializes correctly"""
        optimizer = CostOptimizer()
        assert not optimizer.initialized
        
        await optimizer.initialize()
        assert optimizer.initialized
        assert optimizer.openrouter_client is not None
        assert optimizer.cache_manager is not None
        assert optimizer.analytics is not None
    
    @pytest.mark.asyncio
    async def test_optimize_with_cache_hit(self, optimizer, sample_request):
        """Test optimization with cache hit"""
        # Mock cache hit
        cached_result = OptimizationResult(
            response="Paris is the capital of France.",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("0.001"),
            tokens_used=50,
            quality_score=0.95,
            cache_hit=True,
            optimization_ratio=10.0
        )
        
        with patch.object(optimizer.cache_manager, 'get_cached_result', return_value=(True, cached_result)):
            result = await optimizer.optimize(sample_request)
            
            assert result.cache_hit is True
            assert result.response == "Paris is the capital of France."
            assert result.cost_usd == Decimal("0.001")
            assert result.optimization_ratio == 10.0
    
    @pytest.mark.asyncio
    async def test_optimize_with_cache_miss(self, optimizer, sample_request):
        """Test optimization with cache miss"""
        # Mock cache miss and successful API call
        with patch.object(optimizer.cache_manager, 'get_cached_result', return_value=(False, None)):
            with patch.object(optimizer, '_execute_optimization') as mock_execute:
                mock_result = OptimizationResult(
                    response="Paris is the capital of France.",
                    model_used="claude-4-sonnet",
                    cost_usd=Decimal("0.01"),
                    tokens_used=50,
                    quality_score=0.95,
                    cache_hit=False,
                    optimization_ratio=1.0
                )
                mock_execute.return_value = mock_result
                
                result = await optimizer.optimize(sample_request)
                
                assert result.cache_hit is False
                assert result.response == "Paris is the capital of France."
                assert mock_execute.called
    
    @pytest.mark.asyncio
    async def test_model_selection_strategy(self, optimizer, sample_request):
        """Test intelligent model selection"""
        # Test high quality threshold selects premium model
        high_quality_request = OptimizationRequest(
            prompt="Complex analysis required",
            quality_threshold=0.95
        )
        
        selected_model = await optimizer._select_optimal_model(high_quality_request)
        assert selected_model.tier == ModelTier.PREMIUM
        
        # Test low quality threshold allows cheaper model
        low_quality_request = OptimizationRequest(
            prompt="Simple question",
            quality_threshold=0.6
        )
        
        selected_model = await optimizer._select_optimal_model(low_quality_request)
        assert selected_model.tier in [ModelTier.STANDARD, ModelTier.BUDGET]
    
    @pytest.mark.asyncio
    async def test_prompt_compression(self, optimizer):
        """Test prompt compression functionality"""
        long_prompt = "This is a very long prompt that should be compressed. " * 20
        
        compressed = await optimizer._compress_prompt(long_prompt)
        
        assert len(compressed) < len(long_prompt)
        assert compressed is not None
        assert len(compressed) > 0
    
    @pytest.mark.asyncio
    async def test_quality_assessment(self, optimizer):
        """Test response quality assessment"""
        high_quality_response = "Paris is the capital of France. It is located in the north-central part of the country."
        low_quality_response = "Paris."
        
        high_score = await optimizer._assess_quality("What is the capital of France?", high_quality_response)
        low_score = await optimizer._assess_quality("What is the capital of France?", low_quality_response)
        
        assert high_score > low_score
        assert 0 <= high_score <= 1
        assert 0 <= low_score <= 1
    
    @pytest.mark.asyncio
    async def test_cost_calculation(self, optimizer):
        """Test cost calculation accuracy"""
        model_config = ModelConfig(
            name="claude-4-sonnet",
            provider="anthropic",
            cost_per_token=Decimal("0.00001"),
            tier=ModelTier.PREMIUM
        )
        
        cost = optimizer._calculate_cost(model_config, 1000)
        expected_cost = Decimal("0.01")
        
        assert cost == expected_cost
    
    @pytest.mark.asyncio
    async def test_optimization_ratio_calculation(self, optimizer):
        """Test optimization ratio calculation"""
        baseline_cost = Decimal("0.10")
        optimized_cost = Decimal("0.01")
        
        ratio = optimizer._calculate_optimization_ratio(baseline_cost, optimized_cost)
        expected_ratio = 10.0
        
        assert ratio == expected_ratio
    
    @pytest.mark.asyncio
    async def test_fallback_model_selection(self, optimizer, sample_request):
        """Test fallback when primary model fails"""
        with patch.object(optimizer.openrouter_client, 'generate') as mock_generate:
            # First call fails, second succeeds
            mock_generate.side_effect = [
                ModelNotAvailableError("Primary model unavailable"),
                Mock(response="Fallback response", tokens_used=50)
            ]
            
            result = await optimizer._execute_optimization(sample_request)
            
            assert result is not None
            assert mock_generate.call_count == 2
    
    @pytest.mark.asyncio
    async def test_error_handling(self, optimizer, sample_request):
        """Test error handling in optimization"""
        with patch.object(optimizer.openrouter_client, 'generate', side_effect=Exception("API Error")):
            with pytest.raises(OptimizationError):
                await optimizer._execute_optimization(sample_request)
    
    @pytest.mark.asyncio
    async def test_analytics_tracking(self, optimizer, sample_request):
        """Test analytics data is tracked correctly"""
        with patch.object(optimizer.analytics, 'track_optimization') as mock_track:
            with patch.object(optimizer.cache_manager, 'get_cached_result', return_value=(False, None)):
                with patch.object(optimizer, '_execute_optimization') as mock_execute:
                    mock_result = OptimizationResult(
                        response="Test response",
                        model_used="claude-4-sonnet",
                        cost_usd=Decimal("0.01"),
                        tokens_used=50,
                        quality_score=0.95,
                        cache_hit=False,
                        optimization_ratio=1.0
                    )
                    mock_execute.return_value = mock_result
                    
                    await optimizer.optimize(sample_request)
                    
                    assert mock_track.called
    
    @pytest.mark.asyncio
    async def test_concurrent_optimization_requests(self, optimizer):
        """Test handling concurrent optimization requests"""
        requests = [
            OptimizationRequest(prompt=f"Question {i}", max_tokens=50)
            for i in range(10)
        ]
        
        with patch.object(optimizer, '_execute_optimization') as mock_execute:
            mock_execute.return_value = OptimizationResult(
                response="Test response",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.01"),
                tokens_used=50,
                quality_score=0.95,
                cache_hit=False,
                optimization_ratio=1.0
            )
            
            # Execute concurrent requests
            tasks = [optimizer.optimize(req) for req in requests]
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 10
            assert all(isinstance(result, OptimizationResult) for result in results)
    
    @pytest.mark.asyncio
    async def test_batch_optimization(self, optimizer):
        """Test batch optimization functionality"""
        requests = [
            OptimizationRequest(prompt=f"Question {i}", max_tokens=50)
            for i in range(5)
        ]
        
        with patch.object(optimizer, 'optimize') as mock_optimize:
            mock_optimize.return_value = OptimizationResult(
                response="Test response",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.01"),
                tokens_used=50,
                quality_score=0.95,
                cache_hit=False,
                optimization_ratio=1.0
            )
            
            results = await optimizer.optimize_batch(requests)
            
            assert len(results) == 5
            assert mock_optimize.call_count == 5
    
    def test_model_config_validation(self, optimizer):
        """Test model configuration validation"""
        valid_config = ModelConfig(
            name="claude-4-sonnet",
            provider="anthropic",
            cost_per_token=Decimal("0.00001"),
            tier=ModelTier.PREMIUM
        )
        
        assert optimizer._validate_model_config(valid_config) is True
        
        # Test invalid config
        invalid_config = ModelConfig(
            name="",
            provider="",
            cost_per_token=Decimal("-0.001"),
            tier=ModelTier.PREMIUM
        )
        
        assert optimizer._validate_model_config(invalid_config) is False
    
    @pytest.mark.asyncio
    async def test_cleanup(self, optimizer):
        """Test optimizer cleanup"""
        await optimizer.cleanup()
        
        assert not optimizer.initialized
        # Verify cleanup was called on dependencies
        # This would be mocked in a real test
    
    def test_optimization_request_validation(self):
        """Test optimization request validation"""
        # Valid request
        valid_request = OptimizationRequest(
            prompt="Valid prompt",
            max_tokens=100,
            temperature=0.7,
            quality_threshold=0.8
        )
        assert valid_request.prompt == "Valid prompt"
        
        # Test validation of parameters
        with pytest.raises(ValueError):
            OptimizationRequest(
                prompt="",  # Empty prompt should fail
                max_tokens=100
            )
        
        with pytest.raises(ValueError):
            OptimizationRequest(
                prompt="Valid prompt",
                max_tokens=-1  # Negative tokens should fail
            )
        
        with pytest.raises(ValueError):
            OptimizationRequest(
                prompt="Valid prompt",
                temperature=2.0  # Temperature > 1 should fail
            )
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self, optimizer, sample_request):
        """Test performance metrics collection"""
        with patch.object(optimizer, '_execute_optimization') as mock_execute:
            mock_result = OptimizationResult(
                response="Test response",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.01"),
                tokens_used=50,
                quality_score=0.95,
                cache_hit=False,
                optimization_ratio=1.0,
                processing_time_ms=50.0
            )
            mock_execute.return_value = mock_result
            
            result = await optimizer.optimize(sample_request)
            
            assert result.processing_time_ms is not None
            assert result.processing_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_adaptive_learning(self, optimizer):
        """Test adaptive learning functionality"""
        # Simulate multiple optimization requests to trigger learning
        for i in range(10):
            request = OptimizationRequest(
                prompt=f"Learning prompt {i}",
                quality_threshold=0.8
            )
            
            with patch.object(optimizer, '_execute_optimization') as mock_execute:
                mock_result = OptimizationResult(
                    response="Test response",
                    model_used="claude-4-sonnet",
                    cost_usd=Decimal("0.01"),
                    tokens_used=50,
                    quality_score=0.95,
                    cache_hit=False,
                    optimization_ratio=1.0
                )
                mock_execute.return_value = mock_result
                
                await optimizer.optimize(request)
        
        # Verify learning metrics are updated
        assert optimizer.learning_metrics['total_optimizations'] >= 10
