"""
Unit Tests for Monitoring Service
Comprehensive test coverage for system monitoring, metrics collection, and alerting
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from decimal import Decimal

from src.services.monitoring_service import MonitoringService, SystemMetrics, AlertRule, SLATarget
from src.monitoring.metrics import MetricsCollector
from src.monitoring.alerting import AlertManager
from src.monitoring.health import HealthChecker


class TestMonitoringService:
    """Test suite for MonitoringService class"""
    
    @pytest.fixture
    async def monitoring_service(self):
        """Create monitoring service instance for testing"""
        service = MonitoringService()
        await service.initialize()
        return service
    
    @pytest.fixture
    def sample_system_metrics(self):
        """Sample system metrics data"""
        return SystemMetrics(
            cpu_usage_percent=45.2,
            memory_usage_percent=62.8,
            disk_usage_percent=35.1,
            network_io_bytes=1024000,
            active_connections=150,
            request_rate_per_second=25.5,
            error_rate_percent=0.5,
            avg_response_time_ms=85.3,
            timestamp=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_alert_rule(self):
        """Sample alert rule"""
        return AlertRule(
            name="High CPU Usage",
            metric_name="cpu_usage_percent",
            threshold=80.0,
            comparison="greater_than",
            duration_seconds=300,
            severity="warning",
            enabled=True
        )
    
    @pytest.mark.asyncio
    async def test_monitoring_service_initialization(self):
        """Test monitoring service initializes correctly"""
        service = MonitoringService()
        assert not service.initialized
        
        await service.initialize()
        assert service.initialized
        assert service.metrics_collector is not None
        assert service.alert_manager is not None
        assert service.health_checker is not None
    
    @pytest.mark.asyncio
    async def test_system_metrics_collection(self, monitoring_service):
        """Test system metrics collection"""
        metrics = await monitoring_service.collect_system_metrics()
        
        assert isinstance(metrics, SystemMetrics)
        assert metrics.cpu_usage_percent >= 0
        assert metrics.memory_usage_percent >= 0
        assert metrics.disk_usage_percent >= 0
        assert metrics.timestamp is not None
    
    @pytest.mark.asyncio
    async def test_application_metrics_collection(self, monitoring_service):
        """Test application-specific metrics collection"""
        app_metrics = await monitoring_service.collect_application_metrics()
        
        assert "total_optimizations" in app_metrics
        assert "cache_hit_rate" in app_metrics
        assert "avg_optimization_time" in app_metrics
        assert "cost_savings_total" in app_metrics
        
        # All metrics should be numeric
        for key, value in app_metrics.items():
            assert isinstance(value, (int, float, Decimal))
    
    @pytest.mark.asyncio
    async def test_performance_metrics_tracking(self, monitoring_service):
        """Test performance metrics tracking"""
        # Record performance data
        await monitoring_service.record_request_metrics(
            endpoint="/api/v1/optimize",
            method="POST",
            status_code=200,
            response_time_ms=125.5,
            request_size_bytes=1024,
            response_size_bytes=2048
        )
        
        # Get performance metrics
        perf_metrics = await monitoring_service.get_performance_metrics()
        
        assert "avg_response_time" in perf_metrics
        assert "request_count" in perf_metrics
        assert "error_rate" in perf_metrics
        assert perf_metrics["request_count"] > 0
    
    @pytest.mark.asyncio
    async def test_alert_rule_management(self, monitoring_service, sample_alert_rule):
        """Test alert rule management"""
        # Add alert rule
        rule_id = await monitoring_service.add_alert_rule(sample_alert_rule)
        assert rule_id is not None
        
        # Get alert rules
        rules = await monitoring_service.get_alert_rules()
        assert len(rules) > 0
        assert any(rule["name"] == "High CPU Usage" for rule in rules)
        
        # Update alert rule
        sample_alert_rule.threshold = 90.0
        await monitoring_service.update_alert_rule(rule_id, sample_alert_rule)
        
        # Verify update
        updated_rule = await monitoring_service.get_alert_rule(rule_id)
        assert updated_rule["threshold"] == 90.0
        
        # Delete alert rule
        await monitoring_service.delete_alert_rule(rule_id)
        rules_after_delete = await monitoring_service.get_alert_rules()
        assert not any(rule["id"] == rule_id for rule in rules_after_delete)
    
    @pytest.mark.asyncio
    async def test_alert_evaluation(self, monitoring_service, sample_alert_rule, sample_system_metrics):
        """Test alert evaluation against metrics"""
        # Add alert rule
        rule_id = await monitoring_service.add_alert_rule(sample_alert_rule)
        
        # Test metrics that should trigger alert
        high_cpu_metrics = SystemMetrics(
            **sample_system_metrics.dict(),
            cpu_usage_percent=85.0  # Above threshold
        )
        
        alerts = await monitoring_service.evaluate_alerts(high_cpu_metrics)
        assert len(alerts) > 0
        assert alerts[0]["rule_name"] == "High CPU Usage"
        
        # Test metrics that should not trigger alert
        normal_metrics = SystemMetrics(
            **sample_system_metrics.dict(),
            cpu_usage_percent=45.0  # Below threshold
        )
        
        alerts = await monitoring_service.evaluate_alerts(normal_metrics)
        assert len(alerts) == 0
    
    @pytest.mark.asyncio
    async def test_health_check_monitoring(self, monitoring_service):
        """Test health check monitoring"""
        # Perform health check
        health_status = await monitoring_service.perform_health_check()
        
        assert "overall_status" in health_status
        assert "components" in health_status
        assert "timestamp" in health_status
        
        # Check individual components
        components = health_status["components"]
        assert "database" in components
        assert "cache" in components
        assert "openrouter_api" in components
        
        # Each component should have status
        for component, status in components.items():
            assert "status" in status
            assert status["status"] in ["healthy", "unhealthy", "degraded"]
    
    @pytest.mark.asyncio
    async def test_sla_monitoring(self, monitoring_service):
        """Test SLA monitoring and compliance tracking"""
        # Define SLA targets
        sla_targets = {
            "availability": SLATarget(
                name="Service Availability",
                target_value=99.9,
                measurement_window=3600,
                alert_threshold=99.5
            ),
            "latency": SLATarget(
                name="Response Latency",
                target_value=100.0,
                measurement_window=300,
                alert_threshold=150.0
            )
        }
        
        # Set SLA targets
        await monitoring_service.set_sla_targets(sla_targets)
        
        # Check SLA compliance
        sla_status = await monitoring_service.check_sla_compliance()
        
        assert "availability" in sla_status
        assert "latency" in sla_status
        
        for sla_name, status in sla_status.items():
            assert "current_value" in status
            assert "target_value" in status
            assert "compliance_status" in status
    
    @pytest.mark.asyncio
    async def test_cost_monitoring(self, monitoring_service):
        """Test cost monitoring and tracking"""
        # Record cost data
        await monitoring_service.record_cost_metrics(
            model_name="claude-4-sonnet",
            tokens_used=1000,
            cost_usd=Decimal("0.015"),
            optimization_savings=Decimal("0.005")
        )
        
        # Get cost metrics
        cost_metrics = await monitoring_service.get_cost_metrics()
        
        assert "total_cost" in cost_metrics
        assert "total_savings" in cost_metrics
        assert "cost_by_model" in cost_metrics
        assert "optimization_ratio" in cost_metrics
        
        # Verify cost tracking
        assert cost_metrics["total_cost"] > 0
        assert cost_metrics["total_savings"] >= 0
    
    @pytest.mark.asyncio
    async def test_custom_metrics_tracking(self, monitoring_service):
        """Test custom metrics tracking"""
        # Record custom metric
        await monitoring_service.record_custom_metric(
            name="custom_optimization_score",
            value=0.85,
            tags={"model": "claude-4-sonnet", "complexity": "medium"}
        )
        
        # Get custom metrics
        custom_metrics = await monitoring_service.get_custom_metrics("custom_optimization_score")
        
        assert len(custom_metrics) > 0
        assert custom_metrics[0]["value"] == 0.85
        assert "tags" in custom_metrics[0]
    
    @pytest.mark.asyncio
    async def test_metrics_aggregation(self, monitoring_service, sample_system_metrics):
        """Test metrics aggregation over time periods"""
        # Record multiple metrics
        for i in range(10):
            metrics = SystemMetrics(
                **sample_system_metrics.dict(),
                cpu_usage_percent=40.0 + i,
                timestamp=datetime.utcnow() - timedelta(minutes=i)
            )
            await monitoring_service.store_metrics(metrics)
        
        # Get aggregated metrics
        aggregated = await monitoring_service.get_aggregated_metrics(
            metric_name="cpu_usage_percent",
            time_window="1h",
            aggregation_type="avg"
        )
        
        assert aggregated is not None
        assert "value" in aggregated
        assert "timestamp" in aggregated
    
    @pytest.mark.asyncio
    async def test_dashboard_metrics(self, monitoring_service):
        """Test dashboard metrics compilation"""
        dashboard_data = await monitoring_service.get_dashboard_metrics()
        
        assert "system_overview" in dashboard_data
        assert "performance_metrics" in dashboard_data
        assert "cost_metrics" in dashboard_data
        assert "alert_summary" in dashboard_data
        
        # System overview should contain key metrics
        system_overview = dashboard_data["system_overview"]
        assert "cpu_usage" in system_overview
        assert "memory_usage" in system_overview
        assert "active_requests" in system_overview
    
    @pytest.mark.asyncio
    async def test_metrics_export(self, monitoring_service):
        """Test metrics export functionality"""
        # Export metrics in different formats
        prometheus_metrics = await monitoring_service.export_prometheus_metrics()
        assert isinstance(prometheus_metrics, str)
        assert "# HELP" in prometheus_metrics
        
        json_metrics = await monitoring_service.export_json_metrics()
        assert isinstance(json_metrics, dict)
        assert "timestamp" in json_metrics
        assert "metrics" in json_metrics
    
    @pytest.mark.asyncio
    async def test_alert_notification(self, monitoring_service, sample_alert_rule):
        """Test alert notification system"""
        # Add alert rule
        rule_id = await monitoring_service.add_alert_rule(sample_alert_rule)
        
        # Configure notification channels
        notification_config = {
            "email": {"enabled": True, "recipients": ["<EMAIL>"]},
            "webhook": {"enabled": True, "url": "https://webhook.example.com"},
            "slack": {"enabled": False}
        }
        
        await monitoring_service.configure_notifications(notification_config)
        
        # Trigger alert
        alert_data = {
            "rule_name": "High CPU Usage",
            "metric_value": 85.0,
            "threshold": 80.0,
            "severity": "warning"
        }
        
        with patch.object(monitoring_service.alert_manager, 'send_notification') as mock_send:
            await monitoring_service.send_alert_notification(alert_data)
            mock_send.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_monitoring_data_retention(self, monitoring_service):
        """Test monitoring data retention policies"""
        # Set retention policy
        retention_policy = {
            "raw_metrics": "7d",
            "aggregated_metrics": "30d",
            "alerts": "90d",
            "logs": "14d"
        }
        
        await monitoring_service.set_retention_policy(retention_policy)
        
        # Trigger cleanup
        cleanup_result = await monitoring_service.cleanup_old_data()
        
        assert "deleted_records" in cleanup_result
        assert cleanup_result["deleted_records"] >= 0
    
    @pytest.mark.asyncio
    async def test_monitoring_performance_impact(self, monitoring_service):
        """Test monitoring system performance impact"""
        # Measure monitoring overhead
        start_time = datetime.utcnow()
        
        # Perform monitoring operations
        await monitoring_service.collect_system_metrics()
        await monitoring_service.collect_application_metrics()
        
        end_time = datetime.utcnow()
        monitoring_duration = (end_time - start_time).total_seconds()
        
        # Monitoring should be fast (< 100ms)
        assert monitoring_duration < 0.1
    
    @pytest.mark.asyncio
    async def test_cleanup(self, monitoring_service):
        """Test monitoring service cleanup"""
        await monitoring_service.cleanup()
        
        assert not monitoring_service.initialized
        # Verify cleanup was called on dependencies
