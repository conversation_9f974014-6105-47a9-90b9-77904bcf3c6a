"""
Unit Tests for Adaptive Learner Service
Comprehensive test coverage for ML-powered learning and optimization
"""

import pytest
import asyncio
import numpy as np
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, timedelta

from src.services.adaptive_learner import Adapt<PERSON><PERSON><PERSON>ner, LearningPattern, OptimizationHistory
from src.core.models import OptimizationRequest, OptimizationResponse, TaskComplexity, ModelType


class TestAdaptiveLearner:
    """Test suite for AdaptiveLearner class"""
    
    @pytest.fixture
    async def learner(self):
        """Create adaptive learner instance for testing"""
        learner = AdaptiveLearner()
        await learner.initialize()
        return learner
    
    @pytest.fixture
    def sample_optimization_history(self):
        """Sample optimization history data"""
        return [
            OptimizationHistory(
                prompt="What is Python?",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.01"),
                quality_score=0.95,
                processing_time_ms=150.0,
                task_complexity=TaskComplexity.SIMPLE,
                timestamp=datetime.utcnow() - timedelta(hours=1)
            ),
            OptimizationHistory(
                prompt="Explain machine learning",
                model_used="deepseek-v3",
                cost_usd=Decimal("0.005"),
                quality_score=0.85,
                processing_time_ms=200.0,
                task_complexity=TaskComplexity.MEDIUM,
                timestamp=datetime.utcnow() - timedelta(hours=2)
            ),
            OptimizationHistory(
                prompt="Build a complex system",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.05"),
                quality_score=0.98,
                processing_time_ms=500.0,
                task_complexity=TaskComplexity.EXPERT,
                timestamp=datetime.utcnow() - timedelta(hours=3)
            )
        ]
    
    @pytest.mark.asyncio
    async def test_learner_initialization(self):
        """Test learner initializes correctly"""
        learner = AdaptiveLearner()
        assert not learner.initialized
        
        await learner.initialize()
        assert learner.initialized
        assert learner.learning_patterns is not None
        assert learner.optimization_history is not None
    
    @pytest.mark.asyncio
    async def test_pattern_learning(self, learner, sample_optimization_history):
        """Test learning patterns from optimization history"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Learn patterns
        patterns = await learner.learn_patterns()
        
        assert len(patterns) > 0
        assert all(isinstance(pattern, LearningPattern) for pattern in patterns)
    
    @pytest.mark.asyncio
    async def test_model_recommendation(self, learner, sample_optimization_history):
        """Test model recommendation based on learned patterns"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Learn patterns
        await learner.learn_patterns()
        
        # Test recommendation for simple task
        request = OptimizationRequest(
            prompt="Simple question about Python",
            task_complexity=TaskComplexity.SIMPLE,
            quality_threshold=0.8
        )
        
        recommendation = await learner.recommend_model(request)
        
        assert recommendation is not None
        assert recommendation.model_name in ["claude-4-sonnet", "deepseek-v3", "llama-free"]
        assert recommendation.confidence_score >= 0.0
        assert recommendation.confidence_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_quality_prediction(self, learner, sample_optimization_history):
        """Test quality score prediction"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Train quality predictor
        await learner.train_quality_predictor()
        
        # Predict quality for new request
        request = OptimizationRequest(
            prompt="Explain neural networks",
            task_complexity=TaskComplexity.MEDIUM
        )
        
        predicted_quality = await learner.predict_quality(request, "claude-4-sonnet")
        
        assert 0.0 <= predicted_quality <= 1.0
    
    @pytest.mark.asyncio
    async def test_cost_optimization_learning(self, learner, sample_optimization_history):
        """Test learning cost optimization strategies"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Learn cost optimization patterns
        cost_patterns = await learner.learn_cost_patterns()
        
        assert len(cost_patterns) >= 0
        
        # Test cost optimization recommendation
        request = OptimizationRequest(
            prompt="Cost-sensitive task",
            quality_threshold=0.7
        )
        
        optimization = await learner.recommend_cost_optimization(request)
        
        assert optimization is not None
        assert optimization.recommended_model is not None
        assert optimization.expected_savings >= 0
    
    @pytest.mark.asyncio
    async def test_performance_learning(self, learner, sample_optimization_history):
        """Test learning performance patterns"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Learn performance patterns
        perf_patterns = await learner.learn_performance_patterns()
        
        assert len(perf_patterns) >= 0
        
        # Test performance prediction
        request = OptimizationRequest(
            prompt="Performance-critical task",
            max_tokens=1000
        )
        
        predicted_time = await learner.predict_processing_time(request, "claude-4-sonnet")
        
        assert predicted_time > 0
    
    @pytest.mark.asyncio
    async def test_adaptive_threshold_adjustment(self, learner, sample_optimization_history):
        """Test adaptive quality threshold adjustment"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Learn optimal thresholds
        optimal_thresholds = await learner.learn_optimal_thresholds()
        
        assert optimal_thresholds is not None
        assert "simple" in optimal_thresholds
        assert "medium" in optimal_thresholds
        assert "complex" in optimal_thresholds
        
        # Test threshold recommendation
        request = OptimizationRequest(
            prompt="Test task",
            task_complexity=TaskComplexity.SIMPLE
        )
        
        recommended_threshold = await learner.recommend_quality_threshold(request)
        
        assert 0.0 <= recommended_threshold <= 1.0
    
    @pytest.mark.asyncio
    async def test_feedback_incorporation(self, learner):
        """Test incorporating user feedback"""
        # Simulate user feedback
        feedback = {
            "optimization_id": "test-123",
            "user_rating": 4.5,
            "quality_rating": 4.0,
            "cost_satisfaction": 5.0,
            "comments": "Good optimization"
        }
        
        await learner.incorporate_feedback(feedback)
        
        # Verify feedback is stored
        assert len(learner.user_feedback) > 0
        assert learner.user_feedback[-1]["user_rating"] == 4.5
    
    @pytest.mark.asyncio
    async def test_model_performance_tracking(self, learner):
        """Test tracking model performance over time"""
        # Simulate multiple optimizations
        for i in range(10):
            history = OptimizationHistory(
                prompt=f"Test prompt {i}",
                model_used="claude-4-sonnet",
                cost_usd=Decimal("0.01"),
                quality_score=0.9 + (i * 0.01),  # Improving quality
                processing_time_ms=100.0 + (i * 5),  # Increasing time
                task_complexity=TaskComplexity.SIMPLE,
                timestamp=datetime.utcnow() - timedelta(minutes=i)
            )
            await learner.record_optimization(history)
        
        # Get performance trends
        trends = await learner.get_performance_trends("claude-4-sonnet")
        
        assert trends is not None
        assert "quality_trend" in trends
        assert "cost_trend" in trends
        assert "latency_trend" in trends
    
    @pytest.mark.asyncio
    async def test_anomaly_detection(self, learner, sample_optimization_history):
        """Test anomaly detection in optimization patterns"""
        # Add normal history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Add anomalous optimization
        anomaly = OptimizationHistory(
            prompt="Normal prompt",
            model_used="claude-4-sonnet",
            cost_usd=Decimal("1.0"),  # Unusually high cost
            quality_score=0.3,  # Unusually low quality
            processing_time_ms=5000.0,  # Unusually high latency
            task_complexity=TaskComplexity.SIMPLE,
            timestamp=datetime.utcnow()
        )
        
        is_anomaly = await learner.detect_anomaly(anomaly)
        
        assert is_anomaly is True
    
    @pytest.mark.asyncio
    async def test_learning_rate_adaptation(self, learner):
        """Test adaptive learning rate based on performance"""
        initial_rate = learner.learning_rate
        
        # Simulate poor performance
        for _ in range(5):
            await learner.update_learning_rate(performance_improvement=False)
        
        # Learning rate should decrease
        assert learner.learning_rate <= initial_rate
        
        # Simulate good performance
        for _ in range(10):
            await learner.update_learning_rate(performance_improvement=True)
        
        # Learning rate should increase
        assert learner.learning_rate > initial_rate * 0.5
    
    @pytest.mark.asyncio
    async def test_pattern_clustering(self, learner, sample_optimization_history):
        """Test clustering similar optimization patterns"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Cluster patterns
        clusters = await learner.cluster_patterns()
        
        assert len(clusters) >= 0
        
        # Each cluster should have meaningful patterns
        for cluster in clusters:
            assert "centroid" in cluster
            assert "patterns" in cluster
            assert len(cluster["patterns"]) > 0
    
    @pytest.mark.asyncio
    async def test_online_learning(self, learner):
        """Test online learning capabilities"""
        # Enable online learning
        learner.enable_online_learning()
        
        # Add new optimization
        new_optimization = OptimizationHistory(
            prompt="New learning example",
            model_used="deepseek-v3",
            cost_usd=Decimal("0.003"),
            quality_score=0.88,
            processing_time_ms=120.0,
            task_complexity=TaskComplexity.SIMPLE,
            timestamp=datetime.utcnow()
        )
        
        # Should trigger immediate learning
        await learner.record_optimization(new_optimization)
        
        # Verify learning was triggered
        assert learner.last_learning_update is not None
    
    @pytest.mark.asyncio
    async def test_model_comparison(self, learner, sample_optimization_history):
        """Test comparing model performance"""
        # Add sample history
        for history in sample_optimization_history:
            await learner.record_optimization(history)
        
        # Compare models
        comparison = await learner.compare_models(["claude-4-sonnet", "deepseek-v3"])
        
        assert comparison is not None
        assert "claude-4-sonnet" in comparison
        assert "deepseek-v3" in comparison
        
        # Each model should have performance metrics
        for model, metrics in comparison.items():
            assert "avg_quality" in metrics
            assert "avg_cost" in metrics
            assert "avg_latency" in metrics
    
    @pytest.mark.asyncio
    async def test_cleanup(self, learner):
        """Test learner cleanup"""
        await learner.cleanup()
        
        assert not learner.initialized
        # Verify cleanup was called on dependencies
