"""
Performance Benchmarks and Load Testing
Comprehensive performance testing with <100ms P99 latency validation
"""

import pytest
import asyncio
import time
import statistics
import psutil
import gc
from decimal import Decimal
from unittest.mock import patch, AsyncMock
from concurrent.futures import Thread<PERSON>oolExecutor

from src.core.optimizer import CostOptimizer, OptimizationRequest
from src.core.cache import CacheManager
from src.clients.openrouter import OpenRouterClient


class TestPerformanceBenchmarks:
    """Performance benchmark tests for cost optimization platform"""
    
    @pytest.fixture
    async def optimizer(self):
        """Create optimizer for performance testing"""
        optimizer = CostOptimizer()
        await optimizer.initialize()
        return optimizer
    
    @pytest.fixture
    def mock_openrouter_response(self):
        """Mock OpenRouter response for consistent testing"""
        return AsyncMock(
            content="Paris is the capital of France.",
            model="claude-4-sonnet",
            tokens_used=50,
            prompt_tokens=10,
            completion_tokens=40
        )
    
    @pytest.mark.asyncio
    async def test_single_request_latency(self, optimizer, mock_openrouter_response, benchmark):
        """Test single request latency - target <100ms P99"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            # Benchmark single request
            result = benchmark(asyncio.run, optimizer.optimize(request))
            
            assert result is not None
            assert result.response == "Paris is the capital of France."
    
    @pytest.mark.asyncio
    async def test_cache_hit_performance(self, optimizer, mock_openrouter_response, benchmark):
        """Test cache hit performance - should be <10ms"""
        request = OptimizationRequest(
            prompt="What is the capital of France?",
            max_tokens=100,
            temperature=0.7
        )
        
        # Prime the cache
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            await optimizer.optimize(request)
        
        # Benchmark cache hit
        async def cache_hit_test():
            return await optimizer.optimize(request)
        
        result = benchmark(asyncio.run, cache_hit_test())
        
        assert result is not None
        assert result.cache_hit is True
    
    @pytest.mark.asyncio
    async def test_concurrent_request_performance(self, optimizer, mock_openrouter_response):
        """Test concurrent request handling - 100+ simultaneous requests"""
        requests = [
            OptimizationRequest(
                prompt=f"Test question {i}",
                max_tokens=50,
                temperature=0.7
            )
            for i in range(100)
        ]
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            start_time = time.time()
            
            # Execute 100 concurrent requests
            tasks = [optimizer.optimize(req) for req in requests]
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Verify all requests completed
            assert len(results) == 100
            assert all(result is not None for result in results)
            
            # Calculate performance metrics
            avg_time_per_request = total_time / 100
            requests_per_second = 100 / total_time
            
            # Performance assertions
            assert total_time < 10.0  # All 100 requests in under 10 seconds
            assert avg_time_per_request < 0.1  # Average under 100ms
            assert requests_per_second > 10  # At least 10 RPS
            
            print(f"Concurrent Performance Metrics:")
            print(f"  Total time: {total_time:.2f}s")
            print(f"  Average time per request: {avg_time_per_request*1000:.2f}ms")
            print(f"  Requests per second: {requests_per_second:.2f}")
    
    @pytest.mark.asyncio
    async def test_latency_percentiles(self, optimizer, mock_openrouter_response):
        """Test latency percentiles - P99 <100ms requirement"""
        request = OptimizationRequest(
            prompt="Latency test prompt",
            max_tokens=50,
            temperature=0.7
        )
        
        latencies = []
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            # Measure 1000 requests
            for i in range(1000):
                start_time = time.time()
                result = await optimizer.optimize(request)
                end_time = time.time()
                
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                
                assert result is not None
        
        # Calculate percentiles
        latencies.sort()
        p50 = statistics.median(latencies)
        p95 = latencies[int(0.95 * len(latencies))]
        p99 = latencies[int(0.99 * len(latencies))]
        p999 = latencies[int(0.999 * len(latencies))]
        
        # Performance requirements
        assert p50 < 50.0   # P50 < 50ms
        assert p95 < 75.0   # P95 < 75ms
        assert p99 < 100.0  # P99 < 100ms (key requirement)
        assert p999 < 200.0 # P99.9 < 200ms
        
        print(f"Latency Percentiles:")
        print(f"  P50: {p50:.2f}ms")
        print(f"  P95: {p95:.2f}ms")
        print(f"  P99: {p99:.2f}ms")
        print(f"  P99.9: {p999:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, optimizer, mock_openrouter_response):
        """Test memory usage under sustained load"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        request = OptimizationRequest(
            prompt="Memory test prompt",
            max_tokens=100,
            temperature=0.7
        )
        
        memory_samples = []
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            # Sustained load test
            for batch in range(10):  # 10 batches of 50 requests
                tasks = [optimizer.optimize(request) for _ in range(50)]
                await asyncio.gather(*tasks)
                
                # Sample memory usage
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_samples.append(current_memory)
                
                # Force garbage collection
                gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        max_memory = max(memory_samples)
        memory_growth = final_memory - initial_memory
        
        # Memory usage assertions
        assert memory_growth < 100  # Less than 100MB growth
        assert max_memory < initial_memory + 200  # Peak usage reasonable
        
        print(f"Memory Usage Metrics:")
        print(f"  Initial memory: {initial_memory:.2f}MB")
        print(f"  Final memory: {final_memory:.2f}MB")
        print(f"  Max memory: {max_memory:.2f}MB")
        print(f"  Memory growth: {memory_growth:.2f}MB")
    
    @pytest.mark.asyncio
    async def test_cpu_usage_under_load(self, optimizer, mock_openrouter_response):
        """Test CPU usage under sustained load"""
        process = psutil.Process()
        
        request = OptimizationRequest(
            prompt="CPU test prompt",
            max_tokens=100,
            temperature=0.7
        )
        
        cpu_samples = []
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
            # Monitor CPU during load test
            for batch in range(5):  # 5 batches of 100 requests
                batch_start = time.time()
                
                tasks = [optimizer.optimize(request) for _ in range(100)]
                await asyncio.gather(*tasks)
                
                # Sample CPU usage
                cpu_percent = process.cpu_percent()
                cpu_samples.append(cpu_percent)
                
                batch_time = time.time() - batch_start
                print(f"Batch {batch + 1}: {batch_time:.2f}s, CPU: {cpu_percent:.1f}%")
        
        avg_cpu = statistics.mean(cpu_samples)
        max_cpu = max(cpu_samples)
        
        # CPU usage should be reasonable
        assert avg_cpu < 80.0  # Average CPU < 80%
        assert max_cpu < 95.0  # Peak CPU < 95%
        
        print(f"CPU Usage Metrics:")
        print(f"  Average CPU: {avg_cpu:.1f}%")
        print(f"  Max CPU: {max_cpu:.1f}%")
    
    @pytest.mark.asyncio
    async def test_cache_performance_scaling(self, optimizer, mock_openrouter_response):
        """Test cache performance scaling with increasing load"""
        cache_sizes = [10, 50, 100, 500, 1000]
        performance_results = {}
        
        for cache_size in cache_sizes:
            # Create unique requests for this cache size
            requests = [
                OptimizationRequest(
                    prompt=f"Cache scaling test {i} for size {cache_size}",
                    max_tokens=50,
                    temperature=0.7
                )
                for i in range(cache_size)
            ]
            
            with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
                # Prime cache
                start_time = time.time()
                for request in requests:
                    await optimizer.optimize(request)
                prime_time = time.time() - start_time
                
                # Test cache hits
                start_time = time.time()
                for request in requests:
                    result = await optimizer.optimize(request)
                    assert result.cache_hit is True
                cache_hit_time = time.time() - start_time
                
                avg_cache_hit_time = (cache_hit_time / cache_size) * 1000  # ms
                performance_results[cache_size] = avg_cache_hit_time
                
                # Cache hits should remain fast even with larger cache
                assert avg_cache_hit_time < 10.0  # < 10ms per cache hit
        
        print(f"Cache Performance Scaling:")
        for size, avg_time in performance_results.items():
            print(f"  Cache size {size}: {avg_time:.2f}ms avg hit time")
    
    @pytest.mark.asyncio
    async def test_throughput_scaling(self, optimizer, mock_openrouter_response):
        """Test throughput scaling with different concurrency levels"""
        concurrency_levels = [1, 5, 10, 25, 50, 100]
        throughput_results = {}
        
        request = OptimizationRequest(
            prompt="Throughput scaling test",
            max_tokens=50,
            temperature=0.7
        )
        
        for concurrency in concurrency_levels:
            with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_openrouter_response):
                start_time = time.time()
                
                # Execute requests at this concurrency level
                tasks = [optimizer.optimize(request) for _ in range(concurrency)]
                results = await asyncio.gather(*tasks)
                
                end_time = time.time()
                total_time = end_time - start_time
                throughput = concurrency / total_time  # requests per second
                
                throughput_results[concurrency] = throughput
                
                assert len(results) == concurrency
                assert all(result is not None for result in results)
        
        print(f"Throughput Scaling Results:")
        for concurrency, rps in throughput_results.items():
            print(f"  Concurrency {concurrency}: {rps:.2f} RPS")
        
        # Throughput should scale reasonably with concurrency
        assert throughput_results[100] > throughput_results[1] * 5  # At least 5x improvement
    
    @pytest.mark.asyncio
    async def test_error_handling_performance(self, optimizer):
        """Test performance impact of error handling"""
        request = OptimizationRequest(
            prompt="Error handling test",
            max_tokens=50,
            temperature=0.7
        )
        
        error_latencies = []
        success_latencies = []
        
        # Test error scenarios
        with patch.object(optimizer.openrouter_client, 'generate', side_effect=Exception("API Error")):
            for _ in range(100):
                start_time = time.time()
                try:
                    await optimizer.optimize(request)
                except:
                    pass  # Expected to fail
                end_time = time.time()
                error_latencies.append((end_time - start_time) * 1000)
        
        # Test success scenarios
        mock_response = AsyncMock(
            content="Success response",
            model="claude-4-sonnet",
            tokens_used=25,
            prompt_tokens=5,
            completion_tokens=20
        )
        
        with patch.object(optimizer.openrouter_client, 'generate', return_value=mock_response):
            for _ in range(100):
                start_time = time.time()
                await optimizer.optimize(request)
                end_time = time.time()
                success_latencies.append((end_time - start_time) * 1000)
        
        avg_error_latency = statistics.mean(error_latencies)
        avg_success_latency = statistics.mean(success_latencies)
        
        # Error handling should not significantly impact performance
        assert avg_error_latency < 50.0  # Error handling < 50ms
        assert avg_success_latency < 100.0  # Success case < 100ms
        
        print(f"Error Handling Performance:")
        print(f"  Average error latency: {avg_error_latency:.2f}ms")
        print(f"  Average success latency: {avg_success_latency:.2f}ms")
