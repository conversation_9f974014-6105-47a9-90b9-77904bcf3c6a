"""
Performance and Load Testing
FAANG+ performance testing with comprehensive metrics
"""

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import aiohttp
from fastapi.testclient import TestClient

from src.main import app


class TestPerformance:
    """Performance test suite for optimization endpoints"""
    
    @pytest.fixture
    def client(self):
        """Test client for performance testing"""
        return TestClient(app)
    
    @pytest.fixture
    def api_headers(self):
        """API headers for testing"""
        return {
            "Content-Type": "application/json",
            "Authorization": "Bearer demo-key-12345"
        }
    
    @pytest.fixture
    def sample_requests(self):
        """Sample optimization requests for load testing"""
        return [
            {
                "prompt": "Write a Python function to calculate fibonacci numbers",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": f"user_{i}"
            }
            for i in range(100)
        ]
    
    def test_single_request_latency(self, client, api_headers):
        """Test single request latency requirements (<100ms target)"""
        request_data = {
            "prompt": "Write a simple hello world function",
            "quality_threshold": 0.8,
            "optimization_level": 2,
            "user_id": "perf_test_user"
        }
        
        latencies = []
        
        # Warm up
        for _ in range(5):
            client.post("/api/v1/optimize", json=request_data, headers=api_headers)
        
        # Measure latency
        for _ in range(50):
            start_time = time.time()
            response = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
            end_time = time.time()
            
            assert response.status_code == 200
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
        
        # Performance assertions
        avg_latency = statistics.mean(latencies)
        p95_latency = statistics.quantiles(latencies, n=20)[18]  # 95th percentile
        p99_latency = statistics.quantiles(latencies, n=100)[98]  # 99th percentile
        
        print(f"Average latency: {avg_latency:.2f}ms")
        print(f"P95 latency: {p95_latency:.2f}ms")
        print(f"P99 latency: {p99_latency:.2f}ms")
        
        # Performance requirements
        assert avg_latency < 100, f"Average latency {avg_latency:.2f}ms exceeds 100ms target"
        assert p95_latency < 200, f"P95 latency {p95_latency:.2f}ms exceeds 200ms target"
        assert p99_latency < 500, f"P99 latency {p99_latency:.2f}ms exceeds 500ms target"
    
    def test_concurrent_requests_throughput(self, client, api_headers, sample_requests):
        """Test concurrent request handling and throughput"""
        def make_request(request_data):
            start_time = time.time()
            response = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
            end_time = time.time()
            
            return {
                'status_code': response.status_code,
                'latency_ms': (end_time - start_time) * 1000,
                'success': response.status_code == 200
            }
        
        # Test with different concurrency levels
        concurrency_levels = [1, 5, 10, 20]
        
        for concurrency in concurrency_levels:
            print(f"\nTesting with {concurrency} concurrent requests")
            
            # Use subset of requests for each test
            test_requests = sample_requests[:concurrency * 5]
            
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [
                    executor.submit(make_request, req) 
                    for req in test_requests
                ]
                
                results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            
            # Calculate metrics
            total_time = end_time - start_time
            total_requests = len(test_requests)
            successful_requests = sum(1 for r in results if r['success'])
            failed_requests = total_requests - successful_requests
            
            throughput = total_requests / total_time
            success_rate = successful_requests / total_requests
            
            latencies = [r['latency_ms'] for r in results if r['success']]
            avg_latency = statistics.mean(latencies) if latencies else 0
            
            print(f"Throughput: {throughput:.2f} requests/second")
            print(f"Success rate: {success_rate:.2%}")
            print(f"Average latency: {avg_latency:.2f}ms")
            print(f"Failed requests: {failed_requests}")
            
            # Performance assertions
            assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95% threshold"
            assert throughput >= 10, f"Throughput {throughput:.2f} req/s below 10 req/s threshold"
            
            if concurrency <= 10:
                assert avg_latency < 200, f"Average latency {avg_latency:.2f}ms too high for {concurrency} concurrent requests"
    
    @pytest.mark.asyncio
    async def test_async_concurrent_load(self):
        """Test async concurrent load handling"""
        async def make_async_request(session, request_data):
            start_time = time.time()
            
            async with session.post(
                "http://localhost:8000/api/v1/optimize",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer demo-key-12345"
                }
            ) as response:
                await response.json()
                end_time = time.time()
                
                return {
                    'status': response.status,
                    'latency_ms': (end_time - start_time) * 1000,
                    'success': response.status == 200
                }
        
        request_data = {
            "prompt": "Write a function to reverse a string",
            "quality_threshold": 0.8,
            "optimization_level": 2,
            "user_id": "async_test_user"
        }
        
        # Test with high concurrency
        num_requests = 100
        max_concurrent = 50
        
        async with aiohttp.ClientSession() as session:
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def bounded_request():
                async with semaphore:
                    return await make_async_request(session, request_data)
            
            start_time = time.time()
            
            tasks = [bounded_request() for _ in range(num_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
        
        # Filter out exceptions and calculate metrics
        valid_results = [r for r in results if isinstance(r, dict)]
        successful_results = [r for r in valid_results if r['success']]
        
        total_time = end_time - start_time
        throughput = len(valid_results) / total_time
        success_rate = len(successful_results) / num_requests
        
        if successful_results:
            latencies = [r['latency_ms'] for r in successful_results]
            avg_latency = statistics.mean(latencies)
            p95_latency = statistics.quantiles(latencies, n=20)[18]
        else:
            avg_latency = 0
            p95_latency = 0
        
        print(f"Async load test results:")
        print(f"Total requests: {num_requests}")
        print(f"Successful requests: {len(successful_results)}")
        print(f"Success rate: {success_rate:.2%}")
        print(f"Throughput: {throughput:.2f} requests/second")
        print(f"Average latency: {avg_latency:.2f}ms")
        print(f"P95 latency: {p95_latency:.2f}ms")
        
        # Performance assertions
        assert success_rate >= 0.90, f"Success rate {success_rate:.2%} below 90% threshold"
        assert throughput >= 20, f"Throughput {throughput:.2f} req/s below 20 req/s threshold"
    
    def test_memory_usage_under_load(self, client, api_headers):
        """Test memory usage under sustained load"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        request_data = {
            "prompt": "Create a comprehensive data processing pipeline",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "memory_test_user"
        }
        
        memory_samples = []
        
        # Make sustained requests while monitoring memory
        for i in range(100):
            response = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
            assert response.status_code == 200
            
            if i % 10 == 0:  # Sample memory every 10 requests
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_samples.append(current_memory)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        max_memory = max(memory_samples)
        memory_growth = final_memory - initial_memory
        
        print(f"Initial memory: {initial_memory:.2f} MB")
        print(f"Final memory: {final_memory:.2f} MB")
        print(f"Max memory: {max_memory:.2f} MB")
        print(f"Memory growth: {memory_growth:.2f} MB")
        
        # Memory usage assertions
        assert memory_growth < 100, f"Memory growth {memory_growth:.2f} MB exceeds 100 MB threshold"
        assert max_memory < initial_memory + 150, f"Peak memory usage too high"
    
    def test_cache_performance_impact(self, client, api_headers):
        """Test cache performance impact"""
        request_data = {
            "prompt": "Write a function to calculate prime numbers",
            "quality_threshold": 0.8,
            "optimization_level": 3,
            "user_id": "cache_test_user"
        }
        
        # First request (cache miss)
        start_time = time.time()
        response1 = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
        cache_miss_time = time.time() - start_time
        
        assert response1.status_code == 200
        
        # Second identical request (cache hit)
        start_time = time.time()
        response2 = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
        cache_hit_time = time.time() - start_time
        
        assert response2.status_code == 200
        
        # Cache hit should be significantly faster
        speedup_ratio = cache_miss_time / cache_hit_time
        
        print(f"Cache miss time: {cache_miss_time * 1000:.2f}ms")
        print(f"Cache hit time: {cache_hit_time * 1000:.2f}ms")
        print(f"Speedup ratio: {speedup_ratio:.2f}x")
        
        assert speedup_ratio >= 2.0, f"Cache speedup {speedup_ratio:.2f}x below 2x threshold"
        assert cache_hit_time < 0.050, f"Cache hit time {cache_hit_time * 1000:.2f}ms exceeds 50ms"
    
    def test_batch_processing_performance(self, client, api_headers):
        """Test batch processing performance vs individual requests"""
        individual_requests = [
            {
                "prompt": f"Write a function to process data type {i}",
                "quality_threshold": 0.8,
                "optimization_level": 2,
                "user_id": "batch_test_user"
            }
            for i in range(10)
        ]
        
        # Test individual requests
        start_time = time.time()
        individual_responses = []
        for req in individual_requests:
            response = client.post("/api/v1/optimize", json=req, headers=api_headers)
            individual_responses.append(response)
        individual_time = time.time() - start_time
        
        # Test batch request
        batch_request = {
            "requests": individual_requests,
            "parallel_processing": True
        }
        
        start_time = time.time()
        batch_response = client.post("/api/v1/optimize/batch", json=batch_request, headers=api_headers)
        batch_time = time.time() - start_time
        
        assert batch_response.status_code == 200
        
        # Batch should be faster than individual requests
        speedup_ratio = individual_time / batch_time
        
        print(f"Individual requests time: {individual_time:.2f}s")
        print(f"Batch request time: {batch_time:.2f}s")
        print(f"Batch speedup: {speedup_ratio:.2f}x")
        
        assert speedup_ratio >= 1.5, f"Batch speedup {speedup_ratio:.2f}x below 1.5x threshold"
    
    def test_error_rate_under_load(self, client, api_headers):
        """Test error rate under high load"""
        request_data = {
            "prompt": "Test prompt for error rate testing",
            "quality_threshold": 0.8,
            "optimization_level": 2,
            "user_id": "error_test_user"
        }
        
        total_requests = 200
        successful_requests = 0
        error_responses = []
        
        for i in range(total_requests):
            try:
                response = client.post("/api/v1/optimize", json=request_data, headers=api_headers)
                if response.status_code == 200:
                    successful_requests += 1
                else:
                    error_responses.append(response.status_code)
            except Exception as e:
                error_responses.append(str(e))
        
        success_rate = successful_requests / total_requests
        error_rate = 1 - success_rate
        
        print(f"Total requests: {total_requests}")
        print(f"Successful requests: {successful_requests}")
        print(f"Success rate: {success_rate:.2%}")
        print(f"Error rate: {error_rate:.2%}")
        
        if error_responses:
            print(f"Error types: {set(error_responses)}")
        
        # Error rate should be minimal under normal load
        assert error_rate <= 0.05, f"Error rate {error_rate:.2%} exceeds 5% threshold"
        assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95% threshold"
