"""
Comprehensive Testing Framework
FAANG+ implementation with unit, integration, performance, and security testing
Implements Google testing practices with TikTok performance validation
"""

import asyncio
import logging
import time
import pytest
import pytest_asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from unittest.mock import Mock, AsyncMock, patch
import json
import uuid
from datetime import datetime

from src.core.optimizer import CostOptimizer
from src.core.models import OptimizationRequest, OptimizationLevel, TaskComplexity
from src.services.cache_manager import CacheManager
from src.services.compression_engine import CompressionEngine
from src.services.model_router import ModelRouter
from src.services.security_service import SecurityService
from src.services.async_processor import AsyncProcessor

logger = logging.getLogger(__name__)


@dataclass
class TestResult:
    """Test result with comprehensive metrics"""
    test_name: str
    status: str  # PASS, FAIL, SKIP
    duration_ms: float
    memory_usage_mb: float
    assertions_count: int
    error_message: Optional[str] = None
    performance_metrics: Optional[Dict[str, float]] = None


class TestMetrics:
    """Test metrics collector for performance analysis"""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.start_time = time.time()
        self.memory_baseline = 0
    
    def record_result(self, result: TestResult):
        """Record test result"""
        self.results.append(result)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get test summary statistics"""
        total_tests = len(self.results)
        passed = len([r for r in self.results if r.status == 'PASS'])
        failed = len([r for r in self.results if r.status == 'FAIL'])
        skipped = len([r for r in self.results if r.status == 'SKIP'])
        
        total_duration = sum(r.duration_ms for r in self.results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0
        
        return {
            'total_tests': total_tests,
            'passed': passed,
            'failed': failed,
            'skipped': skipped,
            'pass_rate': passed / total_tests if total_tests > 0 else 0,
            'total_duration_ms': total_duration,
            'average_duration_ms': avg_duration,
            'total_execution_time': time.time() - self.start_time
        }


class MockServices:
    """Mock services for testing"""
    
    @staticmethod
    def create_mock_cache_manager() -> Mock:
        """Create mock cache manager"""
        mock_cache = AsyncMock(spec=CacheManager)
        mock_cache.get_cached_result.return_value = None  # No cache hit by default
        mock_cache.cache_result.return_value = None
        mock_cache.get_stats.return_value = {
            'overall_hit_rate': 0.85,
            'total_requests': 1000,
            'total_hits': 850
        }
        return mock_cache
    
    @staticmethod
    def create_mock_model_router() -> Mock:
        """Create mock model router"""
        mock_router = AsyncMock(spec=ModelRouter)
        mock_router.route_request.return_value = {
            'selected_model': 'deepseek-v3',
            'routing_reason': 'cost_optimization',
            'estimated_cost': 0.001,
            'quality_score': 0.92
        }
        mock_router.get_model_health.return_value = True
        return mock_router
    
    @staticmethod
    def create_mock_compression_engine() -> Mock:
        """Create mock compression engine"""
        mock_compression = AsyncMock(spec=CompressionEngine)
        mock_compression.compress.return_value = ("compressed text", 0.7)
        mock_compression.get_stats.return_value = {
            'total_compressions': 500,
            'average_compression_ratio': 0.72
        }
        return mock_compression


class PerformanceTestSuite:
    """Performance testing suite for optimization pipeline"""
    
    def __init__(self):
        self.metrics = TestMetrics()
        self.performance_thresholds = {
            'optimization_latency_p99': 50.0,  # 50ms P99
            'cache_hit_rate': 0.95,  # 95% cache hit rate
            'compression_ratio': 0.70,  # 70% compression
            'quality_score': 0.85,  # 85% quality preservation
            'throughput_rps': 1000,  # 1000 requests per second
        }
    
    async def test_optimization_latency(self, optimizer: CostOptimizer, sample_size: int = 100):
        """Test optimization latency under load"""
        latencies = []
        
        for i in range(sample_size):
            request = OptimizationRequest(
                prompt=f"Test optimization request {i} with some content to optimize",
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8
            )
            
            start_time = time.time()
            response = await optimizer.optimize(request)
            latency = (time.time() - start_time) * 1000  # Convert to ms
            
            latencies.append(latency)
        
        # Calculate percentiles
        latencies.sort()
        p50 = latencies[len(latencies) // 2]
        p95 = latencies[int(len(latencies) * 0.95)]
        p99 = latencies[int(len(latencies) * 0.99)]
        
        # Validate against thresholds
        assert p99 <= self.performance_thresholds['optimization_latency_p99'], \
            f"P99 latency {p99:.2f}ms exceeds threshold {self.performance_thresholds['optimization_latency_p99']}ms"
        
        return {
            'p50_latency_ms': p50,
            'p95_latency_ms': p95,
            'p99_latency_ms': p99,
            'average_latency_ms': sum(latencies) / len(latencies),
            'sample_size': sample_size
        }
    
    async def test_throughput(self, optimizer: CostOptimizer, duration_seconds: int = 10):
        """Test system throughput"""
        start_time = time.time()
        completed_requests = 0
        errors = 0
        
        async def make_request():
            nonlocal completed_requests, errors
            try:
                request = OptimizationRequest(
                    prompt="Throughput test request for performance validation",
                    optimization_level=OptimizationLevel.MODERATE,
                    quality_threshold=0.8
                )
                await optimizer.optimize(request)
                completed_requests += 1
            except Exception as e:
                errors += 1
                logger.error(f"Throughput test request failed: {e}")
        
        # Generate load for specified duration
        tasks = []
        while time.time() - start_time < duration_seconds:
            task = asyncio.create_task(make_request())
            tasks.append(task)
            await asyncio.sleep(0.001)  # 1ms between requests
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        actual_duration = time.time() - start_time
        throughput = completed_requests / actual_duration
        
        # Validate against threshold
        assert throughput >= self.performance_thresholds['throughput_rps'], \
            f"Throughput {throughput:.2f} RPS below threshold {self.performance_thresholds['throughput_rps']} RPS"
        
        return {
            'throughput_rps': throughput,
            'completed_requests': completed_requests,
            'errors': errors,
            'error_rate': errors / (completed_requests + errors) if (completed_requests + errors) > 0 else 0,
            'duration_seconds': actual_duration
        }
    
    async def test_memory_usage(self, optimizer: CostOptimizer, request_count: int = 1000):
        """Test memory usage under load"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate load
        for i in range(request_count):
            request = OptimizationRequest(
                prompt=f"Memory test request {i} with content to process",
                optimization_level=OptimizationLevel.MODERATE,
                quality_threshold=0.8
            )
            await optimizer.optimize(request)
            
            # Check memory every 100 requests
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - initial_memory
                
                # Fail if memory growth exceeds 500MB
                assert memory_growth < 500, \
                    f"Memory growth {memory_growth:.2f}MB exceeds 500MB threshold"
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_growth = final_memory - initial_memory
        
        return {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'memory_growth_mb': memory_growth,
            'requests_processed': request_count
        }


class SecurityTestSuite:
    """Security testing suite"""
    
    def __init__(self):
        self.security_service = SecurityService()
    
    async def test_sql_injection_detection(self):
        """Test SQL injection detection"""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "1' UNION SELECT * FROM users--",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --"
        ]
        
        for malicious_input in malicious_inputs:
            request_data = {'prompt': malicious_input}
            is_valid, events = await self.security_service.validate_request(
                source_ip='127.0.0.1',
                user_id='test_user',
                request_data=request_data
            )
            
            # Should detect threat and block request
            assert not is_valid, f"Failed to detect SQL injection: {malicious_input}"
            assert any(event.threat_type and 'sql_injection' in event.threat_type.value for event in events), \
                f"SQL injection not properly classified: {malicious_input}"
    
    async def test_xss_detection(self):
        """Test XSS detection"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<iframe src='javascript:alert(\"XSS\")'></iframe>",
            "onload=alert('XSS')"
        ]
        
        for payload in xss_payloads:
            request_data = {'prompt': payload}
            is_valid, events = await self.security_service.validate_request(
                source_ip='127.0.0.1',
                user_id='test_user',
                request_data=request_data
            )
            
            # Should detect threat and block request
            assert not is_valid, f"Failed to detect XSS: {payload}"
            assert any(event.threat_type and 'xss' in event.threat_type.value for event in events), \
                f"XSS not properly classified: {payload}"
    
    async def test_encryption_decryption(self):
        """Test encryption/decryption functionality"""
        await self.security_service.initialize()
        
        test_data = "Sensitive information that needs encryption"
        
        # Test encryption
        encrypted = await self.security_service.encrypt_sensitive_data(test_data)
        assert encrypted != test_data, "Data was not encrypted"
        assert len(encrypted) > 0, "Encrypted data is empty"
        
        # Test decryption
        decrypted = await self.security_service.decrypt_sensitive_data(encrypted)
        assert decrypted == test_data, "Decryption failed to restore original data"
    
    async def test_brute_force_detection(self):
        """Test brute force attack detection"""
        source_ip = '*************'
        
        # Simulate multiple failed attempts
        for i in range(15):  # Exceed threshold of 10
            request_data = {'username': 'admin', 'password': f'wrong_password_{i}'}
            is_valid, events = await self.security_service.validate_request(
                source_ip=source_ip,
                user_id=None,
                request_data=request_data
            )
        
        # Should detect brute force after threshold
        threat_events = [e for e in events if e.threat_type and 'brute_force' in e.threat_type.value]
        assert len(threat_events) > 0, "Failed to detect brute force attack"


class IntegrationTestSuite:
    """Integration testing suite"""
    
    async def test_end_to_end_optimization(self):
        """Test complete optimization pipeline"""
        # Initialize optimizer with real services
        optimizer = CostOptimizer()
        await optimizer.initialize()
        
        # Test request
        request = OptimizationRequest(
            prompt="Optimize this text for cost reduction while maintaining quality",
            optimization_level=OptimizationLevel.AGGRESSIVE,
            quality_threshold=0.85,
            user_id="integration_test_user"
        )
        
        # Execute optimization
        response = await optimizer.optimize(request)
        
        # Validate response
        assert response.optimized_prompt is not None
        assert response.selected_model is not None
        assert response.savings_percentage >= 0
        assert response.quality_score >= request.quality_threshold
        assert response.processing_time_ms > 0
        
        # Validate cost optimization
        if not response.cache_hit:
            assert response.optimized_cost <= response.original_cost
        
        return response
    
    async def test_cache_integration(self):
        """Test cache integration across optimization pipeline"""
        optimizer = CostOptimizer()
        await optimizer.initialize()
        
        test_prompt = "Test prompt for cache integration validation"
        
        # First request - should miss cache
        request1 = OptimizationRequest(
            prompt=test_prompt,
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        response1 = await optimizer.optimize(request1)
        assert not response1.cache_hit, "First request should not hit cache"
        
        # Second identical request - should hit cache
        request2 = OptimizationRequest(
            prompt=test_prompt,
            optimization_level=OptimizationLevel.MODERATE,
            quality_threshold=0.8
        )
        
        response2 = await optimizer.optimize(request2)
        assert response2.cache_hit, "Second identical request should hit cache"
        assert response2.optimized_cost == 0.0, "Cache hit should have zero cost"
        
        return response1, response2
