# OrbStack Optimized Configuration
# Production-grade internal networking with service discovery
# Designed for macOS ARM64 with maximum security and performance

version: '3.8'

services:
  # Main API service - ONLY service exposed externally
  api:
    build: 
      context: .
      dockerfile: Dockerfile
      target: production
    command: ["./simple-entrypoint.sh"]
    ports:
      - "8000:8000"  # ONLY external port - main dashboard access
    environment:
      # Service discovery URLs (internal hostnames)
      - DATABASE_URL=postgresql+asyncpg://costopt:${POSTGRES_PASSWORD:-costopt123}@db:5432/costopt
      - REDIS_URL=redis://redis:6379/0
      - CHROMADB_URL=http://chromadb:8000
      
      # OpenRouter API configuration
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-your_openrouter_api_key_here}
      
      # Environment configuration
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      
      # Security configuration
      - SECRET_KEY=${SECRET_KEY:-$(openssl rand -base64 64)}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:8000,https://localhost:8000}
      
      # Internal service discovery
      - SERVICE_DISCOVERY_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30
      
      # Monitoring endpoints (internal)
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      - PROMETHEUS_ENDPOINT=http://prometheus:9090
      - GRAFANA_ENDPOINT=http://grafana:3000
      - N8N_ENDPOINT=http://n8n:5678
      
      # Performance tuning for OrbStack
      - WORKERS=4
      - MAX_WORKERS=8
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=1000
      - KEEPALIVE=2
      - MAX_REQUESTS=1000
      - MAX_REQUESTS_JITTER=100
      
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    
    volumes:
      - ./logs:/app/logs:rw
      - ./data:/app/data:rw
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "python", "healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    networks:
      - costopt-internal
    
    # OrbStack optimizations
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    
    # Security hardening
    security_opt:
      - no-new-privileges:true
    read_only: false  # Need write access for logs
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # PostgreSQL database with pgvector - INTERNAL ONLY
  db:
    image: pgvector/pgvector:pg15
    hostname: db
    
    environment:
      - POSTGRES_DB=costopt
      - POSTGRES_USER=costopt
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-costopt123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --locale=C
      - PGDATA=/var/lib/postgresql/data/pgdata
    
    # NO external ports - internal access only
    expose:
      - "5432"
    
    volumes:
      - postgres_data:/var/lib/postgresql/data:rw
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./scripts/init-extensions.sql:/docker-entrypoint-initdb.d/02-extensions.sql:ro
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U costopt -d costopt -h localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    networks:
      - costopt-internal
    
    # Performance tuning for OrbStack
    command: >
      postgres
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis cache - INTERNAL ONLY
  redis:
    image: redis:7-alpine
    hostname: redis
    
    # NO external ports - internal access only
    expose:
      - "6379"
    
    volumes:
      - redis_data:/data:rw
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    command: >
      redis-server /usr/local/etc/redis/redis.conf
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --bind 0.0.0.0
      --protected-mode no
      --save 900 1
      --save 300 10
      --save 60 10000
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    networks:
      - costopt-internal
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # ChromaDB for vector similarity - INTERNAL ONLY
  chromadb:
    image: chromadb/chroma:latest
    hostname: chromadb
    
    # NO external ports - internal access only
    expose:
      - "8000"
    
    volumes:
      - chromadb_data:/chroma/chroma:rw
    
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["http://api:8000"]
      - ANONYMIZED_TELEMETRY=false
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    networks:
      - costopt-internal
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Jaeger for distributed tracing - INTERNAL ONLY
  jaeger:
    image: jaegertracing/all-in-one:latest
    hostname: jaeger
    
    # NO external ports - accessible via API proxy
    expose:
      - "14268"  # Collector HTTP
      - "16686"  # UI
    
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
      - MEMORY_MAX_TRACES=50000
    
    restart: unless-stopped
    
    networks:
      - costopt-internal
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Prometheus for metrics - INTERNAL ONLY
  prometheus:
    image: prom/prometheus:latest
    hostname: prometheus
    
    # NO external ports - accessible via API proxy
    expose:
      - "9090"
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus:rw
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=168h'  # 1 week
      - '--web.enable-lifecycle'
      - '--web.listen-address=0.0.0.0:9090'
      - '--storage.tsdb.wal-compression'
    
    restart: unless-stopped
    
    networks:
      - costopt-internal
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Grafana for visualization - INTERNAL ONLY
  grafana:
    image: grafana/grafana:latest
    hostname: grafana
    
    # NO external ports - accessible via API proxy
    expose:
      - "3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_SERVER_HTTP_ADDR=0.0.0.0
      - GF_SERVER_HTTP_PORT=3000
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    
    volumes:
      - grafana_data:/var/lib/grafana:rw
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    restart: unless-stopped
    
    networks:
      - costopt-internal
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

# Persistent volumes with OrbStack optimization
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis
  
  chromadb_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/chromadb
  
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/prometheus
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/grafana

# OrbStack optimized internal network
networks:
  costopt-internal:
    name: costopt-internal
    driver: bridge
    internal: false  # Allow external access for API service only
    enable_ipv6: false
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
          ip_range: **********/24
    driver_opts:
      com.docker.network.bridge.name: costopt-br0
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: 1500
