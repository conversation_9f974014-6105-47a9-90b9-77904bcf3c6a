#!/usr/bin/env python3
"""
Full System Test - Conversation Management with Cost Optimization
Tests the complete ChatGPT/Claude Desktop style conversation system
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime

# Add project root to path
sys.path.append('/home/<USER>/cost-opt-platform')

from src.services.conversation_service import ConversationService
from src.models.conversation import ConversationCreate, MessageCreate, ConversationUpdate
from src.core.database import get_database

async def test_full_conversation_system():
    """Test the complete conversation management system"""
    print("🎯 FULL SYSTEM TEST - Conversation Management with Cost Optimization")
    print("=" * 80)
    
    # Initialize database
    db = get_database()
    await db.connect()
    
    service = ConversationService()
    test_results = {
        "timestamp": datetime.utcnow().isoformat(),
        "tests_passed": 0,
        "tests_failed": 0,
        "total_cost": 0.0,
        "conversations_created": 0,
        "messages_sent": 0,
        "performance_metrics": {}
    }
    
    try:
        # Test 1: Create Multiple Conversations
        print("\n🧪 Test 1: Creating multiple conversations...")
        start_time = time.time()
        
        conversations = []
        for i in range(3):
            conv_data = ConversationCreate(
                title=f"Cost Optimization Session {i+1}",
                user_id="test-user",
                metadata={
                    "model": "claude-4-sonnet",
                    "cost_optimization": True,
                    "session_type": "optimization",
                    "priority": "high" if i == 0 else "normal"
                }
            )
            
            conversation = await service.create_conversation(conv_data)
            conversations.append(conversation)
            test_results["conversations_created"] += 1
            print(f"  ✅ Created conversation {i+1}: {conversation.id}")
        
        test_results["performance_metrics"]["conversation_creation"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 2: Add Messages with Cost Tracking
        print("\n🧪 Test 2: Adding messages with cost optimization...")
        start_time = time.time()
        
        conversation = conversations[0]
        
        # Simulate a realistic conversation about cost optimization
        messages_data = [
            {
                "role": "user",
                "content": "Hello Claude! I need help optimizing my AI costs. I'm currently spending $500/month on OpenAI API calls.",
                "token_count": 25,
                "cost_usd": "0.002",
                "model_used": "claude-4-sonnet"
            },
            {
                "role": "assistant", 
                "content": "I'll help you achieve 85-95% cost reduction! Here's my analysis:\n\n1. **Prompt Optimization**: Compress your prompts by 60%\n2. **Smart Caching**: Implement Redis caching for repeated queries\n3. **Model Selection**: Use Claude 4 Sonnet via OpenRouter for better pricing\n4. **Batching**: Process multiple requests together\n\nWith these optimizations, your $500/month could drop to $25-75/month while maintaining quality.",
                "token_count": 95,
                "cost_usd": "0.008",
                "model_used": "claude-4-sonnet",
                "metadata": {
                    "optimization_applied": True,
                    "compression_ratio": 0.87,
                    "cost_savings_estimate": "85-95%",
                    "strategies_suggested": 4
                }
            },
            {
                "role": "user",
                "content": "That sounds amazing! Can you show me how to implement prompt compression?",
                "token_count": 18,
                "cost_usd": "0.001",
                "model_used": "claude-4-sonnet"
            },
            {
                "role": "assistant",
                "content": "Absolutely! Here's a practical prompt compression example:\n\n**Before (verbose):**\n```\nPlease analyze the following customer feedback and provide a detailed summary of the main themes, sentiment analysis, and actionable recommendations for improvement.\n```\n\n**After (compressed):**\n```\nAnalyze feedback: themes, sentiment, recommendations.\n```\n\n**Result**: 87% token reduction, same output quality!\n\nKey techniques:\n- Remove filler words\n- Use abbreviations\n- Bullet points over sentences\n- Context compression",
                "token_count": 78,
                "cost_usd": "0.007",
                "model_used": "claude-4-sonnet",
                "metadata": {
                    "optimization_applied": True,
                    "compression_ratio": 0.87,
                    "example_provided": True,
                    "token_reduction": "87%"
                }
            }
        ]
        
        for msg_data in messages_data:
            message = MessageCreate(**msg_data)
            result = await service.add_message(conversation.id, message)
            test_results["messages_sent"] += 1
            test_results["total_cost"] += float(msg_data["cost_usd"])
            print(f"  ✅ Added {msg_data['role']} message: {result.id} (${msg_data['cost_usd']})")
        
        test_results["performance_metrics"]["message_creation"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 3: Retrieve Full Conversation
        print("\n🧪 Test 3: Retrieving full conversation with messages...")
        start_time = time.time()
        
        full_conversation = await service.get_conversation(conversation.id, include_messages=True)
        
        print(f"  ✅ Retrieved conversation: {full_conversation.title}")
        print(f"  ✅ Messages count: {len(full_conversation.messages)}")
        print(f"  ✅ Total cost: ${full_conversation.total_cost}")
        print(f"  ✅ Last updated: {full_conversation.updated_at}")
        
        test_results["performance_metrics"]["conversation_retrieval"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 4: List Conversations with Pagination
        print("\n🧪 Test 4: Testing conversation listing and pagination...")
        start_time = time.time()
        
        conversations_list = await service.list_conversations(
            user_id="test-user",
            page=1,
            page_size=10
        )
        
        print(f"  ✅ Found {conversations_list.total} conversations")
        print(f"  ✅ Page {conversations_list.page} of {conversations_list.page_size}")
        print(f"  ✅ Has next: {conversations_list.has_next}")
        
        test_results["performance_metrics"]["conversation_listing"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 5: Update Conversation
        print("\n🧪 Test 5: Updating conversation metadata...")
        start_time = time.time()
        
        update_data = ConversationUpdate(
            title="Advanced Cost Optimization Discussion - Claude 4 Sonnet",
            metadata={
                "updated": True,
                "optimization_level": "advanced",
                "cost_savings_achieved": "87%",
                "techniques_covered": ["prompt_compression", "caching", "batching"]
            }
        )
        
        updated_conversation = await service.update_conversation(conversation.id, update_data)
        print(f"  ✅ Updated conversation title: {updated_conversation.title}")
        print(f"  ✅ Updated metadata: {updated_conversation.metadata}")
        
        test_results["performance_metrics"]["conversation_update"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 6: Search Messages
        print("\n🧪 Test 6: Testing message search functionality...")
        start_time = time.time()
        
        from src.models.conversation import MessageSearchRequest
        search_request = MessageSearchRequest(
            query="cost optimization",
            role="assistant",
            limit=10
        )
        
        search_results = await service.search_messages(search_request)
        print(f"  ✅ Found {len(search_results)} messages matching 'cost optimization'")
        
        test_results["performance_metrics"]["message_search"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Test 7: Get Conversation Statistics
        print("\n🧪 Test 7: Getting conversation statistics...")
        start_time = time.time()
        
        stats = await service.get_conversation_stats(user_id="test-user")
        print(f"  ✅ Total conversations: {stats.total_conversations}")
        print(f"  ✅ Total messages: {stats.total_messages}")
        print(f"  ✅ Total cost: ${stats.total_cost_usd}")
        print(f"  ✅ Avg messages per conversation: {stats.avg_messages_per_conversation}")
        print(f"  ✅ Most used model: {stats.most_used_model}")
        
        test_results["performance_metrics"]["statistics_generation"] = time.time() - start_time
        test_results["tests_passed"] += 1
        
        # Display Final Results
        print("\n" + "=" * 80)
        print("📊 FULL SYSTEM TEST RESULTS")
        print("=" * 80)
        print(f"✅ Tests Passed: {test_results['tests_passed']}")
        print(f"❌ Tests Failed: {test_results['tests_failed']}")
        print(f"💬 Conversations Created: {test_results['conversations_created']}")
        print(f"📝 Messages Sent: {test_results['messages_sent']}")
        print(f"💰 Total Cost: ${test_results['total_cost']:.3f}")
        
        print(f"\n⚡ Performance Metrics:")
        for metric, duration in test_results["performance_metrics"].items():
            print(f"  {metric}: {duration:.3f}s")
        
        # Detailed Conversation Summary
        print(f"\n📋 Conversation Summary:")
        print(f"  ID: {full_conversation.id}")
        print(f"  Title: {full_conversation.title}")
        print(f"  Messages: {len(full_conversation.messages)}")
        print(f"  Total Cost: ${full_conversation.total_cost}")
        print(f"  Created: {full_conversation.created_at}")
        print(f"  Updated: {full_conversation.updated_at}")
        
        print(f"\n💬 Message Breakdown:")
        for i, msg in enumerate(full_conversation.messages, 1):
            print(f"  {i}. [{msg.role.upper()}] {msg.content[:60]}...")
            print(f"     Tokens: {msg.token_count}, Cost: ${msg.cost_usd}")
            if msg.metadata:
                print(f"     Optimization: {msg.metadata}")
        
        print(f"\n🎉 ALL TESTS PASSED! Conversation system is fully operational.")
        print(f"🚀 Ready for production deployment with Claude 4 Sonnet cost optimization.")
        
        # Save test results
        with open('/home/<USER>/cost-opt-platform/test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        test_results["tests_failed"] += 1
        return False
    
    finally:
        await db.disconnect()

if __name__ == "__main__":
    success = asyncio.run(test_full_conversation_system())
    if success:
        print(f"\n✅ Full system test completed successfully!")
        sys.exit(0)
    else:
        print(f"\n❌ Full system test failed.")
        sys.exit(1)
