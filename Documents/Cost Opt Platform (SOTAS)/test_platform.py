#!/usr/bin/env python3
"""
Comprehensive Test Suite for Cost Optimization Platform
Run this to validate all functionality is working correctly
"""

import requests
import json
import time
from typing import Dict, List

class PlatformTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str, latency_ms: float = None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        if latency_ms:
            print(f"   Latency: {latency_ms:.1f}ms")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "latency_ms": latency_ms
        })
    
    def test_health_check(self):
        """Test 1: Health Check"""
        print("\n🔍 Test 1: Health Check")
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("Health Check", True, f"Service healthy - {data.get('status')}", latency)
                return True
            else:
                self.log_test("Health Check", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Health Check", False, f"Connection failed: {e}")
            return False
    
    def test_simple_optimization(self):
        """Test 2: Simple Task Optimization"""
        print("\n🔍 Test 2: Simple Task Optimization")
        try:
            start_time = time.time()
            payload = {
                "prompt": "What is the capital of France?",
                "max_tokens": 50,
                "temperature": 0.7,
                "prefer_cost": True
            }
            
            response = requests.post(f"{self.base_url}/api/v1/optimize", json=payload, timeout=30)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                model = data.get("model_used", "unknown")
                cost = data.get("cost_usd", 0)
                content = data.get("content", "")[:50]
                
                self.log_test("Simple Optimization", True, 
                             f"Model: {model}, Cost: ${cost:.6f}, Response: {content}...", latency)
                return data
            else:
                self.log_test("Simple Optimization", False, f"HTTP {response.status_code}: {response.text[:100]}")
                return None
        except Exception as e:
            self.log_test("Simple Optimization", False, f"Request failed: {e}")
            return None
    
    def test_complex_optimization(self):
        """Test 3: Complex Task Optimization"""
        print("\n🔍 Test 3: Complex Task Optimization")
        try:
            start_time = time.time()
            payload = {
                "prompt": "Explain the differences between machine learning and deep learning with examples",
                "max_tokens": 200,
                "temperature": 0.7,
                "quality_threshold": 0.9,
                "prefer_cost": False
            }
            
            response = requests.post(f"{self.base_url}/api/v1/optimize", json=payload, timeout=45)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                model = data.get("model_used", "unknown")
                cost = data.get("cost_usd", 0)
                quality = data.get("quality_score", 0)
                
                self.log_test("Complex Optimization", True, 
                             f"Model: {model}, Cost: ${cost:.6f}, Quality: {quality:.2f}", latency)
                return data
            else:
                self.log_test("Complex Optimization", False, f"HTTP {response.status_code}: {response.text[:100]}")
                return None
        except Exception as e:
            self.log_test("Complex Optimization", False, f"Request failed: {e}")
            return None
    
    def test_expert_optimization(self):
        """Test 4: Expert Task Optimization"""
        print("\n🔍 Test 4: Expert Task Optimization")
        try:
            start_time = time.time()
            payload = {
                "prompt": "Design a microservices architecture for a high-traffic e-commerce platform",
                "max_tokens": 300,
                "temperature": 0.7,
                "quality_threshold": 0.95,
                "task_complexity": "expert"
            }
            
            response = requests.post(f"{self.base_url}/api/v1/optimize", json=payload, timeout=60)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                model = data.get("model_used", "unknown")
                cost = data.get("cost_usd", 0)
                quality = data.get("quality_score", 0)
                
                self.log_test("Expert Optimization", True, 
                             f"Model: {model}, Cost: ${cost:.6f}, Quality: {quality:.2f}", latency)
                return data
            else:
                self.log_test("Expert Optimization", False, f"HTTP {response.status_code}: {response.text[:100]}")
                return None
        except Exception as e:
            self.log_test("Expert Optimization", False, f"Request failed: {e}")
            return None
    
    def test_statistics_api(self):
        """Test 5: Statistics API"""
        print("\n🔍 Test 5: Statistics API")
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/v1/stats", timeout=10)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                total_requests = data.get("total_requests", 0)
                total_cost = data.get("total_cost_usd", 0)
                total_savings = data.get("total_savings_usd", 0)
                
                self.log_test("Statistics API", True, 
                             f"Requests: {total_requests}, Cost: ${total_cost:.4f}, Savings: ${total_savings:.4f}", latency)
                return data
            else:
                self.log_test("Statistics API", False, f"HTTP {response.status_code}")
                return None
        except Exception as e:
            self.log_test("Statistics API", False, f"Request failed: {e}")
            return None
    
    def test_web_dashboard(self):
        """Test 6: Web Dashboard"""
        print("\n🔍 Test 6: Web Dashboard")
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/", timeout=10)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                content = response.text
                has_title = "Cost Optimization Platform" in content
                has_form = "optimize()" in content
                has_stats = "loadStats()" in content
                
                if has_title and has_form and has_stats:
                    self.log_test("Web Dashboard", True, "Dashboard fully functional", latency)
                    return True
                else:
                    self.log_test("Web Dashboard", False, "Dashboard missing components")
                    return False
            else:
                self.log_test("Web Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Web Dashboard", False, f"Request failed: {e}")
            return False
    
    def test_cost_optimization_validation(self, simple_result, complex_result):
        """Test 7: Cost Optimization Validation"""
        print("\n🔍 Test 7: Cost Optimization Validation")
        
        if not simple_result or not complex_result:
            self.log_test("Cost Optimization", False, "Missing optimization results")
            return False
        
        simple_cost = simple_result.get("cost_usd", 0)
        complex_cost = complex_result.get("cost_usd", 0)
        simple_savings = simple_result.get("optimization_savings", 0)
        complex_savings = complex_result.get("optimization_savings", 0)
        
        total_savings = simple_savings + complex_savings
        total_cost = simple_cost + complex_cost
        
        if total_savings > 0:
            savings_percentage = (total_savings / (total_cost + total_savings)) * 100
            self.log_test("Cost Optimization", True, 
                         f"Total savings: ${total_savings:.6f} ({savings_percentage:.1f}%)")
            return True
        else:
            self.log_test("Cost Optimization", False, "No cost savings detected")
            return False
    
    def run_all_tests(self):
        """Run complete test suite"""
        print("🚅 COST OPTIMIZATION PLATFORM - COMPREHENSIVE TEST SUITE")
        print("=" * 70)
        
        # Run tests
        health_ok = self.test_health_check()
        if not health_ok:
            print("\n❌ Health check failed - stopping tests")
            return False
        
        simple_result = self.test_simple_optimization()
        complex_result = self.test_complex_optimization()
        expert_result = self.test_expert_optimization()
        stats_result = self.test_statistics_api()
        dashboard_ok = self.test_web_dashboard()
        cost_opt_ok = self.test_cost_optimization_validation(simple_result, complex_result)
        
        # Calculate summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        success_rate = (passed_tests / total_tests) * 100
        
        # Performance analysis
        latencies = [r["latency_ms"] for r in self.test_results if r["latency_ms"]]
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        
        print("\n" + "=" * 70)
        print("📊 TEST SUMMARY")
        print("=" * 70)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Average Latency: {avg_latency:.1f}ms")
        
        print("\n🎯 PLATFORM STATUS:")
        if success_rate >= 85:
            print("✅ PLATFORM READY FOR PRODUCTION")
        elif success_rate >= 70:
            print("⚠️  PLATFORM NEEDS MINOR FIXES")
        else:
            print("❌ PLATFORM NEEDS MAJOR FIXES")
        
        # Show model usage
        if simple_result and complex_result:
            simple_model = simple_result.get("model_used", "unknown")
            complex_model = complex_result.get("model_used", "unknown")
            print(f"\n🤖 MODEL USAGE:")
            print(f"Simple Task: {simple_model}")
            print(f"Complex Task: {complex_model}")
            
            if simple_model == "llama-3-8b" and complex_model == "claude-4-sonnet":
                print("✅ Intelligent model selection working correctly")
            else:
                print("⚠️  Model selection may need adjustment")
        
        return success_rate >= 85

def main():
    """Main test execution"""
    print("Starting platform tests...")
    
    tester = PlatformTester()
    success = tester.run_all_tests()
    
    # Save results
    with open("test_results.json", "w") as f:
        json.dump(tester.test_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: test_results.json")
    
    if success:
        print("\n🎉 ALL TESTS PASSED - PLATFORM IS READY!")
    else:
        print("\n⚠️  SOME TESTS FAILED - CHECK RESULTS ABOVE")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
