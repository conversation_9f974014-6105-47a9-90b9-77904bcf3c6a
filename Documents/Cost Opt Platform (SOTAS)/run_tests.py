#!/usr/bin/env python3
"""
FAANG+ Test Runner
Comprehensive test execution with performance monitoring, security validation, and quality gates
"""

import asyncio
import argparse
import logging
import sys
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import subprocess
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class TestSuite:
    """Test suite configuration"""
    name: str
    markers: List[str]
    timeout: int
    required_coverage: float
    max_failures: int
    performance_thresholds: Optional[Dict[str, float]] = None


@dataclass
class TestResult:
    """Test execution result"""
    suite_name: str
    passed: int
    failed: int
    skipped: int
    errors: int
    duration: float
    coverage: float
    exit_code: int
    performance_metrics: Optional[Dict[str, float]] = None


class TestRunner:
    """Production-grade test runner implementing FAANG+ standards"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results: List[TestResult] = []
        
        # Define test suites with FAANG+ standards
        self.test_suites = {
            'unit': TestSuite(
                name='Unit Tests',
                markers=['unit'],
                timeout=300,  # 5 minutes
                required_coverage=90.0,
                max_failures=0  # Zero tolerance for unit test failures
            ),
            'integration': TestSuite(
                name='Integration Tests',
                markers=['integration'],
                timeout=600,  # 10 minutes
                required_coverage=80.0,
                max_failures=1
            ),
            'performance': TestSuite(
                name='Performance Tests',
                markers=['performance'],
                timeout=1800,  # 30 minutes
                required_coverage=70.0,
                max_failures=0,
                performance_thresholds={
                    'optimization_latency_p99': 50.0,
                    'cache_hit_rate': 0.95,
                    'throughput_rps': 1000.0
                }
            ),
            'security': TestSuite(
                name='Security Tests',
                markers=['security'],
                timeout=900,  # 15 minutes
                required_coverage=85.0,
                max_failures=0  # Zero tolerance for security failures
            ),
            'smoke': TestSuite(
                name='Smoke Tests',
                markers=['smoke'],
                timeout=180,  # 3 minutes
                required_coverage=60.0,
                max_failures=0
            ),
            'regression': TestSuite(
                name='Regression Tests',
                markers=['regression'],
                timeout=1200,  # 20 minutes
                required_coverage=75.0,
                max_failures=2
            )
        }
    
    async def run_test_suite(self, suite_name: str, verbose: bool = False) -> TestResult:
        """Run a specific test suite"""
        if suite_name not in self.test_suites:
            raise ValueError(f"Unknown test suite: {suite_name}")
        
        suite = self.test_suites[suite_name]
        logger.info(f"Running {suite.name}...")
        
        # Build pytest command
        cmd = [
            'python', '-m', 'pytest',
            '--tb=short',
            '--durations=10',
            f'--maxfail={suite.max_failures}',
            f'--timeout={suite.timeout}',
            '--cov=src',
            '--cov-report=term-missing',
            '--cov-report=json:coverage.json',
            '--json-report',
            '--json-report-file=test_report.json'
        ]
        
        # Add markers
        if suite.markers:
            marker_expr = ' or '.join(suite.markers)
            cmd.extend(['-m', marker_expr])
        
        # Add verbosity
        if verbose:
            cmd.append('-v')
        
        # Add test directory
        cmd.append('tests/')
        
        # Execute tests
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=suite.timeout
            )
            duration = time.time() - start_time
            
            # Parse results
            test_result = self._parse_test_results(suite_name, result, duration)
            
            # Validate quality gates
            self._validate_quality_gates(suite, test_result)
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"{suite.name} timed out after {suite.timeout} seconds")
            return TestResult(
                suite_name=suite_name,
                passed=0,
                failed=1,
                skipped=0,
                errors=1,
                duration=suite.timeout,
                coverage=0.0,
                exit_code=124  # Timeout exit code
            )
        except Exception as e:
            logger.error(f"Failed to run {suite.name}: {e}")
            return TestResult(
                suite_name=suite_name,
                passed=0,
                failed=1,
                skipped=0,
                errors=1,
                duration=time.time() - start_time,
                coverage=0.0,
                exit_code=1
            )
    
    def _parse_test_results(self, suite_name: str, result: subprocess.CompletedProcess, duration: float) -> TestResult:
        """Parse pytest results"""
        passed = failed = skipped = errors = 0
        coverage = 0.0
        performance_metrics = {}
        
        try:
            # Parse JSON report if available
            report_file = self.project_root / 'test_report.json'
            if report_file.exists():
                with open(report_file) as f:
                    report = json.load(f)
                
                summary = report.get('summary', {})
                passed = summary.get('passed', 0)
                failed = summary.get('failed', 0)
                skipped = summary.get('skipped', 0)
                errors = summary.get('error', 0)
            
            # Parse coverage report
            coverage_file = self.project_root / 'coverage.json'
            if coverage_file.exists():
                with open(coverage_file) as f:
                    coverage_data = json.load(f)
                coverage = coverage_data.get('totals', {}).get('percent_covered', 0.0)
            
            # Extract performance metrics from output
            if 'performance' in suite_name:
                performance_metrics = self._extract_performance_metrics(result.stdout)
        
        except Exception as e:
            logger.warning(f"Failed to parse test results: {e}")
        
        return TestResult(
            suite_name=suite_name,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            duration=duration,
            coverage=coverage,
            exit_code=result.returncode,
            performance_metrics=performance_metrics
        )
    
    def _extract_performance_metrics(self, output: str) -> Dict[str, float]:
        """Extract performance metrics from test output"""
        metrics = {}
        
        # Look for performance metrics in output
        lines = output.split('\n')
        for line in lines:
            if 'p99_latency_ms:' in line:
                try:
                    value = float(line.split(':')[1].strip())
                    metrics['optimization_latency_p99'] = value
                except ValueError:
                    pass
            elif 'throughput_rps:' in line:
                try:
                    value = float(line.split(':')[1].strip())
                    metrics['throughput_rps'] = value
                except ValueError:
                    pass
            elif 'cache_hit_rate:' in line:
                try:
                    value = float(line.split(':')[1].strip())
                    metrics['cache_hit_rate'] = value
                except ValueError:
                    pass
        
        return metrics
    
    def _validate_quality_gates(self, suite: TestSuite, result: TestResult):
        """Validate quality gates for the test suite"""
        failures = []
        
        # Check test failures
        if result.failed > suite.max_failures:
            failures.append(f"Too many test failures: {result.failed} > {suite.max_failures}")
        
        # Check coverage
        if result.coverage < suite.required_coverage:
            failures.append(f"Coverage too low: {result.coverage:.1f}% < {suite.required_coverage}%")
        
        # Check performance thresholds
        if suite.performance_thresholds and result.performance_metrics:
            for metric, threshold in suite.performance_thresholds.items():
                if metric in result.performance_metrics:
                    value = result.performance_metrics[metric]
                    if metric == 'optimization_latency_p99' and value > threshold:
                        failures.append(f"Latency too high: {value:.2f}ms > {threshold}ms")
                    elif metric == 'cache_hit_rate' and value < threshold:
                        failures.append(f"Cache hit rate too low: {value:.3f} < {threshold}")
                    elif metric == 'throughput_rps' and value < threshold:
                        failures.append(f"Throughput too low: {value:.1f} RPS < {threshold} RPS")
        
        if failures:
            logger.error(f"Quality gate failures for {suite.name}:")
            for failure in failures:
                logger.error(f"  - {failure}")
            raise RuntimeError(f"Quality gates failed for {suite.name}")
    
    async def run_all_suites(self, verbose: bool = False) -> bool:
        """Run all test suites in order"""
        logger.info("Starting comprehensive test execution...")
        
        # Define execution order (critical tests first)
        execution_order = ['smoke', 'unit', 'security', 'integration', 'performance', 'regression']
        
        all_passed = True
        
        for suite_name in execution_order:
            try:
                result = await self.run_test_suite(suite_name, verbose)
                self.test_results.append(result)
                
                if result.exit_code != 0:
                    logger.error(f"{self.test_suites[suite_name].name} failed!")
                    all_passed = False
                    
                    # Stop on critical test failures
                    if suite_name in ['smoke', 'unit', 'security']:
                        logger.error("Critical test suite failed, stopping execution")
                        break
                else:
                    logger.info(f"{self.test_suites[suite_name].name} passed!")
                    
            except Exception as e:
                logger.error(f"Failed to execute {suite_name}: {e}")
                all_passed = False
                break
        
        # Generate summary report
        self._generate_summary_report()
        
        return all_passed
    
    def _generate_summary_report(self):
        """Generate comprehensive test summary report"""
        logger.info("\n" + "="*80)
        logger.info("TEST EXECUTION SUMMARY")
        logger.info("="*80)
        
        total_passed = sum(r.passed for r in self.test_results)
        total_failed = sum(r.failed for r in self.test_results)
        total_skipped = sum(r.skipped for r in self.test_results)
        total_errors = sum(r.errors for r in self.test_results)
        total_duration = sum(r.duration for r in self.test_results)
        
        logger.info(f"Total Tests: {total_passed + total_failed + total_skipped}")
        logger.info(f"Passed: {total_passed}")
        logger.info(f"Failed: {total_failed}")
        logger.info(f"Skipped: {total_skipped}")
        logger.info(f"Errors: {total_errors}")
        logger.info(f"Total Duration: {total_duration:.2f} seconds")
        
        logger.info("\nSuite Details:")
        for result in self.test_results:
            status = "✅ PASS" if result.exit_code == 0 else "❌ FAIL"
            logger.info(
                f"  {result.suite_name:12} {status} "
                f"({result.passed}P/{result.failed}F/{result.skipped}S) "
                f"Coverage: {result.coverage:.1f}% "
                f"Duration: {result.duration:.1f}s"
            )
        
        # Performance metrics summary
        perf_results = [r for r in self.test_results if r.performance_metrics]
        if perf_results:
            logger.info("\nPerformance Metrics:")
            for result in perf_results:
                for metric, value in result.performance_metrics.items():
                    logger.info(f"  {metric}: {value}")
        
        logger.info("="*80)


async def main():
    """Main test runner entry point"""
    parser = argparse.ArgumentParser(description='FAANG+ Test Runner')
    parser.add_argument('--suite', choices=['unit', 'integration', 'performance', 'security', 'smoke', 'regression', 'all'], 
                       default='all', help='Test suite to run')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--fail-fast', action='store_true', help='Stop on first failure')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.suite == 'all':
            success = await runner.run_all_suites(args.verbose)
        else:
            result = await runner.run_test_suite(args.suite, args.verbose)
            success = result.exit_code == 0
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
