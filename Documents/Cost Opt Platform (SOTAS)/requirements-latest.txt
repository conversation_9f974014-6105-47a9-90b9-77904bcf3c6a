# Latest OSS Versions from GitHub - July 2024
# Production-ready with latest features and security updates

# Core Framework - Latest from GitHub
fastapi>=0.116.1
uvicorn[standard]>=0.32.0
starlette>=0.47.0
pydantic>=2.11.0
pydantic-settings>=2.11.0

# LLM Integration - Latest
litellm>=1.74.3
openai>=1.96.0
anthropic>=0.40.0

# Database & ORM - Latest
sqlalchemy>=2.0.36
alembic>=1.14.0
aiosqlite>=0.20.0

# Caching - Latest
redis>=5.2.0
hiredis>=3.1.0

# HTTP & Networking - Latest
httpx>=0.28.1
aiohttp>=3.12.14
requests>=2.32.3

# Security - Latest
cryptography>=45.0.3
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Data Processing - Latest
python-multipart>=0.0.18
orjson>=3.10.12
msgpack>=1.1.0

# Monitoring & Observability - Latest
opentelemetry-api>=1.35.0
opentelemetry-sdk>=1.35.0
opentelemetry-instrumentation-fastapi>=0.56b0
prometheus-client>=0.21.1

# Development & Testing - Latest
pytest>=8.4.0
pytest-asyncio>=0.24.0
pytest-cov>=6.2.1
pytest-mock>=3.14.1
pytest-benchmark>=5.1.0
coverage[toml]>=7.9.2

# Code Quality - Latest
black>=24.12.0
isort>=5.13.2
flake8>=7.3.0
mypy>=1.14.0
ruff>=0.9.4

# Utilities - Latest
python-dotenv>=1.0.1
click>=8.1.7
rich>=13.9.4
typer>=0.15.3
jinja2>=3.1.5

# Production Server - Latest
gunicorn>=23.0.0

# Performance - Latest
lz4>=4.3.3
zstandard>=0.23.0
