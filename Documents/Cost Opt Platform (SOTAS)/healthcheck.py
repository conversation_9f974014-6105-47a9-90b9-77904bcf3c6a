#!/usr/bin/env python3
"""
Docker Health Check Script
FAANG+ implementation with comprehensive health validation
"""

import asyncio
import sys
import time
import logging
import json
from typing import Dict, Any, List
import aiohttp
import os

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise in health checks
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HealthChecker:
    """Comprehensive health checker for container health validation"""
    
    def __init__(self):
        self.base_url = f"http://localhost:{os.getenv('PORT', '8000')}"
        self.timeout = int(os.getenv('HEALTH_CHECK_TIMEOUT', '10'))
        self.checks = []
        
    async def check_http_endpoint(self) -> Dict[str, Any]:
        """Check main HTTP endpoint health"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'name': 'http_endpoint',
                            'status': 'healthy',
                            'response_time_ms': data.get('response_time_ms', 0),
                            'details': data
                        }
                    else:
                        return {
                            'name': 'http_endpoint',
                            'status': 'unhealthy',
                            'error': f'HTTP {response.status}',
                            'details': await response.text()
                        }
        except asyncio.TimeoutError:
            return {
                'name': 'http_endpoint',
                'status': 'unhealthy',
                'error': 'timeout',
                'timeout_seconds': self.timeout
            }
        except Exception as e:
            return {
                'name': 'http_endpoint',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_database_connection(self) -> Dict[str, Any]:
        """Check database connectivity"""
        try:
            # Import here to avoid issues if modules aren't available
            from src.core.database import DatabaseManager
            
            db = DatabaseManager()
            await db.initialize()
            
            # Simple health check query
            health_ok = await db.health_check()
            await db.cleanup()
            
            if health_ok:
                return {
                    'name': 'database',
                    'status': 'healthy',
                    'details': 'Connection successful'
                }
            else:
                return {
                    'name': 'database',
                    'status': 'unhealthy',
                    'error': 'Health check failed'
                }
                
        except Exception as e:
            return {
                'name': 'database',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_redis_connection(self) -> Dict[str, Any]:
        """Check Redis connectivity"""
        try:
            import redis.asyncio as redis
            
            redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
            r = redis.from_url(redis_url)
            
            # Simple ping test
            await r.ping()
            await r.close()
            
            return {
                'name': 'redis',
                'status': 'healthy',
                'details': 'Connection successful'
            }
            
        except Exception as e:
            return {
                'name': 'redis',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_memory_usage(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            import psutil
            
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # Memory threshold (configurable)
            memory_threshold_mb = int(os.getenv('MEMORY_THRESHOLD_MB', '2000'))
            
            if memory_mb < memory_threshold_mb:
                return {
                    'name': 'memory',
                    'status': 'healthy',
                    'memory_mb': round(memory_mb, 2),
                    'threshold_mb': memory_threshold_mb
                }
            else:
                return {
                    'name': 'memory',
                    'status': 'unhealthy',
                    'error': 'Memory usage too high',
                    'memory_mb': round(memory_mb, 2),
                    'threshold_mb': memory_threshold_mb
                }
                
        except Exception as e:
            return {
                'name': 'memory',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_disk_space(self) -> Dict[str, Any]:
        """Check disk space"""
        try:
            import shutil
            
            # Check disk space for app directory
            total, used, free = shutil.disk_usage('/app')
            
            # Convert to MB
            free_mb = free / 1024 / 1024
            total_mb = total / 1024 / 1024
            usage_percent = (used / total) * 100
            
            # Disk space threshold
            min_free_mb = int(os.getenv('MIN_FREE_DISK_MB', '100'))
            max_usage_percent = int(os.getenv('MAX_DISK_USAGE_PERCENT', '90'))
            
            if free_mb > min_free_mb and usage_percent < max_usage_percent:
                return {
                    'name': 'disk_space',
                    'status': 'healthy',
                    'free_mb': round(free_mb, 2),
                    'usage_percent': round(usage_percent, 2)
                }
            else:
                return {
                    'name': 'disk_space',
                    'status': 'unhealthy',
                    'error': 'Insufficient disk space',
                    'free_mb': round(free_mb, 2),
                    'usage_percent': round(usage_percent, 2),
                    'min_free_mb': min_free_mb,
                    'max_usage_percent': max_usage_percent
                }
                
        except Exception as e:
            return {
                'name': 'disk_space',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def check_optimization_service(self) -> Dict[str, Any]:
        """Check core optimization service"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                # Simple optimization test
                test_payload = {
                    "prompt": "Health check test prompt",
                    "optimization_level": "moderate",
                    "quality_threshold": 0.8
                }
                
                async with session.post(
                    f"{self.base_url}/api/v1/optimize",
                    json=test_payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'name': 'optimization_service',
                            'status': 'healthy',
                            'response_time_ms': data.get('processing_time_ms', 0),
                            'details': 'Optimization test successful'
                        }
                    else:
                        return {
                            'name': 'optimization_service',
                            'status': 'unhealthy',
                            'error': f'HTTP {response.status}',
                            'details': await response.text()
                        }
                        
        except Exception as e:
            return {
                'name': 'optimization_service',
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        start_time = time.time()
        
        # Define checks to run
        check_functions = [
            self.check_http_endpoint,
            self.check_database_connection,
            self.check_redis_connection,
            self.check_memory_usage,
            self.check_disk_space,
        ]
        
        # Add optimization service check only if not in testing mode
        if os.getenv('ENVIRONMENT') != 'testing':
            check_functions.append(self.check_optimization_service)
        
        # Run all checks concurrently
        results = await asyncio.gather(*[check() for check in check_functions], return_exceptions=True)
        
        # Process results
        checks = []
        healthy_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                checks.append({
                    'name': 'unknown',
                    'status': 'unhealthy',
                    'error': str(result)
                })
            else:
                checks.append(result)
                if result.get('status') == 'healthy':
                    healthy_count += 1
        
        # Determine overall health
        total_checks = len(checks)
        overall_status = 'healthy' if healthy_count == total_checks else 'unhealthy'
        
        # Calculate response time
        response_time_ms = round((time.time() - start_time) * 1000, 2)
        
        return {
            'status': overall_status,
            'timestamp': time.time(),
            'response_time_ms': response_time_ms,
            'checks': {
                'total': total_checks,
                'healthy': healthy_count,
                'unhealthy': total_checks - healthy_count
            },
            'details': checks
        }


async def main():
    """Main health check execution"""
    try:
        checker = HealthChecker()
        result = await checker.run_all_checks()
        
        # Output result for debugging (if verbose mode)
        if os.getenv('HEALTH_CHECK_VERBOSE', 'false').lower() == 'true':
            print(json.dumps(result, indent=2))
        
        # Exit with appropriate code
        if result['status'] == 'healthy':
            print("Health check: HEALTHY")
            sys.exit(0)
        else:
            print("Health check: UNHEALTHY")
            # Print unhealthy checks for debugging
            for check in result['details']:
                if check.get('status') == 'unhealthy':
                    print(f"  - {check['name']}: {check.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"Health check failed with exception: {e}")
        logger.exception("Health check exception")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
