#!/usr/bin/env python3
"""
Enhanced Redis Caching Layer for Cost Optimization Platform
Production-ready caching with intelligent cache strategies
"""

import json
import logging
import os
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import hashlib

try:
    import redis
    from redis.exceptions import ConnectionError, TimeoutError
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

class RedisCache:
    """Production-ready Redis caching layer"""
    
    def __init__(self, redis_url: str = None, default_ttl: int = 3600):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.default_ttl = default_ttl
        self.client = None
        self.connected = False
        
        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "errors": 0,
            "total_requests": 0
        }
        
        self._connect()
    
    def _connect(self):
        """Connect to Redis with error handling"""
        if not REDIS_AVAILABLE:
            logger.warning("Redis not available - caching disabled")
            return
        
        try:
            self.client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            self.client.ping()
            self.connected = True
            logger.info(f"Redis connected successfully: {self.redis_url}")
            
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
            self.connected = False
            self.client = None
    
    def _generate_key(self, prefix: str, data: Dict[str, Any]) -> str:
        """Generate consistent cache key"""
        # Sort data for consistent hashing
        sorted_data = json.dumps(data, sort_keys=True)
        hash_obj = hashlib.md5(sorted_data.encode())
        return f"{prefix}:{hash_obj.hexdigest()}"
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get value from cache with statistics tracking"""
        self.stats["total_requests"] += 1
        
        if not self.connected or not self.client:
            self.stats["misses"] += 1
            return None
        
        try:
            value = self.client.get(key)
            if value:
                self.stats["hits"] += 1
                return json.loads(value)
            else:
                self.stats["misses"] += 1
                return None
                
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            self.stats["errors"] += 1
            return None
    
    def set(self, key: str, value: Dict[str, Any], ttl: int = None) -> bool:
        """Set value in cache with TTL"""
        if not self.connected or not self.client:
            return False
        
        try:
            ttl = ttl or self.default_ttl
            result = self.client.setex(key, ttl, json.dumps(value))
            if result:
                self.stats["sets"] += 1
            return result
            
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            self.stats["errors"] += 1
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.connected or not self.client:
            return False
        
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        if not self.connected or not self.client:
            return 0
        
        try:
            keys = self.client.keys(pattern)
            if keys:
                return self.client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Cache clear pattern error: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        hit_rate = 0
        if self.stats["total_requests"] > 0:
            hit_rate = (self.stats["hits"] / self.stats["total_requests"]) * 100
        
        return {
            **self.stats,
            "hit_rate_percent": round(hit_rate, 2),
            "connected": self.connected,
            "redis_url": self.redis_url
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        if not self.connected or not self.client:
            return {
                "status": "unhealthy",
                "error": "Not connected to Redis",
                "connected": False
            }
        
        try:
            start_time = time.time()
            self.client.ping()
            latency_ms = (time.time() - start_time) * 1000
            
            info = self.client.info()
            
            return {
                "status": "healthy",
                "connected": True,
                "latency_ms": round(latency_ms, 2),
                "redis_version": info.get("redis_version"),
                "used_memory_human": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed")
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connected": False
            }

class OptimizationCache:
    """Specialized cache for LLM optimization responses"""
    
    def __init__(self, redis_cache: RedisCache):
        self.cache = redis_cache
        self.prefix = "llm_opt"
    
    def get_response(self, prompt: str, model: str, max_tokens: int, temperature: float) -> Optional[Dict]:
        """Get cached optimization response"""
        cache_data = {
            "prompt": prompt,
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        key = self.cache._generate_key(self.prefix, cache_data)
        cached_response = self.cache.get(key)
        
        if cached_response:
            # Check if cache is still valid (not expired)
            cached_time = cached_response.get("cached_at", 0)
            if time.time() - cached_time < self.cache.default_ttl:
                logger.info(f"Cache hit for key: {key[:20]}...")
                return cached_response
            else:
                # Remove expired cache
                self.cache.delete(key)
        
        return None
    
    def cache_response(self, prompt: str, model: str, max_tokens: int, temperature: float, 
                      response_data: Dict, ttl: int = None) -> bool:
        """Cache optimization response"""
        cache_data = {
            "prompt": prompt,
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        key = self.cache._generate_key(self.prefix, cache_data)
        
        # Add metadata to cached response
        cached_response = {
            **response_data,
            "cached_at": time.time(),
            "cache_key": key
        }
        
        success = self.cache.set(key, cached_response, ttl)
        if success:
            logger.info(f"Cached response for key: {key[:20]}...")
        
        return success
    
    def get_model_stats(self, model: str) -> Dict[str, Any]:
        """Get statistics for a specific model"""
        pattern = f"{self.prefix}:*"
        # This is a simplified version - in production, you'd want to store model stats separately
        return {
            "model": model,
            "cached_responses": 0,  # Would need separate tracking
            "cache_hit_rate": 0.0
        }
    
    def clear_model_cache(self, model: str) -> int:
        """Clear cache for specific model"""
        # In a more sophisticated implementation, you'd track model-specific keys
        pattern = f"{self.prefix}:*"
        return self.cache.clear_pattern(pattern)

def create_cache_layer() -> tuple[RedisCache, OptimizationCache]:
    """Factory function to create cache layer"""
    redis_cache = RedisCache()
    optimization_cache = OptimizationCache(redis_cache)
    
    return redis_cache, optimization_cache

# Global cache instances
_redis_cache = None
_optimization_cache = None

def get_cache() -> tuple[RedisCache, OptimizationCache]:
    """Get global cache instances (singleton pattern)"""
    global _redis_cache, _optimization_cache
    
    if _redis_cache is None or _optimization_cache is None:
        _redis_cache, _optimization_cache = create_cache_layer()
    
    return _redis_cache, _optimization_cache

if __name__ == "__main__":
    # Test the cache layer
    print("Testing Redis Cache Layer...")
    
    redis_cache, opt_cache = create_cache_layer()
    
    # Health check
    health = redis_cache.health_check()
    print(f"Health Check: {health}")
    
    # Test basic operations
    test_key = "test:key"
    test_data = {"message": "Hello Redis!", "timestamp": time.time()}
    
    # Set and get
    redis_cache.set(test_key, test_data, 60)
    retrieved = redis_cache.get(test_key)
    print(f"Set/Get Test: {retrieved}")
    
    # Test optimization cache
    cached_response = opt_cache.get_response("test prompt", "test-model", 100, 0.7)
    print(f"Optimization Cache Test: {cached_response}")
    
    # Stats
    stats = redis_cache.get_stats()
    print(f"Cache Stats: {stats}")
    
    # Cleanup
    redis_cache.delete(test_key)
    print("Cache layer test completed!")
