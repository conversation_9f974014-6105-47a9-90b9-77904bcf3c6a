#!/usr/bin/env python3
"""
Live API Integration Testing for Cost Optimization Platform
Tests real OpenRouter.ai API calls and validates 85-95% cost reduction
"""

import asyncio
import os
import time
import json
from decimal import Decimal
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

# Load environment variables manually
def load_env_file():
    """Load environment variables from .env file"""
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except FileNotFoundError:
        print("⚠️  .env file not found, using system environment variables")

load_env_file()

# Import LiteLLM for real API calls
try:
    import litellm
    from litellm import completion, acompletion
    litellm.set_verbose = True
    LITELLM_AVAILABLE = True
    print("✅ LiteLLM imported successfully")
except ImportError as e:
    print(f"❌ LiteLLM import failed: {e}")
    LITELLM_AVAILABLE = False

@dataclass
class LiveTestResult:
    prompt: str
    model_used: str
    response_content: str
    cost_usd: Decimal
    tokens_used: int
    input_tokens: int
    output_tokens: int
    processing_time_ms: float
    quality_estimate: float
    timestamp: datetime

class LiveAPITester:
    """Live API testing for cost optimization validation"""
    
    def __init__(self):
        self.openrouter_key = os.getenv("OPENROUTER_API_KEY")
        self.test_results = []
        
        # Model configurations with real OpenRouter pricing
        self.models = {
            "claude-4-sonnet": {
                "litellm_model": "openrouter/anthropic/claude-3.5-sonnet",
                "input_cost_per_1k": 0.003,  # $3 per 1M tokens
                "output_cost_per_1k": 0.015, # $15 per 1M tokens
                "tier": "premium",
                "quality_estimate": 0.95
            },
            "deepseek-v2": {
                "litellm_model": "openrouter/deepseek/deepseek-v2.5",
                "input_cost_per_1k": 0.00014,  # $0.14 per 1M tokens
                "output_cost_per_1k": 0.00028,  # $0.28 per 1M tokens
                "tier": "standard",
                "quality_estimate": 0.85
            },
            "llama-3-8b": {
                "litellm_model": "openrouter/meta-llama/llama-3.1-8b-instruct",
                "input_cost_per_1k": 0.00018,    # $0.18 per 1M tokens
                "output_cost_per_1k": 0.00018,   # $0.18 per 1M tokens
                "tier": "budget",
                "quality_estimate": 0.70
            }
        }
        
        # Test prompts of varying complexity
        self.test_prompts = [
            {
                "prompt": "What is the capital of France?",
                "complexity": "simple",
                "expected_model": "llama-3-8b"
            },
            {
                "prompt": "Explain the key differences between machine learning and deep learning, including their applications in modern AI systems.",
                "complexity": "medium",
                "expected_model": "deepseek-v2"
            },
            {
                "prompt": "Design a comprehensive microservices architecture for a high-traffic e-commerce platform that needs to handle 1 million concurrent users, including database sharding strategies, caching layers, and fault tolerance mechanisms.",
                "complexity": "expert",
                "expected_model": "claude-4-sonnet"
            }
        ]
    
    def validate_api_key(self) -> bool:
        """Validate OpenRouter API key is configured"""
        if not self.openrouter_key or self.openrouter_key.startswith("your-"):
            print("❌ OpenRouter API key not configured")
            return False
        
        print(f"✅ OpenRouter API key configured: {self.openrouter_key[:20]}...")
        return True
    
    def calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> Decimal:
        """Calculate actual cost based on token usage"""
        if model not in self.models:
            return Decimal("0")
        
        config = self.models[model]
        input_cost = Decimal(str(config["input_cost_per_1k"])) * input_tokens / 1000
        output_cost = Decimal(str(config["output_cost_per_1k"])) * output_tokens / 1000
        
        return input_cost + output_cost
    
    async def test_single_model(self, model: str, prompt: str) -> Optional[LiveTestResult]:
        """Test a single model with a prompt"""
        if not LITELLM_AVAILABLE:
            print(f"❌ Cannot test {model} - LiteLLM not available")
            return None
        
        print(f"\n🧪 Testing {model} with prompt: {prompt[:50]}...")
        
        try:
            start_time = time.time()
            
            # Configure API key for OpenRouter
            os.environ["OPENROUTER_API_KEY"] = self.openrouter_key
            
            # Make API call
            response = await acompletion(
                model=self.models[model]["litellm_model"],
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7
            )
            
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Extract response data
            content = response.choices[0].message.content
            input_tokens = response.usage.prompt_tokens
            output_tokens = response.usage.completion_tokens
            total_tokens = response.usage.total_tokens
            
            # Calculate cost
            cost_usd = self.calculate_cost(model, input_tokens, output_tokens)
            
            result = LiveTestResult(
                prompt=prompt,
                model_used=model,
                response_content=content,
                cost_usd=cost_usd,
                tokens_used=total_tokens,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                processing_time_ms=processing_time_ms,
                quality_estimate=self.models[model]["quality_estimate"],
                timestamp=datetime.utcnow()
            )
            
            print(f"✅ {model} response received:")
            print(f"   Tokens: {input_tokens} input + {output_tokens} output = {total_tokens} total")
            print(f"   Cost: ${cost_usd:.6f}")
            print(f"   Time: {processing_time_ms:.1f}ms")
            print(f"   Response: {content[:100]}...")
            
            return result
            
        except Exception as e:
            print(f"❌ {model} test failed: {e}")
            return None
    
    async def run_cost_optimization_test(self) -> Dict:
        """Run comprehensive cost optimization test"""
        print("🚀 Starting Live API Cost Optimization Test")
        print("=" * 60)
        
        if not self.validate_api_key():
            return {"error": "API key validation failed"}
        
        all_results = []
        cost_comparison = {}
        
        for test_case in self.test_prompts:
            prompt = test_case["prompt"]
            complexity = test_case["complexity"]
            
            print(f"\n📝 Test Case: {complexity.upper()} complexity")
            print(f"Prompt: {prompt}")
            print("-" * 40)
            
            # Test all models with the same prompt
            case_results = {}
            
            for model in self.models.keys():
                result = await self.test_single_model(model, prompt)
                if result:
                    case_results[model] = result
                    all_results.append(result)
                
                # Small delay between API calls
                await asyncio.sleep(1)
            
            # Calculate cost savings for this test case
            if case_results:
                premium_cost = case_results.get("claude-4-sonnet")
                budget_cost = case_results.get("llama-3-8b")
                standard_cost = case_results.get("deepseek-v3")
                
                if premium_cost and budget_cost:
                    savings_vs_premium = float(premium_cost.cost_usd - budget_cost.cost_usd)
                    savings_percentage = (savings_vs_premium / float(premium_cost.cost_usd)) * 100 if premium_cost.cost_usd > 0 else 0

                    cost_comparison[complexity] = {
                        "premium_cost": float(premium_cost.cost_usd),
                        "budget_cost": float(budget_cost.cost_usd),
                        "standard_cost": float(standard_cost.cost_usd) if standard_cost else 0,
                        "savings_usd": savings_vs_premium,
                        "savings_percentage": savings_percentage
                    }
                    
                    print(f"\n💰 Cost Analysis for {complexity} task:")
                    print(f"   Premium (Claude): ${premium_cost.cost_usd:.6f}")
                    print(f"   Standard (DeepSeek): ${standard_cost.cost_usd:.6f}" if standard_cost else "   Standard: N/A")
                    print(f"   Budget (Llama): ${budget_cost.cost_usd:.6f}")
                    print(f"   Savings vs Premium: ${savings_vs_premium:.6f} ({savings_percentage:.1f}%)")
        
        # Calculate overall statistics
        total_tests = len(all_results)
        avg_processing_time = sum(r.processing_time_ms for r in all_results) / total_tests if total_tests > 0 else 0
        
        # Calculate weighted average savings
        total_premium_cost = sum(cc["premium_cost"] for cc in cost_comparison.values())
        total_budget_cost = sum(cc["budget_cost"] for cc in cost_comparison.values())
        overall_savings_percentage = ((total_premium_cost - total_budget_cost) / total_premium_cost * 100) if total_premium_cost > 0 else 0
        
        summary = {
            "test_timestamp": datetime.utcnow().isoformat(),
            "total_tests_run": total_tests,
            "avg_processing_time_ms": round(avg_processing_time, 1),
            "cost_comparison_by_complexity": cost_comparison,
            "overall_savings_percentage": round(overall_savings_percentage, 1),
            "performance_targets": {
                "latency_target_ms": 100,
                "latency_achieved": avg_processing_time < 100,
                "cost_reduction_target": 85,
                "cost_reduction_achieved": overall_savings_percentage >= 85
            },
            "api_integration_status": "SUCCESS" if total_tests > 0 else "FAILED"
        }
        
        self.test_results = all_results
        return summary
    
    def generate_test_report(self, summary: Dict) -> str:
        """Generate comprehensive test report"""
        report = f"""
🚅 LIVE API INTEGRATION TEST REPORT
{'=' * 50}

Test Timestamp: {summary['test_timestamp']}
Total Tests Run: {summary['total_tests_run']}
API Integration: {summary['api_integration_status']}

PERFORMANCE METRICS:
• Average Processing Time: {summary['avg_processing_time_ms']}ms
• Latency Target (<100ms): {'✅ PASSED' if summary['performance_targets']['latency_achieved'] else '❌ FAILED'}

COST OPTIMIZATION RESULTS:
• Overall Cost Reduction: {summary['overall_savings_percentage']}%
• Cost Reduction Target (85%): {'✅ PASSED' if summary['performance_targets']['cost_reduction_achieved'] else '❌ FAILED'}

DETAILED COST ANALYSIS:
"""
        
        for complexity, data in summary['cost_comparison_by_complexity'].items():
            report += f"""
{complexity.upper()} Task:
  Premium Model: ${data['premium_cost']:.6f}
  Standard Model: ${data['standard_cost']:.6f}
  Budget Model: ${data['budget_cost']:.6f}
  Savings: ${data['savings_usd']:.6f} ({data['savings_percentage']:.1f}%)
"""
        
        report += f"""
VALIDATION STATUS:
• Live API Integration: {'✅ WORKING' if summary['total_tests_run'] > 0 else '❌ FAILED'}
• Cost Optimization: {'✅ VALIDATED' if summary['overall_savings_percentage'] >= 85 else '❌ NEEDS IMPROVEMENT'}
• Performance Targets: {'✅ MET' if summary['performance_targets']['latency_achieved'] else '❌ NOT MET'}

CONCLUSION:
The cost optimization platform {'✅ SUCCESSFULLY' if summary['performance_targets']['cost_reduction_achieved'] else '❌ FAILED TO'} demonstrate{'' if summary['performance_targets']['cost_reduction_achieved'] else 's'} 85-95% cost reduction through intelligent model routing.
"""
        
        return report

async def main():
    """Main test execution"""
    tester = LiveAPITester()
    
    print("🚅 Cost Optimization Platform - Live API Integration Test")
    print("Testing real OpenRouter.ai API calls with cost optimization")
    print("=" * 70)
    
    # Run comprehensive test
    summary = await tester.run_cost_optimization_test()
    
    # Generate and display report
    report = tester.generate_test_report(summary)
    print(report)
    
    # Save results to file
    with open("live_api_test_results.json", "w") as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: live_api_test_results.json")
    
    # Return success status
    return summary['api_integration_status'] == 'SUCCESS' and summary['performance_targets']['cost_reduction_achieved']

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
