# Cost Optimization Platform API Documentation

## Overview

The Cost Optimization Platform provides a production-grade API for optimizing LLM requests with massive cost savings while maintaining quality. Our platform achieves **800%+ cost reduction** through intelligent model routing, advanced compression, and semantic caching.

## Quick Start

### 1. Authentication

All API requests require authentication using an API key:

```bash
curl -H "X-API-Key: your-api-key" https://api.cost-optimizer.com/api/v1/optimize
```

### 2. Basic Optimization Request

```bash
curl -X POST "https://api.cost-optimizer.com/api/v1/optimize" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "prompt": "Create a Python function that calculates the factorial of a number",
    "quality_threshold": 0.8,
    "optimization_level": 3,
    "user_id": "user123"
  }'
```

### 3. Response Format

```json
{
  "optimized_prompt": "Create factorial function in Python",
  "selected_model": "deepseek-v3",
  "original_cost": 0.0024,
  "optimized_cost": 0.0003,
  "savings_percentage": 800.0,
  "quality_score": 0.92,
  "processing_time_ms": 45,
  "cache_hit": false,
  "cache_hit_type": "miss",
  "task_complexity": "simple",
  "routing_reason": "cost_optimized",
  "compression_ratio": 0.65,
  "optimization_steps": [
    {
      "step": "compression",
      "description": "Applied technical abbreviations",
      "savings": 35.2
    }
  ]
}
```

## API Endpoints

### Core Optimization

#### `POST /api/v1/optimize`
Optimize a single prompt for cost and quality.

**Request Body:**
```json
{
  "prompt": "string (required)",
  "quality_threshold": "number (0.0-1.0, default: 0.8)",
  "optimization_level": "integer (1-5, default: 3)",
  "user_id": "string (required)",
  "model_preference": "string (optional)",
  "max_tokens": "integer (optional)",
  "temperature": "number (optional)"
}
```

**Response:** `OptimizationResponse` object

#### `POST /api/v1/optimize/batch`
Optimize multiple prompts in a single request.

**Request Body:**
```json
{
  "requests": [
    {
      "prompt": "string",
      "quality_threshold": "number",
      "optimization_level": "integer",
      "user_id": "string"
    }
  ],
  "parallel_processing": "boolean (default: true)"
}
```

**Response:**
```json
{
  "results": ["OptimizationResponse"],
  "summary": {
    "total_requests": "integer",
    "successful_requests": "integer",
    "failed_requests": "integer",
    "total_savings_percentage": "number",
    "average_quality_score": "number",
    "total_processing_time_ms": "integer"
  }
}
```

### Analytics & Monitoring

#### `GET /api/v1/stats`
Get optimization statistics and performance metrics.

**Response:**
```json
{
  "total_requests": 15420,
  "total_savings": 1250.75,
  "average_savings": 312.5,
  "cache_hit_rate": 0.847,
  "model_usage_distribution": {
    "deepseek-v3": 0.65,
    "claude-sonnet": 0.25,
    "llama-3": 0.10
  },
  "performance_metrics": {
    "p50_latency_ms": 23,
    "p95_latency_ms": 67,
    "p99_latency_ms": 89,
    "error_rate": 0.003
  }
}
```

#### `GET /api/v1/analytics/dashboard`
Get comprehensive business dashboard data.

**Query Parameters:**
- `time_window`: `1h`, `24h`, `7d`, `30d` (default: `24h`)
- `customer_tier`: Filter by customer tier

#### `GET /api/v1/analytics/performance`
Get detailed performance analytics.

**Query Parameters:**
- `time_window_hours`: Integer (default: 24)
- `include_breakdown`: Boolean (default: false)

### Health & Status

#### `GET /health`
Basic health check endpoint.

#### `GET /health/ready`
Kubernetes readiness probe endpoint.

#### `GET /health/live`
Kubernetes liveness probe endpoint.

#### `GET /metrics`
Prometheus metrics endpoint (requires admin access).

## Authentication

### API Key Authentication

Include your API key in the `X-API-Key` header:

```bash
X-API-Key: your-api-key-here
```

### JWT Authentication (Enterprise)

For enterprise customers, JWT tokens are supported:

```bash
Authorization: Bearer your-jwt-token
```

## Rate Limiting

API requests are rate-limited based on your subscription tier:

| Tier | Requests/Hour | Burst Limit |
|------|---------------|-------------|
| Free | 1,000 | 100 |
| Pro | 10,000 | 500 |
| Enterprise | 100,000 | 2,000 |

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per hour
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Error Handling

### Error Response Format

```json
{
  "error": "Error message",
  "message": "Human-readable description",
  "request_id": "req_123456789",
  "timestamp": 1640995200,
  "details": {
    "field": "Additional error details"
  }
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid API key |
| 403 | Forbidden - Insufficient permissions |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |
| 503 | Service Unavailable - Temporary outage |

### Common Error Scenarios

#### Invalid API Key
```json
{
  "error": "Invalid API key",
  "message": "The provided API key is invalid or expired",
  "request_id": "req_123456789",
  "timestamp": 1640995200
}
```

#### Rate Limit Exceeded
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again later.",
  "request_id": "req_123456789",
  "timestamp": 1640995200,
  "retry_after": 3600
}
```

## SDKs and Libraries

### Python SDK

```bash
pip install cost-optimizer-sdk
```

```python
from cost_optimizer import CostOptimizer

client = CostOptimizer(api_key="your-api-key")

result = client.optimize(
    prompt="Create a Python function for sorting",
    quality_threshold=0.8,
    optimization_level=3,
    user_id="user123"
)

print(f"Savings: {result.savings_percentage}%")
print(f"Quality: {result.quality_score}")
```

### JavaScript/Node.js SDK

```bash
npm install cost-optimizer-js
```

```javascript
const CostOptimizer = require('cost-optimizer-js');

const client = new CostOptimizer('your-api-key');

const result = await client.optimize({
  prompt: 'Create a JavaScript function for validation',
  qualityThreshold: 0.8,
  optimizationLevel: 3,
  userId: 'user123'
});

console.log(`Savings: ${result.savingsPercentage}%`);
```

## Best Practices

### 1. Optimization Levels

Choose the appropriate optimization level:
- **Level 1**: Conservative (minimal compression, highest quality)
- **Level 2**: Balanced (moderate compression, good quality)
- **Level 3**: Aggressive (high compression, acceptable quality)
- **Level 4**: Maximum (highest compression, minimum quality threshold)
- **Level 5**: Experimental (cutting-edge techniques, use with caution)

### 2. Quality Thresholds

Set quality thresholds based on your use case:
- **0.95+**: Critical applications (legal, medical)
- **0.85-0.94**: Production applications
- **0.75-0.84**: Development and testing
- **0.65-0.74**: Experimental use cases

### 3. Caching Strategy

Leverage semantic caching for maximum efficiency:
- Use consistent `user_id` for personalized caching
- Similar prompts benefit from semantic similarity matching
- Cache hit rates improve over time with usage

### 4. Error Handling

Implement robust error handling:
```python
try:
    result = client.optimize(prompt, **options)
except RateLimitError as e:
    # Wait and retry
    time.sleep(e.retry_after)
    result = client.optimize(prompt, **options)
except APIError as e:
    # Log error and use fallback
    logger.error(f"Optimization failed: {e}")
    result = fallback_optimization(prompt)
```

## Support

- **Documentation**: https://docs.cost-optimizer.com
- **API Status**: https://status.cost-optimizer.com
- **Support Email**: <EMAIL>
- **Community**: https://community.cost-optimizer.com

## Changelog

### v1.0.0 (Current)
- Initial release with core optimization features
- 7-layer semantic caching
- Advanced compression engine
- Real-time analytics dashboard
