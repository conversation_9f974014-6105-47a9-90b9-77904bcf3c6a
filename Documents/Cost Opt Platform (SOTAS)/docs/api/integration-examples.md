# Integration Examples

## Python Integration

### Basic Usage

```python
import asyncio
import aiohttp
from typing import Dict, Any

class CostOptimizerClient:
    def __init__(self, api_key: str, base_url: str = "https://api.cost-optimizer.com"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={"X-API-Key": self.api_key}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def optimize(
        self,
        prompt: str,
        quality_threshold: float = 0.8,
        optimization_level: int = 3,
        user_id: str = "default"
    ) -> Dict[str, Any]:
        """Optimize a single prompt"""
        async with self.session.post(
            f"{self.base_url}/api/v1/optimize",
            json={
                "prompt": prompt,
                "quality_threshold": quality_threshold,
                "optimization_level": optimization_level,
                "user_id": user_id
            }
        ) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_data = await response.json()
                raise Exception(f"API Error: {error_data}")
    
    async def batch_optimize(
        self,
        requests: list,
        parallel_processing: bool = True
    ) -> Dict[str, Any]:
        """Optimize multiple prompts"""
        async with self.session.post(
            f"{self.base_url}/api/v1/optimize/batch",
            json={
                "requests": requests,
                "parallel_processing": parallel_processing
            }
        ) as response:
            return await response.json()

# Usage example
async def main():
    async with CostOptimizerClient("your-api-key") as client:
        # Single optimization
        result = await client.optimize(
            prompt="Create a Python function that validates email addresses",
            quality_threshold=0.85,
            optimization_level=3,
            user_id="user123"
        )
        
        print(f"Original cost: ${result['original_cost']:.4f}")
        print(f"Optimized cost: ${result['optimized_cost']:.4f}")
        print(f"Savings: {result['savings_percentage']:.1f}%")
        print(f"Quality score: {result['quality_score']:.2f}")
        
        # Batch optimization
        batch_requests = [
            {
                "prompt": "Create a REST API endpoint for user authentication",
                "quality_threshold": 0.8,
                "optimization_level": 3,
                "user_id": "user123"
            },
            {
                "prompt": "Implement a caching layer for database queries",
                "quality_threshold": 0.85,
                "optimization_level": 2,
                "user_id": "user123"
            }
        ]
        
        batch_result = await client.batch_optimize(batch_requests)
        print(f"Batch processed {batch_result['summary']['total_requests']} requests")
        print(f"Average savings: {batch_result['summary']['total_savings_percentage']:.1f}%")

if __name__ == "__main__":
    asyncio.run(main())
```

### Django Integration

```python
# settings.py
COST_OPTIMIZER_API_KEY = "your-api-key"
COST_OPTIMIZER_BASE_URL = "https://api.cost-optimizer.com"

# services.py
import aiohttp
from django.conf import settings
from asgiref.sync import sync_to_async

class CostOptimizerService:
    @staticmethod
    async def optimize_prompt(prompt: str, user_id: str) -> dict:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{settings.COST_OPTIMIZER_BASE_URL}/api/v1/optimize",
                headers={"X-API-Key": settings.COST_OPTIMIZER_API_KEY},
                json={
                    "prompt": prompt,
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": user_id
                }
            ) as response:
                return await response.json()

# views.py
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

@csrf_exempt
@require_http_methods(["POST"])
async def optimize_view(request):
    data = json.loads(request.body)
    
    result = await CostOptimizerService.optimize_prompt(
        prompt=data['prompt'],
        user_id=request.user.id if request.user.is_authenticated else 'anonymous'
    )
    
    return JsonResponse(result)
```

### Flask Integration

```python
from flask import Flask, request, jsonify
import asyncio
import aiohttp

app = Flask(__name__)

class CostOptimizerClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.cost-optimizer.com"
    
    async def optimize(self, prompt: str, user_id: str) -> dict:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/optimize",
                headers={"X-API-Key": self.api_key},
                json={
                    "prompt": prompt,
                    "quality_threshold": 0.8,
                    "optimization_level": 3,
                    "user_id": user_id
                }
            ) as response:
                return await response.json()

optimizer_client = CostOptimizerClient("your-api-key")

@app.route('/optimize', methods=['POST'])
def optimize():
    data = request.get_json()
    
    # Run async function in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(
            optimizer_client.optimize(
                prompt=data['prompt'],
                user_id=data.get('user_id', 'anonymous')
            )
        )
        return jsonify(result)
    finally:
        loop.close()

if __name__ == '__main__':
    app.run(debug=True)
```

## JavaScript/Node.js Integration

### Express.js Integration

```javascript
const express = require('express');
const axios = require('axios');

const app = express();
app.use(express.json());

class CostOptimizerClient {
    constructor(apiKey, baseUrl = 'https://api.cost-optimizer.com') {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
        this.client = axios.create({
            baseURL: baseUrl,
            headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            }
        });
    }

    async optimize(prompt, options = {}) {
        try {
            const response = await this.client.post('/api/v1/optimize', {
                prompt,
                quality_threshold: options.qualityThreshold || 0.8,
                optimization_level: options.optimizationLevel || 3,
                user_id: options.userId || 'default'
            });
            return response.data;
        } catch (error) {
            throw new Error(`Optimization failed: ${error.response?.data?.error || error.message}`);
        }
    }

    async batchOptimize(requests, parallelProcessing = true) {
        try {
            const response = await this.client.post('/api/v1/optimize/batch', {
                requests,
                parallel_processing: parallelProcessing
            });
            return response.data;
        } catch (error) {
            throw new Error(`Batch optimization failed: ${error.response?.data?.error || error.message}`);
        }
    }
}

const optimizer = new CostOptimizerClient(process.env.COST_OPTIMIZER_API_KEY);

// Single optimization endpoint
app.post('/api/optimize', async (req, res) => {
    try {
        const { prompt, userId } = req.body;
        
        const result = await optimizer.optimize(prompt, {
            userId: userId || 'anonymous',
            qualityThreshold: 0.8,
            optimizationLevel: 3
        });
        
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Batch optimization endpoint
app.post('/api/optimize/batch', async (req, res) => {
    try {
        const { requests } = req.body;
        
        const result = await optimizer.batchOptimize(requests);
        
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(3000, () => {
    console.log('Server running on port 3000');
});
```

### React Frontend Integration

```jsx
import React, { useState } from 'react';
import axios from 'axios';

const CostOptimizerComponent = () => {
    const [prompt, setPrompt] = useState('');
    const [result, setResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const optimizePrompt = async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await axios.post('/api/optimize', {
                prompt,
                userId: 'user123'
            });
            
            setResult(response.data);
        } catch (err) {
            setError(err.response?.data?.error || 'Optimization failed');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="cost-optimizer">
            <h2>Cost Optimizer</h2>
            
            <div className="input-section">
                <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Enter your prompt here..."
                    rows={4}
                    cols={50}
                />
                <br />
                <button 
                    onClick={optimizePrompt} 
                    disabled={loading || !prompt.trim()}
                >
                    {loading ? 'Optimizing...' : 'Optimize'}
                </button>
            </div>

            {error && (
                <div className="error">
                    <strong>Error:</strong> {error}
                </div>
            )}

            {result && (
                <div className="result">
                    <h3>Optimization Result</h3>
                    <div className="metrics">
                        <p><strong>Original Cost:</strong> ${result.original_cost.toFixed(4)}</p>
                        <p><strong>Optimized Cost:</strong> ${result.optimized_cost.toFixed(4)}</p>
                        <p><strong>Savings:</strong> {result.savings_percentage.toFixed(1)}%</p>
                        <p><strong>Quality Score:</strong> {result.quality_score.toFixed(2)}</p>
                        <p><strong>Processing Time:</strong> {result.processing_time_ms}ms</p>
                        <p><strong>Selected Model:</strong> {result.selected_model}</p>
                    </div>
                    
                    <div className="prompts">
                        <h4>Original Prompt:</h4>
                        <p>{prompt}</p>
                        
                        <h4>Optimized Prompt:</h4>
                        <p>{result.optimized_prompt}</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CostOptimizerComponent;
```

## Go Integration

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

type CostOptimizerClient struct {
    APIKey  string
    BaseURL string
    Client  *http.Client
}

type OptimizationRequest struct {
    Prompt            string  `json:"prompt"`
    QualityThreshold  float64 `json:"quality_threshold"`
    OptimizationLevel int     `json:"optimization_level"`
    UserID           string  `json:"user_id"`
}

type OptimizationResponse struct {
    OptimizedPrompt     string  `json:"optimized_prompt"`
    SelectedModel       string  `json:"selected_model"`
    OriginalCost        float64 `json:"original_cost"`
    OptimizedCost       float64 `json:"optimized_cost"`
    SavingsPercentage   float64 `json:"savings_percentage"`
    QualityScore        float64 `json:"quality_score"`
    ProcessingTimeMs    int     `json:"processing_time_ms"`
    CacheHit           bool    `json:"cache_hit"`
    TaskComplexity     string  `json:"task_complexity"`
    CompressionRatio   float64 `json:"compression_ratio"`
}

func NewCostOptimizerClient(apiKey string) *CostOptimizerClient {
    return &CostOptimizerClient{
        APIKey:  apiKey,
        BaseURL: "https://api.cost-optimizer.com",
        Client: &http.Client{
            Timeout: 30 * time.Second,
        },
    }
}

func (c *CostOptimizerClient) Optimize(req OptimizationRequest) (*OptimizationResponse, error) {
    jsonData, err := json.Marshal(req)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    httpReq, err := http.NewRequest("POST", c.BaseURL+"/api/v1/optimize", bytes.NewBuffer(jsonData))
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("X-API-Key", c.APIKey)

    resp, err := c.Client.Do(httpReq)
    if err != nil {
        return nil, fmt.Errorf("failed to send request: %w", err)
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response: %w", err)
    }

    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API error: %s", string(body))
    }

    var result OptimizationResponse
    if err := json.Unmarshal(body, &result); err != nil {
        return nil, fmt.Errorf("failed to unmarshal response: %w", err)
    }

    return &result, nil
}

func main() {
    client := NewCostOptimizerClient("your-api-key")

    req := OptimizationRequest{
        Prompt:            "Create a Go function that handles HTTP requests",
        QualityThreshold:  0.8,
        OptimizationLevel: 3,
        UserID:           "user123",
    }

    result, err := client.Optimize(req)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }

    fmt.Printf("Original Cost: $%.4f\n", result.OriginalCost)
    fmt.Printf("Optimized Cost: $%.4f\n", result.OptimizedCost)
    fmt.Printf("Savings: %.1f%%\n", result.SavingsPercentage)
    fmt.Printf("Quality Score: %.2f\n", result.QualityScore)
    fmt.Printf("Selected Model: %s\n", result.SelectedModel)
}
```

## Webhook Integration

```python
# Webhook handler for optimization results
from flask import Flask, request, jsonify
import hmac
import hashlib

app = Flask(__name__)

WEBHOOK_SECRET = "your-webhook-secret"

def verify_webhook_signature(payload, signature):
    """Verify webhook signature for security"""
    expected_signature = hmac.new(
        WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f"sha256={expected_signature}", signature)

@app.route('/webhook/optimization-complete', methods=['POST'])
def optimization_complete():
    # Verify signature
    signature = request.headers.get('X-Signature-256')
    if not verify_webhook_signature(request.data, signature):
        return jsonify({'error': 'Invalid signature'}), 401
    
    data = request.get_json()
    
    # Process optimization result
    optimization_id = data['optimization_id']
    user_id = data['user_id']
    savings_percentage = data['savings_percentage']
    quality_score = data['quality_score']
    
    # Your business logic here
    print(f"Optimization {optimization_id} completed for user {user_id}")
    print(f"Achieved {savings_percentage}% savings with {quality_score} quality")
    
    # Send notification, update database, etc.
    
    return jsonify({'status': 'received'}), 200

if __name__ == '__main__':
    app.run(port=5000)
```
