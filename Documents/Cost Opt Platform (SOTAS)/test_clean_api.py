"""
Test the clean API implementation
"""

import httpx
import pytest


def test_root_endpoint():
    """Test the root endpoint"""
    response = httpx.get("http://localhost:8001/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Claude Sonnet Cost Optimizer API"
    assert data["version"] == "1.0.0"
    assert data["status"] == "running"


def test_health_endpoint():
    """Test the health endpoint"""
    response = httpx.get("http://localhost:8001/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data


def test_optimization_endpoint():
    """Test the optimization endpoint"""
    test_request = {
        "prompt": "Explain quantum computing in simple terms",
        "model": "claude-4-sonnet"
    }
    
    response = httpx.post("http://localhost:8001/api/v1/optimize", json=test_request)
    assert response.status_code == 200
    
    data = response.json()
    assert "optimized_prompt" in data
    assert "selected_model" in data
    assert "original_cost" in data
    assert "optimized_cost" in data
    assert "savings_percentage" in data
    assert "quality_score" in data
    assert "processing_time_ms" in data
    assert "cache_hit" in data
    
    # Verify cost optimization
    assert data["optimized_cost"] < data["original_cost"]
    assert data["savings_percentage"] > 0
    assert data["quality_score"] >= 0.8


if __name__ == "__main__":
    print("Testing Clean API Implementation...")
    
    try:
        test_root_endpoint()
        print("✅ Root endpoint test passed")
        
        test_health_endpoint()
        print("✅ Health endpoint test passed")
        
        test_optimization_endpoint()
        print("✅ Optimization endpoint test passed")
        
        print("\n🎉 All tests passed! Clean implementation is working.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
