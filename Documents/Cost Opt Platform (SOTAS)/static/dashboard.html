<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Cost Optimizer - Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .metric-card {
            transition: transform 0.2s ease-in-out;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-brain text-3xl"></i>
                    <div>
                        <h1 class="text-2xl font-bold">Claude Sonnet Cost Optimizer</h1>
                        <p class="text-blue-100">Production-grade AI cost optimization platform</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-blue-100">Status</p>
                        <p class="font-semibold text-green-300">
                            <i class="fas fa-circle text-green-400 mr-1"></i>
                            Online
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Savings -->
            <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Cost Saved</p>
                        <p class="text-3xl font-bold text-green-600" id="totalSaved">$0.00</p>
                        <p class="text-sm text-gray-500 mt-1">This month</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Savings Percentage -->
            <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Average Savings</p>
                        <p class="text-3xl font-bold text-blue-600" id="avgSavings">87.5%</p>
                        <p class="text-sm text-gray-500 mt-1">Per request</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-percentage text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Cache Hit Rate -->
            <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Cache Hit Rate</p>
                        <p class="text-3xl font-bold text-purple-600" id="cacheHitRate">65%</p>
                        <p class="text-sm text-gray-500 mt-1">Last 24 hours</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-memory text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Requests -->
            <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Requests</p>
                        <p class="text-3xl font-bold text-orange-600" id="totalRequests">0</p>
                        <p class="text-sm text-gray-500 mt-1">All time</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Cost Savings Chart -->
            <div class="bg-white rounded-lg p-6 card-shadow">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-area text-green-600 mr-2"></i>
                    Cost Savings Over Time
                </h3>
                <canvas id="savingsChart" height="200"></canvas>
            </div>

            <!-- Model Usage Distribution -->
            <div class="bg-white rounded-lg p-6 card-shadow">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-pie-chart text-blue-600 mr-2"></i>
                    Model Usage Distribution
                </h3>
                <canvas id="modelChart" height="200"></canvas>
            </div>
        </div>

        <!-- Live Optimization Test -->
        <div class="bg-white rounded-lg p-6 card-shadow mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-rocket text-purple-600 mr-2"></i>
                Live Optimization Test
            </h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Prompt</label>
                    <textarea 
                        id="testPrompt" 
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="4"
                        placeholder="Enter a prompt to test cost optimization..."
                    >Explain quantum computing in simple terms for a beginner</textarea>
                    
                    <div class="mt-4 flex space-x-4">
                        <select id="targetModel" class="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="claude-4-sonnet">Claude 4 Sonnet</option>
                            <option value="claude-3-haiku">Claude 3 Haiku</option>
                            <option value="deepseek-chat">DeepSeek Chat</option>
                        </select>
                        <button 
                            id="optimizeBtn" 
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            <i class="fas fa-magic mr-2"></i>
                            Optimize
                        </button>
                    </div>
                </div>
                
                <div id="optimizationResult" class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-500 text-center">Run an optimization to see results</p>
                </div>
            </div>
        </div>

        <!-- Available Models -->
        <div class="bg-white rounded-lg p-6 card-shadow">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-cogs text-gray-600 mr-2"></i>
                Available Models
            </h3>
            <div id="modelsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Models will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6 mt-12">
        <div class="container mx-auto px-6 text-center">
            <p>&copy; 2025 Claude Sonnet Cost Optimizer. Built with FAANG+ engineering standards.</p>
        </div>
    </footer>

    <script>
        // API Base URL
        const API_BASE = window.location.origin;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadModels();
            initializeCharts();
            setupOptimizationTest();
            
            // Refresh stats every 30 seconds
            setInterval(loadStats, 30000);
        });

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/stats`);
                const stats = await response.json();
                
                document.getElementById('totalSaved').textContent = `$${stats.total_cost_saved.toFixed(2)}`;
                document.getElementById('avgSavings').textContent = `${stats.average_savings_percentage.toFixed(1)}%`;
                document.getElementById('cacheHitRate').textContent = `${(stats.cache_hit_rate * 100).toFixed(0)}%`;
                document.getElementById('totalRequests').textContent = stats.total_requests.toLocaleString();
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // Load available models
        async function loadModels() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/models`);
                const data = await response.json();
                
                const modelsGrid = document.getElementById('modelsGrid');
                modelsGrid.innerHTML = data.models.map(model => `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="font-semibold text-gray-800">${model.display_name}</h4>
                        <p class="text-sm text-gray-600 mt-1">${model.recommended_for}</p>
                        <div class="mt-3 flex justify-between items-center">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                Quality: ${(model.quality_score * 100).toFixed(0)}%
                            </span>
                            <span class="text-xs text-gray-500">
                                $${(model.cost_per_token * 1000000).toFixed(2)}/1M tokens
                            </span>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Failed to load models:', error);
            }
        }

        // Initialize charts
        function initializeCharts() {
            // Cost Savings Chart
            const savingsCtx = document.getElementById('savingsChart').getContext('2d');
            new Chart(savingsCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Cost Savings ($)',
                        data: [120, 190, 300, 500, 720, 890],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Model Usage Chart
            const modelCtx = document.getElementById('modelChart').getContext('2d');
            new Chart(modelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Claude 3 Haiku', 'Claude 4 Sonnet', 'DeepSeek Chat', 'Llama 3.1'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(147, 51, 234)',
                            'rgb(34, 197, 94)',
                            'rgb(249, 115, 22)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Setup optimization test
        function setupOptimizationTest() {
            const optimizeBtn = document.getElementById('optimizeBtn');
            const resultDiv = document.getElementById('optimizationResult');
            
            optimizeBtn.addEventListener('click', async function() {
                const prompt = document.getElementById('testPrompt').value;
                const model = document.getElementById('targetModel').value;
                
                if (!prompt.trim()) {
                    alert('Please enter a prompt to test');
                    return;
                }
                
                optimizeBtn.disabled = true;
                optimizeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Optimizing...';
                
                try {
                    const response = await fetch(`${API_BASE}/api/v1/optimize`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            model: model
                        })
                    });
                    
                    const result = await response.json();
                    
                    resultDiv.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Selected Model:</span>
                                <span class="text-blue-600">${result.selected_model}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Cost Savings:</span>
                                <span class="text-green-600 font-bold">${result.savings_percentage.toFixed(1)}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Quality Score:</span>
                                <span class="text-purple-600">${(result.quality_score * 100).toFixed(0)}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Processing Time:</span>
                                <span class="text-gray-600">${result.processing_time_ms}ms</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Cache Hit:</span>
                                <span class="${result.cache_hit ? 'text-green-600' : 'text-gray-600'}">
                                    ${result.cache_hit ? 'Yes' : 'No'}
                                </span>
                            </div>
                            <div class="pt-2 border-t">
                                <span class="text-sm text-gray-500">Strategy: ${result.optimization_strategy}</span>
                            </div>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="text-red-600">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Optimization failed: ${error.message}
                        </div>
                    `;
                } finally {
                    optimizeBtn.disabled = false;
                    optimizeBtn.innerHTML = '<i class="fas fa-magic mr-2"></i>Optimize';
                }
            });
        }
    </script>
</body>
</html>
