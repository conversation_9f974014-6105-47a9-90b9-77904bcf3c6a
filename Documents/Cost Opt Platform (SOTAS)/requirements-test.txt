# Testing Dependencies for Cost Optimization Platform
# FAANG+ testing standards with comprehensive coverage

# Core Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0  # Parallel test execution
pytest-html>=3.2.0   # HTML test reports
pytest-json-report>=1.5.0

# HTTP Testing
httpx>=0.24.0
aiohttp>=3.8.0
requests-mock>=1.11.0

# Database Testing
pytest-postgresql>=5.0.0
pytest-redis>=3.0.0
sqlalchemy-utils>=0.41.0

# Performance Testing
pytest-benchmark>=4.0.0
locust>=2.15.0  # Load testing
memory-profiler>=0.60.0

# Security Testing
bandit>=1.7.5
safety>=2.3.0
semgrep>=1.31.0

# Code Quality
flake8>=6.0.0
mypy>=1.4.0
black>=23.7.0
isort>=5.12.0
pylint>=2.17.0

# Coverage and Reporting
coverage>=7.2.0
coverage-badge>=1.1.0

# Mock and Fixtures
factory-boy>=3.3.0
faker>=19.0.0
responses>=0.23.0

# Async Testing
asynctest>=0.13.0
pytest-trio>=0.8.0

# API Testing
fastapi[test]>=0.100.0
starlette[test]>=0.27.0

# Time and Date Testing
freezegun>=1.2.0
time-machine>=2.10.0

# Environment and Configuration
python-dotenv>=1.0.0
pydantic[dotenv]>=2.0.0

# Monitoring and Observability Testing
prometheus-client>=0.17.0
opentelemetry-api>=1.18.0
opentelemetry-sdk>=1.18.0

# Machine Learning Testing
numpy>=1.24.0
scikit-learn>=1.3.0

# Utilities
psutil>=5.9.0  # System monitoring
colorama>=0.4.6  # Colored output
rich>=13.4.0  # Rich console output