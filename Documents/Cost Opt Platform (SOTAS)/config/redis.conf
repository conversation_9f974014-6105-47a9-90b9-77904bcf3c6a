# Redis Configuration for Cost Optimization Platform
# Optimized for OrbStack deployment with security and performance

# Network configuration
bind 0.0.0.0
port 6379
protected-mode no  # Disabled for internal Docker network
tcp-backlog 511
timeout 0
tcp-keepalive 300

# General configuration
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Snapshotting configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication (not used in single instance)
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security (basic - enhanced by network isolation)
requirepass ""  # No password needed in isolated network
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_COSTOPT
rename-command DEBUG ""
rename-command EVAL ""

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Lazy freeing
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua scripting
lua-time-limit 5000

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# Event notification
notify-keyspace-events ""

# Advanced config
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# Enable active defragmentation
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 5
active-defrag-cycle-max 75
active-defrag-max-scan-fields 1000

# Jemalloc background thread for purging
jemalloc-bg-thread yes

# RDB and AOF file permissions
rdb-save-incremental-fsync yes
aof-rewrite-incremental-fsync yes

# TLS/SSL (disabled for internal network)
tls-port 0
tls-cert-file ""
tls-key-file ""
tls-dh-params-file ""
tls-ca-cert-file ""
tls-ca-cert-dir ""
tls-auth-clients no
tls-protocols "TLSv1.2 TLSv1.3"
tls-ciphers ""
tls-ciphersuites ""
tls-prefer-server-ciphers yes
tls-session-caching no
tls-session-cache-size 5000
tls-session-cache-timeout 60

# Cluster (disabled for single instance)
cluster-enabled no

# Modules
loadmodule ""

# Performance optimizations for OrbStack
tcp-nodelay yes
rdbcompression yes
rdbchecksum yes

# Memory usage optimization
maxmemory-policy allkeys-lru
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes

# Disable some features for performance
save ""  # Disable automatic saves for performance (rely on AOF)
appendonly yes
appendfsync everysec

# Connection limits
maxclients 10000

# Timeout settings
timeout 0
tcp-keepalive 300

# Log configuration
loglevel notice
syslog-enabled no

# Database configuration
databases 16

# Key expiration
active-expire-effort 1
