# Development Environment Configuration
# This file contains development-specific settings

# =============================================================================
# Development Environment Settings
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
HOST=0.0.0.0
PORT=8000

# Development secrets (use secure values in production)
JWT_SECRET_KEY=dev-jwt-secret-key-not-for-production-use
API_KEY=dev-api-key-12345-not-for-production

# =============================================================================
# Development Database Configuration
# =============================================================================
DATABASE_URL=postgresql+asyncpg://costopt:costopt123@localhost:5432/costopt_dev
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Test database for running tests
TEST_DATABASE_URL=postgresql+asyncpg://costopt:costopt123@localhost:5432/costopt_test

# =============================================================================
# Development Cache Configuration
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=5
REDIS_MAX_CONNECTIONS=20

# Test Redis for running tests
TEST_REDIS_URL=redis://localhost:6379/1

# =============================================================================
# Development OpenRouter Configuration
# =============================================================================
# Use your actual OpenRouter API key for development
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_TIMEOUT=30
OPENROUTER_MAX_RETRIES=3

# =============================================================================
# Development Vector Database Configuration
# =============================================================================
CHROMADB_HOST=localhost
CHROMADB_PORT=8001
CHROMADB_COLLECTION_NAME=cost_optimization_dev

QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=semantic_cache_dev
QDRANT_API_KEY=

WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_API_KEY=

MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=optimization_vectors_dev

ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_NAME=cost_optimization_dev

# =============================================================================
# Development Performance Settings
# =============================================================================
MAX_CONCURRENT_REQUESTS=20
MAX_CONCURRENT_OPTIMIZATIONS=10
REQUEST_TIMEOUT_SECONDS=30
MAX_PROMPT_LENGTH=50000

# Compression settings
MAX_COMPRESSION_RATIO=0.7
DEFAULT_OPTIMIZATION_LEVEL=3
COMPRESSION_TIMEOUT=10

# Cache settings
CACHE_TTL_SECONDS=1800  # 30 minutes
CACHE_MAX_SIZE=1000
SEMANTIC_SIMILARITY_THRESHOLD=0.85

# =============================================================================
# Development Security Settings
# =============================================================================
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:3000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Rate limiting (relaxed for development)
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_BURST_SIZE=100
RATE_LIMIT_WINDOW_SIZE=60

# =============================================================================
# Development Monitoring Settings
# =============================================================================
JAEGER_ENABLED=true
JAEGER_HOST=localhost
JAEGER_PORT=14268

METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# =============================================================================
# Development Feature Flags
# =============================================================================
COMPRESSION_ENABLED=true
ADAPTIVE_LEARNING_ENABLED=true
VECTOR_CACHING_ENABLED=true
QUALITY_ASSESSMENT_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Development-specific features
MOCK_OPENROUTER=false
MOCK_VECTOR_DATABASES=false
ENABLE_PROFILING=true
ENABLE_DEBUG_TOOLBAR=true

# Experimental features (safe to enable in development)
EXPERIMENTAL_FEATURES_ENABLED=true
BETA_COMPRESSION_ALGORITHM=true
ADVANCED_MODEL_ROUTING=true

# =============================================================================
# Development Model Configuration
# =============================================================================
CLAUDE_SONNET_MODEL=anthropic/claude-3.5-sonnet
DEEPSEEK_V3_MODEL=deepseek/deepseek-v3
LLAMA_FREE_MODEL=meta-llama/llama-3.1-8b-instruct:free
MISTRAL_FREE_MODEL=mistralai/mistral-7b-instruct:free

# =============================================================================
# Development Quality Settings
# =============================================================================
DEFAULT_QUALITY_THRESHOLD=0.8  # Slightly lower for development
CACHE_SIMILARITY_THRESHOLD=0.75

# Circuit breaker settings (relaxed for development)
CIRCUIT_BREAKER_FAILURE_THRESHOLD=10
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30

# =============================================================================
# Development External Integrations
# =============================================================================
N8N_WEBHOOK_URL=http://localhost:5678/webhook/cost-optimization
N8N_API_KEY=

TERMINAL_MONITOR_ENABLED=true
TERMINAL_MONITOR_INTERVAL=60

# =============================================================================
# Development Health Check Settings
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
