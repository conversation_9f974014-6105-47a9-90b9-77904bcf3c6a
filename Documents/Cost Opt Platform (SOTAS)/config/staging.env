# Staging Environment Configuration
# This file contains staging-specific settings
# Staging should mirror production as closely as possible

# =============================================================================
# Staging Environment Settings
# =============================================================================
ENVIRONMENT=staging
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Staging secrets (use production-like security)
JWT_SECRET_KEY=staging-jwt-secret-key-change-for-production
API_KEY=staging-api-key-12345-change-for-production

# =============================================================================
# Staging Database Configuration
# =============================================================================
DATABASE_URL=postgresql+asyncpg://costopt:password@staging-db:5432/costopt_staging
DATABASE_POOL_SIZE=15
DATABASE_MAX_OVERFLOW=25
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# SSL configuration for staging database
DATABASE_SSL_MODE=require

# =============================================================================
# Staging Cache Configuration
# =============================================================================
REDIS_URL=redis://staging-redis:6379/0
REDIS_PASSWORD=staging-redis-password
REDIS_POOL_SIZE=15
REDIS_MAX_CONNECTIONS=50

# =============================================================================
# Staging OpenRouter Configuration
# =============================================================================
OPENROUTER_API_KEY=your_staging_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=https://staging.your-domain.com
OPENROUTER_APP_NAME=Cost Optimization Platform (Staging)
OPENROUTER_TIMEOUT=30
OPENROUTER_MAX_RETRIES=3

# =============================================================================
# Staging Vector Database Configuration
# =============================================================================
CHROMADB_HOST=staging-chromadb
CHROMADB_PORT=8000
CHROMADB_COLLECTION_NAME=cost_optimization_staging

QDRANT_HOST=staging-qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=semantic_cache_staging
QDRANT_API_KEY=staging-qdrant-api-key

WEAVIATE_HOST=staging-weaviate
WEAVIATE_PORT=8080
WEAVIATE_API_KEY=staging-weaviate-api-key

MILVUS_HOST=staging-milvus
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=optimization_vectors_staging

ELASTICSEARCH_HOST=staging-elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=staging-elasticsearch-password
ELASTICSEARCH_INDEX_NAME=cost_optimization_staging

# =============================================================================
# Staging Performance Settings
# =============================================================================
MAX_CONCURRENT_REQUESTS=75
MAX_CONCURRENT_OPTIMIZATIONS=35
REQUEST_TIMEOUT_SECONDS=30
MAX_PROMPT_LENGTH=100000

# Compression settings
MAX_COMPRESSION_RATIO=0.7
DEFAULT_OPTIMIZATION_LEVEL=3
COMPRESSION_TIMEOUT=10

# Cache settings
CACHE_TTL_SECONDS=3600  # 1 hour
CACHE_MAX_SIZE=5000
SEMANTIC_SIMILARITY_THRESHOLD=0.85

# =============================================================================
# Staging Security Settings
# =============================================================================
# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/staging/ssl/cert.pem
SSL_KEY_PATH=/path/to/staging/ssl/key.pem

# CORS settings
ALLOWED_ORIGINS=["https://staging.your-domain.com", "https://staging-admin.your-domain.com"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["Content-Type", "Authorization", "X-API-Key"]

# Rate limiting (production-like)
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20
RATE_LIMIT_WINDOW_SIZE=60

# Security headers
SECURITY_HEADERS_ENABLED=true

# =============================================================================
# Staging Monitoring Settings
# =============================================================================
# Jaeger tracing
JAEGER_ENABLED=true
JAEGER_HOST=staging-jaeger
JAEGER_PORT=14268

# Prometheus metrics
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# External monitoring services
SENTRY_DSN=https://<EMAIL>/staging-project-id
DATADOG_API_KEY=staging-datadog-api-key

# Logging configuration
LOG_FORMAT=json
LOG_FILE=/app/logs/application.log
ACCESS_LOG=/app/logs/access.log
ERROR_LOG=/app/logs/error.log

# =============================================================================
# Staging Worker Configuration
# =============================================================================
WORKERS=2
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_CONNECTIONS=500
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
GRACEFUL_TIMEOUT=30
KEEPALIVE=2

# =============================================================================
# Staging Feature Flags
# =============================================================================
COMPRESSION_ENABLED=true
ADAPTIVE_LEARNING_ENABLED=true
VECTOR_CACHING_ENABLED=true
QUALITY_ASSESSMENT_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Disable development features
MOCK_OPENROUTER=false
MOCK_VECTOR_DATABASES=false
ENABLE_PROFILING=false
ENABLE_DEBUG_TOOLBAR=false

# Experimental features (can be enabled for testing)
EXPERIMENTAL_FEATURES_ENABLED=true
BETA_COMPRESSION_ALGORITHM=true
ADVANCED_MODEL_ROUTING=true

# =============================================================================
# Staging Model Configuration
# =============================================================================
CLAUDE_SONNET_MODEL=anthropic/claude-3.5-sonnet
DEEPSEEK_V3_MODEL=deepseek/deepseek-v3
LLAMA_FREE_MODEL=meta-llama/llama-3.1-8b-instruct:free
MISTRAL_FREE_MODEL=mistralai/mistral-7b-instruct:free

# =============================================================================
# Staging Quality Settings
# =============================================================================
DEFAULT_QUALITY_THRESHOLD=0.85
CACHE_SIMILARITY_THRESHOLD=0.75

# Circuit breaker settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# =============================================================================
# Staging External Integrations
# =============================================================================
N8N_WEBHOOK_URL=https://staging-n8n.your-domain.com/webhook/cost-optimization
N8N_API_KEY=staging-n8n-api-key

TERMINAL_MONITOR_ENABLED=true
TERMINAL_MONITOR_INTERVAL=60

# =============================================================================
# Staging Health Check Settings
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# Staging Backup Configuration
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 3 * * *  # Daily at 3 AM
BACKUP_RETENTION_DAYS=14
BACKUP_S3_BUCKET=your-staging-backup-bucket
BACKUP_S3_ACCESS_KEY=staging-s3-access-key
BACKUP_S3_SECRET_KEY=staging-s3-secret-key

# =============================================================================
# Staging Email Configuration
# =============================================================================
EMAIL_ENABLED=true
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USERNAME=staging-smtp-username
SMTP_PASSWORD=staging-smtp-password
FROM_EMAIL=<EMAIL>

# =============================================================================
# Staging Webhook Configuration
# =============================================================================
WEBHOOK_ENABLED=true
WEBHOOK_URL=https://staging-webhook.your-domain.com/cost-optimization
WEBHOOK_SECRET=staging-webhook-secret
