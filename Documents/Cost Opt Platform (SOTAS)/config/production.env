# Production Environment Configuration
# This file contains production-specific settings
# IMPORTANT: Replace all placeholder values with actual production values

# =============================================================================
# Production Environment Settings
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Production secrets (MUST be changed)
JWT_SECRET_KEY=CHANGE_THIS_TO_SECURE_RANDOM_KEY_IN_PRODUCTION
API_KEY=CHANGE_THIS_TO_SECURE_API_KEY_IN_PRODUCTION

# =============================================================================
# Production Database Configuration
# =============================================================================
DATABASE_URL=postgresql+asyncpg://username:password@db-host:5432/costopt_prod
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# SSL configuration for production database
DATABASE_SSL_MODE=require
DATABASE_SSL_CERT=/path/to/client-cert.pem
DATABASE_SSL_KEY=/path/to/client-key.pem
DATABASE_SSL_ROOT_CERT=/path/to/ca-cert.pem

# =============================================================================
# Production Cache Configuration
# =============================================================================
REDIS_URL=redis://redis-host:6379/0
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_POOL_SIZE=20
REDIS_MAX_CONNECTIONS=100

# =============================================================================
# Production OpenRouter Configuration
# =============================================================================
OPENROUTER_API_KEY=CHANGE_THIS_TO_YOUR_OPENROUTER_API_KEY
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=https://your-domain.com
OPENROUTER_APP_NAME=Cost Optimization Platform
OPENROUTER_TIMEOUT=30
OPENROUTER_MAX_RETRIES=3

# =============================================================================
# Production Vector Database Configuration
# =============================================================================
CHROMADB_HOST=chromadb-host
CHROMADB_PORT=8000
CHROMADB_COLLECTION_NAME=cost_optimization_prod

QDRANT_HOST=qdrant-host
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=semantic_cache_prod
QDRANT_API_KEY=CHANGE_THIS_QDRANT_API_KEY

WEAVIATE_HOST=weaviate-host
WEAVIATE_PORT=8080
WEAVIATE_API_KEY=CHANGE_THIS_WEAVIATE_API_KEY

MILVUS_HOST=milvus-host
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=optimization_vectors_prod

ELASTICSEARCH_HOST=elasticsearch-host
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic_user
ELASTICSEARCH_PASSWORD=CHANGE_THIS_ELASTICSEARCH_PASSWORD
ELASTICSEARCH_INDEX_NAME=cost_optimization_prod

# =============================================================================
# Production Performance Settings
# =============================================================================
MAX_CONCURRENT_REQUESTS=100
MAX_CONCURRENT_OPTIMIZATIONS=50
REQUEST_TIMEOUT_SECONDS=30
MAX_PROMPT_LENGTH=100000

# Compression settings
MAX_COMPRESSION_RATIO=0.7
DEFAULT_OPTIMIZATION_LEVEL=3
COMPRESSION_TIMEOUT=10

# Cache settings
CACHE_TTL_SECONDS=3600  # 1 hour
CACHE_MAX_SIZE=10000
SEMANTIC_SIMILARITY_THRESHOLD=0.85

# =============================================================================
# Production Security Settings
# =============================================================================
# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# CORS settings (restrict to your domains)
ALLOWED_ORIGINS=["https://your-frontend-domain.com", "https://your-admin-domain.com"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE"]
ALLOWED_HEADERS=["Content-Type", "Authorization", "X-API-Key"]

# Rate limiting (strict for production)
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20
RATE_LIMIT_WINDOW_SIZE=60

# Security headers
SECURITY_HEADERS_ENABLED=true

# =============================================================================
# Production Monitoring Settings
# =============================================================================
# Jaeger tracing
JAEGER_ENABLED=true
JAEGER_HOST=jaeger-host
JAEGER_PORT=14268

# Prometheus metrics
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# External monitoring services
SENTRY_DSN=https://<EMAIL>/project-id
DATADOG_API_KEY=CHANGE_THIS_DATADOG_API_KEY

# Logging configuration
LOG_FORMAT=json
LOG_FILE=/app/logs/application.log
ACCESS_LOG=/app/logs/access.log
ERROR_LOG=/app/logs/error.log

# =============================================================================
# Production Worker Configuration
# =============================================================================
WORKERS=4
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
GRACEFUL_TIMEOUT=30
KEEPALIVE=2

# =============================================================================
# Production Feature Flags
# =============================================================================
COMPRESSION_ENABLED=true
ADAPTIVE_LEARNING_ENABLED=true
VECTOR_CACHING_ENABLED=true
QUALITY_ASSESSMENT_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Disable development features
MOCK_OPENROUTER=false
MOCK_VECTOR_DATABASES=false
ENABLE_PROFILING=false
ENABLE_DEBUG_TOOLBAR=false

# Experimental features (disable in production unless tested)
EXPERIMENTAL_FEATURES_ENABLED=false
BETA_COMPRESSION_ALGORITHM=false
ADVANCED_MODEL_ROUTING=false

# =============================================================================
# Production Model Configuration
# =============================================================================
CLAUDE_SONNET_MODEL=anthropic/claude-3.5-sonnet
DEEPSEEK_V3_MODEL=deepseek/deepseek-v3
LLAMA_FREE_MODEL=meta-llama/llama-3.1-8b-instruct:free
MISTRAL_FREE_MODEL=mistralai/mistral-7b-instruct:free

# =============================================================================
# Production Quality Settings
# =============================================================================
DEFAULT_QUALITY_THRESHOLD=0.85
CACHE_SIMILARITY_THRESHOLD=0.75

# Circuit breaker settings (strict for production)
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# =============================================================================
# Production External Integrations
# =============================================================================
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/cost-optimization
N8N_API_KEY=CHANGE_THIS_N8N_API_KEY

TERMINAL_MONITOR_ENABLED=true
TERMINAL_MONITOR_INTERVAL=60

# =============================================================================
# Production Health Check Settings
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# Production Backup Configuration
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=CHANGE_THIS_S3_ACCESS_KEY
BACKUP_S3_SECRET_KEY=CHANGE_THIS_S3_SECRET_KEY

# =============================================================================
# Production Email Configuration
# =============================================================================
EMAIL_ENABLED=true
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=CHANGE_THIS_SMTP_PASSWORD
FROM_EMAIL=<EMAIL>

# =============================================================================
# Production Webhook Configuration
# =============================================================================
WEBHOOK_ENABLED=true
WEBHOOK_URL=https://your-webhook-endpoint.com/cost-optimization
WEBHOOK_SECRET=CHANGE_THIS_WEBHOOK_SECRET
