#!/usr/bin/env python3
"""
Simple test script to verify web dashboard functionality
"""

import asyncio
import sys
from pathlib import Path
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
import uvicorn

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

app = FastAPI(title="Dashboard Test")

# Mount static files
static_dir = Path(__file__).parent / "src" / "static"
app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/", response_class=HTMLResponse)
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Serve the main dashboard HTML file"""
    html_file = static_dir / "index.html"
    if html_file.exists():
        return FileResponse(html_file, media_type="text/html")
    else:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Dashboard Not Found</title></head>
                <body>
                    <h1>Dashboard files not found</h1>
                    <p>Please ensure the static files are properly deployed.</p>
                </body>
            </html>
            """,
            status_code=404
        )

@app.get("/health")
async def health():
    """Simple health check"""
    return {"status": "ok", "message": "Dashboard test server running"}

@app.get("/api/v1/metrics/dashboard")
async def mock_metrics():
    """Mock metrics endpoint for dashboard testing"""
    return {
        "total_savings": 1250.75,
        "savings_rate": 85.2,
        "avg_latency": 45,
        "cache_hit_rate": 92.5,
        "recent_optimizations": [
            {
                "id": "opt_001",
                "timestamp": "2025-06-27T00:00:00Z",
                "original_cost": 10.50,
                "optimized_cost": 1.25,
                "savings": 88.1,
                "model": "claude-3-sonnet"
            },
            {
                "id": "opt_002", 
                "timestamp": "2025-06-26T23:45:00Z",
                "original_cost": 8.75,
                "optimized_cost": 1.10,
                "savings": 87.4,
                "model": "claude-3-opus"
            }
        ]
    }

if __name__ == "__main__":
    print("Starting dashboard test server...")
    print("Dashboard will be available at: http://localhost:8002")
    print("Health check at: http://localhost:8002/health")
    print("Press Ctrl+C to stop")
    
    uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
