<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">20%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 13:09 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html">cost_optimizer.py</a></td>
                <td>301</td>
                <td>301</td>
                <td>0</td>
                <td class="right" data-ratio="0 301">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_cost_savings_py.html">demo_cost_savings.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="healthcheck_py.html">healthcheck.py</a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="live_api_test_py.html">live_api_test.py</a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="manual_test_py.html">manual_test.py</a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_validation_py.html">production_validation.py</a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quality_test_py.html">quality_test.py</a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="redis_cache_py.html">redis_cache.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_py.html">run.py</a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html">run_tests.py</a></td>
                <td>198</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="0 198">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="setup_production_py.html">setup_production.py</a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html">simple_demo.py</a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="0 154">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860___init___py.html">src/api/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860_dependencies_py.html">src/api/dependencies.py</a></td>
                <td>179</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="42 179">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a3eecf107d40a09d___init___py.html">src/api/v1/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c___init___py.html">src/api/v1/routes/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_dashboard_py.html">src/api/v1/routes/dashboard.py</a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_health_py.html">src/api/v1/routes/health.py</a></td>
                <td>150</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="33 150">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_metrics_py.html">src/api/v1/routes/metrics.py</a></td>
                <td>107</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="31 107">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_optimization_py.html">src/api/v1/routes/optimization.py</a></td>
                <td>122</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="24 122">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_proxy_py.html">src/api/v1/routes/proxy.py</a></td>
                <td>53</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="25 53">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_service_discovery_py.html">src/api/v1/routes/service_discovery.py</a></td>
                <td>121</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="22 121">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e041423fa8cb161___init___py.html">src/clients/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e041423fa8cb161_openrouter_py.html">src/clients/openrouter.py</a></td>
                <td>110</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="36 110">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca___init___py.html">src/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cache_py.html">src/core/cache.py</a></td>
                <td>174</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="41 174">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html">src/core/config.py</a></td>
                <td>256</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="204 256">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html">src/core/config_clean.py</a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html">src/core/cost_optimizer.py</a></td>
                <td>134</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 134">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html">src/core/database.py</a></td>
                <td>156</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="117 156">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html">src/core/database_clean.py</a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html">src/core/env_validator.py</a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html">src/core/models.py</a></td>
                <td>162</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="159 162">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html">src/core/openrouter_client.py</a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html">src/core/optimizer.py</a></td>
                <td>233</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="68 233">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html">src/core/security_config.py</a></td>
                <td>159</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="95 159">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html">src/core/service_discovery.py</a></td>
                <td>202</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="71 202">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html">src/core/simple_optimizer.py</a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src/main.py</a></td>
                <td>105</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="61 105">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_clean_py.html">src/main_clean.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736___init___py.html">src/middleware/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html">src/middleware/auth.py</a></td>
                <td>141</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="54 141">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_cors_security_py.html">src/middleware/cors_security.py</a></td>
                <td>139</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="25 139">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_input_validation_py.html">src/middleware/input_validation.py</a></td>
                <td>176</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="29 176">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html">src/middleware/logging.py</a></td>
                <td>118</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="26 118">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html">src/middleware/monitoring.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_rate_limiting_py.html">src/middleware/rate_limiting.py</a></td>
                <td>198</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="30 198">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_security_py.html">src/middleware/security.py</a></td>
                <td>164</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="31 164">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b___init___py.html">src/monitoring/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html">src/monitoring/alerting.py</a></td>
                <td>256</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="91 256">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_analytics_py.html">src/monitoring/analytics.py</a></td>
                <td>111</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="49 111">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html">src/monitoring/health.py</a></td>
                <td>203</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="39 203">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html">src/monitoring/logging_config.py</a></td>
                <td>109</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="31 109">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html">src/monitoring/metrics.py</a></td>
                <td>153</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="51 153">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_registry_py.html">src/monitoring/metrics_registry.py</a></td>
                <td>113</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="58 113">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html">src/monitoring/performance_profiler.py</a></td>
                <td>212</td>
                <td>212</td>
                <td>0</td>
                <td class="right" data-ratio="0 212">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_setup_py.html">src/monitoring/setup.py</a></td>
                <td>161</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="32 161">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_tracing_py.html">src/monitoring/tracing.py</a></td>
                <td>135</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="29 135">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831___init___py.html">src/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_adaptive_learner_py.html">src/services/adaptive_learner.py</a></td>
                <td>204</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="54 204">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html">src/services/async_processor.py</a></td>
                <td>307</td>
                <td>205</td>
                <td>0</td>
                <td class="right" data-ratio="102 307">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html">src/services/cache_manager.py</a></td>
                <td>556</td>
                <td>451</td>
                <td>0</td>
                <td class="right" data-ratio="105 556">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html">src/services/claude_optimizer.py</a></td>
                <td>146</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="52 146">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html">src/services/compression_engine.py</a></td>
                <td>145</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="41 145">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html">src/services/metrics_service.py</a></td>
                <td>157</td>
                <td>157</td>
                <td>0</td>
                <td class="right" data-ratio="0 157">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html">src/services/model_router.py</a></td>
                <td>226</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="62 226">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html">src/services/monitoring_service.py</a></td>
                <td>366</td>
                <td>245</td>
                <td>0</td>
                <td class="right" data-ratio="121 366">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html">src/services/openrouter_client.py</a></td>
                <td>200</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="83 200">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html">src/services/performance_benchmark.py</a></td>
                <td>299</td>
                <td>299</td>
                <td>0</td>
                <td class="right" data-ratio="0 299">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html">src/services/quality_assessor.py</a></td>
                <td>254</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="77 254">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html">src/services/security_service.py</a></td>
                <td>321</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="107 321">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html">src/services/terminal_monitor.py</a></td>
                <td>293</td>
                <td>293</td>
                <td>0</td>
                <td class="right" data-ratio="0 293">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html">src/services/vector_database.py</a></td>
                <td>306</td>
                <td>306</td>
                <td>0</td>
                <td class="right" data-ratio="0 306">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154___init___py.html">src/services/vector_databases/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html">src/services/vector_databases/elasticsearch_client.py</a></td>
                <td>293</td>
                <td>293</td>
                <td>0</td>
                <td class="right" data-ratio="0 293">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html">src/services/vector_databases/milvus_client.py</a></td>
                <td>280</td>
                <td>280</td>
                <td>0</td>
                <td class="right" data-ratio="0 280">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html">src/services/vector_databases/qdrant_client.py</a></td>
                <td>231</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="0 231">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html">src/services/vector_databases/weaviate_client.py</a></td>
                <td>287</td>
                <td>287</td>
                <td>0</td>
                <td class="right" data-ratio="0 287">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_clean_api_py.html">test_clean_api.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_complete_system_py.html">test_complete_system.py</a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_dashboard_py.html">test_dashboard.py</a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_imports_py.html">test_imports.py</a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_main_minimal_py.html">test_main_minimal.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_platform_py.html">test_platform.py</a></td>
                <td>198</td>
                <td>198</td>
                <td>0</td>
                <td class="right" data-ratio="0 198">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_production_system_py.html">test_production_system.py</a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52b758d7847d4488_test_api_documentation_py.html">tests/api/test_api_documentation.py</a></td>
                <td>156</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="23 156">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html">tests/conftest.py</a></td>
                <td>132</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="64 132">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_cost_savings_validation_py.html">tests/e2e/test_cost_savings_validation.py</a></td>
                <td>183</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="24 183">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_end_to_end_py.html">tests/e2e/test_end_to_end.py</a></td>
                <td>138</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="22 138">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_database_integration_py.html">tests/integration/test_database_integration.py</a></td>
                <td>125</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="31 125">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_flow_py.html">tests/integration/test_optimization_flow.py</a></td>
                <td>142</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="39 142">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_pipeline_py.html">tests/integration/test_optimization_pipeline.py</a></td>
                <td>171</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="24 171">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_redis_integration_py.html">tests/integration/test_redis_integration.py</a></td>
                <td>173</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="40 173">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_load_testing_py.html">tests/performance/test_load_testing.py</a></td>
                <td>190</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="24 190">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_performance_benchmarks_py.html">tests/performance/test_performance_benchmarks.py</a></td>
                <td>198</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="35 198">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_run_comprehensive_tests_py.html">tests/run_comprehensive_tests.py</a></td>
                <td>157</td>
                <td>157</td>
                <td>0</td>
                <td class="right" data-ratio="0 157">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html">tests/test_framework.py</a></td>
                <td>176</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="53 176">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html">tests/test_optimization.py</a></td>
                <td>135</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="26 135">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html">tests/test_optimizer.py</a></td>
                <td>183</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="40 183">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_adaptive_learner_py.html">tests/unit/test_adaptive_learner.py</a></td>
                <td>147</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="43 147">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_async_processor_py.html">tests/unit/test_async_processor.py</a></td>
                <td>188</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="44 188">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html">tests/unit/test_cache_manager.py</a></td>
                <td>175</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="44 175">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_compression_engine_py.html">tests/unit/test_compression_engine.py</a></td>
                <td>133</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="38 133">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cost_optimizer_py.html">tests/unit/test_cost_optimizer.py</a></td>
                <td>153</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="45 153">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_model_router_py.html">tests/unit/test_model_router.py</a></td>
                <td>139</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="45 139">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_monitoring_service_py.html">tests/unit/test_monitoring_service.py</a></td>
                <td>177</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="50 177">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_openrouter_client_py.html">tests/unit/test_openrouter_client.py</a></td>
                <td>183</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="43 183">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_quality_assessor_py.html">tests/unit/test_quality_assessor.py</a></td>
                <td>141</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="38 141">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_security_service_py.html">tests/unit/test_security_service.py</a></td>
                <td>169</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="53 169">31%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>16643</td>
                <td>13328</td>
                <td>0</td>
                <td class="right" data-ratio="3315 16643">20%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 13:09 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_17bd492d087794b9_test_security_service_py.html"></a>
        <a id="nextFileLink" class="nav" href="cost_optimizer_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
