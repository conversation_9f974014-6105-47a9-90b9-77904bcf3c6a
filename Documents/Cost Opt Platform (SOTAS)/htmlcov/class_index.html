<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">15%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 12:50 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t36">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t36"><data value='ModelTier'>ModelTier</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t41">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t41"><data value='TaskComplexity'>TaskComplexity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t48">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t48"><data value='OptimizationRequest'>OptimizationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t60">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t60"><data value='OptimizationResponse'>OptimizationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t71">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t71"><data value='CostOptimizer'>CostOptimizer</data></a></td>
                <td>192</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="0 192">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t633">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t633"><data value='OptimizeRequest'>OptimizeRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html#t644">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html#t644"><data value='OptimizeResponseModel'>OptimizeResponseModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="cost_optimizer_py.html">cost_optimizer.py</a></td>
                <td class="name left"><a href="cost_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_cost_savings_py.html#t18">demo_cost_savings.py</a></td>
                <td class="name left"><a href="demo_cost_savings_py.html#t18"><data value='CostSavingsDemonstration'>CostSavingsDemonstration</data></a></td>
                <td>138</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="0 138">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_cost_savings_py.html">demo_cost_savings.py</a></td>
                <td class="name left"><a href="demo_cost_savings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="healthcheck_py.html#t24">healthcheck.py</a></td>
                <td class="name left"><a href="healthcheck_py.html#t24"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="healthcheck_py.html">healthcheck.py</a></td>
                <td class="name left"><a href="healthcheck_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="live_api_test_py.html#t42">live_api_test.py</a></td>
                <td class="name left"><a href="live_api_test_py.html#t42"><data value='LiveTestResult'>LiveTestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="live_api_test_py.html#t54">live_api_test.py</a></td>
                <td class="name left"><a href="live_api_test_py.html#t54"><data value='LiveAPITester'>LiveAPITester</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="live_api_test_py.html">live_api_test.py</a></td>
                <td class="name left"><a href="live_api_test_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="manual_test_py.html">manual_test.py</a></td>
                <td class="name left"><a href="manual_test_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_validation_py.html#t17">production_validation.py</a></td>
                <td class="name left"><a href="production_validation_py.html#t17"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_validation_py.html#t24">production_validation.py</a></td>
                <td class="name left"><a href="production_validation_py.html#t24"><data value='ProductionValidator'>ProductionValidator</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_validation_py.html">production_validation.py</a></td>
                <td class="name left"><a href="production_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quality_test_py.html">quality_test.py</a></td>
                <td class="name left"><a href="quality_test_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="redis_cache_py.html#t24">redis_cache.py</a></td>
                <td class="name left"><a href="redis_cache_py.html#t24"><data value='RedisCache'>RedisCache</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="redis_cache_py.html#t187">redis_cache.py</a></td>
                <td class="name left"><a href="redis_cache_py.html#t187"><data value='OptimizationCache'>OptimizationCache</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="redis_cache_py.html">redis_cache.py</a></td>
                <td class="name left"><a href="redis_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_py.html">run.py</a></td>
                <td class="name left"><a href="run_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html#t28">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html#t28"><data value='TestSuite'>TestSuite</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html#t39">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html#t39"><data value='TestResult'>TestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html#t52">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html#t52"><data value='TestRunner'>TestRunner</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_tests_py.html">run_tests.py</a></td>
                <td class="name left"><a href="run_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="setup_production_py.html">setup_production.py</a></td>
                <td class="name left"><a href="setup_production_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html#t24">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html#t24"><data value='ModelTier'>ModelTier</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html#t29">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html#t29"><data value='TaskComplexity'>TaskComplexity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html#t36">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html#t36"><data value='OptimizationRequest'>OptimizationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html#t44">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html#t44"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html#t53">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html#t53"><data value='SimpleCostOptimizer'>SimpleCostOptimizer</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simple_demo_py.html">simple_demo.py</a></td>
                <td class="name left"><a href="simple_demo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src/__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860___init___py.html">src/api/__init__.py</a></td>
                <td class="name left"><a href="z_f0e24b7f04f99860___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0e24b7f04f99860_dependencies_py.html">src/api/dependencies.py</a></td>
                <td class="name left"><a href="z_f0e24b7f04f99860_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>179</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="42 179">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a3eecf107d40a09d___init___py.html">src/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_a3eecf107d40a09d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c___init___py.html">src/api/v1/routes/__init__.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_dashboard_py.html">src/api/v1/routes/dashboard.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_health_py.html">src/api/v1/routes/health.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>150</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="33 150">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_metrics_py.html">src/api/v1/routes/metrics.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="31 107">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_optimization_py.html">src/api/v1/routes/optimization.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>122</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="24 122">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_proxy_py.html">src/api/v1/routes/proxy.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_proxy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="25 53">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a35e228392542e9c_service_discovery_py.html">src/api/v1/routes/service_discovery.py</a></td>
                <td class="name left"><a href="z_a35e228392542e9c_service_discovery_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>121</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="22 121">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca___init___py.html">src/core/__init__.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cache_py.html#t61">src/core/cache.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cache_py.html#t61"><data value='CacheManager'>CacheManager</data></a></td>
                <td>130</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="0 130">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cache_py.html">src/core/cache.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="41 44">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t53">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t53"><data value='Settings'>Settings</data></a></td>
                <td>33</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="22 33">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t294">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t294"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t307">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t307"><data value='DevelopmentSettings'>DevelopmentSettings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t335">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t335"><data value='StagingSettings'>StagingSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t359">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t359"><data value='ProductionSettings'>ProductionSettings</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t400">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html#t400"><data value='TestSettings'>TestSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html">src/core/config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>217</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="181 217">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html#t13">src/core/config_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html#t13"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html#t45">src/core/config_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html#t45"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html">src/core/config_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_config_clean_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t28">src/core/cost_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t28"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t42">src/core/cost_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t42"><data value='ModelConfig'>ModelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t51">src/core/cost_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html#t51"><data value='CostOptimizer'>CostOptimizer</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html">src/core/cost_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_cost_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t39">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t39"><data value='OptimizationRequest'>OptimizationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t70">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t70"><data value='ModelPerformance'>ModelPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t91">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t91"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t112">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t112"><data value='OptimizationStep'>OptimizationStep</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t136">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t136"><data value='AdaptiveLearningMetrics'>AdaptiveLearningMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t155">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t155"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t170">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html#t170"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>39</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="4 39">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html">src/core/database.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>116</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="112 116">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html#t41">src/core/database_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html#t41"><data value='OptimizationRequest'>OptimizationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html#t60">src/core/database_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html#t60"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html">src/core/database_clean.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_database_clean_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t16">src/core/env_validator.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t16"><data value='ValidationLevel'>ValidationLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t24">src/core/env_validator.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t24"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t33">src/core/env_validator.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html#t33"><data value='EnvironmentValidator'>EnvironmentValidator</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html">src/core/env_validator.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_env_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t15">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t15"><data value='ModelType'>ModelType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t24">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t24"><data value='TaskComplexity'>TaskComplexity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t32">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t32"><data value='OptimizationLevel'>OptimizationLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t41">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t41"><data value='CacheHitType'>CacheHitType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t50">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t50"><data value='OptimizationRequest'>OptimizationRequest</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t67">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t67"><data value='BatchOptimizationRequest'>BatchOptimizationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t74">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t74"><data value='OptimizationStep'>OptimizationStep</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t84">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t84"><data value='ModelPerformance'>ModelPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t94">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t94"><data value='OptimizationResponse'>OptimizationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t113">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t113"><data value='BatchOptimizationResponse'>BatchOptimizationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t124">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t124"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t138">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t138"><data value='UsageMetrics'>UsageMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t150">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t150"><data value='CacheStats'>CacheStats</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t160">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t160"><data value='ServiceStatus'>ServiceStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t167">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t167"><data value='HealthResponse'>HealthResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t177">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t177"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t187">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t187"><data value='ModelConfig'>ModelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t199">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t199"><data value='OptimizationConfig'>OptimizationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t211">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html#t211"><data value='LearningMetrics'>LearningMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html">src/core/models.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="143 143">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t24">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t24"><data value='Usage'>Usage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t33">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t33"><data value='ChatMessage'>ChatMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t40">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t40"><data value='ChatRequest'>ChatRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t53">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t53"><data value='ChatResponse'>ChatResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t63">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t63"><data value='OpenRouterError'>OpenRouterError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t71">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t71"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t76">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html#t76"><data value='OpenRouterClient'>OpenRouterClient</data></a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html">src/core/openrouter_client.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_openrouter_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t97">src/core/optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t97"><data value='OptimizationLayerState'>OptimizationLayerState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t105">src/core/optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t105"><data value='OptimizationCircuitBreaker'>OptimizationCircuitBreaker</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t144">src/core/optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html#t144"><data value='CostOptimizer'>CostOptimizer</data></a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html">src/core/optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="68 72">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t16">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t16"><data value='SecurityLevel'>SecurityLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t24">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t24"><data value='SecurityHeaders'>SecurityHeaders</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t145">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t145"><data value='RateLimitConfig'>RateLimitConfig</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t177">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t177"><data value='AuthenticationConfig'>AuthenticationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t206">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t206"><data value='InputValidationConfig'>InputValidationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t227">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html#t227"><data value='SecurityConfigManager'>SecurityConfigManager</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html">src/core/security_config.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_security_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>108</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="95 108">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t46">src/core/service_discovery.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t46"><data value='ServiceStatus'>ServiceStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t56">src/core/service_discovery.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t56"><data value='ServiceEndpoint'>ServiceEndpoint</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t104">src/core/service_discovery.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html#t104"><data value='ServiceRegistry'>ServiceRegistry</data></a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html">src/core/service_discovery.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_service_discovery_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="71 92">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t21">src/core/simple_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t21"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t35">src/core/simple_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t35"><data value='ModelConfig'>ModelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t45">src/core/simple_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html#t45"><data value='SimpleCostOptimizer'>SimpleCostOptimizer</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html">src/core/simple_optimizer.py</a></td>
                <td class="name left"><a href="z_0618756b1ff51bca_simple_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src/main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>105</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="61 105">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_clean_py.html">src/main_clean.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_clean_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736___init___py.html">src/middleware/__init__.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t53">src/middleware/auth.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t53"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t61">src/middleware/auth.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t61"><data value='JWTManager'>JWTManager</data></a></td>
                <td>23</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="6 23">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t147">src/middleware/auth.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t147"><data value='APIKeyAuth'>APIKeyAuth</data></a></td>
                <td>14</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="2 14">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t323">src/middleware/auth.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html#t323"><data value='AuthenticationMiddleware'>AuthenticationMiddleware</data></a></td>
                <td>25</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="3 25">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html">src/middleware/auth.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="43 76">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_cors_security_py.html#t34">src/middleware/cors_security.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_cors_security_py.html#t34"><data value='SecureCORSMiddleware'>SecureCORSMiddleware</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_cors_security_py.html">src/middleware/cors_security.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_cors_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="25 26">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_input_validation_py.html#t32">src/middleware/input_validation.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_input_validation_py.html#t32"><data value='InputValidationMiddleware'>InputValidationMiddleware</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_input_validation_py.html">src/middleware/input_validation.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_input_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html#t38">src/middleware/logging.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html#t38"><data value='LoggingMiddleware'>LoggingMiddleware</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html#t258">src/middleware/logging.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html#t258"><data value='PerformanceLoggingMiddleware'>PerformanceLoggingMiddleware</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html">src/middleware/logging.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="26 33">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html#t18">src/middleware/monitoring.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html#t18"><data value='MonitoringMiddleware'>MonitoringMiddleware</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html#t293">src/middleware/monitoring.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html#t293"><data value='RequestContextMiddleware'>RequestContextMiddleware</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html">src/middleware/monitoring.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_rate_limiting_py.html#t21">src/middleware/rate_limiting.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_rate_limiting_py.html#t21"><data value='RateLimitingMiddleware'>RateLimitingMiddleware</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_rate_limiting_py.html">src/middleware/rate_limiting.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_rate_limiting_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="26 27">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_security_py.html#t43">src/middleware/security.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_security_py.html#t43"><data value='SecurityMiddleware'>SecurityMiddleware</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_security_py.html#t463">src/middleware/security.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_security_py.html#t463"><data value='InputSanitizationMiddleware'>InputSanitizationMiddleware</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_652c2a627971f736_security_py.html">src/middleware/security.py</a></td>
                <td class="name left"><a href="z_652c2a627971f736_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b___init___py.html">src/monitoring/__init__.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t40">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t40"><data value='AlertSeverity'>AlertSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t49">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t49"><data value='AlertStatus'>AlertStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t58">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t58"><data value='AlertRule'>AlertRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t85">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t85"><data value='Alert'>Alert</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t118">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t118"><data value='NotificationChannel'>NotificationChannel</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t131">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t131"><data value='SlackNotificationChannel'>SlackNotificationChannel</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t215">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t215"><data value='EmailNotificationChannel'>EmailNotificationChannel</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t229">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html#t229"><data value='AlertManager'>AlertManager</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html">src/monitoring/alerting.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_alerting_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="91 100">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t22">src/monitoring/health.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t22"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t31">src/monitoring/health.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t31"><data value='ComponentHealth'>ComponentHealth</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t52">src/monitoring/health.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html#t52"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html">src/monitoring/health.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>85</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="39 85">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t19">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t19"><data value='CorrelationIdFilter'>CorrelationIdFilter</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t38">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t38"><data value='SecurityAuditFilter'>SecurityAuditFilter</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t55">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t55"><data value='PerformanceFilter'>PerformanceFilter</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t71">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t71"><data value='CustomJsonFormatter'>CustomJsonFormatter</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t113">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html#t113"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html">src/monitoring/logging_config.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="31 44">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t27">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t27"><data value='MetricType'>MetricType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t36">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t36"><data value='MetricDefinition'>MetricDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t46">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t46"><data value='OptimizationMetrics'>OptimizationMetrics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t132">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t132"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t223">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t223"><data value='BusinessMetrics'>BusinessMetrics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t284">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t284"><data value='MetricsCollector'>MetricsCollector</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t589">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html#t589"><data value='MetricsAggregator'>MetricsAggregator</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html">src/monitoring/metrics.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="51 59">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t78">src/monitoring/performance_profiler.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t78"><data value='PerformanceMetric'>PerformanceMetric</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t88">src/monitoring/performance_profiler.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t88"><data value='FunctionProfile'>FunctionProfile</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t117">src/monitoring/performance_profiler.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html#t117"><data value='PerformanceProfiler'>PerformanceProfiler</data></a></td>
                <td>130</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="0 130">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html">src/monitoring/performance_profiler.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_performance_profiler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_setup_py.html#t22">src/monitoring/setup.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_setup_py.html#t22"><data value='MonitoringManager'>MonitoringManager</data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_setup_py.html">src/monitoring/setup.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_setup_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="32 62">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_tracing_py.html#t25">src/monitoring/tracing.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_tracing_py.html#t25"><data value='TracingManager'>TracingManager</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7530dad8cd1eb93b_tracing_py.html">src/monitoring/tracing.py</a></td>
                <td class="name left"><a href="z_7530dad8cd1eb93b_tracing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="29 81">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831___init___py.html">src/services/__init__.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_adaptive_learner_py.html#t28">src/services/adaptive_learner.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_adaptive_learner_py.html#t28"><data value='AdaptiveLearner'>AdaptiveLearner</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_adaptive_learner_py.html">src/services/adaptive_learner.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_adaptive_learner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t60">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t60"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t69">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t69"><data value='TaskStatus'>TaskStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t80">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t80"><data value='AsyncTask'>AsyncTask</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t108">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t108"><data value='BackpressureController'>BackpressureController</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t166">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t166"><data value='AsyncTaskQueue'>AsyncTaskQueue</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t302">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html#t302"><data value='AsyncProcessor'>AsyncProcessor</data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html">src/services/async_processor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_async_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="76 76">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t64">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t64"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t77">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t77"><data value='CacheLayer'>CacheLayer</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t106">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t106"><data value='RedisCache'>RedisCache</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t193">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t193"><data value='MemoryCache'>MemoryCache</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t265">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t265"><data value='ChromaDBCache'>ChromaDBCache</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t382">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t382"><data value='QdrantCache'>QdrantCache</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t503">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t503"><data value='WeaviateCache'>WeaviateCache</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t628">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t628"><data value='MilvusCache'>MilvusCache</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t753">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t753"><data value='ElasticsearchCache'>ElasticsearchCache</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t890">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html#t890"><data value='CacheManager'>CacheManager</data></a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html">src/services/cache_manager.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>105</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="105 105">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t45">src/services/claude_optimizer.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t45"><data value='PreprocessingModel'>PreprocessingModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t51">src/services/claude_optimizer.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t51"><data value='OptimizationType'>OptimizationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t59">src/services/claude_optimizer.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t59"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t71">src/services/claude_optimizer.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html#t71"><data value='ClaudeOptimizer'>ClaudeOptimizer</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html">src/services/claude_optimizer.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_claude_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html#t51">src/services/compression_engine.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html#t51"><data value='CompressionRule'>CompressionRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html#t59">src/services/compression_engine.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html#t59"><data value='CompressionEngine'>CompressionEngine</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html">src/services/compression_engine.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_compression_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html#t27">src/services/metrics_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html#t27"><data value='BusinessMetrics'>BusinessMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html#t39">src/services/metrics_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html#t39"><data value='MetricsService'>MetricsService</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html">src/services/metrics_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_metrics_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t45">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t45"><data value='ModelHealth'>ModelHealth</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t52">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t52"><data value='RoutingStrategy'>RoutingStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t60">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t60"><data value='ModelRoutingDecision'>ModelRoutingDecision</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t81">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t81"><data value='CircuitBreaker'>CircuitBreaker</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t119">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html#t119"><data value='ModelRouter'>ModelRouter</data></a></td>
                <td>138</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="0 138">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html">src/services/model_router.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_model_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t71">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t71"><data value='AlertSeverity'>AlertSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t79">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t79"><data value='SLAType'>SLAType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t89">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t89"><data value='Alert'>Alert</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t106">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t106"><data value='SLATarget'>SLATarget</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t116">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t116"><data value='PerformanceProfiler'>PerformanceProfiler</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t225">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t225"><data value='AlertManager'>AlertManager</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t389">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t389"><data value='SLAMonitor'>SLAMonitor</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t558">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html#t558"><data value='MonitoringService'>MonitoringService</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html">src/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_monitoring_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="13 95">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t52">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t52"><data value='TaskType'>TaskType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t64">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t64"><data value='ModelTier'>ModelTier</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t73">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t73"><data value='ModelInfo'>ModelInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t89">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t89"><data value='OpenRouterRequest'>OpenRouterRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t105">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t105"><data value='OpenRouterResponse'>OpenRouterResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t119">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t119"><data value='ModelSelector'>ModelSelector</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t347">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html#t347"><data value='OpenRouterClient'>OpenRouterClient</data></a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html">src/services/openrouter_client.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_openrouter_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t64">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t64"><data value='BenchmarkType'>BenchmarkType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t76">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t76"><data value='LoadLevel'>LoadLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t85">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t85"><data value='BenchmarkConfig'>BenchmarkConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t102">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t102"><data value='BenchmarkResult'>BenchmarkResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t188">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t188"><data value='ResourceMonitor'>ResourceMonitor</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t256">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t256"><data value='LoadGenerator'>LoadGenerator</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t340">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html#t340"><data value='PerformanceBenchmark'>PerformanceBenchmark</data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html">src/services/performance_benchmark.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_performance_benchmark_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t28">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t28"><data value='QualityMetrics'>QualityMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t63">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t63"><data value='QualityTier'>QualityTier</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t71">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t71"><data value='CircuitBreakerState'>CircuitBreakerState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t79">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t79"><data value='CircuitBreaker'>CircuitBreaker</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t115">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t115"><data value='QualityMetrics'>QualityMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t148">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html#t148"><data value='QualityAssessor'>QualityAssessor</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html">src/services/quality_assessor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_quality_assessor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="77 77">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t59">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t59"><data value='SecurityEventType'>SecurityEventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t72">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t72"><data value='ThreatType'>ThreatType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t84">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t84"><data value='ComplianceFramework'>ComplianceFramework</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t94">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t94"><data value='SecurityEvent'>SecurityEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t108">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t108"><data value='EncryptionService'>EncryptionService</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t218">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t218"><data value='ThreatDetectionEngine'>ThreatDetectionEngine</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t300">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t300"><data value='ComplianceEngine'>ComplianceEngine</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t406">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html#t406"><data value='SecurityService'>SecurityService</data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html">src/services/security_service.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_security_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="23 92">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t56">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t56"><data value='SessionType'>SessionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t65">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t65"><data value='CommandStatus'>CommandStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t74">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t74"><data value='TerminalSession'>TerminalSession</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t91">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t91"><data value='CommandExecution'>CommandExecution</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t106">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t106"><data value='GitHubIntegration'>GitHubIntegration</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t197">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t197"><data value='ProcessMonitor'>ProcessMonitor</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t306">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html#t306"><data value='TerminalMonitor'>TerminalMonitor</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html">src/services/terminal_monitor.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_terminal_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>89</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="0 89">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t53">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t53"><data value='VectorDatabaseType'>VectorDatabaseType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t63">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t63"><data value='VectorSearchResult'>VectorSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t73">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t73"><data value='VectorDocument'>VectorDocument</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t82">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t82"><data value='VectorDatabaseClient'>VectorDatabaseClient</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t117">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t117"><data value='ChromaDBClient'>ChromaDBClient</data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t255">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t255"><data value='QdrantClient'>QdrantClient</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t405">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html#t405"><data value='VectorDatabaseManager'>VectorDatabaseManager</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html">src/services/vector_database.py</a></td>
                <td class="name left"><a href="z_67f6f9a13eff1831_vector_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154___init___py.html">src/services/vector_databases/__init__.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t52">src/services/vector_databases/elasticsearch_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t52"><data value='ElasticsearchSearchResult'>ElasticsearchSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t62">src/services/vector_databases/elasticsearch_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t62"><data value='ElasticsearchConfig'>ElasticsearchConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t184">src/services/vector_databases/elasticsearch_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html#t184"><data value='ElasticsearchVectorDB'>ElasticsearchVectorDB</data></a></td>
                <td>227</td>
                <td>227</td>
                <td>0</td>
                <td class="right" data-ratio="0 227">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html">src/services/vector_databases/elasticsearch_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_elasticsearch_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t54">src/services/vector_databases/milvus_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t54"><data value='MilvusSearchResult'>MilvusSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t63">src/services/vector_databases/milvus_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t63"><data value='MilvusConfig'>MilvusConfig</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t125">src/services/vector_databases/milvus_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html#t125"><data value='MilvusVectorDB'>MilvusVectorDB</data></a></td>
                <td>211</td>
                <td>211</td>
                <td>0</td>
                <td class="right" data-ratio="0 211">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html">src/services/vector_databases/milvus_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_milvus_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t57">src/services/vector_databases/qdrant_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t57"><data value='VectorSearchResult'>VectorSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t66">src/services/vector_databases/qdrant_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t66"><data value='QdrantConfig'>QdrantConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t101">src/services/vector_databases/qdrant_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html#t101"><data value='QdrantVectorDB'>QdrantVectorDB</data></a></td>
                <td>172</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html">src/services/vector_databases/qdrant_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_qdrant_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t53">src/services/vector_databases/weaviate_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t53"><data value='WeaviateSearchResult'>WeaviateSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t64">src/services/vector_databases/weaviate_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t64"><data value='WeaviateConfig'>WeaviateConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t113">src/services/vector_databases/weaviate_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html#t113"><data value='WeaviateVectorDB'>WeaviateVectorDB</data></a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td class="right" data-ratio="0 225">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html">src/services/vector_databases/weaviate_client.py</a></td>
                <td class="name left"><a href="z_4719c4df4d320154_weaviate_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_clean_api_py.html">test_clean_api.py</a></td>
                <td class="name left"><a href="test_clean_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_complete_system_py.html">test_complete_system.py</a></td>
                <td class="name left"><a href="test_complete_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_dashboard_py.html">test_dashboard.py</a></td>
                <td class="name left"><a href="test_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_imports_py.html">test_imports.py</a></td>
                <td class="name left"><a href="test_imports_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_main_minimal_py.html">test_main_minimal.py</a></td>
                <td class="name left"><a href="test_main_minimal_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_platform_py.html#t12">test_platform.py</a></td>
                <td class="name left"><a href="test_platform_py.html#t12"><data value='PlatformTester'>PlatformTester</data></a></td>
                <td>169</td>
                <td>169</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_platform_py.html">test_platform.py</a></td>
                <td class="name left"><a href="test_platform_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_production_system_py.html#t12">test_production_system.py</a></td>
                <td class="name left"><a href="test_production_system_py.html#t12"><data value='ProductionSystemTester'>ProductionSystemTester</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_production_system_py.html">test_production_system.py</a></td>
                <td class="name left"><a href="test_production_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests/__init__.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52b758d7847d4488_test_api_documentation_py.html#t16">tests/api/test_api_documentation.py</a></td>
                <td class="name left"><a href="z_52b758d7847d4488_test_api_documentation_py.html#t16"><data value='TestAPIDocumentation'>TestAPIDocumentation</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_52b758d7847d4488_test_api_documentation_py.html">tests/api/test_api_documentation.py</a></td>
                <td class="name left"><a href="z_52b758d7847d4488_test_api_documentation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html#t255">tests/conftest.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html#t255"><data value='TestUtils'>TestUtils</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html">tests/conftest.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_conftest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>113</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="56 113">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_cost_savings_validation_py.html#t18">tests/e2e/test_cost_savings_validation.py</a></td>
                <td class="name left"><a href="z_3d90d8bad468c07f_test_cost_savings_validation_py.html#t18"><data value='TestCostSavingsValidation'>TestCostSavingsValidation</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_cost_savings_validation_py.html">tests/e2e/test_cost_savings_validation.py</a></td>
                <td class="name left"><a href="z_3d90d8bad468c07f_test_cost_savings_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="7 24">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_end_to_end_py.html#t16">tests/e2e/test_end_to_end.py</a></td>
                <td class="name left"><a href="z_3d90d8bad468c07f_test_end_to_end_py.html#t16"><data value='TestEndToEnd'>TestEndToEnd</data></a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d90d8bad468c07f_test_end_to_end_py.html">tests/e2e/test_end_to_end.py</a></td>
                <td class="name left"><a href="z_3d90d8bad468c07f_test_end_to_end_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_database_integration_py.html#t19">tests/integration/test_database_integration.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_database_integration_py.html#t19"><data value='TestDatabaseIntegration'>TestDatabaseIntegration</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_database_integration_py.html">tests/integration/test_database_integration.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_database_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="9 31">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_flow_py.html#t17">tests/integration/test_optimization_flow.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_flow_py.html#t17"><data value='TestOptimizationFlow'>TestOptimizationFlow</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_flow_py.html">tests/integration/test_optimization_flow.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_flow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="7 39">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_pipeline_py.html#t13">tests/integration/test_optimization_pipeline.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_pipeline_py.html#t13"><data value='TestOptimizationPipeline'>TestOptimizationPipeline</data></a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_pipeline_py.html">tests/integration/test_optimization_pipeline.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_optimization_pipeline_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_redis_integration_py.html#t18">tests/integration/test_redis_integration.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_redis_integration_py.html#t18"><data value='TestRedisIntegration'>TestRedisIntegration</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a40f6d277e0496df_test_redis_integration_py.html">tests/integration/test_redis_integration.py</a></td>
                <td class="name left"><a href="z_a40f6d277e0496df_test_redis_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="9 40">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_load_testing_py.html#t18">tests/performance/test_load_testing.py</a></td>
                <td class="name left"><a href="z_14bf83e2a4686d21_test_load_testing_py.html#t18"><data value='TestPerformance'>TestPerformance</data></a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_load_testing_py.html">tests/performance/test_load_testing.py</a></td>
                <td class="name left"><a href="z_14bf83e2a4686d21_test_load_testing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_performance_benchmarks_py.html#t21">tests/performance/test_performance_benchmarks.py</a></td>
                <td class="name left"><a href="z_14bf83e2a4686d21_test_performance_benchmarks_py.html#t21"><data value='TestPerformanceBenchmarks'>TestPerformanceBenchmarks</data></a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_14bf83e2a4686d21_test_performance_benchmarks_py.html">tests/performance/test_performance_benchmarks.py</a></td>
                <td class="name left"><a href="z_14bf83e2a4686d21_test_performance_benchmarks_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="12 35">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_run_comprehensive_tests_py.html#t22">tests/run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_run_comprehensive_tests_py.html#t22"><data value='ComprehensiveTestRunner'>ComprehensiveTestRunner</data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_run_comprehensive_tests_py.html">tests/run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_run_comprehensive_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t31">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t31"><data value='TestResult'>TestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t42">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t42"><data value='TestMetrics'>TestMetrics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t76">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t76"><data value='MockServices'>MockServices</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t117">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t117"><data value='PerformanceTestSuite'>PerformanceTestSuite</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t247">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t247"><data value='SecurityTestSuite'>SecurityTestSuite</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t332">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html#t332"><data value='IntegrationTestSuite'>IntegrationTestSuite</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html">tests/test_framework.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_framework_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="17 53">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t9">tests/test_optimization.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t9"><data value='TestOptimizationEndpoints'>TestOptimizationEndpoints</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t167">tests/test_optimization.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t167"><data value='TestOptimizationLogic'>TestOptimizationLogic</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t237">tests/test_optimization.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t237"><data value='TestPerformanceRequirements'>TestPerformanceRequirements</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t294">tests/test_optimization.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html#t294"><data value='TestErrorHandling'>TestErrorHandling</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html">tests/test_optimization.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t20">tests/test_optimizer.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t20"><data value='TestOptimizationCircuitBreaker'>TestOptimizationCircuitBreaker</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t76">tests/test_optimizer.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t76"><data value='TestCostOptimizer'>TestCostOptimizer</data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t322">tests/test_optimizer.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html#t322"><data value='TestOptimizationPerformance'>TestOptimizationPerformance</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html">tests/test_optimizer.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="8 40">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_adaptive_learner_py.html#t17">tests/unit/test_adaptive_learner.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_adaptive_learner_py.html#t17"><data value='TestAdaptiveLearner'>TestAdaptiveLearner</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_adaptive_learner_py.html">tests/unit/test_adaptive_learner.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_adaptive_learner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="7 43">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_async_processor_py.html#t16">tests/unit/test_async_processor.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_async_processor_py.html#t16"><data value='TestAsyncProcessor'>TestAsyncProcessor</data></a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_async_processor_py.html">tests/unit/test_async_processor.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_async_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="6 44">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t16">tests/unit/test_cache_manager.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t16"><data value='TestCacheEntry'>TestCacheEntry</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t59">tests/unit/test_cache_manager.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t59"><data value='TestMemoryCache'>TestMemoryCache</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t148">tests/unit/test_cache_manager.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html#t148"><data value='TestCacheManager'>TestCacheManager</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html">tests/unit/test_cache_manager.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_compression_engine_py.html#t14">tests/unit/test_compression_engine.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_compression_engine_py.html#t14"><data value='TestCompressionEngine'>TestCompressionEngine</data></a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_compression_engine_py.html">tests/unit/test_compression_engine.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_compression_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cost_optimizer_py.html#t15">tests/unit/test_cost_optimizer.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cost_optimizer_py.html#t15"><data value='TestCostOptimizer'>TestCostOptimizer</data></a></td>
                <td>108</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_cost_optimizer_py.html">tests/unit/test_cost_optimizer.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_cost_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_model_router_py.html#t17">tests/unit/test_model_router.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_model_router_py.html#t17"><data value='TestModelRouter'>TestModelRouter</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_model_router_py.html">tests/unit/test_model_router.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_model_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="6 45">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_monitoring_service_py.html#t18">tests/unit/test_monitoring_service.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_monitoring_service_py.html#t18"><data value='TestMonitoringService'>TestMonitoringService</data></a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_monitoring_service_py.html">tests/unit/test_monitoring_service.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_monitoring_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="6 50">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_openrouter_client_py.html#t16">tests/unit/test_openrouter_client.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_openrouter_client_py.html#t16"><data value='TestOpenRouterClient'>TestOpenRouterClient</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_openrouter_client_py.html">tests/unit/test_openrouter_client.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_openrouter_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_quality_assessor_py.html#t15">tests/unit/test_quality_assessor.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_quality_assessor_py.html#t15"><data value='TestQualityAssessor'>TestQualityAssessor</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_quality_assessor_py.html">tests/unit/test_quality_assessor.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_quality_assessor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_security_service_py.html#t19">tests/unit/test_security_service.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_security_service_py.html#t19"><data value='TestSecurityService'>TestSecurityService</data></a></td>
                <td>116</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="0 116">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_17bd492d087794b9_test_security_service_py.html">tests/unit/test_security_service.py</a></td>
                <td class="name left"><a href="z_17bd492d087794b9_test_security_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="7 53">13%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>16106</td>
                <td>13621</td>
                <td>0</td>
                <td class="right" data-ratio="2485 16106">15%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 12:50 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
