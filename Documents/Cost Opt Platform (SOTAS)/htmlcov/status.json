{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "0f9d7a50b5b07f49cce75639272f7888", "files": {"cost_optimizer_py": {"hash": "b2d5b4e5f7c81dd2d14ebeb10d236b19", "index": {"url": "cost_optimizer_py.html", "file": "cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 301, "n_excluded": 0, "n_missing": 301, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "demo_cost_savings_py": {"hash": "b6dd86c185e3f6bc5b487ade8f9c1c47", "index": {"url": "demo_cost_savings_py.html", "file": "demo_cost_savings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "healthcheck_py": {"hash": "04d1c1b0bb81fc4e8331f95f9a6bf63a", "index": {"url": "healthcheck_py.html", "file": "healthcheck.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "live_api_test_py": {"hash": "84afa727e98a1ff352dff86f479ac2cf", "index": {"url": "live_api_test_py.html", "file": "live_api_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "manual_test_py": {"hash": "cc807b5113e8ce5acae890ff2253853f", "index": {"url": "manual_test_py.html", "file": "manual_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 68, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "production_validation_py": {"hash": "eb4cc8245f219807a5878b4be27cda42", "index": {"url": "production_validation_py.html", "file": "production_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "quality_test_py": {"hash": "020aaea5ab1532faf464dc44fae9b3d7", "index": {"url": "quality_test_py.html", "file": "quality_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "redis_cache_py": {"hash": "0a6e12d082f69766b28acb060d72ac7b", "index": {"url": "redis_cache_py.html", "file": "redis_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_py": {"hash": "5421d195e1bfb1873ecd1aec6120bd52", "index": {"url": "run_py.html", "file": "run.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_tests_py": {"hash": "b65820eb9f621da8678bccaa337ef8c5", "index": {"url": "run_tests_py.html", "file": "run_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "setup_production_py": {"hash": "2c453a44935ae7b9e19b1c3949b1bd13", "index": {"url": "setup_production_py.html", "file": "setup_production.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "simple_demo_py": {"hash": "f911596f45c4edf368bc3126dd4abac3", "index": {"url": "simple_demo_py.html", "file": "simple_demo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6___init___py": {"hash": "469178a6b2d7737abdc16c1b80fac115", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860___init___py": {"hash": "dbbe5fc76a5dea6b13672c6a595af869", "index": {"url": "z_f0e24b7f04f99860___init___py.html", "file": "src/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860_dependencies_py": {"hash": "62cecf75f50849d9e46aa5ce4417ab98", "index": {"url": "z_f0e24b7f04f99860_dependencies_py.html", "file": "src/api/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a3eecf107d40a09d___init___py": {"hash": "327d62ce560770672630a6e7dedb68ab", "index": {"url": "z_a3eecf107d40a09d___init___py.html", "file": "src/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c___init___py": {"hash": "214cdfe71e2b7512a64141d401c572a0", "index": {"url": "z_a35e228392542e9c___init___py.html", "file": "src/api/v1/routes/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_dashboard_py": {"hash": "68075bb8583ff43b7ce2fdb84e515e21", "index": {"url": "z_a35e228392542e9c_dashboard_py.html", "file": "src/api/v1/routes/dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_health_py": {"hash": "f0b8dd262df4d4cf2897159bccd0fc3e", "index": {"url": "z_a35e228392542e9c_health_py.html", "file": "src/api/v1/routes/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_metrics_py": {"hash": "fcccc11dae1789971012bd0acf31022f", "index": {"url": "z_a35e228392542e9c_metrics_py.html", "file": "src/api/v1/routes/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_optimization_py": {"hash": "c464f966f92ecfd9d0429093699c11cb", "index": {"url": "z_a35e228392542e9c_optimization_py.html", "file": "src/api/v1/routes/optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 122, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_proxy_py": {"hash": "73d4edf86973f25eba28882a4132da18", "index": {"url": "z_a35e228392542e9c_proxy_py.html", "file": "src/api/v1/routes/proxy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_service_discovery_py": {"hash": "92717240314d8c50c690a000c30f2ece", "index": {"url": "z_a35e228392542e9c_service_discovery_py.html", "file": "src/api/v1/routes/service_discovery.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca___init___py": {"hash": "0d6b1001a415b388517128484c3517bf", "index": {"url": "z_0618756b1ff51bca___init___py.html", "file": "src/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_cache_py": {"hash": "78cf847ad2272c6582d184243b347ba4", "index": {"url": "z_0618756b1ff51bca_cache_py.html", "file": "src/core/cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_config_py": {"hash": "c2588567e284136f627f0b2bda38842e", "index": {"url": "z_0618756b1ff51bca_config_py.html", "file": "src/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_config_clean_py": {"hash": "85c66e1a4f24fda695646516344c4e42", "index": {"url": "z_0618756b1ff51bca_config_clean_py.html", "file": "src/core/config_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_cost_optimizer_py": {"hash": "f901d4a0e48a0cb54a3f8a023c3b837d", "index": {"url": "z_0618756b1ff51bca_cost_optimizer_py.html", "file": "src/core/cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_database_py": {"hash": "341feb662a214dbf1f2750c2f0f1b404", "index": {"url": "z_0618756b1ff51bca_database_py.html", "file": "src/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_database_clean_py": {"hash": "e82a41da238875d19e96e297d44af44b", "index": {"url": "z_0618756b1ff51bca_database_clean_py.html", "file": "src/core/database_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_env_validator_py": {"hash": "c0e7a475e4ddc0ce404bedee1b379b80", "index": {"url": "z_0618756b1ff51bca_env_validator_py.html", "file": "src/core/env_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_models_py": {"hash": "23f7597df73966e5e1a6793661ab814e", "index": {"url": "z_0618756b1ff51bca_models_py.html", "file": "src/core/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_openrouter_client_py": {"hash": "c38eb9a19aa08e1c67d5f843a84a8ca8", "index": {"url": "z_0618756b1ff51bca_openrouter_client_py.html", "file": "src/core/openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_optimizer_py": {"hash": "8b78f6f4475113b9095fb8c819007d5c", "index": {"url": "z_0618756b1ff51bca_optimizer_py.html", "file": "src/core/optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_security_config_py": {"hash": "e6cacaf58fa8824bd65b298ebe19f0aa", "index": {"url": "z_0618756b1ff51bca_security_config_py.html", "file": "src/core/security_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_service_discovery_py": {"hash": "9795e55b70d41700d53c8b920f38dbcb", "index": {"url": "z_0618756b1ff51bca_service_discovery_py.html", "file": "src/core/service_discovery.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_simple_optimizer_py": {"hash": "308d689d47b072b0ca147cecc4b7dd83", "index": {"url": "z_0618756b1ff51bca_simple_optimizer_py.html", "file": "src/core/simple_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "7b93e6b67f324be8ea0add56f4f96f33", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_clean_py": {"hash": "af2bbcc4128f87051e034204b8157909", "index": {"url": "z_145eef247bfb46b6_main_clean_py.html", "file": "src/main_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736___init___py": {"hash": "6eee159b18a7ba057116a4668b704613", "index": {"url": "z_652c2a627971f736___init___py.html", "file": "src/middleware/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_auth_py": {"hash": "8a5232d12b4a18ea9d9d81b0b48bc366", "index": {"url": "z_652c2a627971f736_auth_py.html", "file": "src/middleware/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_cors_security_py": {"hash": "d385404c71e5b4639c794859ea2d960d", "index": {"url": "z_652c2a627971f736_cors_security_py.html", "file": "src/middleware/cors_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_input_validation_py": {"hash": "54ab9a7cd60f85d3bfe1341e26f41ee0", "index": {"url": "z_652c2a627971f736_input_validation_py.html", "file": "src/middleware/input_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 110, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_logging_py": {"hash": "a1bf2eca2971fb77a891e4e6e29c99f4", "index": {"url": "z_652c2a627971f736_logging_py.html", "file": "src/middleware/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_monitoring_py": {"hash": "91833a73f6cb9dde6b97712ebd51bfec", "index": {"url": "z_652c2a627971f736_monitoring_py.html", "file": "src/middleware/monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_rate_limiting_py": {"hash": "59ac6c40d19c0a15cf165ddd96656c78", "index": {"url": "z_652c2a627971f736_rate_limiting_py.html", "file": "src/middleware/rate_limiting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_security_py": {"hash": "dbac6dedf7bc47503aa1c5018f14383a", "index": {"url": "z_652c2a627971f736_security_py.html", "file": "src/middleware/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b___init___py": {"hash": "1f2707660aede37c611efc326412ba34", "index": {"url": "z_7530dad8cd1eb93b___init___py.html", "file": "src/monitoring/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_alerting_py": {"hash": "9d597ea758436c7fba501c38fb17913f", "index": {"url": "z_7530dad8cd1eb93b_alerting_py.html", "file": "src/monitoring/alerting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_health_py": {"hash": "443e5149990f1e51ad01919b8bae3713", "index": {"url": "z_7530dad8cd1eb93b_health_py.html", "file": "src/monitoring/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 203, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_logging_config_py": {"hash": "93497fd957d53aa7d739a53469f9cd9e", "index": {"url": "z_7530dad8cd1eb93b_logging_config_py.html", "file": "src/monitoring/logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_metrics_py": {"hash": "7664574e45fe0030d4e5b73bf01840ee", "index": {"url": "z_7530dad8cd1eb93b_metrics_py.html", "file": "src/monitoring/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_performance_profiler_py": {"hash": "c6045f13af67f50d192c32b71e232096", "index": {"url": "z_7530dad8cd1eb93b_performance_profiler_py.html", "file": "src/monitoring/performance_profiler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_setup_py": {"hash": "22b73c1f65ace9e83b60ba859a2afd07", "index": {"url": "z_7530dad8cd1eb93b_setup_py.html", "file": "src/monitoring/setup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_tracing_py": {"hash": "1e20043d23c2c1036550e842a5ed2472", "index": {"url": "z_7530dad8cd1eb93b_tracing_py.html", "file": "src/monitoring/tracing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831___init___py": {"hash": "e9c650501a82f0b05ad04a7ee820c1ec", "index": {"url": "z_67f6f9a13eff1831___init___py.html", "file": "src/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_adaptive_learner_py": {"hash": "91a4c1879e6d26998bc37815be2559a3", "index": {"url": "z_67f6f9a13eff1831_adaptive_learner_py.html", "file": "src/services/adaptive_learner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_async_processor_py": {"hash": "98aaee9e98770ecc1056d037abd2cd15", "index": {"url": "z_67f6f9a13eff1831_async_processor_py.html", "file": "src/services/async_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 281, "n_excluded": 0, "n_missing": 205, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_cache_manager_py": {"hash": "8f142efa112d4184b5f337a7dbdc11d4", "index": {"url": "z_67f6f9a13eff1831_cache_manager_py.html", "file": "src/services/cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 556, "n_excluded": 0, "n_missing": 451, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_claude_optimizer_py": {"hash": "49ec971d82898aa17734517d594e98ba", "index": {"url": "z_67f6f9a13eff1831_claude_optimizer_py.html", "file": "src/services/claude_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_compression_engine_py": {"hash": "26ae221ab6d215df427c04ea12668a92", "index": {"url": "z_67f6f9a13eff1831_compression_engine_py.html", "file": "src/services/compression_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_metrics_service_py": {"hash": "4278814bf26b0a6417273a03f806ebc3", "index": {"url": "z_67f6f9a13eff1831_metrics_service_py.html", "file": "src/services/metrics_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_model_router_py": {"hash": "4bf13e5066369b2ff72b586b749bd147", "index": {"url": "z_67f6f9a13eff1831_model_router_py.html", "file": "src/services/model_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_monitoring_service_py": {"hash": "f1931248973d132c87958fca42965db5", "index": {"url": "z_67f6f9a13eff1831_monitoring_service_py.html", "file": "src/services/monitoring_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 340, "n_excluded": 0, "n_missing": 327, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_openrouter_client_py": {"hash": "a11101b635c959258d78d6b153b0dbdb", "index": {"url": "z_67f6f9a13eff1831_openrouter_client_py.html", "file": "src/services/openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_performance_benchmark_py": {"hash": "5b382c6b3c0a2a22b9ea8f0e39c19e1e", "index": {"url": "z_67f6f9a13eff1831_performance_benchmark_py.html", "file": "src/services/performance_benchmark.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 299, "n_excluded": 0, "n_missing": 299, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_quality_assessor_py": {"hash": "6760de39b5ac6f042f10503c4aad7539", "index": {"url": "z_67f6f9a13eff1831_quality_assessor_py.html", "file": "src/services/quality_assessor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 254, "n_excluded": 0, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_security_service_py": {"hash": "2513b313bd12e73fb7dc18ec048edc5c", "index": {"url": "z_67f6f9a13eff1831_security_service_py.html", "file": "src/services/security_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 306, "n_excluded": 0, "n_missing": 283, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_terminal_monitor_py": {"hash": "dc40da2aa4a8d65e76dd848f6699e7c5", "index": {"url": "z_67f6f9a13eff1831_terminal_monitor_py.html", "file": "src/services/terminal_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 293, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_vector_database_py": {"hash": "7126b7420322586c93646c0bb4964cbd", "index": {"url": "z_67f6f9a13eff1831_vector_database_py.html", "file": "src/services/vector_database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 306, "n_excluded": 0, "n_missing": 306, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154___init___py": {"hash": "a9e0a9394937a92c9aa79fdaef71d0b3", "index": {"url": "z_4719c4df4d320154___init___py.html", "file": "src/services/vector_databases/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_elasticsearch_client_py": {"hash": "8f37a12cc96d01f14ff17d4579d809ad", "index": {"url": "z_4719c4df4d320154_elasticsearch_client_py.html", "file": "src/services/vector_databases/elasticsearch_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 293, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_milvus_client_py": {"hash": "04b540f8736dcb37b64c04a82407693c", "index": {"url": "z_4719c4df4d320154_milvus_client_py.html", "file": "src/services/vector_databases/milvus_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 280, "n_excluded": 0, "n_missing": 280, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_qdrant_client_py": {"hash": "fa5b75a57ba7e5dd6a99a74b50538011", "index": {"url": "z_4719c4df4d320154_qdrant_client_py.html", "file": "src/services/vector_databases/qdrant_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 0, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_weaviate_client_py": {"hash": "9c539d9162821c987101a6c039ddea3f", "index": {"url": "z_4719c4df4d320154_weaviate_client_py.html", "file": "src/services/vector_databases/weaviate_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 287, "n_excluded": 0, "n_missing": 287, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_clean_api_py": {"hash": "3db6c71552b9dfe83d34c2c90479e50d", "index": {"url": "test_clean_api_py.html", "file": "test_clean_api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_complete_system_py": {"hash": "672bb00026b71ff1154029d49d5e010b", "index": {"url": "test_complete_system_py.html", "file": "test_complete_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_dashboard_py": {"hash": "3d35f8f91d6bc78dcede2b608164bd8c", "index": {"url": "test_dashboard_py.html", "file": "test_dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_imports_py": {"hash": "ebfc142a3e8a41c0afd215311c78533f", "index": {"url": "test_imports_py.html", "file": "test_imports.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_main_minimal_py": {"hash": "7e75e79752525205ccc9d7dfae5b9b20", "index": {"url": "test_main_minimal_py.html", "file": "test_main_minimal.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_platform_py": {"hash": "bd2521b39d1e2afafb35735c2695feaf", "index": {"url": "test_platform_py.html", "file": "test_platform.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_production_system_py": {"hash": "48a942d8e3c36be4e3f1943996491fc5", "index": {"url": "test_production_system_py.html", "file": "test_production_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531___init___py": {"hash": "a034f7de44f50711690aa28b169228f6", "index": {"url": "z_a44f0ac069e85531___init___py.html", "file": "tests/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52b758d7847d4488_test_api_documentation_py": {"hash": "cf03cbfa44924c3b9688357fb13712b3", "index": {"url": "z_52b758d7847d4488_test_api_documentation_py.html", "file": "tests/api/test_api_documentation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_conftest_py": {"hash": "0d2dd91b50e1f70a9ddf2171e0183a4d", "index": {"url": "z_a44f0ac069e85531_conftest_py.html", "file": "tests/conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d90d8bad468c07f_test_cost_savings_validation_py": {"hash": "decb74d0f781b985ec0d7dc3f4973395", "index": {"url": "z_3d90d8bad468c07f_test_cost_savings_validation_py.html", "file": "tests/e2e/test_cost_savings_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d90d8bad468c07f_test_end_to_end_py": {"hash": "6103ecfa303da324bb085362f6c6e9e4", "index": {"url": "z_3d90d8bad468c07f_test_end_to_end_py.html", "file": "tests/e2e/test_end_to_end.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_database_integration_py": {"hash": "a76c8c72e87899c3d0cc3183c9d49e27", "index": {"url": "z_a40f6d277e0496df_test_database_integration_py.html", "file": "tests/integration/test_database_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_optimization_flow_py": {"hash": "b88871021c9b5bc46ac6187a8a45c370", "index": {"url": "z_a40f6d277e0496df_test_optimization_flow_py.html", "file": "tests/integration/test_optimization_flow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 135, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_optimization_pipeline_py": {"hash": "912eae31add4d41bd3f26de1eed37a96", "index": {"url": "z_a40f6d277e0496df_test_optimization_pipeline_py.html", "file": "tests/integration/test_optimization_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_redis_integration_py": {"hash": "c2c8b2b0059a28e13d3687343e9f822d", "index": {"url": "z_a40f6d277e0496df_test_redis_integration_py.html", "file": "tests/integration/test_redis_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_14bf83e2a4686d21_test_load_testing_py": {"hash": "cffefc0c6c4dd6c3f0f4d454e87846d4", "index": {"url": "z_14bf83e2a4686d21_test_load_testing_py.html", "file": "tests/performance/test_load_testing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_14bf83e2a4686d21_test_performance_benchmarks_py": {"hash": "6df996ed3bf37d28f2118b0706ab2f17", "index": {"url": "z_14bf83e2a4686d21_test_performance_benchmarks_py.html", "file": "tests/performance/test_performance_benchmarks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 186, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_run_comprehensive_tests_py": {"hash": "520b235f42a7b42b649817c1e7face65", "index": {"url": "z_a44f0ac069e85531_run_comprehensive_tests_py.html", "file": "tests/run_comprehensive_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_framework_py": {"hash": "f2d90ec80626e059cdb0fded158abbda", "index": {"url": "z_a44f0ac069e85531_test_framework_py.html", "file": "tests/test_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_optimization_py": {"hash": "b119dd2914df4df3dab75f56f35b57ae", "index": {"url": "z_a44f0ac069e85531_test_optimization_py.html", "file": "tests/test_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_optimizer_py": {"hash": "0fff98dd7f788d08830678140f93bdf8", "index": {"url": "z_a44f0ac069e85531_test_optimizer_py.html", "file": "tests/test_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_adaptive_learner_py": {"hash": "2fa3ce3af9ac6b5c2d37c6ad4135a083", "index": {"url": "z_17bd492d087794b9_test_adaptive_learner_py.html", "file": "tests/unit/test_adaptive_learner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_async_processor_py": {"hash": "7941945c0dda36b96fff86db245c9945", "index": {"url": "z_17bd492d087794b9_test_async_processor_py.html", "file": "tests/unit/test_async_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_cache_manager_py": {"hash": "2a977bb6bc3832e31dc00d58d902a7c7", "index": {"url": "z_17bd492d087794b9_test_cache_manager_py.html", "file": "tests/unit/test_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_compression_engine_py": {"hash": "53b9a04b4bc30f80d79840938c3e6c10", "index": {"url": "z_17bd492d087794b9_test_compression_engine_py.html", "file": "tests/unit/test_compression_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_cost_optimizer_py": {"hash": "67e2e4e708f64dc98cdf72ba9f8d4e0d", "index": {"url": "z_17bd492d087794b9_test_cost_optimizer_py.html", "file": "tests/unit/test_cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_model_router_py": {"hash": "2149be847bc178a32f3555f09bbd50bf", "index": {"url": "z_17bd492d087794b9_test_model_router_py.html", "file": "tests/unit/test_model_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_monitoring_service_py": {"hash": "aa489e63122cd69c541285e3e52dc1b8", "index": {"url": "z_17bd492d087794b9_test_monitoring_service_py.html", "file": "tests/unit/test_monitoring_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_openrouter_client_py": {"hash": "83f672e29b1d0dc6cc373ea1ccdbdc37", "index": {"url": "z_17bd492d087794b9_test_openrouter_client_py.html", "file": "tests/unit/test_openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_quality_assessor_py": {"hash": "0feb447b45fd8a0d62b3185059074175", "index": {"url": "z_17bd492d087794b9_test_quality_assessor_py.html", "file": "tests/unit/test_quality_assessor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_security_service_py": {"hash": "d50c35cb6e0ff53bfafdc55c9a1398bf", "index": {"url": "z_17bd492d087794b9_test_security_service_py.html", "file": "tests/unit/test_security_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}