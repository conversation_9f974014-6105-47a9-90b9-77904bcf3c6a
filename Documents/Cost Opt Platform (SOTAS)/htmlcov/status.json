{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "0f9d7a50b5b07f49cce75639272f7888", "files": {"cost_optimizer_py": {"hash": "b2d5b4e5f7c81dd2d14ebeb10d236b19", "index": {"url": "cost_optimizer_py.html", "file": "cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 301, "n_excluded": 0, "n_missing": 301, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "demo_cost_savings_py": {"hash": "b6dd86c185e3f6bc5b487ade8f9c1c47", "index": {"url": "demo_cost_savings_py.html", "file": "demo_cost_savings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "healthcheck_py": {"hash": "04d1c1b0bb81fc4e8331f95f9a6bf63a", "index": {"url": "healthcheck_py.html", "file": "healthcheck.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "live_api_test_py": {"hash": "84afa727e98a1ff352dff86f479ac2cf", "index": {"url": "live_api_test_py.html", "file": "live_api_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "manual_test_py": {"hash": "cc807b5113e8ce5acae890ff2253853f", "index": {"url": "manual_test_py.html", "file": "manual_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 68, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "production_validation_py": {"hash": "eb4cc8245f219807a5878b4be27cda42", "index": {"url": "production_validation_py.html", "file": "production_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "quality_test_py": {"hash": "020aaea5ab1532faf464dc44fae9b3d7", "index": {"url": "quality_test_py.html", "file": "quality_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "redis_cache_py": {"hash": "0a6e12d082f69766b28acb060d72ac7b", "index": {"url": "redis_cache_py.html", "file": "redis_cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_py": {"hash": "5421d195e1bfb1873ecd1aec6120bd52", "index": {"url": "run_py.html", "file": "run.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_tests_py": {"hash": "b65820eb9f621da8678bccaa337ef8c5", "index": {"url": "run_tests_py.html", "file": "run_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "setup_production_py": {"hash": "2c453a44935ae7b9e19b1c3949b1bd13", "index": {"url": "setup_production_py.html", "file": "setup_production.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "simple_demo_py": {"hash": "f911596f45c4edf368bc3126dd4abac3", "index": {"url": "simple_demo_py.html", "file": "simple_demo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6___init___py": {"hash": "469178a6b2d7737abdc16c1b80fac115", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860___init___py": {"hash": "dbbe5fc76a5dea6b13672c6a595af869", "index": {"url": "z_f0e24b7f04f99860___init___py.html", "file": "src/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0e24b7f04f99860_dependencies_py": {"hash": "2eea7f4d34f29c117efd2fb6260ea99a", "index": {"url": "z_f0e24b7f04f99860_dependencies_py.html", "file": "src/api/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a3eecf107d40a09d___init___py": {"hash": "327d62ce560770672630a6e7dedb68ab", "index": {"url": "z_a3eecf107d40a09d___init___py.html", "file": "src/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c___init___py": {"hash": "214cdfe71e2b7512a64141d401c572a0", "index": {"url": "z_a35e228392542e9c___init___py.html", "file": "src/api/v1/routes/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_dashboard_py": {"hash": "68075bb8583ff43b7ce2fdb84e515e21", "index": {"url": "z_a35e228392542e9c_dashboard_py.html", "file": "src/api/v1/routes/dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_health_py": {"hash": "f0b8dd262df4d4cf2897159bccd0fc3e", "index": {"url": "z_a35e228392542e9c_health_py.html", "file": "src/api/v1/routes/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_metrics_py": {"hash": "fcccc11dae1789971012bd0acf31022f", "index": {"url": "z_a35e228392542e9c_metrics_py.html", "file": "src/api/v1/routes/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_optimization_py": {"hash": "c464f966f92ecfd9d0429093699c11cb", "index": {"url": "z_a35e228392542e9c_optimization_py.html", "file": "src/api/v1/routes/optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 122, "n_excluded": 0, "n_missing": 98, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_proxy_py": {"hash": "73d4edf86973f25eba28882a4132da18", "index": {"url": "z_a35e228392542e9c_proxy_py.html", "file": "src/api/v1/routes/proxy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a35e228392542e9c_service_discovery_py": {"hash": "92717240314d8c50c690a000c30f2ece", "index": {"url": "z_a35e228392542e9c_service_discovery_py.html", "file": "src/api/v1/routes/service_discovery.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca___init___py": {"hash": "0d6b1001a415b388517128484c3517bf", "index": {"url": "z_0618756b1ff51bca___init___py.html", "file": "src/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_cache_py": {"hash": "78cf847ad2272c6582d184243b347ba4", "index": {"url": "z_0618756b1ff51bca_cache_py.html", "file": "src/core/cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 174, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_config_py": {"hash": "c2588567e284136f627f0b2bda38842e", "index": {"url": "z_0618756b1ff51bca_config_py.html", "file": "src/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_config_clean_py": {"hash": "85c66e1a4f24fda695646516344c4e42", "index": {"url": "z_0618756b1ff51bca_config_clean_py.html", "file": "src/core/config_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_cost_optimizer_py": {"hash": "f901d4a0e48a0cb54a3f8a023c3b837d", "index": {"url": "z_0618756b1ff51bca_cost_optimizer_py.html", "file": "src/core/cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_database_py": {"hash": "d3f83d69a118a4bae5d9beaaece945f0", "index": {"url": "z_0618756b1ff51bca_database_py.html", "file": "src/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_database_clean_py": {"hash": "e82a41da238875d19e96e297d44af44b", "index": {"url": "z_0618756b1ff51bca_database_clean_py.html", "file": "src/core/database_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_env_validator_py": {"hash": "c0e7a475e4ddc0ce404bedee1b379b80", "index": {"url": "z_0618756b1ff51bca_env_validator_py.html", "file": "src/core/env_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_models_py": {"hash": "45a6b6042fd0a38a21b057fe024637d9", "index": {"url": "z_0618756b1ff51bca_models_py.html", "file": "src/core/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_openrouter_client_py": {"hash": "c38eb9a19aa08e1c67d5f843a84a8ca8", "index": {"url": "z_0618756b1ff51bca_openrouter_client_py.html", "file": "src/core/openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_optimizer_py": {"hash": "e380c534cabb108f4df3327dfdd1e1b3", "index": {"url": "z_0618756b1ff51bca_optimizer_py.html", "file": "src/core/optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_security_config_py": {"hash": "4ade93b17a0e6277667e8772d7c8a940", "index": {"url": "z_0618756b1ff51bca_security_config_py.html", "file": "src/core/security_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_service_discovery_py": {"hash": "9795e55b70d41700d53c8b920f38dbcb", "index": {"url": "z_0618756b1ff51bca_service_discovery_py.html", "file": "src/core/service_discovery.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0618756b1ff51bca_simple_optimizer_py": {"hash": "308d689d47b072b0ca147cecc4b7dd83", "index": {"url": "z_0618756b1ff51bca_simple_optimizer_py.html", "file": "src/core/simple_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "15cd17690ffe376767c927e393b2b5c1", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_clean_py": {"hash": "af2bbcc4128f87051e034204b8157909", "index": {"url": "z_145eef247bfb46b6_main_clean_py.html", "file": "src/main_clean.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736___init___py": {"hash": "6eee159b18a7ba057116a4668b704613", "index": {"url": "z_652c2a627971f736___init___py.html", "file": "src/middleware/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_auth_py": {"hash": "4f1ab954cabc24520dab0a67b76dccee", "index": {"url": "z_652c2a627971f736_auth_py.html", "file": "src/middleware/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_cors_security_py": {"hash": "d385404c71e5b4639c794859ea2d960d", "index": {"url": "z_652c2a627971f736_cors_security_py.html", "file": "src/middleware/cors_security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_input_validation_py": {"hash": "3fb1155bda78c509dec55794ca15ebbd", "index": {"url": "z_652c2a627971f736_input_validation_py.html", "file": "src/middleware/input_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 142, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_logging_py": {"hash": "ea884e34b853b5490ddfe8fa9b208fa1", "index": {"url": "z_652c2a627971f736_logging_py.html", "file": "src/middleware/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_monitoring_py": {"hash": "91833a73f6cb9dde6b97712ebd51bfec", "index": {"url": "z_652c2a627971f736_monitoring_py.html", "file": "src/middleware/monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_rate_limiting_py": {"hash": "061ea70307845672323ca7974e10ed1e", "index": {"url": "z_652c2a627971f736_rate_limiting_py.html", "file": "src/middleware/rate_limiting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_652c2a627971f736_security_py": {"hash": "75dedbd1e282abee0ac19602bd470f35", "index": {"url": "z_652c2a627971f736_security_py.html", "file": "src/middleware/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b___init___py": {"hash": "1f2707660aede37c611efc326412ba34", "index": {"url": "z_7530dad8cd1eb93b___init___py.html", "file": "src/monitoring/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_alerting_py": {"hash": "9d597ea758436c7fba501c38fb17913f", "index": {"url": "z_7530dad8cd1eb93b_alerting_py.html", "file": "src/monitoring/alerting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 256, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_health_py": {"hash": "443e5149990f1e51ad01919b8bae3713", "index": {"url": "z_7530dad8cd1eb93b_health_py.html", "file": "src/monitoring/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 203, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_logging_config_py": {"hash": "93497fd957d53aa7d739a53469f9cd9e", "index": {"url": "z_7530dad8cd1eb93b_logging_config_py.html", "file": "src/monitoring/logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_metrics_py": {"hash": "7664574e45fe0030d4e5b73bf01840ee", "index": {"url": "z_7530dad8cd1eb93b_metrics_py.html", "file": "src/monitoring/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_performance_profiler_py": {"hash": "c6045f13af67f50d192c32b71e232096", "index": {"url": "z_7530dad8cd1eb93b_performance_profiler_py.html", "file": "src/monitoring/performance_profiler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_setup_py": {"hash": "22b73c1f65ace9e83b60ba859a2afd07", "index": {"url": "z_7530dad8cd1eb93b_setup_py.html", "file": "src/monitoring/setup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_tracing_py": {"hash": "1e20043d23c2c1036550e842a5ed2472", "index": {"url": "z_7530dad8cd1eb93b_tracing_py.html", "file": "src/monitoring/tracing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831___init___py": {"hash": "e9c650501a82f0b05ad04a7ee820c1ec", "index": {"url": "z_67f6f9a13eff1831___init___py.html", "file": "src/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_adaptive_learner_py": {"hash": "da72744164298c68ed6d7f8f018d8059", "index": {"url": "z_67f6f9a13eff1831_adaptive_learner_py.html", "file": "src/services/adaptive_learner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 204, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_async_processor_py": {"hash": "b6ddacab5aefed655bde86de86a799c8", "index": {"url": "z_67f6f9a13eff1831_async_processor_py.html", "file": "src/services/async_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 307, "n_excluded": 0, "n_missing": 205, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_cache_manager_py": {"hash": "0f6bbad1faa06d6682b7b6567b7e875d", "index": {"url": "z_67f6f9a13eff1831_cache_manager_py.html", "file": "src/services/cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 556, "n_excluded": 0, "n_missing": 401, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_claude_optimizer_py": {"hash": "5fd029c7a585fc4274157bb86d662881", "index": {"url": "z_67f6f9a13eff1831_claude_optimizer_py.html", "file": "src/services/claude_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_compression_engine_py": {"hash": "623ecb7f5f64e8c79ca565ad508e86a3", "index": {"url": "z_67f6f9a13eff1831_compression_engine_py.html", "file": "src/services/compression_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_metrics_service_py": {"hash": "4278814bf26b0a6417273a03f806ebc3", "index": {"url": "z_67f6f9a13eff1831_metrics_service_py.html", "file": "src/services/metrics_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_model_router_py": {"hash": "2223dc25a9f4b8a93051d5396700f81c", "index": {"url": "z_67f6f9a13eff1831_model_router_py.html", "file": "src/services/model_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_monitoring_service_py": {"hash": "b9a07b9919f7be837658b9ca72ad0788", "index": {"url": "z_67f6f9a13eff1831_monitoring_service_py.html", "file": "src/services/monitoring_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 366, "n_excluded": 0, "n_missing": 245, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_openrouter_client_py": {"hash": "97222091f40c7fcfb037c999ece07ad6", "index": {"url": "z_67f6f9a13eff1831_openrouter_client_py.html", "file": "src/services/openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_performance_benchmark_py": {"hash": "5b382c6b3c0a2a22b9ea8f0e39c19e1e", "index": {"url": "z_67f6f9a13eff1831_performance_benchmark_py.html", "file": "src/services/performance_benchmark.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 299, "n_excluded": 0, "n_missing": 299, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_quality_assessor_py": {"hash": "cc0417712449fe88984935d381732540", "index": {"url": "z_67f6f9a13eff1831_quality_assessor_py.html", "file": "src/services/quality_assessor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 254, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_security_service_py": {"hash": "9dbcad025772a49ec0879e2cdc9796b2", "index": {"url": "z_67f6f9a13eff1831_security_service_py.html", "file": "src/services/security_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 321, "n_excluded": 0, "n_missing": 214, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_terminal_monitor_py": {"hash": "dc40da2aa4a8d65e76dd848f6699e7c5", "index": {"url": "z_67f6f9a13eff1831_terminal_monitor_py.html", "file": "src/services/terminal_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 293, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67f6f9a13eff1831_vector_database_py": {"hash": "7126b7420322586c93646c0bb4964cbd", "index": {"url": "z_67f6f9a13eff1831_vector_database_py.html", "file": "src/services/vector_database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 306, "n_excluded": 0, "n_missing": 306, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154___init___py": {"hash": "a9e0a9394937a92c9aa79fdaef71d0b3", "index": {"url": "z_4719c4df4d320154___init___py.html", "file": "src/services/vector_databases/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_elasticsearch_client_py": {"hash": "8f37a12cc96d01f14ff17d4579d809ad", "index": {"url": "z_4719c4df4d320154_elasticsearch_client_py.html", "file": "src/services/vector_databases/elasticsearch_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 293, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_milvus_client_py": {"hash": "04b540f8736dcb37b64c04a82407693c", "index": {"url": "z_4719c4df4d320154_milvus_client_py.html", "file": "src/services/vector_databases/milvus_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 280, "n_excluded": 0, "n_missing": 280, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_qdrant_client_py": {"hash": "fa5b75a57ba7e5dd6a99a74b50538011", "index": {"url": "z_4719c4df4d320154_qdrant_client_py.html", "file": "src/services/vector_databases/qdrant_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 0, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4719c4df4d320154_weaviate_client_py": {"hash": "9c539d9162821c987101a6c039ddea3f", "index": {"url": "z_4719c4df4d320154_weaviate_client_py.html", "file": "src/services/vector_databases/weaviate_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 287, "n_excluded": 0, "n_missing": 287, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_clean_api_py": {"hash": "3db6c71552b9dfe83d34c2c90479e50d", "index": {"url": "test_clean_api_py.html", "file": "test_clean_api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_complete_system_py": {"hash": "672bb00026b71ff1154029d49d5e010b", "index": {"url": "test_complete_system_py.html", "file": "test_complete_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_dashboard_py": {"hash": "3d35f8f91d6bc78dcede2b608164bd8c", "index": {"url": "test_dashboard_py.html", "file": "test_dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_imports_py": {"hash": "ebfc142a3e8a41c0afd215311c78533f", "index": {"url": "test_imports_py.html", "file": "test_imports.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_main_minimal_py": {"hash": "7e75e79752525205ccc9d7dfae5b9b20", "index": {"url": "test_main_minimal_py.html", "file": "test_main_minimal.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_platform_py": {"hash": "bd2521b39d1e2afafb35735c2695feaf", "index": {"url": "test_platform_py.html", "file": "test_platform.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_production_system_py": {"hash": "48a942d8e3c36be4e3f1943996491fc5", "index": {"url": "test_production_system_py.html", "file": "test_production_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 146, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531___init___py": {"hash": "a034f7de44f50711690aa28b169228f6", "index": {"url": "z_a44f0ac069e85531___init___py.html", "file": "tests/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_52b758d7847d4488_test_api_documentation_py": {"hash": "cf03cbfa44924c3b9688357fb13712b3", "index": {"url": "z_52b758d7847d4488_test_api_documentation_py.html", "file": "tests/api/test_api_documentation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_conftest_py": {"hash": "af5ed2e26ae1c24c3139d6a4c875acad", "index": {"url": "z_a44f0ac069e85531_conftest_py.html", "file": "tests/conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d90d8bad468c07f_test_cost_savings_validation_py": {"hash": "868f768711d00c766ce84ff5f686f10a", "index": {"url": "z_3d90d8bad468c07f_test_cost_savings_validation_py.html", "file": "tests/e2e/test_cost_savings_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d90d8bad468c07f_test_end_to_end_py": {"hash": "6103ecfa303da324bb085362f6c6e9e4", "index": {"url": "z_3d90d8bad468c07f_test_end_to_end_py.html", "file": "tests/e2e/test_end_to_end.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_database_integration_py": {"hash": "671c844668cea35a260825dd20bb27f9", "index": {"url": "z_a40f6d277e0496df_test_database_integration_py.html", "file": "tests/integration/test_database_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_optimization_flow_py": {"hash": "ecd13e7d9adbae178ef0cb442009f3e7", "index": {"url": "z_a40f6d277e0496df_test_optimization_flow_py.html", "file": "tests/integration/test_optimization_flow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_optimization_pipeline_py": {"hash": "912eae31add4d41bd3f26de1eed37a96", "index": {"url": "z_a40f6d277e0496df_test_optimization_pipeline_py.html", "file": "tests/integration/test_optimization_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a40f6d277e0496df_test_redis_integration_py": {"hash": "0c6257951583113a7067740d9f56a043", "index": {"url": "z_a40f6d277e0496df_test_redis_integration_py.html", "file": "tests/integration/test_redis_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_14bf83e2a4686d21_test_load_testing_py": {"hash": "cffefc0c6c4dd6c3f0f4d454e87846d4", "index": {"url": "z_14bf83e2a4686d21_test_load_testing_py.html", "file": "tests/performance/test_load_testing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_14bf83e2a4686d21_test_performance_benchmarks_py": {"hash": "f6277e7950fdefff693a365f9174a4b9", "index": {"url": "z_14bf83e2a4686d21_test_performance_benchmarks_py.html", "file": "tests/performance/test_performance_benchmarks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_run_comprehensive_tests_py": {"hash": "520b235f42a7b42b649817c1e7face65", "index": {"url": "z_a44f0ac069e85531_run_comprehensive_tests_py.html", "file": "tests/run_comprehensive_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_framework_py": {"hash": "10d8932714b98cb0fad0c0c04c25fd34", "index": {"url": "z_a44f0ac069e85531_test_framework_py.html", "file": "tests/test_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 123, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_optimization_py": {"hash": "c2622e78a28ee6bdeac706a79f449677", "index": {"url": "z_a44f0ac069e85531_test_optimization_py.html", "file": "tests/test_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 135, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_optimizer_py": {"hash": "510225833fcbdb75b3b0c6e9ca75087d", "index": {"url": "z_a44f0ac069e85531_test_optimizer_py.html", "file": "tests/test_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_adaptive_learner_py": {"hash": "3d0ed51d935dde34d21e387c1006e4a8", "index": {"url": "z_17bd492d087794b9_test_adaptive_learner_py.html", "file": "tests/unit/test_adaptive_learner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_async_processor_py": {"hash": "84a029bec6fa93edb27bfb8a49dc8931", "index": {"url": "z_17bd492d087794b9_test_async_processor_py.html", "file": "tests/unit/test_async_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_cache_manager_py": {"hash": "2a977bb6bc3832e31dc00d58d902a7c7", "index": {"url": "z_17bd492d087794b9_test_cache_manager_py.html", "file": "tests/unit/test_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_compression_engine_py": {"hash": "53b9a04b4bc30f80d79840938c3e6c10", "index": {"url": "z_17bd492d087794b9_test_compression_engine_py.html", "file": "tests/unit/test_compression_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_cost_optimizer_py": {"hash": "67e2e4e708f64dc98cdf72ba9f8d4e0d", "index": {"url": "z_17bd492d087794b9_test_cost_optimizer_py.html", "file": "tests/unit/test_cost_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_model_router_py": {"hash": "47b7021ef3c8df7449da9c17da519b72", "index": {"url": "z_17bd492d087794b9_test_model_router_py.html", "file": "tests/unit/test_model_router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_monitoring_service_py": {"hash": "3c5364717278714427c185d088b6c309", "index": {"url": "z_17bd492d087794b9_test_monitoring_service_py.html", "file": "tests/unit/test_monitoring_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_openrouter_client_py": {"hash": "83f672e29b1d0dc6cc373ea1ccdbdc37", "index": {"url": "z_17bd492d087794b9_test_openrouter_client_py.html", "file": "tests/unit/test_openrouter_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_quality_assessor_py": {"hash": "0feb447b45fd8a0d62b3185059074175", "index": {"url": "z_17bd492d087794b9_test_quality_assessor_py.html", "file": "tests/unit/test_quality_assessor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_17bd492d087794b9_test_security_service_py": {"hash": "ee7c81c12fe19e09a81127a4634b2530", "index": {"url": "z_17bd492d087794b9_test_security_service_py.html", "file": "tests/unit/test_security_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e041423fa8cb161___init___py": {"hash": "fba58d5179b2bcfb85a016849d660d51", "index": {"url": "z_9e041423fa8cb161___init___py.html", "file": "src/clients/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e041423fa8cb161_openrouter_py": {"hash": "da4b5b3d8c738dcdf181a5c7ff883573", "index": {"url": "z_9e041423fa8cb161_openrouter_py.html", "file": "src/clients/openrouter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_analytics_py": {"hash": "4420710c4779eb45094d366360f5207b", "index": {"url": "z_7530dad8cd1eb93b_analytics_py.html", "file": "src/monitoring/analytics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7530dad8cd1eb93b_metrics_registry_py": {"hash": "e718e2c7e6f80a1a8aadda0926bf1bd5", "index": {"url": "z_7530dad8cd1eb93b_metrics_registry_py.html", "file": "src/monitoring/metrics_registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}