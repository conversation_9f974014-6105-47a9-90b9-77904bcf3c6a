"""
Gunicorn Configuration for FAANG+ Production Deployment
Implements TikTok performance optimization with Google reliability standards
"""

import os
import multiprocessing
from typing import Dict, Any

# Server socket
bind = f"0.0.0.0:{os.getenv('PORT', '8000')}"
backlog = int(os.getenv('BACKLOG', '2048'))

# Worker processes
workers = int(os.getenv('WORKERS', multiprocessing.cpu_count() * 2 + 1))
worker_class = os.getenv('WORKER_CLASS', 'uvicorn.workers.UvicornWorker')
worker_connections = int(os.getenv('WORKER_CONNECTIONS', '1000'))
max_requests = int(os.getenv('MAX_REQUESTS', '1000'))
max_requests_jitter = int(os.getenv('MAX_REQUESTS_JITTER', '100'))

# Worker timeout and lifecycle
timeout = int(os.getenv('TIMEOUT', '30'))
graceful_timeout = int(os.getenv('GRACEFUL_TIMEOUT', '30'))
keepalive = int(os.getenv('KEEPALIVE', '2'))

# Performance optimizations
preload_app = True
worker_tmp_dir = '/dev/shm'  # Use shared memory for better performance

# Process naming
proc_name = 'cost-optimizer'

# User and group (running as non-root)
user = os.getenv('USER', 'appuser')
group = os.getenv('GROUP', 'appuser')

# Logging configuration
loglevel = os.getenv('LOG_LEVEL', 'info').lower()
accesslog = '-'  # Log to stdout
errorlog = '-'   # Log to stderr
access_log_format = (
    '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s '
    '"%(f)s" "%(a)s" %(D)s %(p)s'
)

# Security
limit_request_line = int(os.getenv('LIMIT_REQUEST_LINE', '4096'))
limit_request_fields = int(os.getenv('LIMIT_REQUEST_FIELDS', '100'))
limit_request_field_size = int(os.getenv('LIMIT_REQUEST_FIELD_SIZE', '8190'))

# SSL/TLS (if enabled)
keyfile = os.getenv('SSL_KEYFILE')
certfile = os.getenv('SSL_CERTFILE')
ssl_version = 2  # TLS 1.2+
ciphers = 'ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS'

# Environment-specific configurations
environment = os.getenv('ENVIRONMENT', 'production')

if environment == 'development':
    # Development settings
    workers = 1
    reload = True
    loglevel = 'debug'
    timeout = 0  # No timeout for debugging
elif environment == 'testing':
    # Testing settings
    workers = 1
    timeout = 60
    loglevel = 'warning'
elif environment == 'production':
    # Production settings (use defaults above)
    pass

# Monitoring and health checks
def when_ready(server):
    """Called when the server is ready to accept connections"""
    server.log.info("Cost Optimizer server is ready. Listening on %s", bind)
    
    # Initialize monitoring
    try:
        import prometheus_client
        prometheus_client.start_http_server(9090)
        server.log.info("Prometheus metrics server started on port 9090")
    except ImportError:
        server.log.warning("Prometheus client not available, metrics disabled")
    except Exception as e:
        server.log.error("Failed to start metrics server: %s", e)

def worker_int(worker):
    """Called when a worker receives the INT or QUIT signal"""
    worker.log.info("Worker %s interrupted", worker.pid)

def pre_fork(server, worker):
    """Called before a worker is forked"""
    server.log.info("Worker %s spawned", worker.pid)

def post_fork(server, worker):
    """Called after a worker is forked"""
    server.log.info("Worker %s ready", worker.pid)
    
    # Set worker-specific configurations
    try:
        # Set process title
        import setproctitle
        setproctitle.setproctitle(f'cost-optimizer-worker-{worker.pid}')
    except ImportError:
        pass

def worker_abort(worker):
    """Called when a worker is aborted"""
    worker.log.error("Worker %s aborted", worker.pid)

def pre_exec(server):
    """Called before the server starts"""
    server.log.info("Starting Cost Optimizer server...")

def on_exit(server):
    """Called when the server exits"""
    server.log.info("Cost Optimizer server shutting down...")

def on_reload(server):
    """Called when the server reloads"""
    server.log.info("Cost Optimizer server reloading...")

# Custom application configuration
def post_worker_init(worker):
    """Called after worker initialization"""
    worker.log.info("Worker %s initialized", worker.pid)
    
    # Worker-specific initialization
    try:
        # Set up worker-specific monitoring
        import psutil
        process = psutil.Process()
        worker.log.info(
            "Worker %s memory usage: %.2f MB",
            worker.pid,
            process.memory_info().rss / 1024 / 1024
        )
    except ImportError:
        pass

# Error handling
def worker_exit(server, worker):
    """Called when a worker exits"""
    server.log.info("Worker %s exited", worker.pid)

# Custom logging
class GunicornLogger:
    """Custom logger for Gunicorn with structured logging"""
    
    def __init__(self, cfg):
        self.cfg = cfg
        
    def setup(self, cfg):
        """Setup logging configuration"""
        import logging
        import json
        from datetime import datetime
        
        class StructuredFormatter(logging.Formatter):
            def format(self, record):
                log_entry = {
                    'timestamp': datetime.utcnow().isoformat(),
                    'level': record.levelname,
                    'logger': record.name,
                    'message': record.getMessage(),
                    'module': record.module,
                    'function': record.funcName,
                    'line': record.lineno
                }
                
                if hasattr(record, 'request_id'):
                    log_entry['request_id'] = record.request_id
                
                if record.exc_info:
                    log_entry['exception'] = self.formatException(record.exc_info)
                
                return json.dumps(log_entry)
        
        # Configure structured logging for production
        if environment == 'production':
            handler = logging.StreamHandler()
            handler.setFormatter(StructuredFormatter())
            
            # Set up root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, loglevel.upper()))
            root_logger.addHandler(handler)

# Performance tuning based on environment
def tune_performance():
    """Apply performance tuning based on environment"""
    import os
    
    # Set optimal worker count based on CPU and memory
    cpu_count = multiprocessing.cpu_count()
    memory_gb = os.sysconf('SC_PAGE_SIZE') * os.sysconf('SC_PHYS_PAGES') / (1024**3)
    
    # Calculate optimal workers (CPU-bound workload)
    optimal_workers = min(
        cpu_count * 2 + 1,  # Standard formula
        int(memory_gb * 2),  # Memory constraint
        32  # Maximum reasonable workers
    )
    
    # Override if not explicitly set
    if 'WORKERS' not in os.environ:
        global workers
        workers = optimal_workers

# Apply performance tuning
tune_performance()

# Validation
def validate_config():
    """Validate configuration parameters"""
    errors = []
    
    if workers < 1:
        errors.append("Workers must be >= 1")
    
    if timeout < 0:
        errors.append("Timeout must be >= 0")
    
    if worker_connections < 1:
        errors.append("Worker connections must be >= 1")
    
    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}")

# Run validation
validate_config()

# Export configuration summary
config_summary = {
    'bind': bind,
    'workers': workers,
    'worker_class': worker_class,
    'worker_connections': worker_connections,
    'timeout': timeout,
    'environment': environment,
    'preload_app': preload_app,
    'loglevel': loglevel
}

print(f"Gunicorn configuration: {config_summary}")
