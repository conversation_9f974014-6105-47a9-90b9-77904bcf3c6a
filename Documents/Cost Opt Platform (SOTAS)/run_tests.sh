#!/bin/bash

# Test runner script for Cost Optimization Platform
# Runs comprehensive test suite with coverage reporting

set -e

echo "🧪 Running Cost Optimization Platform Test Suite"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if pytest is installed
if ! command -v pytest &> /dev/null; then
    print_error "pytest is not installed. Installing..."
    pip install pytest pytest-asyncio pytest-cov
fi

# Set test environment
export ENVIRONMENT=test
export DATABASE_URL=sqlite:///./test.db
export REDIS_URL=redis://localhost:6379/1
export OPENROUTER_API_KEY=test-key

print_status "Setting up test environment..."

# Clean up any existing test artifacts
rm -f test.db
rm -rf .pytest_cache
rm -rf htmlcov
rm -f .coverage

print_status "Running unit tests..."

# Run unit tests with coverage
pytest tests/ \
    --verbose \
    --cov=src \
    --cov-report=html \
    --cov-report=term-missing \
    --cov-fail-under=80 \
    --tb=short

if [ $? -eq 0 ]; then
    print_success "All tests passed!"
    
    # Display coverage summary
    echo ""
    print_status "Coverage Report:"
    echo "HTML coverage report generated in htmlcov/"
    echo "Open htmlcov/index.html in your browser to view detailed coverage"
    
    # Check if coverage meets requirements
    coverage_percentage=$(coverage report | tail -1 | awk '{print $4}' | sed 's/%//')
    if [ "${coverage_percentage%.*}" -ge 80 ]; then
        print_success "Coverage requirement met: ${coverage_percentage}%"
    else
        print_warning "Coverage below 80%: ${coverage_percentage}%"
    fi
    
else
    print_error "Some tests failed!"
    exit 1
fi

# Run linting if available
if command -v flake8 &> /dev/null; then
    print_status "Running code linting..."
    flake8 src/ --max-line-length=100 --ignore=E203,W503
    
    if [ $? -eq 0 ]; then
        print_success "Code linting passed!"
    else
        print_warning "Code linting found issues"
    fi
fi

# Run type checking if available
if command -v mypy &> /dev/null; then
    print_status "Running type checking..."
    mypy src/ --ignore-missing-imports
    
    if [ $? -eq 0 ]; then
        print_success "Type checking passed!"
    else
        print_warning "Type checking found issues"
    fi
fi

print_success "Test suite completed successfully!"
echo ""
echo "📊 Test Results Summary:"
echo "  • Unit tests: PASSED"
echo "  • Coverage: ${coverage_percentage}%"
echo "  • Code quality: CHECKED"
echo ""
echo "🚀 Ready for deployment!"
