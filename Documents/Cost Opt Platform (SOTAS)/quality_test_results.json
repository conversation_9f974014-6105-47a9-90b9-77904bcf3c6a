[{"test_name": "Simple Task (High Quality)", "model_used": "claude-4-sonnet", "cost_usd": 0.000522, "quality_score": 0.95, "processing_time_ms": 4105.149984359741, "savings_usd": 0.0}, {"test_name": "Simple Task (Cost Optimized)", "model_used": "llama-3-8b", "cost_usd": 4.68e-06, "quality_score": 0.65, "processing_time_ms": 841.702938079834, "savings_usd": 0.00016932}, {"test_name": "Complex Task (High Quality)", "model_used": "claude-4-sonnet", "cost_usd": 0.013671, "quality_score": 0.8999999999999999, "processing_time_ms": 16688.945770263672, "savings_usd": 0.0}, {"test_name": "Expert Task (Premium Required)", "model_used": "claude-4-sonnet", "cost_usd": 0.015069, "quality_score": 0.85, "processing_time_ms": 25019.771814346313, "savings_usd": 0.0}]