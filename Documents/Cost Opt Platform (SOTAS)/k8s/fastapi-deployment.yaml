apiVersion: apps/v1
kind: Deployment
metadata:
  name: fastapi-deployment
  labels:
    app: fastapi
    component: api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fastapi
  template:
    metadata:
      labels:
        app: fastapi
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: fastapi
        image: cost-optimizer:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8001
          name: metrics
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          value: "sqlite+aiosqlite:///./data/cost_optimizer.db"
        - name: REDIS_URL
          value: "redis://redis-service:6379/0"
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: openrouter-api-key
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: secret-key
        - name: ALLOWED_ORIGINS
          value: "http://localhost:30801,http://localhost:30080"
        volumeMounts:
        - name: database-storage
          mountPath: /app/data
        - name: logs-storage
          mountPath: /app/logs
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: database-storage
        persistentVolumeClaim:
          claimName: database-pvc
      - name: logs-storage
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  name: fastapi-service
  labels:
    app: fastapi
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: fastapi
  ports:
  - name: http
    protocol: TCP
    port: 80
    targetPort: 8000
    nodePort: 30800
  - name: metrics
    protocol: TCP
    port: 8001
    targetPort: 8001
    nodePort: 30801
  type: NodePort

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: database-pvc
  labels:
    app: fastapi
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-storage
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: Secret
metadata:
  name: api-secrets
  labels:
    app: fastapi
    component: config
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  openrouter-api-key: eW91ci1vcGVucm91dGVyLWFwaS1rZXk=  # your-openrouter-api-key
  secret-key: cHJvZHVjdGlvbi1zZWNyZXQta2V5LTMyLWNoYXJhY3RlcnMtbG9uZw==  # production-secret-key-32-characters-long

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fastapi-config
  labels:
    app: fastapi
    component: config
data:
  environment: "production"
  log_level: "info"
  workers: "4"
  max_requests: "1000"
  max_requests_jitter: "100"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fastapi-hpa
  labels:
    app: fastapi
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fastapi-deployment
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fastapi-pdb
  labels:
    app: fastapi
    component: availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: fastapi
