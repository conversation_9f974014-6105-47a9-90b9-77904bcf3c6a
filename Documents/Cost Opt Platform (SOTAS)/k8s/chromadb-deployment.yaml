apiVersion: apps/v1
kind: Deployment
metadata:
  name: chromadb-deployment
  labels:
    app: chromadb
    component: vector-database
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: chromadb
  template:
    metadata:
      labels:
        app: chromadb
        component: vector-database
    spec:
      containers:
      - name: chromadb
        image: chromadb/chroma:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: CHROMA_SERVER_HOST
          value: "0.0.0.0"
        - name: CHROMA_SERVER_HTTP_PORT
          value: "8000"
        - name: CHROMA_DB_IMPL
          value: "duckdb+parquet"
        - name: PERSIST_DIRECTORY
          value: "/chroma/chroma"
        volumeMounts:
        - name: chromadb-storage
          mountPath: /chroma/chroma
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/heartbeat
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/heartbeat
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: chromadb-storage
        persistentVolumeClaim:
          claimName: chromadb-pvc
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: chromadb-service
  labels:
    app: chromadb
    component: vector-database
spec:
  selector:
    app: chromadb
  ports:
  - name: http
    protocol: TCP
    port: 8000
    targetPort: 8000
    nodePort: 30802
  type: NodePort

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chromadb-pvc
  labels:
    app: chromadb
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-storage
  resources:
    requests:
      storage: 20Gi
