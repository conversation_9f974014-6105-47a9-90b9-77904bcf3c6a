apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cost-optimizer-ingress
  namespace: cost-optimization
  labels:
    app: cost-optimizer
  annotations:
    # Nginx ingress controller annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    
    # Request size limits
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/client-max-body-size: "10m"
    
    # Timeouts
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';" always;
    
    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://your-frontend-domain.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key"
    nginx.ingress.kubernetes.io/cors-expose-headers: "Content-Length,Content-Range,X-Request-ID,X-Processing-Time"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    
    # Health checks
    nginx.ingress.kubernetes.io/health-check-path: "/health"
    nginx.ingress.kubernetes.io/health-check-interval: "30s"
    
    # Monitoring
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "false"
    
    # Certificate management (cert-manager)
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"

spec:
  tls:
  - hosts:
    - api.cost-optimizer.com
    - cost-optimizer.com
    secretName: cost-optimizer-tls
  rules:
  - host: api.cost-optimizer.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cost-optimizer-service
            port:
              number: 80
  - host: cost-optimizer.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cost-optimizer-service
            port:
              number: 80

---
# Alternative ingress for internal metrics (restricted access)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cost-optimizer-metrics-ingress
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    component: metrics
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # Restrict access to metrics endpoint
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # Basic auth for metrics (optional)
    nginx.ingress.kubernetes.io/auth-type: "basic"
    nginx.ingress.kubernetes.io/auth-secret: "cost-optimizer-metrics-auth"
    nginx.ingress.kubernetes.io/auth-realm: "Cost Optimizer Metrics"
    
    # No rate limiting for metrics scraping
    nginx.ingress.kubernetes.io/rate-limit: "0"
    
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

spec:
  tls:
  - hosts:
    - metrics.cost-optimizer.com
    secretName: cost-optimizer-metrics-tls
  rules:
  - host: metrics.cost-optimizer.com
    http:
      paths:
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: cost-optimizer-metrics
            port:
              number: 9090

---
# Ingress for development environment
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cost-optimizer-dev-ingress
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    environment: development
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    
    # More permissive settings for development
    nginx.ingress.kubernetes.io/rate-limit: "10000"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    
    # Development CORS settings
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"

spec:
  rules:
  - host: dev.cost-optimizer.com
    http:
      paths:
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: cost-optimizer-service
            port:
              number: 80
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: cost-optimizer-service
            port:
              number: 80
      - path: /openapi.json
        pathType: Exact
        backend:
          service:
            name: cost-optimizer-service
            port:
              number: 80
