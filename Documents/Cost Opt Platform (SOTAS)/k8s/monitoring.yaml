apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cost-optimizer-metrics
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    component: metrics
spec:
  selector:
    matchLabels:
      app: cost-optimizer
      component: metrics
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s
    honorLabels: true
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'go_.*'
      action: drop
    - sourceLabels: [__name__]
      regex: 'process_.*'
      action: drop

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cost-optimizer-alerts
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: cost-optimizer.rules
    rules:
    # High-level SLA alerts
    - alert: CostOptimizerHighErrorRate
      expr: |
        (
          sum(rate(http_requests_total{job="cost-optimizer-metrics",code!~"2.."}[5m])) /
          sum(rate(http_requests_total{job="cost-optimizer-metrics"}[5m]))
        ) > 0.05
      for: 2m
      labels:
        severity: critical
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer has high error rate"
        description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
        runbook_url: "https://runbooks.company.com/cost-optimizer/high-error-rate"

    - alert: CostOptimizerHighLatency
      expr: |
        histogram_quantile(0.99,
          sum(rate(http_request_duration_seconds_bucket{job="cost-optimizer-metrics"}[5m]))
          by (le)
        ) > 10
      for: 5m
      labels:
        severity: critical
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer has high latency"
        description: "99th percentile latency is {{ $value }}s"
        runbook_url: "https://runbooks.company.com/cost-optimizer/high-latency"

    - alert: CostOptimizerPodCrashLooping
      expr: |
        rate(kube_pod_container_status_restarts_total{namespace="cost-optimization",pod=~"cost-optimizer-.*"}[5m]) > 0
      for: 5m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer pod is crash looping"
        description: "Pod {{ $labels.pod }} is restarting frequently"
        runbook_url: "https://runbooks.company.com/cost-optimizer/pod-crash-loop"

    - alert: CostOptimizerPodNotReady
      expr: |
        kube_pod_status_ready{namespace="cost-optimization",pod=~"cost-optimizer-.*",condition="false"} == 1
      for: 5m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer pod not ready"
        description: "Pod {{ $labels.pod }} has been not ready for more than 5 minutes"
        runbook_url: "https://runbooks.company.com/cost-optimizer/pod-not-ready"

    - alert: CostOptimizerHighMemoryUsage
      expr: |
        (
          container_memory_working_set_bytes{namespace="cost-optimization",pod=~"cost-optimizer-.*",container="cost-optimizer"} /
          container_spec_memory_limit_bytes{namespace="cost-optimization",pod=~"cost-optimizer-.*",container="cost-optimizer"}
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer high memory usage"
        description: "Pod {{ $labels.pod }} memory usage is {{ $value | humanizePercentage }}"
        runbook_url: "https://runbooks.company.com/cost-optimizer/high-memory"

    - alert: CostOptimizerHighCPUUsage
      expr: |
        (
          rate(container_cpu_usage_seconds_total{namespace="cost-optimization",pod=~"cost-optimizer-.*",container="cost-optimizer"}[5m]) /
          container_spec_cpu_quota{namespace="cost-optimization",pod=~"cost-optimizer-.*",container="cost-optimizer"} * 100000
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer high CPU usage"
        description: "Pod {{ $labels.pod }} CPU usage is {{ $value | humanizePercentage }}"
        runbook_url: "https://runbooks.company.com/cost-optimizer/high-cpu"

    # Business metrics alerts
    - alert: CostOptimizerLowSavings
      expr: |
        avg_over_time(cost_savings_percentage[1h]) < 200
      for: 30m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer achieving low savings"
        description: "Average cost savings is {{ $value }}% over the last hour"
        runbook_url: "https://runbooks.company.com/cost-optimizer/low-savings"

    - alert: CostOptimizerCacheHitRateLow
      expr: |
        cache_hit_ratio < 0.7
      for: 15m
      labels:
        severity: warning
        service: cost-optimizer
      annotations:
        summary: "Cost Optimizer cache hit rate is low"
        description: "Cache hit rate is {{ $value | humanizePercentage }}"
        runbook_url: "https://runbooks.company.com/cost-optimizer/low-cache-hit-rate"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-cost-optimizer
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  cost-optimizer-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Cost Optimizer - Kubernetes",
        "tags": ["cost-optimizer", "kubernetes"],
        "style": "dark",
        "timezone": "browser",
        "refresh": "30s",
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "panels": [
          {
            "id": 1,
            "title": "Pod Status",
            "type": "stat",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "sum(kube_pod_status_ready{namespace=\"cost-optimization\",condition=\"true\"})",
                "legendFormat": "Ready Pods",
                "refId": "A"
              }
            ]
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{job=\"cost-optimizer-metrics\"}[5m]))",
                "legendFormat": "Requests/sec",
                "refId": "A"
              }
            ]
          },
          {
            "id": 3,
            "title": "Response Time",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
            "targets": [
              {
                "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{job=\"cost-optimizer-metrics\"}[5m])) by (le))",
                "legendFormat": "P50",
                "refId": "A"
              },
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"cost-optimizer-metrics\"}[5m])) by (le))",
                "legendFormat": "P95",
                "refId": "B"
              }
            ]
          },
          {
            "id": 4,
            "title": "Memory Usage",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16},
            "targets": [
              {
                "expr": "sum(container_memory_working_set_bytes{namespace=\"cost-optimization\",pod=~\"cost-optimizer-.*\"}) / 1024 / 1024",
                "legendFormat": "Memory Usage (MB)",
                "refId": "A"
              }
            ]
          },
          {
            "id": 5,
            "title": "CPU Usage",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16},
            "targets": [
              {
                "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"cost-optimization\",pod=~\"cost-optimizer-.*\"}[5m])) * 100",
                "legendFormat": "CPU Usage (%)",
                "refId": "A"
              }
            ]
          }
        ]
      }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: cost-optimizer-metrics-auth
  namespace: cost-optimization
type: Opaque
data:
  # Basic auth for metrics endpoint: admin:metrics123
  auth: YWRtaW46JGFwcjEkSDZ1YjJJVGwkNEhKdkVsVGRkS1V6eGtUZDJKN1EvLgo=
