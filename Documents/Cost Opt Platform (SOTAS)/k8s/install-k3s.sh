#!/bin/bash
# K3s Installation and Setup Script for Cost Optimization Platform
# Lightweight Kubernetes for production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[K3S-SETUP]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Configuration
K3S_VERSION=${K3S_VERSION:-"v1.28.5+k3s1"}
KUBECONFIG_PATH="/etc/rancher/k3s/k3s.yaml"
NAMESPACE="cost-optimizer"

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Install K3s
install_k3s() {
    log "Installing K3s version $K3S_VERSION..."
    
    # Download and install K3s
    curl -sfL https://get.k3s.io | INSTALL_K3S_VERSION="$K3S_VERSION" sh -s - \
        --write-kubeconfig-mode 644 \
        --disable traefik \
        --disable servicelb \
        --node-name cost-optimizer-node
    
    # Wait for K3s to be ready
    log "Waiting for K3s to be ready..."
    sleep 10
    
    # Check K3s status
    systemctl status k3s --no-pager
    
    log "✅ K3s installed successfully"
}

# Install Helm
install_helm() {
    log "Installing Helm package manager..."
    
    # Download and install Helm
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    
    # Verify Helm installation
    helm version
    
    log "✅ Helm installed successfully"
}

# Setup kubectl for non-root user
setup_kubectl() {
    local user_home="/home/<USER>"
    local user="parallels"
    
    log "Setting up kubectl for user $user..."
    
    # Create .kube directory
    sudo -u $user mkdir -p "$user_home/.kube"
    
    # Copy kubeconfig
    cp "$KUBECONFIG_PATH" "$user_home/.kube/config"
    chown $user:$user "$user_home/.kube/config"
    
    # Test kubectl access
    sudo -u $user kubectl get nodes
    
    log "✅ kubectl configured for user $user"
}

# Create namespace
create_namespace() {
    log "Creating namespace $NAMESPACE..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    log "✅ Namespace $NAMESPACE created"
}

# Install NGINX Ingress Controller
install_nginx_ingress() {
    log "Installing NGINX Ingress Controller..."
    
    # Add NGINX Helm repository
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    # Install NGINX Ingress Controller
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.service.type=NodePort \
        --set controller.service.nodePorts.http=30080 \
        --set controller.service.nodePorts.https=30443
    
    # Wait for deployment
    kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=120s
    
    log "✅ NGINX Ingress Controller installed"
}

# Install Prometheus & Grafana
install_monitoring() {
    log "Installing Prometheus and Grafana..."
    
    # Add Prometheus Helm repository
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    # Install kube-prometheus-stack
    helm upgrade --install monitoring prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.service.type=NodePort \
        --set prometheus.service.nodePort=30090 \
        --set grafana.service.type=NodePort \
        --set grafana.service.nodePort=30300 \
        --set grafana.adminPassword=admin123 \
        --set alertmanager.service.type=NodePort \
        --set alertmanager.service.nodePort=30093
    
    # Wait for deployment
    kubectl wait --namespace monitoring \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=prometheus \
        --timeout=300s
    
    log "✅ Monitoring stack installed"
}

# Install Redis
install_redis() {
    log "Installing Redis..."
    
    # Add Bitnami Helm repository
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    
    # Install Redis
    helm upgrade --install redis bitnami/redis \
        --namespace $NAMESPACE \
        --set auth.enabled=false \
        --set master.service.type=NodePort \
        --set master.service.nodePorts.redis=30379
    
    # Wait for deployment
    kubectl wait --namespace $NAMESPACE \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/name=redis \
        --timeout=120s
    
    log "✅ Redis installed"
}

# Create persistent volumes
create_persistent_volumes() {
    log "Creating persistent volumes..."
    
    # Create local storage directory
    mkdir -p /opt/k3s-storage/{database,chromadb,prometheus,grafana}
    
    # Apply persistent volume manifests
    kubectl apply -f - <<EOF
apiVersion: v1
kind: PersistentVolume
metadata:
  name: database-pv
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: /opt/k3s-storage/database
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - cost-optimizer-node
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: chromadb-pv
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  local:
    path: /opt/k3s-storage/chromadb
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - cost-optimizer-node
EOF
    
    log "✅ Persistent volumes created"
}

# Deploy application
deploy_application() {
    log "Deploying Cost Optimization Platform..."
    
    # Apply all Kubernetes manifests
    kubectl apply -f /home/<USER>/cost-opt-platform/k8s/ -n $NAMESPACE
    
    # Wait for deployments
    kubectl wait --namespace $NAMESPACE \
        --for=condition=available deployment \
        --all \
        --timeout=300s
    
    log "✅ Application deployed successfully"
}

# Print access information
print_access_info() {
    local node_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
    
    log "🎉 K3s setup complete!"
    echo
    info "Access Information:"
    echo "  Node IP: $node_ip"
    echo "  Kubeconfig: $KUBECONFIG_PATH"
    echo
    info "Services:"
    echo "  • Cost Optimizer API: http://$node_ip:30800"
    echo "  • Grafana Dashboard: http://$node_ip:30300 (admin/admin123)"
    echo "  • Prometheus: http://$node_ip:30090"
    echo "  • Redis: $node_ip:30379"
    echo
    info "Useful Commands:"
    echo "  • kubectl get pods -n $NAMESPACE"
    echo "  • kubectl logs -f deployment/fastapi-deployment -n $NAMESPACE"
    echo "  • kubectl get services -n $NAMESPACE"
    echo "  • helm list -A"
}

# Main installation function
main() {
    log "🚀 Starting K3s installation for Cost Optimization Platform"
    
    # Check prerequisites
    check_root
    
    # Install K3s
    install_k3s
    
    # Install Helm
    install_helm
    
    # Setup kubectl
    setup_kubectl
    
    # Create namespace
    create_namespace
    
    # Create persistent volumes
    create_persistent_volumes
    
    # Install NGINX Ingress
    install_nginx_ingress
    
    # Install monitoring
    install_monitoring
    
    # Install Redis
    install_redis
    
    # Deploy application
    if [ -d "/home/<USER>/cost-opt-platform/k8s" ]; then
        deploy_application
    else
        warn "Application manifests not found, skipping deployment"
    fi
    
    # Print access information
    print_access_info
}

# Cleanup function
cleanup() {
    log "🧹 Cleaning up K3s installation..."
    
    # Uninstall K3s
    /usr/local/bin/k3s-uninstall.sh
    
    # Remove storage directories
    rm -rf /opt/k3s-storage
    
    log "✅ K3s cleanup complete"
}

# Handle command line arguments
case "${1:-install}" in
    install)
        main
        ;;
    cleanup)
        cleanup
        ;;
    *)
        echo "Usage: $0 {install|cleanup}"
        exit 1
        ;;
esac
