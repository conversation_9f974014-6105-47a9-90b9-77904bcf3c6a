apiVersion: apps/v1
kind: Deployment
metadata:
  name: cost-optimizer
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    version: v1
    component: api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cost-optimizer
  template:
    metadata:
      labels:
        app: cost-optimizer
        version: v1
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: cost-optimizer
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: cost-optimizer
        image: cost-optimizer:latest
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: PORT
          value: "8000"
        - name: WORKERS
          value: "4"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cost-optimizer-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: cost-optimizer-secrets
              key: redis-url
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: cost-optimizer-secrets
              key: openrouter-api-key
        - name: JAEGER_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: cost-optimizer-config
              key: jaeger-endpoint
        - name: PROMETHEUS_MULTIPROC_DIR
          value: "/tmp/prometheus"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/live
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: prometheus-multiproc
          mountPath: /tmp/prometheus
        - name: app-logs
          mountPath: /app/logs
        - name: app-data
          mountPath: /app/data
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: tmp
        emptyDir: {}
      - name: prometheus-multiproc
        emptyDir: {}
      - name: app-logs
        emptyDir: {}
      - name: app-data
        emptyDir: {}
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "cost-optimizer"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - cost-optimizer
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - compute-optimized
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  name: cost-optimizer-service
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    component: api
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: http
    protocol: TCP
  selector:
    app: cost-optimizer

---
apiVersion: v1
kind: Service
metadata:
  name: cost-optimizer-metrics
  namespace: cost-optimization
  labels:
    app: cost-optimizer
    component: metrics
spec:
  type: ClusterIP
  ports:
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: cost-optimizer

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cost-optimizer
  namespace: cost-optimization
  labels:
    app: cost-optimizer

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cost-optimization
  name: cost-optimizer-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cost-optimizer-rolebinding
  namespace: cost-optimization
subjects:
- kind: ServiceAccount
  name: cost-optimizer
  namespace: cost-optimization
roleRef:
  kind: Role
  name: cost-optimizer-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cost-optimizer-config
  namespace: cost-optimization
  labels:
    app: cost-optimizer
data:
  jaeger-endpoint: "http://jaeger-collector:14268/api/traces"
  prometheus-endpoint: "http://prometheus:9090"
  log-level: "INFO"
  cache-ttl: "3600"
  max-request-size: "********"  # 10MB
  rate-limit-requests: "1000"
  rate-limit-window: "3600"

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cost-optimizer-pdb
  namespace: cost-optimization
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: cost-optimizer

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cost-optimizer-hpa
  namespace: cost-optimization
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cost-optimizer
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
