# Kubernetes Secrets Template
# Copy this file to secrets.yaml and replace placeholders with actual values
# DO NOT commit secrets.yaml to version control

apiVersion: v1
kind: Secret
metadata:
  name: cost-optimizer-secrets
  namespace: cost-optimization
  labels:
    app: cost-optimizer
type: Opaque
stringData:
  # Database connection string
  database-url: "****************************************************/cost_optimizer"
  
  # Redis connection string
  redis-url: "redis://redis-service:6379/0"
  
  # OpenRouter API key for LLM access
  openrouter-api-key: "your-openrouter-api-key-here"
  
  # JWT secret for authentication
  jwt-secret: "your-jwt-secret-key-here"
  
  # API keys for different tiers
  demo-api-key: "demo-key-12345"
  admin-api-key: "admin-key-67890"
  
  # External service API keys
  anthropic-api-key: "your-anthropic-api-key-here"
  openai-api-key: "your-openai-api-key-here"
  
  # Encryption key for sensitive data
  encryption-key: "your-32-character-encryption-key"
  
  # Monitoring and observability
  jaeger-auth-token: "your-jaeger-auth-token"
  prometheus-auth-token: "your-prometheus-auth-token"

---
apiVersion: v1
kind: Secret
metadata:
  name: cost-optimizer-tls
  namespace: cost-optimization
  labels:
    app: cost-optimizer
type: kubernetes.io/tls
data:
  # Base64 encoded TLS certificate
  tls.crt: LS0tLS1CRUdJTi... # Replace with actual certificate
  # Base64 encoded TLS private key
  tls.key: LS0tLS1CRUdJTi... # Replace with actual private key

---
apiVersion: v1
kind: Secret
metadata:
  name: cost-optimizer-registry
  namespace: cost-optimization
  labels:
    app: cost-optimizer
type: kubernetes.io/dockerconfigjson
data:
  # Base64 encoded Docker registry credentials
  .dockerconfigjson: eyJhdXRocyI6... # Replace with actual registry credentials

---
# Example of how to create secrets using kubectl:
# 
# kubectl create secret generic cost-optimizer-secrets \
#   --namespace=cost-optimization \
#   --from-literal=database-url="********************************/db" \
#   --from-literal=redis-url="redis://host:6379/0" \
#   --from-literal=openrouter-api-key="your-key" \
#   --from-literal=jwt-secret="your-secret"
#
# kubectl create secret tls cost-optimizer-tls \
#   --namespace=cost-optimization \
#   --cert=path/to/tls.crt \
#   --key=path/to/tls.key
#
# kubectl create secret docker-registry cost-optimizer-registry \
#   --namespace=cost-optimization \
#   --docker-server=your-registry.com \
#   --docker-username=your-username \
#   --docker-password=your-password \
#   --docker-email=<EMAIL>
