# Test Environment Configuration
ENVIRONMENT=testing

# Security
SECRET_KEY=test-secret-key-that-is-at-least-32-characters-long-for-testing-purposes

# Database
DATABASE_URL=sqlite:///:memory:

# Redis
REDIS_URL=redis://localhost:6379/15

# API Keys (test values)
OPENROUTER_API_KEY=test-openrouter-key
ANTHROPIC_API_KEY=test-anthropic-key
OPENAI_API_KEY=test-openai-key

# Logging
LOG_LEVEL=DEBUG

# Testing flags
TESTING=true
DISABLE_AUTH=true
DISABLE_RATE_LIMITING=true
DISABLE_TELEMETRY=true
