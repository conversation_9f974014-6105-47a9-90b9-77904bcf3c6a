# FAANG+ Quality Gates Configuration
# Zero-tolerance quality standards implementing Google, TikTok, and OpenAI practices

version: "1.0"

# Code Quality Gates
code_quality:
  # Test Coverage Requirements
  coverage:
    minimum_total: 85.0          # Minimum overall coverage
    minimum_new_code: 90.0       # Minimum coverage for new code
    minimum_unit_tests: 95.0     # Minimum unit test coverage
    minimum_integration: 80.0    # Minimum integration test coverage
    
  # Code Complexity Limits
  complexity:
    max_cyclomatic: 10           # Maximum cyclomatic complexity
    max_cognitive: 15            # Maximum cognitive complexity
    max_function_length: 50      # Maximum lines per function
    max_class_length: 500        # Maximum lines per class
    max_file_length: 1000        # Maximum lines per file
    
  # Code Quality Metrics
  maintainability:
    min_maintainability_index: 70  # Minimum maintainability index
    max_technical_debt_ratio: 5.0  # Maximum technical debt ratio (%)
    max_code_smells: 0             # Zero tolerance for code smells
    max_duplicated_lines: 3.0      # Maximum duplicated lines (%)

# Security Gates
security:
  # Vulnerability Tolerance
  vulnerabilities:
    max_critical: 0              # Zero critical vulnerabilities
    max_high: 0                  # Zero high vulnerabilities
    max_medium: 2                # Maximum medium vulnerabilities
    max_low: 10                  # Maximum low vulnerabilities
    
  # Security Scan Requirements
  scans:
    sast_required: true          # Static Application Security Testing
    dast_required: true          # Dynamic Application Security Testing
    dependency_scan: true        # Dependency vulnerability scanning
    container_scan: true         # Container security scanning
    
  # Security Standards
  compliance:
    owasp_top10: true           # OWASP Top 10 compliance
    cwe_top25: true             # CWE Top 25 compliance
    sans_top25: true            # SANS Top 25 compliance

# Performance Gates
performance:
  # Latency Requirements (milliseconds)
  latency:
    p50_max: 20.0               # 50th percentile maximum
    p95_max: 40.0               # 95th percentile maximum
    p99_max: 50.0               # 99th percentile maximum
    p99_9_max: 100.0            # 99.9th percentile maximum
    
  # Throughput Requirements
  throughput:
    min_rps: 1000               # Minimum requests per second
    min_optimization_rps: 800   # Minimum optimization requests per second
    min_cache_hit_rate: 0.95    # Minimum cache hit rate (95%)
    
  # Resource Utilization Limits
  resources:
    max_cpu_percent: 80.0       # Maximum CPU utilization
    max_memory_mb: 2000         # Maximum memory usage (MB)
    max_memory_growth_mb: 500   # Maximum memory growth during test
    max_file_descriptors: 1000  # Maximum file descriptors
    
  # Error Rate Limits
  reliability:
    max_error_rate: 0.01        # Maximum error rate (1%)
    max_timeout_rate: 0.005     # Maximum timeout rate (0.5%)
    min_availability: 0.999     # Minimum availability (99.9%)

# Functional Gates
functional:
  # Core Functionality Requirements
  optimization:
    min_cost_reduction: 0.80    # Minimum 80% cost reduction
    min_quality_score: 0.85     # Minimum quality preservation
    max_compression_loss: 0.15  # Maximum quality loss from compression
    
  # Cache Performance
  caching:
    min_hit_rate_redis: 0.90    # Redis cache hit rate
    min_hit_rate_semantic: 0.85 # Semantic cache hit rate
    max_cache_miss_latency: 100 # Maximum cache miss latency (ms)
    
  # Model Routing
  routing:
    min_routing_accuracy: 0.95  # Minimum routing accuracy
    max_routing_latency: 10     # Maximum routing decision time (ms)
    min_fallback_success: 0.99  # Minimum fallback success rate

# Deployment Gates
deployment:
  # Pre-deployment Checks
  pre_deployment:
    all_tests_pass: true        # All tests must pass
    security_scan_pass: true    # Security scans must pass
    performance_test_pass: true # Performance tests must pass
    manual_approval: true       # Manual approval required for production
    
  # Post-deployment Validation
  post_deployment:
    health_check_timeout: 300   # Health check timeout (seconds)
    smoke_test_timeout: 180     # Smoke test timeout (seconds)
    rollback_threshold: 0.05    # Error rate threshold for rollback
    
  # Blue-Green Deployment
  blue_green:
    traffic_shift_duration: 600 # Traffic shift duration (seconds)
    monitoring_duration: 1800   # Monitoring duration after shift (seconds)
    success_criteria:
      error_rate_increase: 0.01 # Maximum error rate increase
      latency_increase: 0.20     # Maximum latency increase (20%)

# Monitoring & Alerting Gates
monitoring:
  # SLI/SLO Requirements
  sli_slo:
    availability_slo: 0.999     # 99.9% availability SLO
    latency_slo_p99: 50         # P99 latency SLO (ms)
    error_budget_burn: 0.10     # Maximum error budget burn rate
    
  # Alert Configuration
  alerts:
    critical_response_time: 300  # Critical alert response time (seconds)
    high_response_time: 900      # High alert response time (seconds)
    escalation_timeout: 1800     # Alert escalation timeout (seconds)
    
  # Observability Requirements
  observability:
    trace_sampling_rate: 0.01   # Distributed tracing sampling rate
    metric_retention_days: 90   # Metrics retention period
    log_retention_days: 30      # Log retention period

# Quality Gate Enforcement
enforcement:
  # Gate Behavior
  blocking_gates:
    - security.vulnerabilities.max_critical
    - security.vulnerabilities.max_high
    - code_quality.coverage.minimum_total
    - performance.latency.p99_max
    - functional.optimization.min_cost_reduction
    
  warning_gates:
    - code_quality.complexity.max_cyclomatic
    - performance.resources.max_cpu_percent
    - monitoring.sli_slo.error_budget_burn
    
  # Bypass Conditions
  bypass:
    emergency_deployment: false  # Emergency bypass disabled
    hotfix_bypass: true         # Hotfix bypass allowed with approval
    security_bypass: false      # Security bypass never allowed
    
  # Approval Requirements
  approvals:
    security_team: true         # Security team approval required
    performance_team: true      # Performance team approval for perf changes
    architecture_review: true   # Architecture review for major changes

# Environment-Specific Overrides
environments:
  development:
    code_quality.coverage.minimum_total: 70.0
    performance.latency.p99_max: 100.0
    security.vulnerabilities.max_medium: 5
    
  staging:
    code_quality.coverage.minimum_total: 80.0
    performance.latency.p99_max: 75.0
    security.vulnerabilities.max_medium: 3
    
  production:
    # Use default strict values
    enforcement.bypass.hotfix_bypass: false
    monitoring.alerts.critical_response_time: 180

# Reporting Configuration
reporting:
  # Quality Dashboard
  dashboard:
    update_frequency: 300       # Dashboard update frequency (seconds)
    retention_days: 365         # Dashboard data retention
    
  # Quality Reports
  reports:
    daily_quality_report: true  # Generate daily quality reports
    weekly_trend_analysis: true # Generate weekly trend analysis
    monthly_review: true        # Generate monthly quality review
    
  # Notifications
  notifications:
    slack_channel: "#quality-gates"
    email_list: ["<EMAIL>"]
    pagerduty_integration: true

# Continuous Improvement
improvement:
  # Quality Metrics Trending
  trending:
    track_coverage_trend: true
    track_performance_trend: true
    track_security_trend: true
    alert_on_degradation: true
    
  # Automated Optimization
  automation:
    auto_fix_code_style: true
    auto_update_dependencies: false  # Manual review required
    auto_performance_tuning: false   # Manual review required
    
  # Learning & Adaptation
  learning:
    collect_quality_metrics: true
    analyze_failure_patterns: true
    suggest_threshold_updates: true
    ml_based_predictions: false      # Future enhancement
