# Claude Cost Optimization Platform - Environment Configuration Template
# Copy this file to .env and fill in your actual values
# 
# Security Note: Never commit .env files to version control
# Use different values for development, staging, and production

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment: development, staging, production
ENVIRONMENT=development

# Debug mode (true/false) - NEVER enable in production
DEBUG=true

# Application host and port
HOST=0.0.0.0
PORT=8000

# CORS allowed origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL connection string with pgvector support
DATABASE_URL=postgresql://costopt:costopt123@localhost:5432/costopt

# Database connection pool settings
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Redis connection string
REDIS_URL=redis://localhost:6379

# Redis connection pool settings
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=50

# ChromaDB configuration
CHROMADB_HOST=localhost
CHROMADB_PORT=8001
CHROMADB_COLLECTION_NAME=cost_optimization_cache

# Qdrant configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=semantic_cache
QDRANT_API_KEY=

# Weaviate configuration
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_API_KEY=

# Milvus configuration
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=optimization_vectors

# Elasticsearch configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_NAME=cost_optimization

# =============================================================================
# MODEL ROUTING & API KEYS
# =============================================================================

# OpenRouter.ai API key (REQUIRED)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenRouter configuration
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=https://costopt.example.com
OPENROUTER_APP_NAME=Claude Cost Optimizer

# Model configuration
CLAUDE_SONNET_MODEL=anthropic/claude-3.5-sonnet
DEEPSEEK_V3_MODEL=deepseek/deepseek-v3
LLAMA_FREE_MODEL=meta-llama/llama-3.1-8b-instruct:free
MISTRAL_FREE_MODEL=mistralai/mistral-7b-instruct:free
DEEPSEEK_CODER_MODEL=deepseek/deepseek-coder-33b-instruct

# =============================================================================
# OPTIMIZATION SETTINGS
# =============================================================================

# Quality and compression thresholds
DEFAULT_QUALITY_THRESHOLD=0.85
MAX_COMPRESSION_RATIO=0.7
CACHE_SIMILARITY_THRESHOLD=0.75
CACHE_TTL_SECONDS=3600

# Performance settings
MAX_CONCURRENT_REQUESTS=100
MAX_CONCURRENT_OPTIMIZATIONS=50
REQUEST_TIMEOUT_SECONDS=30

# Circuit breaker settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# Cost optimization targets
INITIAL_COST_REDUCTION_TARGET=200.0
MAX_COST_REDUCTION_TARGET=800.0
LEARNING_RATE=0.1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT secret key (generate a secure random key for production)
JWT_SECRET_KEY=your_jwt_secret_key_here_change_in_production

# JWT settings
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API key for internal services
API_KEY=your_api_key_here_change_in_production

# Security headers
SECURITY_HEADERS_ENABLED=true

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Jaeger tracing
JAEGER_HOST=localhost
JAEGER_PORT=14268
JAEGER_ENABLED=true

# Prometheus metrics
METRICS_ENABLED=true
METRICS_PORT=9090

# Logging configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# N8N workflow automation
N8N_WEBHOOK_URL=http://localhost:5678/webhook/cost-optimization
N8N_API_KEY=

# Terminal monitoring
TERMINAL_MONITOR_ENABLED=true
TERMINAL_MONITOR_INTERVAL=60

# =============================================================================
# DEVELOPMENT SETTINGS (Development/Testing Only)
# =============================================================================

# Test database (for running tests)
TEST_DATABASE_URL=postgresql://costopt:costopt123@localhost:5432/costopt_test

# Mock external services in development
MOCK_OPENROUTER=false
MOCK_VECTOR_DATABASES=false

# Development tools
ENABLE_PROFILING=false
ENABLE_DEBUG_TOOLBAR=false

# =============================================================================
# PRODUCTION SETTINGS (Production Only)
# =============================================================================

# SSL/TLS settings
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Production database settings
DATABASE_SSL_MODE=require
DATABASE_SSL_CERT=
DATABASE_SSL_KEY=
DATABASE_SSL_ROOT_CERT=

# Production monitoring
SENTRY_DSN=
DATADOG_API_KEY=

# Health check settings
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable specific features
COMPRESSION_ENABLED=true
ADAPTIVE_LEARNING_ENABLED=true
VECTOR_CACHING_ENABLED=true
QUALITY_ASSESSMENT_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Experimental features
EXPERIMENTAL_FEATURES_ENABLED=false
BETA_COMPRESSION_ALGORITHM=false
ADVANCED_MODEL_ROUTING=false
