#!/bin/bash

# Quick Start Script for Claude Optimizer Platform
# For when you just want to get up and running fast!

set -e

echo "⚡ Claude Optimizer - Quick Start"
echo "================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if already running
if docker-compose ps | grep -q "Up"; then
    print_status "Services are already running!"
    echo ""
    echo "📊 Dashboard:     http://localhost:8000"
    echo "📚 API Docs:      http://localhost:8000/docs"
    echo "📈 Grafana:       http://localhost:3000"
    echo ""
    echo "To restart: docker-compose restart"
    echo "To stop: docker-compose down"
    exit 0
fi

# Quick setup
print_status "Starting Claude Optimizer Platform..."

# Create .env if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating basic configuration..."
    cat > .env << EOF
OPENROUTER_API_KEY=your_openrouter_api_key_here
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=***************************************/costopt
REDIS_URL=redis://redis:6379
CHROMADB_HOST=chromadb
CHROMADB_PORT=8000
JAEGER_HOST=jaeger
JAEGER_PORT=14268
SECRET_KEY=dev-secret-key-change-in-production
ALLOWED_ORIGINS=["http://localhost:8000", "http://127.0.0.1:8000"]
EOF
fi

# Start services
print_status "Starting all services..."
docker-compose up -d

# Wait a moment
print_status "Waiting for services to initialize..."
sleep 15

# Check if API is responding
print_status "Checking if services are ready..."
for i in {1..20}; do
    if curl -f http://localhost:8000/health &> /dev/null; then
        print_success "All services are ready!"
        break
    fi
    if [ $i -eq 20 ]; then
        print_warning "Services might still be starting up..."
    fi
    sleep 3
done

echo ""
print_success "🎉 Claude Optimizer Platform is running!"
echo ""
echo "🌐 Open your browser and go to: http://localhost:8000"
echo ""
echo "📋 Quick Actions:"
echo "  • View logs:    docker-compose logs -f"
echo "  • Stop all:     docker-compose down"
echo "  • Restart:      docker-compose restart"
echo ""

# Check for API key
if grep -q "your_openrouter_api_key_here" .env; then
    print_warning "⚠️  Remember to add your OpenRouter API key!"
    echo "   1. Go to Settings in the dashboard"
    echo "   2. Enter your OpenRouter API key"
    echo "   3. Start optimizing!"
fi

echo ""
echo "🚀 Ready to save money on Claude API calls!"
