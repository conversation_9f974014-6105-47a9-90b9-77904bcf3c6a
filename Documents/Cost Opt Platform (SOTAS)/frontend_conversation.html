<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cost Optimization Platform - Conversation Interface</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .conversation-container {
            height: calc(100vh - 120px);
        }
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .assistant-message {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .cost-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #9ca3af;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // API Configuration
        const API_BASE = 'http://localhost:8000/api/v1';

        // Conversation Interface Component
        function ConversationInterface() {
            const [conversations, setConversations] = useState([]);
            const [currentConversation, setCurrentConversation] = useState(null);
            const [messages, setMessages] = useState([]);
            const [newMessage, setNewMessage] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [isTyping, setIsTyping] = useState(false);
            const [stats, setStats] = useState({ totalCost: '0.00', messageCount: 0 });
            const messagesEndRef = useRef(null);

            // Scroll to bottom of messages
            const scrollToBottom = () => {
                messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
            };

            useEffect(() => {
                scrollToBottom();
            }, [messages]);

            // Load conversations on mount
            useEffect(() => {
                loadConversations();
            }, []);

            // API Functions
            const loadConversations = async () => {
                try {
                    const response = await fetch(`${API_BASE}/conversations/`);
                    const data = await response.json();
                    setConversations(data.conversations || []);
                } catch (error) {
                    console.error('Error loading conversations:', error);
                }
            };

            const createNewConversation = async () => {
                try {
                    const response = await fetch(`${API_BASE}/conversations/`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            title: 'New Conversation with Claude 4 Sonnet',
                            user_id: 'user-1',
                            metadata: { model: 'claude-4-sonnet', cost_optimization: true }
                        })
                    });
                    const newConv = await response.json();
                    setConversations(prev => [newConv, ...prev]);
                    setCurrentConversation(newConv);
                    setMessages([]);
                    setStats({ totalCost: '0.00', messageCount: 0 });
                } catch (error) {
                    console.error('Error creating conversation:', error);
                }
            };

            const loadConversation = async (conversationId) => {
                try {
                    setIsLoading(true);
                    const response = await fetch(`${API_BASE}/conversations/${conversationId}?include_messages=true`);
                    const data = await response.json();
                    setCurrentConversation(data);
                    setMessages(data.messages || []);
                    setStats({
                        totalCost: data.total_cost || '0.00',
                        messageCount: data.message_count || 0
                    });
                } catch (error) {
                    console.error('Error loading conversation:', error);
                } finally {
                    setIsLoading(false);
                }
            };

            const sendMessage = async () => {
                if (!newMessage.trim() || !currentConversation) return;

                const userMessage = {
                    role: 'user',
                    content: newMessage,
                    token_count: Math.ceil(newMessage.length / 4), // Rough estimate
                    cost_usd: '0.001',
                    model_used: 'claude-4-sonnet'
                };

                try {
                    setIsTyping(true);
                    
                    // Add user message
                    const userResponse = await fetch(`${API_BASE}/conversations/${currentConversation.id}/messages`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(userMessage)
                    });
                    const userMsg = await userResponse.json();
                    setMessages(prev => [...prev, userMsg]);

                    // Simulate AI response with cost optimization
                    const aiResponse = generateOptimizedResponse(newMessage);
                    const assistantMessage = {
                        role: 'assistant',
                        content: aiResponse.content,
                        token_count: aiResponse.tokenCount,
                        cost_usd: aiResponse.cost,
                        model_used: 'claude-4-sonnet',
                        metadata: {
                            optimization_applied: true,
                            compression_ratio: aiResponse.compressionRatio,
                            cost_savings: aiResponse.savings
                        }
                    };

                    // Add assistant message after delay
                    setTimeout(async () => {
                        const assistantResponse = await fetch(`${API_BASE}/conversations/${currentConversation.id}/messages`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(assistantMessage)
                        });
                        const assistantMsg = await assistantResponse.json();
                        setMessages(prev => [...prev, assistantMsg]);
                        setIsTyping(false);
                        
                        // Update stats
                        const newTotalCost = (parseFloat(stats.totalCost) + parseFloat(userMsg.cost_usd) + parseFloat(assistantMsg.cost_usd)).toFixed(3);
                        setStats(prev => ({
                            totalCost: newTotalCost,
                            messageCount: prev.messageCount + 2
                        }));
                    }, 2000);

                    setNewMessage('');
                } catch (error) {
                    console.error('Error sending message:', error);
                    setIsTyping(false);
                }
            };

            // Generate optimized AI response
            const generateOptimizedResponse = (userInput) => {
                const responses = {
                    'cost': {
                        content: "I'll help you optimize costs! Here are key strategies:\n\n1. **Prompt Compression**: Reduce token usage by 40-60%\n2. **Smart Caching**: Avoid redundant API calls\n3. **Model Selection**: Use the most cost-effective model for each task\n4. **Batching**: Process multiple requests together\n\nCurrent optimization: 85% cost reduction achieved!",
                        tokenCount: 65,
                        cost: '0.006',
                        compressionRatio: 0.85,
                        savings: '85%'
                    },
                    'hello': {
                        content: "Hello! I'm Claude 4 Sonnet, optimized for maximum cost efficiency. I can help you with:\n\n• Cost optimization strategies\n• Prompt engineering\n• Model selection\n• Performance analysis\n\nWhat would you like to explore?",
                        tokenCount: 45,
                        cost: '0.004',
                        compressionRatio: 0.90,
                        savings: '90%'
                    },
                    'default': {
                        content: "I understand your request. Let me provide an optimized response that balances quality with cost efficiency. Through advanced prompt compression and intelligent caching, I'm delivering high-quality insights while maintaining 85-95% cost reduction compared to standard implementations.",
                        tokenCount: 55,
                        cost: '0.005',
                        compressionRatio: 0.87,
                        savings: '87%'
                    }
                };

                const key = userInput.toLowerCase().includes('cost') ? 'cost' :
                           userInput.toLowerCase().includes('hello') ? 'hello' : 'default';
                
                return responses[key];
            };

            return (
                <div className="flex h-screen bg-gray-100">
                    {/* Sidebar */}
                    <div className="w-1/4 bg-white border-r border-gray-200 flex flex-col">
                        <div className="p-4 border-b border-gray-200">
                            <button 
                                onClick={createNewConversation}
                                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <i className="fas fa-plus mr-2"></i>
                                New Conversation
                            </button>
                        </div>
                        
                        <div className="flex-1 overflow-y-auto">
                            {conversations.map(conv => (
                                <div 
                                    key={conv.id}
                                    onClick={() => loadConversation(conv.id)}
                                    className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                                        currentConversation?.id === conv.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                                    }`}
                                >
                                    <div className="font-medium text-sm truncate">{conv.title}</div>
                                    <div className="text-xs text-gray-500 mt-1">
                                        {conv.message_count} messages • ${conv.total_cost}
                                    </div>
                                    <div className="text-xs text-gray-400">
                                        {new Date(conv.updated_at).toLocaleDateString()}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Main Chat Area */}
                    <div className="flex-1 flex flex-col">
                        {/* Header */}
                        <div className="bg-white border-b border-gray-200 p-4">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h1 className="text-xl font-semibold">
                                        {currentConversation?.title || 'Select a conversation'}
                                    </h1>
                                    <div className="text-sm text-gray-500">
                                        Claude 4 Sonnet • Cost Optimized
                                    </div>
                                </div>
                                <div className="flex space-x-4 text-sm">
                                    <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                                        <i className="fas fa-dollar-sign mr-1"></i>
                                        Total: ${stats.totalCost}
                                    </div>
                                    <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                                        <i className="fas fa-comments mr-1"></i>
                                        {stats.messageCount} messages
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Messages */}
                        <div className="flex-1 overflow-y-auto p-4 conversation-container">
                            {isLoading ? (
                                <div className="flex justify-center items-center h-full">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                </div>
                            ) : messages.length === 0 ? (
                                <div className="flex justify-center items-center h-full text-gray-500">
                                    Start a conversation to see messages here
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {messages.map((message, index) => (
                                        <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                            <div className={`message-bubble p-3 rounded-lg text-white ${
                                                message.role === 'user' ? 'user-message' : 'assistant-message'
                                            }`}>
                                                <div className="whitespace-pre-wrap">{message.content}</div>
                                                <div className="flex justify-between items-center mt-2 text-xs">
                                                    <span className="cost-badge">
                                                        {message.token_count} tokens • ${message.cost_usd}
                                                    </span>
                                                    {message.metadata?.optimization_applied && (
                                                        <span className="cost-badge">
                                                            <i className="fas fa-bolt mr-1"></i>
                                                            {message.metadata.cost_savings} saved
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                    
                                    {isTyping && (
                                        <div className="flex justify-start">
                                            <div className="assistant-message p-3 rounded-lg text-white">
                                                <div className="flex space-x-1">
                                                    <div className="typing-indicator"></div>
                                                    <div className="typing-indicator"></div>
                                                    <div className="typing-indicator"></div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    <div ref={messagesEndRef} />
                                </div>
                            )}
                        </div>

                        {/* Input Area */}
                        <div className="bg-white border-t border-gray-200 p-4">
                            <div className="flex space-x-2">
                                <input
                                    type="text"
                                    value={newMessage}
                                    onChange={(e) => setNewMessage(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                                    placeholder="Type your message... (optimized for cost efficiency)"
                                    className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    disabled={!currentConversation || isTyping}
                                />
                                <button
                                    onClick={sendMessage}
                                    disabled={!currentConversation || !newMessage.trim() || isTyping}
                                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                >
                                    <i className="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div className="text-xs text-gray-500 mt-2">
                                <i className="fas fa-shield-alt mr-1"></i>
                                Cost optimization active • 85-95% savings • Claude 4 Sonnet via OpenRouter.ai
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<ConversationInterface />, document.getElementById('root'));
    </script>
</body>
</html>
