# Cost Optimization Platform Environment Variables
# Copy this file to .env and fill in your API keys

# OpenAI API Key (for GPT models)
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic API Key (for Claude models)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# OpenRouter API Key (for access to multiple models)
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Redis URL for caching (optional, will work without Redis)
REDIS_URL=redis://localhost:6379

# Database URL (SQLite by default)
DATABASE_URL=sqlite:///./data/cost_optimizer.db

# LiteLLM Configuration
LITELLM_LOG=INFO
LITELLM_DROP_PARAMS=true

# Application Settings
APP_HOST=0.0.0.0
APP_PORT=8000
APP_RELOAD=true

# Security (change in production)
SECRET_KEY=your-secret-key-here
MASTER_KEY=sk-1234567890abcdef

# Cost Optimization Settings
DEFAULT_QUALITY_THRESHOLD=0.8
DEFAULT_MAX_COST=0.10
CACHE_TTL=3600

# Rate Limiting
RATE_LIMIT_RPM=1000
RATE_LIMIT_TPM=100000

# Budget Alerts
DAILY_BUDGET_USD=100.0
BUDGET_ALERT_THRESHOLD=0.8
