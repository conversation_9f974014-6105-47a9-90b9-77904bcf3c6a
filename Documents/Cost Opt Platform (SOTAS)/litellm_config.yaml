model_list:
  # Premium Models (High Quality, Higher Cost)
  - model_name: claude-4-sonnet
    litellm_params:
      model: anthropic/claude-3-5-sonnet-20241022
      api_key: os.environ/ANTHROPIC_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.000003
      output_cost_per_token: 0.000015
      max_tokens: 8192
      tier: premium
      
  - model_name: gpt-4o
    litellm_params:
      model: openai/gpt-4o
      api_key: os.environ/OPENAI_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.000005
      output_cost_per_token: 0.000015
      max_tokens: 4096
      tier: premium

  # Standard Models (Good Quality, Moderate Cost)
  - model_name: deepseek-v3
    litellm_params:
      model: openrouter/deepseek/deepseek-v3
      api_key: os.environ/OPENROUTER_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.000002
      output_cost_per_token: 0.000009
      max_tokens: 8192
      tier: standard
      
  - model_name: llama-3-70b
    litellm_params:
      model: openrouter/meta-llama/llama-3.1-70b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.000001
      output_cost_per_token: 0.000003
      max_tokens: 4096
      tier: standard

  # Budget Models (Basic Quality, Low Cost)
  - model_name: llama-3-8b
    litellm_params:
      model: openrouter/meta-llama/llama-3.1-8b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.0000001
      output_cost_per_token: 0.0000003
      max_tokens: 4096
      tier: budget

  - model_name: mistral-7b
    litellm_params:
      model: openrouter/mistralai/mistral-7b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
    model_info:
      mode: chat
      input_cost_per_token: 0.0000001
      output_cost_per_token: 0.0000003
      max_tokens: 4096
      tier: budget

# Router Configuration for Cost Optimization
router_settings:
  routing_strategy: cost-optimized
  fallbacks:
    - claude-4-sonnet
    - gpt-4o
    - deepseek-v3
    - llama-3-70b
    - llama-3-8b
    - mistral-7b
  
  # Cost optimization rules
  cost_optimization:
    enabled: true
    quality_threshold: 0.8
    max_cost_per_request: 0.10
    prefer_cheaper_models: true
    
  # Caching configuration
  cache:
    type: redis
    ttl: 3600  # 1 hour
    similarity_threshold: 0.9
    
  # Rate limiting
  rate_limit:
    rpm: 1000  # requests per minute
    tpm: 100000  # tokens per minute
    
  # Retry configuration
  retry:
    max_retries: 3
    backoff_factor: 2
    
# General settings
general_settings:
  master_key: sk-1234567890abcdef  # Change this in production
  database_url: sqlite:///./data/litellm.db
  ui_access_mode: admin_only
  
  # Cost tracking
  cost_tracking:
    enabled: true
    budget_alerts: true
    daily_budget: 100.0  # USD
    
  # Logging
  logging:
    level: INFO
    format: json
    
  # Security
  security:
    cors_origins: ["*"]
    api_key_required: true
    
# Environment variables template
environment:
  OPENAI_API_KEY: "your-openai-api-key"
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
  OPENROUTER_API_KEY: "your-openrouter-api-key"
  REDIS_URL: "redis://localhost:6379"
  DATABASE_URL: "sqlite:///./data/litellm.db"
